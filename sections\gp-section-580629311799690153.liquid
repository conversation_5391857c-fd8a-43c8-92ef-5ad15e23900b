
    
    <style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-580629311799690153.gps.gpsil [style*="--as:"]{align-self:var(--as)}.gps-580629311799690153.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-580629311799690153.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-580629311799690153.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-580629311799690153.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-580629311799690153.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-580629311799690153.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-580629311799690153.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-580629311799690153.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-580629311799690153.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-580629311799690153.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-580629311799690153.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-580629311799690153.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-580629311799690153.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-580629311799690153.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-580629311799690153.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-580629311799690153.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-580629311799690153.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-580629311799690153.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-580629311799690153.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-580629311799690153.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-580629311799690153.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-580629311799690153.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-580629311799690153.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-580629311799690153.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-580629311799690153.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-580629311799690153.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-580629311799690153.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-580629311799690153.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-580629311799690153.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-580629311799690153.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-580629311799690153.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-580629311799690153.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-580629311799690153.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-580629311799690153.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-580629311799690153.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-580629311799690153.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-580629311799690153.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-580629311799690153.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-580629311799690153.gps.gpsil [style*="--rg:"]{row-gap:var(--rg)}.gps-580629311799690153.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-580629311799690153.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-580629311799690153.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-580629311799690153.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-580629311799690153.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-580629311799690153.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-580629311799690153.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-580629311799690153.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-580629311799690153.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-580629311799690153.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-580629311799690153.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-580629311799690153.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-580629311799690153.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-580629311799690153.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-580629311799690153.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-580629311799690153.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-580629311799690153.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-580629311799690153.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-580629311799690153.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-580629311799690153.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-580629311799690153.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-580629311799690153 .\!gp-relative{position:relative!important}.gps-580629311799690153 .gp-relative{position:relative}.gps-580629311799690153 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-580629311799690153 .gp-mb-0{margin-bottom:0}.gps-580629311799690153 .gp-inline-block{display:inline-block}.gps-580629311799690153 .gp-flex{display:flex}.gps-580629311799690153 .gp-inline-flex{display:inline-flex}.gps-580629311799690153 .gp-grid{display:grid}.gps-580629311799690153 .\!gp-hidden{display:none!important}.gps-580629311799690153 .gp-hidden{display:none}.gps-580629311799690153 .gp-h-auto{height:auto}.gps-580629311799690153 .gp-h-full{height:100%}.gps-580629311799690153 .gp-w-\[0\.01px\]{width:.01px}.gps-580629311799690153 .gp-w-full{width:100%}.gps-580629311799690153 .gp-max-w-full{max-width:100%}.gps-580629311799690153 .gp-flex-none{flex:none}.gps-580629311799690153 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-580629311799690153 .gp-flex-col{flex-direction:column}.gps-580629311799690153 .gp-items-center{align-items:center}.gps-580629311799690153 .gp-overflow-hidden{overflow:hidden}.gps-580629311799690153 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-580629311799690153 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-580629311799690153 .gp-duration-200{transition-duration:.2s}.gps-580629311799690153 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-580629311799690153 .tablet\:\!gp-hidden{display:none!important}.gps-580629311799690153 .tablet\:gp-hidden{display:none}.gps-580629311799690153 .tablet\:gp-h-auto{height:auto}.gps-580629311799690153 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-580629311799690153 .mobile\:\!gp-hidden{display:none!important}.gps-580629311799690153 .mobile\:gp-hidden{display:none}.gps-580629311799690153 .mobile\:gp-h-auto{height:auto}.gps-580629311799690153 .mobile\:gp-flex-none{flex:none}}.gps-580629311799690153 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}</style>
    
    
    

    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}

    
        <section
          class="gp-mx-auto gp-max-w-full [&_*]:gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
          style="--w:100%;--w-tablet:calc(var(--g-ct-w, 100%) + 2 * var(--g-ct-p, 15px));--w-mobile:calc(var(--g-ct-w, 100%) + 2 * var(--g-ct-p, 15px));--pl:none;--pl-tablet:none;--pl-mobile:none;--pr:none;--pr-tablet:none;--pr-mobile:none"
        >
          
    <gp-row gp-data='{"background":{"desktop":{"type":"color","color":"transparent","image":{"src":"","width":0,"height":0},"size":"cover","position":{"x":50,"y":50},"repeat":"no-repeat","attachment":"scroll"}},"uid":"gxx8TogGuG"}' data-id="gxx8TogGuG" id="gxx8TogGuG" data-same-height-subgrid-container class="gxx8TogGuG gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--blockPadding:base;--pt:var(--g-s-2xl);--pb:var(--g-s-2xl);--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:var(--g-s-2xl);--pr-mobile:0px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-3, line-3);--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--op:100%;--cg:16px;--rg:0px;--gtc:minmax(0, 12fr);--w:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="giwnGenjkF gp-relative gp-flex gp-flex-col"
    >
      
  <script src="https://assets.gemcommerce.com/assets-v2/gp-marquee-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  <gp-marquee data-id="g0Nv-KaYKu" gp-data='{"setting":{"activeItem":"0","childItem":["Item 1","Item 2","Item 3","Item 4","Item 5"],"direction":"left","hasItemShadow":false,"hoverItem":"0","iconSeparatorSvg":"<svg height=\"100%\" width=\"100%\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 256 256\" fill=\"currentColor\">\n              <path fill=\"currentColor\" strokelinecap=\"round\" strokelinejoin=\"round\" d=\"M232,128A104,104,0,1,1,128,24,104.13,104.13,0,0,1,232,128Z\"></path></svg>","isPreview":false,"isShowIconSeparator":false,"itemWidthType":{"desktop":"BASE_ON_NUMBER_OF_ITEMS","mobile":"CUSTOM","tablet":"BASE_ON_NUMBER_OF_ITEMS"},"speed":"0.5","stopOnHover":false,"uid":"g0Nv-KaYKu"},"styles":{"align":{"desktop":"center"},"backgroundColor":{"desktop":"transparent"},"iconSeparatorColor":"#0C0C0C","iconSeparatorSize":{"desktop":24,"mobile":24,"tablet":24},"itemBackgroundColor":{"desktop":"transparent"},"itemBorderStyle":{"border":"none","borderType":"none","borderWidth":"1px","color":"#000000","isCustom":false,"width":"1px 1px 1px 1px"},"itemCorner":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"itemMaxWidth":{"desktop":"300px","mobile":"150px","tablet":"300px"},"itemShadow":{"angle":90,"blur":"12px","color":"#121212","distance":"4px","spread":"0px","type":"shadow-1"},"itemSpacing":{"desktop":"10px","mobile":"5px","tablet":"8px"},"sizeSetting":{"desktop":{"height":"auto","shapeLinked":false,"width":"100%"}}}}'
   class="g0Nv-KaYKu">
   <div class="gp-flex gp-w-full gp-relative" style="--jc:center">
      <div class="!gp-relative" style="--h:auto;--h-tablet:auto;--h-mobile:auto;--w:100%;--w-tablet:100%;--w-mobile:100%;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:12px;--pb:12px;--pt-mobile:0px;--pb-mobile:0px;--bg:transparent">
         <div class="gem-marquee gp-overflow-hidden gp-flex gp-items-center gp-w-full gp-h-full g0Nv-KaYKu">
            <div class="gp-overflow-hidden gp-w-full gp-h-full">
               
  <div style="--pause-on-hover:running;--pause-on-click:running;--width:100%;--transform:none;min-width:100%" class="rfm-marquee-container">
    <div class="rfm-marquee" style="--play:running;--direction:normal;--delay:0s;--iteration-count:infinite;--min-width:auto;--w:100%;--w-tablet:100%;--w-mobile:auto;--minw:100%;--minw-tablet:100%;--minw-mobile:auto">
      <div class="rfm-initial-child-container">
        
        <div style="--transform:none;--w:100%;--w-tablet:100%;--w-mobile:auto;--minw:100%;--minw-tablet:100%;--minw-mobile:auto" class="rfm-child">
          
               <div class="gp-flex gp-items-center gp-marque-child-item gem-child-marquee-item gp-w-full">
                  
                  <div class="gp-inline-flex gp-items-center gp-relative gp-transition-all gp-w-full"
                     style="--pr:10px;--pr-tablet:8px;--pr-mobile:5px;text-wrap:wrap;--w:calc(100% / 5);--w-tablet:calc(100% / 5);--w-mobile:150px;--minw:calc(100% / 5);--minw-tablet:calc(100% / 5);--minw-mobile:150px;--maxw:calc(100% / 5);--maxw-tablet:calc(100% / 5);--maxw-mobile:150px">
                     <div
      class="gp-relative gp-overflow-hidden gem-marquee-item  g-5XcDD5ov gem-marquee-item-g-5XcDD5ov"
      style="--bg:transparent;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%);--shadow:none;--b:none;--bw:1px 1px 1px 1px;--bc:#000000"
    >
      <div className="first-block gp-w-[0.01px]"></div>
      
    <div
      role="presentation"
      data-id="gO41beZrqf"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:0px;--pl:0px;--pb:0px;--pr:0px;--pt-tablet:0px;--pl-tablet:0px;--pb-tablet:0px;--pr-tablet:0px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--ta:center"
      class="gp-group/image gp-relative force-publish-1757425942271 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto gO41beZrqf"
    >
      <div
        
        style="border-radius:inherit;--jc:center"
        class="gp-h-full gp-w-full gp-flex pointer-events-auto"
      >
        
    <img
      width="1948" height="2428" draggable="false" quality-type quality-percent data-sizes="auto" sizes="100vw" src="{{ "gempages_553400155311702965-05dc4cf7-3e77-418d-b359-f44e18e7a51f.png" | file_url }}" isNotLazyload="true" srcset loading="eager" fetchpriority="high"
      id=""
      style="--w:auto;--w-tablet:auto;--w-mobile:auto;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--radiusType:none;--shadow:none;--aspect:3/4;--aspect-tablet:3/4;--aspect-mobile:3/4;--objf:cover"
      class="gp-inline-block gp-w-full gp-max-w-full gp_force_load"
    />
      </div>
    </div>
  
    </div>
                     
                  </div>
                  
                  <div class="gp-inline-flex gp-items-center gp-relative gp-transition-all gp-w-full"
                     style="--pr:10px;--pr-tablet:8px;--pr-mobile:5px;text-wrap:wrap;--w:calc(100% / 5);--w-tablet:calc(100% / 5);--w-mobile:150px;--minw:calc(100% / 5);--minw-tablet:calc(100% / 5);--minw-mobile:150px;--maxw:calc(100% / 5);--maxw-tablet:calc(100% / 5);--maxw-mobile:150px">
                     <div
      class="gp-relative gp-overflow-hidden gem-marquee-item  g4jKeGAWxB gem-marquee-item-g4jKeGAWxB"
      style="--bg:transparent;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%);--shadow:none;--b:none;--bw:1px 1px 1px 1px;--bc:#000000"
    >
      <div className="first-block gp-w-[0.01px]"></div>
      
    <div
      role="presentation"
      data-id="gIbtiRWl5z"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:0px;--pl:0px;--pb:0px;--pr:0px;--pt-tablet:0px;--pl-tablet:0px;--pb-tablet:0px;--pr-tablet:0px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--ta:center"
      class="gp-group/image gp-relative force-publish-1757425942271 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto gIbtiRWl5z"
    >
      <div
        
        style="border-radius:inherit;--jc:center"
        class="gp-h-full gp-w-full gp-flex pointer-events-auto"
      >
        
    <img
      width="2494" height="3118" draggable="false" quality-type quality-percent data-sizes="auto" sizes="100vw" src="{{ "gempages_553400155311702965-61e8a67b-9543-4a3f-bace-8bf61512cc29.jpg" | file_url }}" isNotLazyload="true" srcset loading="eager" fetchpriority="high"
      id=""
      style="--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--radiusType:none;--shadow:none;--aspect:3/4;--aspect-tablet:3/4;--aspect-mobile:3/4;--objf:cover"
      class="gp-inline-block gp-w-full gp-max-w-full gp_force_load"
    />
      </div>
    </div>
  
    </div>
                     
                  </div>
                  
                  <div class="gp-inline-flex gp-items-center gp-relative gp-transition-all gp-w-full"
                     style="--pr:10px;--pr-tablet:8px;--pr-mobile:5px;text-wrap:wrap;--w:calc(100% / 5);--w-tablet:calc(100% / 5);--w-mobile:150px;--minw:calc(100% / 5);--minw-tablet:calc(100% / 5);--minw-mobile:150px;--maxw:calc(100% / 5);--maxw-tablet:calc(100% / 5);--maxw-mobile:150px">
                     <div
      class="gp-relative gp-overflow-hidden gem-marquee-item  gONXMUswK1 gem-marquee-item-gONXMUswK1"
      style="--bg:transparent;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%);--shadow:none;--b:none;--bw:1px 1px 1px 1px;--bc:#000000"
    >
      <div className="first-block gp-w-[0.01px]"></div>
      
    <div
      role="presentation"
      data-id="gPtGYmVm5h"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:0px;--pl:0px;--pb:0px;--pr:0px;--pt-tablet:0px;--pl-tablet:0px;--pb-tablet:0px;--pr-tablet:0px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--ta:center"
      class="gp-group/image gp-relative force-publish-1757425942272 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto gPtGYmVm5h"
    >
      <div
        
        style="border-radius:inherit;--jc:center"
        class="gp-h-full gp-w-full gp-flex pointer-events-auto"
      >
        
    <img
      width="2005" height="2731" draggable="false" quality-type quality-percent data-sizes="auto" sizes="100vw" src="{{ "gempages_553400155311702965-63be5449-ce6c-497f-b312-ef3db15d68ad.png" | file_url }}" isNotLazyload="true" srcset loading="eager" fetchpriority="high"
      id=""
      style="--w:auto;--w-tablet:auto;--w-mobile:auto;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--radiusType:none;--shadow:none;--aspect:3/4;--aspect-tablet:3/4;--aspect-mobile:3/4;--objf:cover"
      class="gp-inline-block gp-w-full gp-max-w-full gp_force_load"
    />
      </div>
    </div>
  
    </div>
                     
                  </div>
                  
                  <div class="gp-inline-flex gp-items-center gp-relative gp-transition-all gp-w-full"
                     style="--pr:10px;--pr-tablet:8px;--pr-mobile:5px;text-wrap:wrap;--w:calc(100% / 5);--w-tablet:calc(100% / 5);--w-mobile:150px;--minw:calc(100% / 5);--minw-tablet:calc(100% / 5);--minw-mobile:150px;--maxw:calc(100% / 5);--maxw-tablet:calc(100% / 5);--maxw-mobile:150px">
                     <div
      class="gp-relative gp-overflow-hidden gem-marquee-item  geU_GaEPin gem-marquee-item-geU_GaEPin"
      style="--bg:transparent;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%);--shadow:none;--b:none;--bw:1px 1px 1px 1px;--bc:#000000"
    >
      <div className="first-block gp-w-[0.01px]"></div>
      
    <div
      role="presentation"
      data-id="gclbsv-blL"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:0px;--pl:0px;--pb:0px;--pr:0px;--pt-tablet:0px;--pl-tablet:0px;--pb-tablet:0px;--pr-tablet:0px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--ta:center"
      class="gp-group/image gp-relative force-publish-1757425942273 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto gclbsv-blL"
    >
      <div
        
        style="border-radius:inherit;--jc:center"
        class="gp-h-full gp-w-full gp-flex pointer-events-auto"
      >
        
    <img
      width="2445" height="3056" draggable="false" quality-type quality-percent data-sizes="auto" sizes="100vw" src="{{ "gempages_553400155311702965-dce85f57-86c5-48d5-971a-5dbf60d81c76.jpg" | file_url }}" isNotLazyload="true" srcset loading="eager" fetchpriority="high"
      id=""
      style="--w:auto;--w-tablet:auto;--w-mobile:auto;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--radiusType:none;--shadow:none;--aspect:3/4;--aspect-tablet:3/4;--aspect-mobile:3/4;--objf:cover"
      class="gp-inline-block gp-w-full gp-max-w-full gp_force_load"
    />
      </div>
    </div>
  
    </div>
                     
                  </div>
                  
                  <div class="gp-inline-flex gp-items-center gp-relative gp-transition-all gp-w-full"
                     style="--pr:10px;--pr-tablet:8px;--pr-mobile:5px;text-wrap:wrap;--w:calc(100% / 5);--w-tablet:calc(100% / 5);--w-mobile:150px;--minw:calc(100% / 5);--minw-tablet:calc(100% / 5);--minw-mobile:150px;--maxw:calc(100% / 5);--maxw-tablet:calc(100% / 5);--maxw-mobile:150px">
                     <div
      class="gp-relative gp-overflow-hidden gem-marquee-item  gm_VcCZ9-1 gem-marquee-item-gm_VcCZ9-1"
      style="--bg:transparent;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%);--shadow:none;--b:none;--bw:1px 1px 1px 1px;--bc:#000000"
    >
      <div className="first-block gp-w-[0.01px]"></div>
      
    <div
      role="presentation"
      data-id="g5TkfZFOoP"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:0px;--pl:0px;--pb:0px;--pr:0px;--pt-tablet:0px;--pl-tablet:0px;--pb-tablet:0px;--pr-tablet:0px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--ta:center"
      class="gp-group/image gp-relative force-publish-1757425942273 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto g5TkfZFOoP"
    >
      <div
        
        style="border-radius:inherit;--jc:center"
        class="gp-h-full gp-w-full gp-flex pointer-events-auto"
      >
        
    <img
      width="1624" height="2438" draggable="false" quality-type quality-percent data-sizes="auto" sizes="100vw" src="{{ "gempages_553400155311702965-5824f484-4d82-4eaf-b333-4215136fb6b8.png" | file_url }}" isNotLazyload="true" srcset loading="eager" fetchpriority="high"
      id=""
      style="--w:auto;--w-tablet:auto;--w-mobile:auto;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--radiusType:none;--shadow:none;--aspect:3/4;--aspect-tablet:3/4;--aspect-mobile:3/4;--objf:cover"
      class="gp-inline-block gp-w-full gp-max-w-full gp_force_load"
    />
      </div>
    </div>
  
    </div>
                     
                  </div>
                  
               </div>
               
        </div>
        
      </div>
    </div>
    <div class="rfm-marquee placeholder-marquee" style="--play:running;--direction:normal;--delay:0s;--iteration-count:infinite;--min-width:auto;--w:100%;--w-tablet:100%;--w-mobile:auto;--minw:100%;--minw-tablet:100%;--minw-mobile:auto"></div>
  </div>
  
            </div>
         </div>
      </div>
   </div>
</gp-marquee>

    </div>

      
    </gp-row>
  
        </section>
      
  
    
    
{% schema %}
  {
    
    "name": "Section 12",
    "tag": "section",
    "class": "gps-580629311799690153 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=578718746651132539&sectionId=580629311799690153)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
  