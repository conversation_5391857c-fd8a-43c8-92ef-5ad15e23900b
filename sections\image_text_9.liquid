
<section class="image_text_9" id="{{ section.id }}"  style="background:{{section.settings.bg_color}}; {% if section.settings.bg_image != blank %}background-image: url({{ section.settings.bg_image | img_url: 'master' }}); background-size: cover; background-repeat: no-repeat; background-position: center;{% endif %};">
  <div class="page_wrapper">
    <div class="wrapper">
      <div class="text_block">

        {% if section.settings.heading != blank %}
        <h2 class="heading">{{ section.settings.heading }}</h2>
        {% endif %}
  
        <div class="timmer_wrap">
           {% for block in section.blocks %}
             <div class="timmer_item">
               {% if block.settings.image != blank %}
          <div class="image">
          <img src="{{ block.settings.image | img_url: 'master' }}">
            </div>
                         {% endif %}
          <div class="content_block">
              {% if block.settings.heading != blank %}
            <h4 class="heading">{{ block.settings.heading }} {% if block.settings.tag != blank %}<span>{{ block.settings.tag }}</span>{% endif %}</h4>
            {% endif %}
                         {% if block.settings.content != blank %}
            <div class="content">{{ block.settings.content }}</div>
            {% endif %}
          </div>
             </div>
          {% endfor %}
        </div>

<div class="swiper-container timmer_swiper" style="display: none;">
  <div class="swiper-wrapper timmer_wrap">
    {% for block in section.blocks %}
      {% if block.settings.image != blank %}
        <div class="swiper-slide timmer_item">
          <div class="image">
          <img src="{{ block.settings.image | img_url: 'master' }}">
            </div>
            <div class="content_block">
              {% if block.settings.heading != blank %}
            <h4 class="heading">{{ block.settings.heading }} {% if block.settings.tag != blank %}<span>{{ block.settings.tag }}</span>{% endif %}</h4>
            {% endif %}
                         {% if block.settings.content != blank %}
            <div class="content">{{ block.settings.content }}</div>
            {% endif %}
          </div>
        </div>
      {% endif %}
    {% endfor %}
  </div>
      <div class="swiper-pagination"></div>
</div>


        
          
      </div>
   
      
    </div>
  </div>
</section>

<style>


section.image_text_9 .wrapper .text_block {
    width: 100%;
    display: flex;
      gap: 40px;
    flex-direction: column;

}
section.image_text_9 .wrapper .text_block h2.heading {
    font-size: 74px;
    font-family: Helvetica-Bold;
    margin: 0;
  color:#2a2b2b;

      text-transform: none;
}
  section.image_text_9 .wrapper .text_block h2.heading p{
      margin: 0;
  }
section.image_text_9 .wrapper .text_block .content p {
    margin: 0;
    font-size: 25px;
    font-family: Helvetica-Bold;
    color: #676666;
  font-weight:700;
}
  section.image_text_9 .wrapper .image_block {
    width: 100%;
    font-size: 0;
}
  section.image_text_9 .wrapper .image_block img{
    width: 100%;
    }
  /* .image_text_9 .wrapper {

    display: grid;
        align-items: center;
    gap:50px;
} */
  section.image_text_9 .wrapper .text_block .content {
    width: 100%;

}
  section.image_text_9 .wrapper .text_block .sub_heading {
    margin: 0;
    font-size: 20px;
    font-family: Helvetica-Bold;
    color: #676666;
}

section.image_text_9 .wrapper .text_block .timmer_wrap {
    display: grid
;
    grid-template-columns: repeat(3, 1fr);
      gap: 100px;
}
  section.image_text_9 .wrapper .text_block .timmer_wrap .timmer_item {
    width: 100%;
}
  section.image_text_9 .wrapper .text_block .timmer_wrap .timmer_item img{
    width: 100%;
}
  section.image_text_9 .wrapper .text_block .icon_wrap {
    display: grid
;
    grid-template-columns: repeat(14, auto);
        gap: 20px;
  }
  section.image_text_9 .wrapper .text_block .icon_wrap .icon_item {
    width: 100%;
    max-width: 50px;
}
  section.image_text_9 .wrapper .text_block .icon_wrap .icon_item img {
    width: 100%;
}
section.image_text_9 .wrapper .text_block .icon_wrap .icon_item.more_btn {
    display: flex !important
;
    justify-content: left;
    align-items: center;
    max-width: 100%;
    font-size: 15px;
    font-family: Helvetica-Bold;
    color: #999c9e;
}


    section.image_text_9 {
    padding: 50px 150px;
}
  section.image_text_9 .wrapper .text_block .icon_wrap .icon_item.more_btn.clicked {
    display: none !important;
}
section.image_text_9 .wrapper .text_block h4.heading {
    font-size: 25px;
    font-family: Helvetica-Bold;
    color: #000000;
    letter-spacing: 0;
    margin: 0;
    padding: 40px 0 10px;
    text-transform: none;
    display: flex
;
    align-items: center;
    justify-content: start;
    gap: 10px;
}
  
section.image_text_9 .wrapper .text_block h4.heading span {
    background: #0071e4;
    color: #ffffff;
    font-size: 15px;
    padding: 4px 10px;
    border-radius: 100px;
}
  @media only screen and (min-width: 2600px) {
 section.image_text_9 .wrapper .text_block h2.heading {
    font-size: 135px;
 }
    section.image_text_9 .wrapper .text_block h4.heading span {
    font-size: 30px;
    padding: 8px 17px;
  }
section.image_text_9 .wrapper .text_block h4.heading, section.image_text_9 .wrapper .text_block .content p{
  font-size:50px;
}
  }
   @media only screen and (max-width: 1600px) {
  section.image_text_9 {
    padding: 25px 60px;
}
   }
  @media only screen and (max-width: 1280px) {
section.image_text_9 .wrapper .text_block h2.heading {
    font-size: 48px;
}
    section.image_text_9 .wrapper .text_block h4.heading, section.image_text_9 .wrapper .text_block .content p{
  font-size:20px;
}
    section.image_text_9 .wrapper .text_block h4.heading span{
      font-size: 12px;
    padding: 4px 10px;
    }
  }
  @media only screen and (max-width: 1024px) {
  section.image_text_9 .wrapper .text_block .timmer_wrap {
    gap: 50px;
}
    section.image_text_9 .wrapper .text_block .icon_wrap {

    grid-template-columns: repeat(10, auto);
    }
  }
  @media only screen and (max-width: 840px) {
        section.image_text_9 {
        padding: 30px 20px;
    }
        section.image_text_9 .wrapper .text_block .icon_wrap {
    grid-template-columns: repeat(8, auto);
    gap: 15px;
}
  }
  @media only screen and (max-width: 480px) {
    section.image_text_9 .wrapper .text_block .timmer_wrap{
      display:none;
    }
    .image_text_9 span.swiper-pagination-bullet {
    width: 6px;
    height: 6px;
    background-color: currentColor;
    opacity: 0.4;
}
    .image_text_9 .swiper-pagination{
      bottom:0;
    }
    .image_text_9 .swiper-pagination-bullet.swiper-pagination-bullet-active {
    opacity: 1;
    background-color: currentColor;
    width: 9px;
    height: 9px;
}
  section.image_text_9 .wrapper .text_block h2.heading {
    font-size: 38px;
}
    section.image_text_9 .wrapper .text_block .icon_wrap {
    grid-template-columns: repeat(5, auto);
    gap: 10px;
}
    section.image_text_9 .wrapper .text_block .swiper-container {
    display: block !important;
              overflow: hidden;
      position:relative;
      padding-bottom: 20px;
}
section.image_text_9 .wrapper .text_block .swiper-container .timmer_wrap{

        display: flex;
        gap: 0;
    }
section.image_text_9 .wrapper .text_block h4.heading span{
      font-size: 11px;
    padding: 3px 8px;
}

  }


  
</style>

<script>

$(document).ready(function () {
  var mySwiper = new Swiper('#{{ section.id }}.image_text_9 .timmer_swiper', {
    loop: true,
    slidesPerView: 1,
    spaceBetween: 0,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    effect: 'slide', // or 'fade'
    pagination: {
      el: '#{{ section.id }}.image_text_9 .swiper-pagination',
      clickable: true,
    }
  });
});


</script>


  

{% schema %}
{
  "name": "Image Text 9",
  "settings": [
    {
      "type": "color",
      "id": "bg_color",
      "default": "transparent",
      "label": "Backgroud Color"
    },
       {
      "type": "image_picker",
      "id": "bg_image",
      "label": "Background Image"
    },
    

      {
          "type": "text",
          "id": "heading",
          "label": "Heading"
        },


      
  ],

    "blocks": [
    {
      "type": "block",
      "name": "Block",
      "settings": [

        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
  {
          "type": "text",
          "id": "heading",
          "label": "Heading"
        },
         {
          "type": "text",
          "id": "tag",
          "label": "Tag"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content"
        },
    
    
      
      ]
    }
  ],
  
  "presets": [
    {
      "name": "Image Text 9",
      "blocks": []
    }
  ]
}
{% endschema %}

