{%- if section.settings.divider -%}<div class="section--divider">{%- endif -%}

{%- liquid
  assign overlap_images = true
  if section.settings.image != blank and section.settings.image2 == blank
    assign overlap_images = false
  endif
-%}

<div class="index-section">

  <div class="page-width feature-row-wrapper">
    {%- capture image_layout -%}
      <div class="feature-row__item{% if overlap_images %} feature-row__item--overlap-images{% endif %}" style="max-width: {{ section.settings.image_width }}px" data-aos>
          {%- if section.settings.image != blank -%}
            <div>
              {%- if section.settings.button_label != blank and section.settings.button_link != blank -%}<a href="{{ section.settings.button_link }}">{%- endif -%}
                <div class="image-wrap" style="height: 0; padding-bottom: {{ 100 | divided_by: section.settings.image.aspect_ratio }}%;">
                  {%- assign img_url = section.settings.image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}
                  <img class="feature-row__image lazyload"
                    data-src="{{ img_url }}"
                    data-widths="[180, 360, 540, 750, 900, 1080]"
                    data-aspectratio="{{ section.settings.image.aspect_ratio }}"
                    data-sizes="auto"
                    alt="{{ section.settings.image.alt }}">
                </div>
              {%- if section.settings.button_label != blank and section.settings.button_link != blank -%}</a>{%- endif -%}
            </div>
          {%- endif -%}
          {%- if section.settings.image2 != blank -%}
            <div>
              {%- if section.settings.button_label != blank and section.settings.button_link != blank -%}<a href="{{ section.settings.button_link }}">{%- endif -%}
                <div class="image-wrap" style="height: 0; padding-bottom: {{ 100 | divided_by: section.settings.image2.aspect_ratio }}%;">
                  {%- assign img_url = section.settings.image2 | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}
                  <img class="feature-row__image lazyload"
                    data-src="{{ img_url }}"
                    data-widths="[180, 360, 540, 750, 900, 1080]"
                    data-aspectratio="{{ section.settings.image2.aspect_ratio }}"
                    data-sizes="auto"
                    alt="{{ section.settings.image2.alt }}">
                </div>
              {%- if section.settings.button_label != blank and section.settings.button_link != blank -%}</a>{%- endif -%}
            </div>
          {%- endif -%}
          {%- if section.settings.image == blank and section.settings.image2 == blank -%}
            <div>
              <div class="image-wrap">
                {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
              </div>
            </div>
            <div>
              <div class="image-wrap">
                {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
              </div>
            </div>
          {%- endif -%}
      </div>
    {%- endcapture -%}

    <div class="feature-row">
      {%- if section.settings.layout == 'left' -%}
        {{ image_layout }}
      {%- endif -%}

      <div class="feature-row__item feature-row__text feature-row__text--{{ section.settings.layout }} text-{{ section.settings.align_text }}" data-aos>
        {%- if section.settings.subtitle != blank -%}
          <div class="subheading appear-delay{% cycle '','-1','-2','-3','-4' %}">{{ section.settings.subtitle }}</div>
        {%- endif -%}
        {%- if section.settings.title != blank -%}
          <div class="h1 appear-delay{% cycle '','-1','-2','-3','-4' %}">{{ section.settings.title | escape }}</div>
        {%- endif -%}
        {%- if section.settings.text != blank -%}
          <div class="rte appear-delay{% cycle '','-1','-2','-3','-4' %}">{{ section.settings.text }}</div>
        {%- endif -%}
        {%- if section.settings.button_label != blank -%}
          <div class="appear-delay{% cycle '','-1','-2','-3','-4' %}">
            <a href="{{ section.settings.button_link }}" class="btn{% if section.settings.button_style == 'secondary' %} btn--tertiary{% endif %}">
              {{ section.settings.button_label }}
            </a>
          </div>
        {%- endif -%}
      </div>

      {%- if section.settings.layout == 'right' -%}
        {{ image_layout }}
      {%- endif -%}
    </div>
  </div>

</div>

{%- if section.settings.divider -%}</div>{%- endif -%}

{% schema %}
{
  "name": "t:sections.text-and-image.name",
  "settings": [
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.text-and-image.settings.image.label"
    },
    {
      "type": "image_picker",
      "id": "image2",
      "label": "t:sections.text-and-image.settings.image2.label"
    },
    {
      "type": "range",
      "id": "image_width",
      "label": "t:sections.text-and-image.settings.image_width.label",
      "default": 700,
      "min": 200,
      "max": 700,
      "step": 10,
      "unit": "px"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "t:sections.text-and-image.settings.subtitle.label"
    },
    {
      "type": "text",
      "id": "title",
      "label": "t:sections.text-and-image.settings.title.label",
      "default": "Image with text"
    },
    {
      "type": "richtext",
      "id": "text",
      "label": "t:sections.text-and-image.settings.text.label",
      "default": "<p>Pair large text with an image to tell a story, explain a detail about your product, or describe a new promotion.</p>"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "t:sections.text-and-image.settings.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.text-and-image.settings.button_link.label"
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "t:sections.text-and-image.settings.button_style.label",
      "default": "primary",
      "options": [
        {
          "value": "primary",
          "label": "t:sections.text-and-image.settings.button_style.options.primary.label"
        },
        {
          "value": "secondary",
          "label": "t:sections.text-and-image.settings.button_style.options.secondary.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "align_text",
      "label": "t:sections.text-and-image.settings.align_text.label",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:sections.text-and-image.settings.align_text.options.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.text-and-image.settings.align_text.options.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.text-and-image.settings.align_text.options.right.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "layout",
      "label": "t:sections.text-and-image.settings.layout.label",
      "default": "right",
      "options": [
        {
          "value": "left",
          "label": "t:sections.text-and-image.settings.layout.options.left.label"
        },
        {
          "value": "right",
          "label": "t:sections.text-and-image.settings.layout.options.right.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "divider",
      "label": "t:sections.text-and-image.settings.divider.label",
      "default": false
    }
  ],
  "presets": [
    {
      "name": "t:sections.text-and-image.presets.image_with_text.name"
    }
  ]
}
{% endschema %}
