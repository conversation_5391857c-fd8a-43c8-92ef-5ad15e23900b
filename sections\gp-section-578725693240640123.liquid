
    
    <style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-578725693240640123.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-578725693240640123.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-578725693240640123.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-578725693240640123.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-578725693240640123.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-578725693240640123.gps.gpsil [style*="--hvr-bgi:"]:hover{background-image:var(--hvr-bgi)}.gps-578725693240640123.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-578725693240640123.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-578725693240640123.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-578725693240640123.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-578725693240640123.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-578725693240640123.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-578725693240640123.gps.gpsil [style*="--hvr-bb:"]:hover{border-bottom:var(--hvr-bb)}.gps-578725693240640123.gps.gpsil [style*="--bbw:"]{border-bottom-width:var(--bbw)}.gps-578725693240640123.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-578725693240640123.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-578725693240640123.gps.gpsil [style*="--hvr-bblr:"]:hover{border-bottom-left-radius:var(--hvr-bblr)}.gps-578725693240640123.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-578725693240640123.gps.gpsil [style*="--hvr-bbrr:"]:hover{border-bottom-right-radius:var(--hvr-bbrr)}.gps-578725693240640123.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-578725693240640123.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-578725693240640123.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-578725693240640123.gps.gpsil [style*="--hvr-bt:"]:hover{border-top:var(--hvr-bt)}.gps-578725693240640123.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-578725693240640123.gps.gpsil [style*="--hvr-btlr:"]:hover{border-top-left-radius:var(--hvr-btlr)}.gps-578725693240640123.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-578725693240640123.gps.gpsil [style*="--hvr-btrr:"]:hover{border-top-right-radius:var(--hvr-btrr)}.gps-578725693240640123.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-578725693240640123.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-578725693240640123.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-578725693240640123.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-578725693240640123.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-578725693240640123.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-578725693240640123.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-578725693240640123.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-578725693240640123.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-578725693240640123.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-578725693240640123.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-578725693240640123.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-578725693240640123.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-578725693240640123.gps.gpsil [style*="--tdt:"]{text-decoration-thickness:var(--tdt)}.gps-578725693240640123.gps.gpsil [style*="--tdl:"]{text-decoration-line:var(--tdl)}.gps-578725693240640123.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-578725693240640123.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-578725693240640123.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-578725693240640123.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-578725693240640123.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-578725693240640123.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-578725693240640123.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-578725693240640123.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-578725693240640123.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-578725693240640123.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-578725693240640123.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-578725693240640123.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-578725693240640123.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-578725693240640123.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-578725693240640123.gps.gpsil [style*="--rg:"]{row-gap:var(--rg)}.gps-578725693240640123.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-578725693240640123.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-578725693240640123.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-578725693240640123.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-578725693240640123.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-578725693240640123.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-578725693240640123.gps.gpsil [style*="--bbw-tablet:"]{border-bottom-width:var(--bbw-tablet)}.gps-578725693240640123.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-578725693240640123.gps.gpsil [style*="--gtc-tablet:"]{grid-template-columns:var(--gtc-tablet)}.gps-578725693240640123.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-578725693240640123.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-578725693240640123.gps.gpsil [style*="--mb-tablet:"]{margin-bottom:var(--mb-tablet)}.gps-578725693240640123.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-578725693240640123.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-578725693240640123.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-578725693240640123.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-578725693240640123.gps.gpsil [style*="--ta-tablet:"]{text-align:var(--ta-tablet)}.gps-578725693240640123.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-578725693240640123.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-578725693240640123.gps.gpsil [style*="--bbw-mobile:"]{border-bottom-width:var(--bbw-mobile)}.gps-578725693240640123.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-578725693240640123.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-578725693240640123.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-578725693240640123.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-578725693240640123.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-578725693240640123.gps.gpsil [style*="--op-mobile:"]{opacity:var(--op-mobile)}.gps-578725693240640123.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-578725693240640123.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-578725693240640123.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-578725693240640123.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-578725693240640123.gps.gpsil [style*="--ta-mobile:"]{text-align:var(--ta-mobile)}.gps-578725693240640123.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-578725693240640123.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-578725693240640123 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-578725693240640123 .gp-relative{position:relative}.gps-578725693240640123 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-578725693240640123 .gp-mb-0{margin-bottom:0}.gps-578725693240640123 .gp-block{display:block}.gps-578725693240640123 .gp-inline-block{display:inline-block}.gps-578725693240640123 .gp-flex{display:flex}.gps-578725693240640123 .gp-inline-flex{display:inline-flex}.gps-578725693240640123 .gp-grid{display:grid}.gps-578725693240640123 .gp-contents{display:contents}.gps-578725693240640123 .\!gp-hidden{display:none!important}.gps-578725693240640123 .gp-hidden{display:none}.gps-578725693240640123 .gp-h-full{height:100%}.gps-578725693240640123 .gp-w-full{width:100%}.gps-578725693240640123 .gp-max-w-full{max-width:100%}.gps-578725693240640123 .gp-flex-1{flex:1 1 0%}.gps-578725693240640123 .gp-flex-none{flex:none}.gps-578725693240640123 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-578725693240640123 .gp-flex-col{flex-direction:column}.gps-578725693240640123 .gp-items-center{align-items:center}.gps-578725693240640123 .gp-justify-start{justify-content:flex-start}.gps-578725693240640123 .gp-justify-center{justify-content:center}.gps-578725693240640123 .gp-overflow-hidden{overflow:hidden}.gps-578725693240640123 .gp-break-words{overflow-wrap:break-word}.gps-578725693240640123 .gp-text-center{text-align:center}.gps-578725693240640123 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-578725693240640123 .gp-no-underline{text-decoration-line:none}.gps-578725693240640123 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-578725693240640123 .gp-duration-200{transition-duration:.2s}.gps-578725693240640123 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-578725693240640123 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-578725693240640123 .disabled\:gp-opacity-30:disabled{opacity:.3}.gps-578725693240640123 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-578725693240640123 .gp-group[data-state=loading] .group-data-\[state\=loading\]\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-578725693240640123 .tablet\:gp-block{display:block}.gps-578725693240640123 .tablet\:\!gp-hidden{display:none!important}.gps-578725693240640123 .tablet\:gp-hidden{display:none}.gps-578725693240640123 .tablet\:gp-h-full{height:100%}.gps-578725693240640123 .tablet\:gp-flex-1{flex:1 1 0%}}@media (max-width:767px){.gps-578725693240640123 .mobile\:gp-block{display:block}.gps-578725693240640123 .mobile\:\!gp-hidden{display:none!important}.gps-578725693240640123 .mobile\:gp-hidden{display:none}.gps-578725693240640123 .mobile\:gp-h-full{height:100%}.gps-578725693240640123 .mobile\:gp-flex-1{flex:1 1 0%}}.gps-578725693240640123 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-578725693240640123 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}</style>
    
    
    

    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}

    
        <section
          class="gp-mx-auto gp-max-w-full [&_*]:gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:none;--pl-tablet:none;--pl-mobile:none;--pr:none;--pr-tablet:none;--pr-mobile:none"
        >
          
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"#FFFFFF","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"g0LIZfscrm"}' data-id="g0LIZfscrm" id="g0LIZfscrm" data-same-height-subgrid-container class="g0LIZfscrm gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:var(--g-s-4xl);--pl:24px;--pb:var(--g-s-4xl);--pr:24px;--pt-tablet:var(--g-s-4xl);--pl-tablet:15px;--pb-tablet:var(--g-s-4xl);--pr-tablet:15px;--pt-mobile:24px;--pl-mobile:24px;--pb-mobile:24px;--pr-mobile:var(--g-s-xl);--cg:var(--g-s-2xl);--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="gCjyhzYciD gp-relative gp-flex gp-flex-col"
    >
      
    
      {%- assign gpBkProduct = product -%}
      
      {%- liquid
        if request.page_type == 'product'
          if 'static' == 'static'
            if '15432304329086' == 'latest'
              paginate collections.all.products by 100000
                assign product = collections.all.products | sort: 'created_at' | reverse | first
              endpaginate
            else
              assign product = all_products['white-health-series-infinity-loop']
              assign productId = '15432304329086' | times: 1
              if product == empty or product == null
                paginate collections.all.products by 100000
                  for item in collections.all.products
                    if item.id == productId
                      assign product = item
                    endif
                  endfor
                endpaginate
              endif
            endif
          endif
        else
          if '15432304329086' == 'latest'
            paginate collections.all.products by 100000
              assign product = collections.all.products | sort: 'created_at'| reverse | first
            endpaginate
          else
            assign product = all_products['white-health-series-infinity-loop']
            assign productId = '15432304329086' | times: 1
            if product == empty or product == null
              paginate collections.all.products by 100000
                for item in collections.all.products
                  if item.id == productId
                    assign product = item
                  endif
                endfor
              endpaginate
            endif
          endif
        endif
      -%}
    
    
    {% if product != empty and product != null %}
      
      {%- assign initVariantId =  -%}
      {%- assign product_form_id = 'product-form-' | append: "gCHu_LX7iO" -%}
      {%- assign variant = product.selected_or_first_available_variant -%}
      {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%-if productSelectedVariant == empty or productSelectedVariant == null -%}
        {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      {%-if variant == empty or variant == null -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
      {%- endif -%}
    
      <gp-product
        data-uid="gCHu_LX7iO"
        data-id="gCHu_LX7iO"
        
        
        gp-context='{"productId": {{ product.id }}, "preSelectedOptionIds": [], "isSyncProduct": "", "variantSelected": {{ variant | json | escape }},  "inventory_management": {{ variant.inventory_management | json | escape  }}, "inventory_policy": {{ variant.inventory_policy | json | escape }}, "inventoryQuantity": {{ variant.inventory_quantity }}, "quantity": 1, "formId": "{{ product_form_id }}" }'
        gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }}}'
      >
        <product-form class="product-form">
          {%- form 'product', product, id: product_form_id, class: 'form contents', data-type: 'add-to-cart-form', autocomplete: 'off'  -%}
            <input type="hidden" name="id" value="{{ variant.id }}" />
            <input type="hidden" name="quantity" value="{{ quantity }}" />
            <button type="submit" onclick="return false;" style="display:none;"></button>
            
    

    

    
    <gp-row gp-data='{"uid":"gCHu_LX7iO"}' data-id="gCHu_LX7iO-row" id="gCHu_LX7iO" data-same-height-subgrid-container class="gCHu_LX7iO gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--cg:30px;--rg:0px;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="g9VJzPA3jA gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gbx77u-hB0"}' data-id="gbx77u-hB0" id="gbx77u-hB0" data-same-height-subgrid-container class="gbx77u-hB0 gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--cg:62px;--gtc:minmax(0, 7fr) minmax(0, 5fr);--gtc-tablet:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--h:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:space-between"
      class="gfhgGt7t2i gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"g6FzY3UnEN"}' data-id="g6FzY3UnEN" id="g6FzY3UnEN" data-same-height-subgrid-container class="g6FzY3UnEN gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:0px;--mb-mobile:13px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--cg:var(--g-s-2xl);--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--h:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:space-between"
      class="gML6GqDAN0 gp-relative gp-flex gp-flex-col"
    >
      
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gH6cNoW2s5"
        class="gH6cNoW2s5"
        style="--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:9px;--pt:0px;--pl:0px;--pb:0px;--pr:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425944603"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          data-gp-text
          class="gp-text-g-text-2 gp-text-instant gp-text"
          style="--w:80%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--ta-tablet:left;--ta-mobile:left;--c:#242424;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-heading, heading);--weight:700;--ls:normal;--size:38px;--size-tablet:30px;--size-mobile:24px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggH6cNoW2s5_text | replace: '$locationOrigin', locationOrigin }}
        </h2>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gRF7wE1GVe"
        class="gRF7wE1GVe"
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-3, line-3);--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:9px;--mb-mobile:9px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--mb-tablet:9px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425944605"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--b:none;--bc:#121212;--bw:1px 1px 1px 1px;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:18px;--size-tablet:18px;--size-mobile:16px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--c:#000000;--tt:none;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggRF7wE1GVe_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    <div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-3, line-3);--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:12px;--pb:12px" class="gzHqV9z319 ">
      
    <div data-id="gzHqV9z319" class="gp-flex gp-justify-start">
      <div style="--w:50%;--w-tablet:50%;--w-mobile:50%;--bs:solid;--bbw:2px;--bbw-tablet:2px;--bbw-mobile:2px;--t:gp-rotate(0deg);--bc:#5594E7" class="gp-block tablet:gp-block mobile:gp-block"></div>
      <div class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden" style="--w:50%;--w-tablet:50%;--w-mobile:50%;--bc:#5594E7;--t:gp-rotate(0deg)">
        <div class="gp-hidden" style="--bc:#5594E7;--bs:solid;--bbw:2px;--bbw-tablet:2px;--bbw-mobile:2px;--minw:50%;--w:50%;--w-tablet:50%;--w-mobile:50%"></div>
        
            <span
              class="gp-inline-flex gp-flex-none gp-items-center gp-justify-center"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-flex-none gp-items-center gp-justify-center gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s);--ml:var(--g-s-s)"
            >
              Title
            </span>
          
        <div class="gp-flex" style="--bc:#5594E7;--bs:solid;--bbw:2px;--bbw-tablet:2px;--bbw-mobile:2px;--minw:50%;--w:50%;--w-tablet:50%;--w-mobile:50%"></div>
      </div>
    </div>
  
      </div>
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gAM4RwObfx"
        class="gAM4RwObfx"
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-3, line-3);--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:18px;--pt:0px;--pl:0px;--pb:0px;--pr:0px;--mb-tablet:18px;--pt-tablet:0px;--pl-tablet:0px;--pb-tablet:0px;--pr-tablet:0px;--mb-mobile:18px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425944615"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--b:none;--bc:#121212;--bw:1px 1px 1px 1px;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:18px;--size-tablet:18px;--size-mobile:16px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--c:#000000;--tt:none;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggAM4RwObfx_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    <gp-button
      gp-data='{"btnLink":{"link":"#g071lCCNnn","type":"scroll-to","title":"Section 9"}}'
      
      class="gp-flex gp-flex-col"
    >
      <div style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--op-mobile:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb-mobile:0px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--mb-tablet:0px;--ta:left;--ta-mobile:center" >
        <style>[data-id="gf5Is7y5MB"].gp-button-base::before, [data-id="gf5Is7y5MB-interaction"].gp-button-base::before {
      
    content: "";
    height: 100%;
    width: 100%;
    position: absolute;
    pointer-events: none;
    top: 0;
    left: 0;
    border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
    
      border-bottom-left-radius: 999px;
      border-bottom-right-radius: 999px;
      border-top-left-radius: 999px;
      border-top-right-radius: 999px;
      
  
    }
  
      
  [data-id="gf5Is7y5MB"]:hover::before, [data-id="gf5Is7y5MB-interaction"]:hover::before {
    
    
  }</style>
        <a
            class="gp-trigger-button-link gp-hidden"
            href=#g071lCCNnn
            target=undefined
          ></a>
        <a
          data-id="gf5Is7y5MB" dataId="gf5Is7y5MB" data-state="idle" aria-label="<p>GEAR UP WITH ULTRA 2+ TODAY</p>"
          
          href="#g071lCCNnn" target
          style="--w:75%;--w-tablet:75%;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:12px;--pb:12px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:12px;--pb-mobile:12px;--bblr:999px;--bbrr:999px;--btlr:999px;--btrr:999px;--hvr-bblr:999px;--hvr-bbrr:999px;--hvr-btlr:999px;--hvr-btrr:999px;--shadow:none;--bgi:;--hvr-bgi:;--hvr-bg:#007AF4;--bg:#0171E3;--c:#FFFFFF;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:700;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:16px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase"
          class="gf5Is7y5MB gp-text-center gp-button-base gp-group gp-relative gp-inline-flex gp-max-w-full gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-200 disabled:gp-btn-disabled disabled:gp-opacity-30 "
        >
        
        <div class="gp-inline-flex">
          
          
    <span
      data-gp-text
      style="--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:700;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:16px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;--ts:none;word-break:break-word"
      class="gp-content-product-button group-active/button:!gp-text-inherit gp-relative gp-flex gp-h-full gp-items-center gp-overflow-hidden gp-break-words group-data-[state=loading]:gp-invisible [&_p]:gp-whitespace-pre-line gp-text button-text"
    >
      {{ section.settings.ggf5Is7y5MB_label }}
    </span>
        </div>
        
        </a>
      </div>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-button-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    </gp-button>
  
    </div>

      
    </gp-row>
  
  
    </div><div
      data-same-height-display-contents
      style="--jc:space-between"
      class="gotv5T3uC- gp-relative gp-flex gp-flex-col"
    >
      
    <div
      role="presentation"
      data-id="gsFAxoKl1y"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-3, line-3);--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:center"
      class="gp-group/image gp-relative force-publish-1757425944619 gp-flex-1 gp-h-full mobile:gp-flex-1 mobile:gp-h-full tablet:gp-flex-1 tablet:gp-h-full gsFAxoKl1y"
    >
      <div
        
        style="border-radius:inherit;--jc:center"
        class="gp-h-full gp-w-full gp-flex pointer-events-auto"
      >
        
    <picture style="border-radius:inherit" class="gp-contents">
      
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_553400155311702965-2a951aaa-d2ec-4d58-b755-9271c983160f.png" | file_url }}" srcset="{{ "gempages_553400155311702965-2a951aaa-d2ec-4d58-b755-9271c983160f.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_553400155311702965-2a951aaa-d2ec-4d58-b755-9271c983160f.png" | file_url }}" srcset="{{ "gempages_553400155311702965-2a951aaa-d2ec-4d58-b755-9271c983160f.png" | file_url }}" />
    
      
      <img
        loading="eager" fetchpriority="high"
        src="{{ "gempages_553400155311702965-2a951aaa-d2ec-4d58-b755-9271c983160f.png" | file_url }}" data-src="{{ "gempages_553400155311702965-2a951aaa-d2ec-4d58-b755-9271c983160f.png" | file_url }}"
        alt=""
        width="100%"
        style="--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:100%;--h-tablet:100%;--h-mobile:100%;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--bblr:12px;--bbrr:12px;--btlr:12px;--btrr:12px;--radiusType:rounded;--shadow:none"
        class="gp-inline-block gp-w-full gp-max-w-full gp_force_load"
      />
    
    </picture>
  
      </div>
    </div>
  
    </div>

      
    </gp-row>
  
  
    </div>

      
    </gp-row>
  
  
          {%- endform -%}
        </product-form>
      </gp-product>
      {%- assign product = gpBkProduct -%}
    {% else %}
      <div class="gp-text-center">{{ 'gempages.Product.product_not_found' | t }}</div>
    {% endif %}
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-product-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>

      
    </gp-row>
  
        </section>
      
  
    <style {% if section.settings.section_preload == "false" %} class="gps-link" type="text/disabled-css" {% endif %}>@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWubEbVmUiAw.woff) format('woff');
}
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmUiAw.woff) format('woff');
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBHMdazQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBHMdazQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style>
    
{% schema %}
  {
    
    "name": "Section 3",
    "tag": "section",
    "class": "gps-578725693240640123 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=578718746651132539&sectionId=578725693240640123)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggH6cNoW2s5_text","label":"ggH6cNoW2s5_text","default":"Most Smartwatches Fail When Life Gets Tough."},{"type":"html","id":"ggRF7wE1GVe_text","label":"ggRF7wE1GVe_text","default":"<p><strong>Cracked screens. Dead batteries. Water damage</strong> after a single downpour. Most smartwatches look good — until life gets rough.</p><p>&nbsp;</p><p>If you’re hiking, working in the elements, or pushing your limits, your gear shouldn’t hold you back. Why settle for fragile tech that quits when you need it most?</p>"},{"type":"html","id":"ggAM4RwObfx_text","label":"ggAM4RwObfx_text","default":"<p><strong>Meet the GardPro Ultra 2+ — an incredibly durable smartwatch engineered to survive the extreme. </strong>From construction sites to mountain trails, <strong>the Ultra 2+ thrives in mud, rain, and impact.</strong> Built tough, so you can focus on the adventure — not the watch on your wrist.</p>"},{"type":"html","id":"ggf5Is7y5MB_label","label":"ggf5Is7y5MB_label","default":"<p>GEAR UP WITH ULTRA 2+ TODAY</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
  