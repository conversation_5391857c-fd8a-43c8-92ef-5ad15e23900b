<section class="image_text_4"  style="background:{{section.settings.bg_color}}; {% if section.settings.bg_image != blank %}background-image: url({{ section.settings.bg_image | img_url: 'master' }}); background-size: cover; background-repeat: no-repeat; background-position: center;{% endif %};">
  <div class="page_wrapper">
    <div class="wrapper">
      <div class="text_block">
             {% if section.settings.sub_heading != blank %}
        <div class="sub_heading">{{ section.settings.sub_heading }}</div>
        {% endif %}
        {% if section.settings.heading != blank %}
        <h2 class="heading">{{ section.settings.heading }}</h2>
        {% endif %}
        {% if section.settings.content != blank %}
        <div class="content">{{ section.settings.content }}</div>
        {% endif %}
        <div class="image_text_block_wrap">
        <div class="timmer_wrap">
           {% for block in section.blocks %}
          <div class="timmer_item">
             <div class="timmer_item_wrap">
            <h3 class="item_heading">{{ block.settings.heading }}</h3>
             <p class="item_text">{{ block.settings.content }}</p>
      
          </div>
          </div>
          {% endfor %}
        </div>
         <div class="image_block">
        {% if section.settings.image != blank %}
        <img src="{{ section.settings.image |img_url :'master' }}">
        {% endif %}
        {% if section.settings.mobile_image != blank %}
        <img style="display:none;" class="mobile_img" src="{{ section.settings.mobile_image |img_url :'master' }}">
        {% endif %}
      </div>
      </div>
      </div>
     
      
    </div>
  </div>
</section>

<style>
section.image_text_4 .wrapper .image_block img{
box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.3);
}


section.image_text_4 .wrapper .text_block {
    width: 100%;
    display: flex;
      /* max-width: 775px; */
    flex-direction: column;

}
section.image_text_4 .wrapper .text_block h2.heading {
    font-size: 74px;
    font-family: Helvetica-Bold;
    margin: 0;
  color:#2a2b2b;
    padding-bottom: 20px;
    text-transform: none;
      width: 100%;
    max-width: 1000px;
}
section.image_text_4 .wrapper .text_block .content p {
    margin: 0;
    font-size: 25px;
    font-family: Helvetica-Bold;
    color: #676666;
   font-weight: 700;
}
  section.image_text_4 .wrapper .image_block {
    width: 100%;
    font-size: 0;
}
  section.image_text_4 .wrapper .image_block img{
    width: 100%;
    }
  /* .image_text_4 .wrapper {
    grid-template-columns: 1.5fr 1fr;
    display: grid;
        align-items: center;
    gap:50px;
} */
  section.image_text_4 .wrapper .text_block .content {
    width: 100%;

}
  section.image_text_4 .wrapper .text_block .sub_heading {
    margin: 0;
    font-size: 25px;
    font-family: Helvetica-Bold;
    color: #676666;
        font-weight: 700;
}
  section.image_text_4 .wrapper .text_block .timmer_wrap {
display: grid
;
    grid-template-columns: 1fr 1.5fr 1fr;
    padding: 30px 0;
    gap:15px;
        position: absolute;
    inset: 0;
}
  /* section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(1) {
    border-right: 2px solid #a6a6a6;
} */
  section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item .item_text {
    margin: 0;
    font-size: 22px;
    font-family: Helvetica-Bold;
    color: #676666;
    font-weight: 700;
}
  section.image_text_4 .wrapper .text_block .item_heading {
       color: #000000;
    font-size: 25px;
    margin: 0;
    font-family: Helvetica-Bold;
    text-transform: none;
}
  section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item {
    width: 100%;
max-width: 253px;
    position: relative;
  }
  /* section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(2) {
    display: flex
;
    flex-direction: column;
    justify-content: end;
    width: 100%;
    max-width: 330px;
    margin-left: auto;
} */
    section.image_text_4 {
    padding: 50px 150px;
}
.image_text_4 .image_text_block_wrap {
    position: relative;
}

section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item img {
    width: 6%;
    position: absolute;
    top: 0;
}
  section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(1) {
    margin: 0;
}
  section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(2) {
       margin-left: auto;
}
  section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(3) {
          margin-left: auto;
}

  section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(1) img {
    right: -20%;
    transform: rotate(-40deg);
    bottom: unset;
    top: -100%;
    width: 8%;
  }
  section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(2) img {
    left: -15px;
}
  section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(3) img {
    left: -25%;
    transform: rotate(36deg);
    bottom: unset;
    top: -110%;
    width: 8%;
}
  @media only screen and (min-width: 2600px) {
    section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item {
    width: 100%;
    max-width: 510px;
    }
       section.image_text_4 .wrapper .text_block .sub_heading {
    margin: 0;
    font-size: 50px;
     }
     section.image_text_4 .wrapper .text_block h2.heading {
    font-size: 135px;
       padding-bottom: 50px;
               max-width: 1900px;
     }
     section.image_text_4 .wrapper .text_block .content p {
    margin: 0;
    font-size: 50px;
    
  }
    section.image_text_4 .wrapper .text_block .item_heading{
          font-size: 50px;
          
    }
    section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item .item_text {
    margin: 0;
    font-size: 45px;
      
  }
    /* section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(2) {
    max-width: 620px;
    } */
    section.image_text_4 .wrapper .text_block .timmer_wrap {
    padding-top: 50px;
}
    /* section.image_text_4 .wrapper .text_block {
    max-width: 1500px;
    } */
  }
   @media only screen and (max-width: 1600px) {
  section.image_text_4 {
    padding: 25px 60px;
}
   }
  @media only screen and (max-width: 1280px) {
section.image_text_4 .wrapper .text_block h2.heading {
    font-size: 48px;
          max-width: 660px;
}
    section.image_text_4 .wrapper .text_block .item_heading {
    font-size: 20px;
    }
    section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item .item_text{
      font-size:18px;
    }
    section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item {
    width: 100%;
    max-width: 210px;
}
    /* section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(2) {
  
    max-width: 250px;
    } */
       section.image_text_4 .wrapper .text_block .content p {

    font-size: 20px;
      }
  }
  @media only screen and (max-width: 1024px) {
  /* section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(2) {

    max-width: 200px;

  } */
        section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item {
        width: 100%;
        max-width: 165px;
    }
        section.image_text_4 .wrapper .text_block .item_heading {
        font-size: 16px;
    }
    section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item .item_text {
        font-size: 15px;
    }
  }
  @media only screen and (max-width: 900px) {
      section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item {
        width: 100%;
        max-width: 140px;
    }
        section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item .item_text {
        font-size: 12px;
    }
  }
  @media only screen and (max-width: 840px) {

        section.image_text_4 {
        padding: 30px 20px;
    } 
  }
@media only screen and (max-width: 768px) {
section.image_text_4 .wrapper .text_block .timmer_wrap {
    position: unset;
    grid-template-columns: 1fr 1fr 1fr;
}
  section.image_text_4 .wrapper .image_block img{
    display:none;
  }
  section.image_text_4 .wrapper .image_block img.mobile_img{
    display:flex!important;
  }
   section.image_text_4 .wrapper .text_block .item_heading {
    font-size: 18px;

      }
      section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item .item_text {

    font-size: 16px;
      }
  section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item {
        width: 100%;
        max-width: 180px;
    }
  }
    @media only screen and (max-width: 480px) {
      
section.image_text_4 .wrapper .text_block .timmer_wrap {
    grid-template-columns: 1fr;
}
      section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item {
    max-width: 100%;
}
      section.image_text_4 .wrapper .text_block .content p {

    font-size: 18px;
      }
section.image_text_4 .wrapper .text_block .sub_heading {
    margin: 0;
    font-size: 20px;
  }
      section.image_text_4 .wrapper .text_block h2.heading {
    font-size: 38px;
      }
      section.image_text_4 .wrapper .text_block .content p {
    font-size: 20px;
      }
     
      /* section.image_text_4 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(2) {
    max-width: 155px;
      } */
    }
</style>




  

{% schema %}
{
  "name": "Image Text 4",
  "settings": [
    {
      "type": "color",
      "id": "bg_color",
      "default": "transparent",
      "label": "Backgroud Color"
    },
       {
      "type": "image_picker",
      "id": "bg_image",
      "label": "Background Image"
    },
    
    {
          "type": "text",
          "id": "sub_heading",
          "label": "Sub Heading"
        },
      {
          "type": "text",
          "id": "heading",
          "label": "Heading"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "content"
        },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    },
      {
      "type": "image_picker",
      "id": "mobile_image",
      "label": "Mobile Image"
    }
    
      
  ],

    "blocks": [
    {
      "type": "block",
      "name": "Block",
      "settings": [

   
      {
          "type": "text",
          "id": "heading",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "content",
          "label": "content"
        },
    
    
      
      ]
    }
  ],
  
  "presets": [
    {
      "name": "Image Text 4",
      "blocks": []
    }
  ]
}
{% endschema %}

