{% comment %}
This file is auto-generated by PageFly. The content can be overridden when publish page in PageFly. Please do not update this file directly.
If you plan to remove PageFly, please see the guide in our help center first: https://help.pagefly.io/.
{% endcomment %}
{%- assign new_comment = false -%}{% if comment and comment.created_at %}{%- assign new_comment = true -%}{%- assign new_comment_id = comment.id -%}{% endif %}{% if new_comment %}{%- assign duplicate_comment = false %}{% for comment in article.comments %}{% if comment.id == new_comment_id %}{%- assign duplicate_comment = true %}{% break %}{% endif %}{% endfor %}{% if duplicate_comment %}{%- assign number_of_comments = article.comments_count -%}{% else %}{%- assign number_of_comments = article.comments_count | plus: 1 -%}{% endif %}{% else %}{%- assign number_of_comments = article.comments_count -%}{% endif %}{% capture pagefly_comment %}<div style="border-bottom:1px solid #ebebeb;margin-bottom:10px;padding-bottom:10px;"><span style="margin-right:15px;font-weight:bold">{{ comment.author }}</span><small>{{ comment.created_at | date: "%b %d, %Y" }}</small></div>{{ comment.content }}{% endcapture %}{% if blog.comments_enabled? %}<div class="pf-arc" style="margin-top:30px;"><div style="max-width:1170px;margin:auto;width:100%;padding:0 15px;">{% if number_of_comments > 0 %}<div style="margin-bottom:30px"><h3>{{ 'pagefly.comments.comments_with_count' | t: count: number_of_comments }}</h3>{% paginate article.comments by 5 %}{% comment %}#comments is required, it is used as an anchor link by Shopify.{% endcomment %}{% if new_comment %}<p class="note form-success">{% if blog.moderated? %}{{ 'pagefly.comments.success_moderated' | t }}{% else %}{{ 'pagefly.comments.success' | t }}{% endif %}</p>{% endif %}<ul class="comments">{% comment %}If a comment was just submitted with no blank field, show it.{% endcomment %}{% if new_comment %}{% unless paginate.current_page > 1 %}<li id="comment-{{ comment.id }}" class="comment">{{ pagefly_comment }}</li>{% endunless %}{% endif %}{% for comment in article.comments %}{% unless comment.id == new_comment_id %}<li id="comment-{{ comment.id }}" class="comment"><div style="border-bottom:1px solid #ebebeb;margin-bottom:10px;padding-bottom:10px;"><span style="margin-right:15px;font-weight:bold">{{ comment.author }}</span><small>{{ comment.created_at | date: "%b %d, %Y" }}</small></div>{{ comment.content }}</li>{% endunless %}{% endfor %}</ul>{% if paginate.pages > 1 %}<div class="pf-pagination">{{ paginate | default_pagination | replace: '&laquo; Previous', '&larr;' | replace: 'Next &raquo;', '&rarr;' }}</div>{% endif %}{% endpaginate %}</div>{% endif %}{% form 'new_comment', article %}<h3>{{ 'pagefly.comments.title' | t }}</h3>{{ form.errors | default_errors }}<div class="pf-r"><div class="pf-c pf-c-xs--12 pf-c-lg--6"><label for='CommentAuthor'>{{ 'pagefly.comments.name' | t }}</label><input style="width:calc(100% - 15px)" type='text' name='comment[author]' id='CommentAuthor' class='input-full{% if form.errors contains 'author' %} input--error{% endif %}' value='{{ form.author }}'></div><div class="pf-c pf-c-xs--12 pf-c-lg--6"><label for='CommentEmail'>{{ 'pagefly.comments.email' | t }}</label><input style="width:calc(100% - 15px);margin-left:15px;" type='email' name='comment[email]' id='CommentEmail' class='input-full{% if form.errors contains 'email' %} input--error{% endif %}' value='{{ form.email }}' autocorrect='off' autocapitalize='off'></div><div class="pf-c pf-c-xs--12 pf-c-lg--12" style="margin:30px 0"><label for='CommentBody'>{{ 'pagefly.comments.message' | t }}</label><textarea style="width:100%" name='comment[body]' id='CommentBody' class='input-full{% if form.errors contains 'body' %} input--error{% endif %}'>{{ form.body }}</textarea></div><div class="pf-c pf-c-xs--12 pf-c-lg--12">{% if blog.moderated? %}<p class='fine-print'>{{ 'pagefly.comments.moderated' | t }}</p>{% endif %}<input type='submit' class='btn' value='{{ 'pagefly.comments.post' | t }}'></div></div>{% endform %}</div></div>{% endif %}