

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-555524770926625714.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-555524770926625714.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-555524770926625714.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-555524770926625714.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-555524770926625714.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-555524770926625714.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-555524770926625714.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-555524770926625714.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-555524770926625714.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-555524770926625714.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-555524770926625714.gps.gpsil [style*="--bbw:"]{border-bottom-width:var(--bbw)}.gps-555524770926625714.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-555524770926625714.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-555524770926625714.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-555524770926625714.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-555524770926625714.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-555524770926625714.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-555524770926625714.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-555524770926625714.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-555524770926625714.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-555524770926625714.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-555524770926625714.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-555524770926625714.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-555524770926625714.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-555524770926625714.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-555524770926625714.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-555524770926625714.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-555524770926625714.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-555524770926625714.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-555524770926625714.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-555524770926625714.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-555524770926625714.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-555524770926625714.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-555524770926625714.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-555524770926625714.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-555524770926625714.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-555524770926625714.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-555524770926625714.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-555524770926625714.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-555524770926625714.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-555524770926625714.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-555524770926625714.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-555524770926625714.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-555524770926625714.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-555524770926625714.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-555524770926625714.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-555524770926625714.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-555524770926625714.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-555524770926625714.gps.gpsil [style*="--bbw-tablet:"]{border-bottom-width:var(--bbw-tablet)}.gps-555524770926625714.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-555524770926625714.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-555524770926625714.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-555524770926625714.gps.gpsil [style*="--jc-tablet:"]{justify-content:var(--jc-tablet)}.gps-555524770926625714.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-555524770926625714.gps.gpsil [style*="--mt-tablet:"]{margin-top:var(--mt-tablet)}.gps-555524770926625714.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-555524770926625714.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-555524770926625714.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-555524770926625714.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-555524770926625714.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-555524770926625714.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-555524770926625714.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-555524770926625714.gps.gpsil [style*="--bbw-mobile:"]{border-bottom-width:var(--bbw-mobile)}.gps-555524770926625714.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-555524770926625714.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-555524770926625714.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-555524770926625714.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-555524770926625714.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-555524770926625714.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-555524770926625714.gps.gpsil [style*="--ml-mobile:"]{margin-left:var(--ml-mobile)}.gps-555524770926625714.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-555524770926625714.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-555524770926625714.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-555524770926625714.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-555524770926625714.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-555524770926625714.gps.gpsil [style*="--ta-mobile:"]{text-align:var(--ta-mobile)}.gps-555524770926625714.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-555524770926625714.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-555524770926625714 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-555524770926625714 .gp-relative{position:relative}.gps-555524770926625714 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-555524770926625714 .gp-mb-0{margin-bottom:0}.gps-555524770926625714 .gp-block{display:block}.gps-555524770926625714 .gp-flex{display:flex}.gps-555524770926625714 .gp-inline-flex{display:inline-flex}.gps-555524770926625714 .gp-grid{display:grid}.gps-555524770926625714 .gp-contents{display:contents}.gps-555524770926625714 .\!gp-hidden{display:none!important}.gps-555524770926625714 .gp-hidden{display:none}.gps-555524770926625714 .gp-h-auto{height:auto}.gps-555524770926625714 .gp-h-full{height:100%}.gps-555524770926625714 .gp-w-full{width:100%}.gps-555524770926625714 .gp-max-w-full{max-width:100%}.gps-555524770926625714 .gp-flex-none{flex:none}.gps-555524770926625714 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-555524770926625714 .gp-flex-col{flex-direction:column}.gps-555524770926625714 .gp-items-center{align-items:center}.gps-555524770926625714 .gp-justify-start{justify-content:flex-start}.gps-555524770926625714 .gp-justify-center{justify-content:center}.gps-555524770926625714 .gp-gap-y-0{row-gap:0}.gps-555524770926625714 .gp-overflow-hidden{overflow:hidden}.gps-555524770926625714 .gp-leading-\[0\]{line-height:0}.gps-555524770926625714 .gp-text-g-line-3{color:var(--g-c-line-3)}.gps-555524770926625714 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555524770926625714 .gp-duration-200{transition-duration:.2s}.gps-555524770926625714 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-555524770926625714 .tablet\:gp-block{display:block}.gps-555524770926625714 .tablet\:\!gp-hidden{display:none!important}.gps-555524770926625714 .tablet\:gp-hidden{display:none}.gps-555524770926625714 .tablet\:gp-h-auto{height:auto}.gps-555524770926625714 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-555524770926625714 .mobile\:gp-block{display:block}.gps-555524770926625714 .mobile\:\!gp-hidden{display:none!important}.gps-555524770926625714 .mobile\:gp-hidden{display:none}.gps-555524770926625714 .mobile\:gp-h-auto{height:auto}.gps-555524770926625714 .mobile\:gp-flex-none{flex:none}}.gps-555524770926625714 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-555524770926625714 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}@media (max-width:1024px){.gps-555524770926625714 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-555524770926625714 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-555524770926625714 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-555524770926625714 .\[\&_p\]\:gp-inline p{display:inline}.gps-555524770926625714 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-555524770926625714 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gLNKbGkhqe" data-id="gLNKbGkhqe"
        style="--blockPadding:base;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-2xl);--pb:var(--g-s-2xl);--pt-mobile:0px;--pb-mobile:0px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gLNKbGkhqe gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gjzWKvHYvW gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gYXP8AiwwD" data-id="gYXP8AiwwD"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mt-tablet:0px;--pt-tablet:var(--g-s-xl);--pb-tablet:var(--g-s-xl);--cg:0px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FBFBFB;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gYXP8AiwwD gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center;--jc-tablet:start"
      class="geIUIjHLY1 gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gQ_ozbLpwA"
    role="presentation"
    class="gp-group/image gQ_ozbLpwA gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb-mobile:var(--g-s-3xl);--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Img_268_1_1_768x.jpg?v=1703168626" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Img_268_1_1_768x.jpg?v=1703168626" />
      <source media="(max-width: 1024px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Img_268_1_1_1024x.jpg?v=1703168626" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Img_268_1_1_1024x.jpg?v=1703168626" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Img_268_1_1.jpg?v=1703168626"
        data-src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Img_268_1_1.jpg?v=1703168626"
        width="100%"
        alt=""
        style="--aspect:auto;--objf:fill;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center;--jc-tablet:start"
      class="gK_LsvPW7W gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gt8goMkXq_" data-id="gt8goMkXq_"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--ml:var(undefined);--pl:var(--g-s-3xl);--pr:134px;--pl-mobile:15px;--pr-mobile:15px;--pl-tablet:var(--g-s-3xl);--pr-tablet:15px;--cg:96px;--cg-tablet:96px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gt8goMkXq_ gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gJrYV7SCkK gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-2xl);--ta:left;--ta-mobile:center"
      class="gp-leading-[0] gDEF_fKRn7"
    >
      <div 
      data-id="gDEF_fKRn7"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:50px;--w-tablet:50px;--w-mobile:50px;--h:50px;--h-tablet:50px;--h-mobile:50px;--minw:50px;--minw-tablet:50px;--minw-mobile:50px;--height-desktop:50px;--height-tablet:50px;--height-mobile:50px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817569156694376">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M134.88,6.17a12,12,0,0,0-13.76,0,259,259,0,0,0-42.18,39C50.85,77.43,36,111.62,36,144a92,92,0,0,0,184,0C220,66.64,138.36,8.6,134.88,6.17ZM128,212a68.07,68.07,0,0,1-68-68c0-33.31,20-63.37,36.7-82.71A249.35,249.35,0,0,1,128,31.11a249.35,249.35,0,0,1,31.3,30.18C176,80.63,196,110.69,196,144A68.07,68.07,0,0,1,128,212Zm49.62-52.4a52,52,0,0,1-34,34,12.2,12.2,0,0,1-3.6.55,12,12,0,0,1-3.6-23.45,28,28,0,0,0,18.32-18.32,12,12,0,0,1,22.9,7.2Z" /></svg></span>
    </div>
      </div>
    </div>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gTjrjEpagu">
    <div
      parentTag="Col"
        class="gTjrjEpagu "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l);--mb-mobile:8px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--ta-mobile:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:150%;--c:#242424;--tt:uppercase;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggTjrjEpagu_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="goWnOXo-bv">
    <div
      parentTag="Col"
        class="goWnOXo-bv "
        style="--ta:left;--ta-mobile:center;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggoWnOXo-bv_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:none;--d-mobile:block;--d-tablet:none;--op:100%;--pt:16px;--pb:16px;--pt-mobile:var(--g-s-3xl);--pb-mobile:var(--g-s-3xl)" class="gbdv4fkpzr ">
      
    <div
    data-id="gbdv4fkpzr"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--t:gp-rotate(0deg);--bc:#EEEEEE"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:#EEEEEE;--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:#EEEEEE;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:#EEEEEE;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gZT9-JgUk9 gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-2xl);--ta:left;--ta-mobile:center"
      class="gp-leading-[0] g5-2Nfz7aP"
    >
      <div 
      data-id="g5-2Nfz7aP"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:50px;--w-tablet:50px;--w-mobile:50px;--h:50px;--h-tablet:50px;--h-mobile:50px;--minw:50px;--minw-tablet:50px;--minw-mobile:50px;--height-desktop:50px;--height-tablet:50px;--height-mobile:50px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817690726629736">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M164,80a28,28,0,1,0-28-28A28,28,0,0,0,164,80Zm0-40a12,12,0,1,1-12,12A12,12,0,0,1,164,40Zm90.88,155.92-54.56-92.08A15.87,15.87,0,0,0,186.55,96h0a15.85,15.85,0,0,0-13.76,7.84L146.63,148l-44.84-76.1a16,16,0,0,0-27.58,0L1.11,195.94A8,8,0,0,0,8,208H248a8,8,0,0,0,6.88-12.08ZM88,80l23.57,40H64.43ZM22,192l33-56h66l18.74,31.8,0,0L154,192Zm150.57,0-16.66-28.28L186.55,112,234,192Z" /></svg></span>
    </div>
      </div>
    </div>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gc9YFzsvCl">
    <div
      parentTag="Col"
        class="gc9YFzsvCl "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l);--mb-mobile:8px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--ta-mobile:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:150%;--c:#242424;--tt:uppercase;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggc9YFzsvCl_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gizEPyBHzQ">
    <div
      parentTag="Col"
        class="gizEPyBHzQ "
        style="--ta:left;--ta-mobile:center;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggizEPyBHzQ_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:none;--d-mobile:block;--d-tablet:none;--op:100%;--pt:16px;--pb:16px;--pt-mobile:var(--g-s-3xl);--pl-mobile:15px;--pb-mobile:var(--g-s-3xl);--pr-mobile:15px" class="goV9xGwCu4 ">
      
    <div
    data-id="goV9xGwCu4"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--t:gp-rotate(0deg);--bc:#EEEEEE"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:#EEEEEE;--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:#EEEEEE;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:#EEEEEE;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div>
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gU2moPvyUY" data-id="gU2moPvyUY"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:0px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FBFBFB;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gU2moPvyUY gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center;--jc-tablet:start"
      class="gt8_ykzejV gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gFu2b4eQJX" data-id="gFu2b4eQJX"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--ml:var(undefined);--pl:134px;--pr:48px;--ml-mobile:var(undefined);--mb-mobile:var(--g-s-3xl);--pl-mobile:15px;--pr-mobile:15px;--pl-tablet:15px;--pr-tablet:48px;--cg:96px;--cg-tablet:50px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gFu2b4eQJX gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="ggAAb_RVrY gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-2xl);--ta:left;--ta-mobile:center"
      class="gp-leading-[0] gXcvutseIf"
    >
      <div 
      data-id="gXcvutseIf"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:50px;--w-tablet:50px;--w-mobile:50px;--h:50px;--h-tablet:50px;--h-mobile:50px;--minw:50px;--minw-tablet:50px;--minw-mobile:50px;--height-desktop:50px;--height-tablet:50px;--height-mobile:50px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817577244033384">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M250.18,105.17,186.71,41.25a100.11,100.11,0,0,0-141.43,0l-.13.14L31.37,55.61a12,12,0,1,0,17.24,16.7L62.32,58.16A75.68,75.68,0,0,1,77.49,46.43L119,88,25.85,181.16a20,20,0,0,0,0,28.29l20.69,20.69a20,20,0,0,0,28.28,0L168,137l1.51,1.51h0l23.65,23.66a20,20,0,0,0,28.29,0l28.69-28.7A20,20,0,0,0,250.18,105.17ZM60.68,210.34l-15-15L108,133l15,15ZM140,131l-15-15,19.51-19.51a12,12,0,0,0,0-17L102.24,37.24a75.94,75.94,0,0,1,67.47,20.95l31.44,31.67L178,113l-1.51-1.51a12,12,0,0,0-17,0Zm67.32,11.31L195,130l23.09-23.09,12.3,12.39Z" /></svg></span>
    </div>
      </div>
    </div>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gBXBWOxRS0">
    <div
      parentTag="Col"
        class="gBXBWOxRS0 "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l);--mb-mobile:8px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--ta-mobile:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:150%;--c:#242424;--tt:uppercase;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggBXBWOxRS0_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gSxQgQgrkD">
    <div
      parentTag="Col"
        class="gSxQgQgrkD "
        style="--ta:left;--ta-mobile:center;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggSxQgQgrkD_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:none;--d-mobile:block;--d-tablet:none;--op:100%;--pt:16px;--pb:16px;--pt-mobile:var(--g-s-3xl);--pb-mobile:var(--g-s-3xl)" class="ghbishM8kR ">
      
    <div
    data-id="ghbishM8kR"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--t:gp-rotate(0deg);--bc:#EEEEEE"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:#EEEEEE;--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:#EEEEEE;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:#EEEEEE;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gibfzt9xoI gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-2xl);--ta:left;--ta-mobile:center"
      class="gp-leading-[0] g4BYSz8EQO"
    >
      <div 
      data-id="g4BYSz8EQO"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:50px;--w-tablet:50px;--w-mobile:50px;--h:50px;--h-tablet:50px;--h-mobile:50px;--minw:50px;--minw-tablet:50px;--minw-mobile:50px;--height-desktop:50px;--height-tablet:50px;--height-mobile:50px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817742716010856">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M240,102c0,70-103.79,126.66-108.21,129a8,8,0,0,1-7.58,0c-3.35-1.8-63.55-34.69-92.68-80.89A4,4,0,0,1,34.92,144H72a8,8,0,0,0,6.66-3.56l9.34-14,25.34,38a8,8,0,0,0,9.16,3.16,8.23,8.23,0,0,0,4.28-3.34L140.28,144H160a8,8,0,0,0,8-8.53,8.18,8.18,0,0,0-8.25-7.47H136a8,8,0,0,0-6.66,3.56l-9.34,14-25.34-38a8,8,0,0,0-9.17-3.16,8.25,8.25,0,0,0-4.27,3.34L67.72,128H23.53a4,4,0,0,1-3.83-2.81A76.93,76.93,0,0,1,16,102,62.07,62.07,0,0,1,78,40c20.65,0,38.73,8.88,50,23.89C139.27,48.88,157.35,40,178,40A62.07,62.07,0,0,1,240,102Z" /></svg></span>
    </div>
      </div>
    </div>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gpm9I-EX2u">
    <div
      parentTag="Col"
        class="gpm9I-EX2u "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l);--mb-mobile:8px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--ta-mobile:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:150%;--c:#242424;--tt:uppercase;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggpm9I-EX2u_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gI-Rusr01i">
    <div
      parentTag="Col"
        class="gI-Rusr01i "
        style="--ta:left;--ta-mobile:center;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggI-Rusr01i_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center;--jc-tablet:start"
      class="g4gBlltdOv gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gZb5HJP96L"
    role="presentation"
    class="gp-group/image gZb5HJP96L gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:none;--d-tablet:block;--op:100%;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Img_265_2_2_768x.jpg?v=1703168335" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Img_265_2_2_768x.jpg?v=1703168335" />
      <source media="(max-width: 1024px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Img_265_2_2_1024x.jpg?v=1703168335" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Img_265_2_2_1024x.jpg?v=1703168335" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Img_265_2_2.jpg?v=1703168335"
        data-src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Img_265_2_2.jpg?v=1703168335"
        width="100%"
        alt=""
        style="--aspect:auto;--objf:fill;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 3",
    "tag": "section",
    "class": "gps-555524770926625714 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555524770859320242&sectionId=555524770926625714)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggTjrjEpagu_text","label":"ggTjrjEpagu_text","default":"<p><strong>Unmatched Water Resistance</strong></p>"},{"type":"html","id":"ggoWnOXo-bv_text","label":"ggoWnOXo-bv_text","default":"<p>Engineered for the depths—dive up to 30 meters with confidence and explore the underwater world without limits.</p>"},{"type":"html","id":"ggc9YFzsvCl_text","label":"ggc9YFzsvCl_text","default":"<p><strong>Adventure-Ready Design</strong></p>"},{"type":"html","id":"ggizEPyBHzQ_text","label":"ggizEPyBHzQ_text","default":"<p>Built to withstand the elements—whether you're scaling mountains, braving the ocean, or navigating the unknown, this watch is your ultimate companion.</p>"},{"type":"html","id":"ggBXBWOxRS0_text","label":"ggBXBWOxRS0_text","default":"<p><strong>Extreme Durability</strong></p>"},{"type":"html","id":"ggSxQgQgrkD_text","label":"ggSxQgQgrkD_text","default":"<p>Built to withstand the toughest conditions, from intense workouts to harsh outdoor environments.</p>"},{"type":"html","id":"ggpm9I-EX2u_text","label":"ggpm9I-EX2u_text","default":"<p><strong>Performance Tracking</strong></p>"},{"type":"html","id":"ggI-Rusr01i_text","label":"ggI-Rusr01i_text","default":"<p>Stay ahead of your goals with real-time heart rate, step count, and calorie tracking, ensuring peak performance.</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
