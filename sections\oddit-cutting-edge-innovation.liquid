{% if section.settings.heading != blank
  or section.settings.sub_heading != blank
  or section.settings.image != blank
  or section.settings.title != blank
  or section.settings.sub_title != blank
  or section.settings.desc != blank
  or section.blocks.size > 0
%}
  <style>
    .cutting-edge-container .cutting-edge-image-info::after {
      background: linear-gradient(180deg, rgba(74, 71, 65, 0) 28.41%, rgba(74, 71, 65, 0.68) 100%);
    }
    .cutting-edge-innovation {
      padding-left: calc((100% - 1340px) / 2 + 0px);
    }
    .cutting-edge-main .swiper-pagination .swiper-pagination-bullet {
      margin: 0;
      border-radius: 100%;
      padding: 0;
      background-color: #4a4741;
      opacity: 0.2;
      width: 8px;
      height: 8px;
    }
    .cutting-edge-main .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
      background-color: #4a4741;
      opacity: 1;
    }
  </style>
  <div class="cutting-edge-main tw-pt-[108px] tw-mb-[40px] max-md:tw-pt-[60px] max-md:tw-mb-[20px] tw-overflow-hidden">
    <div class="cutting-edge-innovation max-lg:!tw-px-[48px] max-md:!tw-px-[16px]">
      <div class="{% if section.settings.full_width %}full-width{% else %}page-width{% endif %} !tw-pl-[48px] max-lg:!tw-pl-0 max-lg:!tw-pr-0">
        {% if section.settings.heading != blank or section.settings.sub_heading != blank %}
          <div class="cutting-edge-header tw-mb-[38px] max-md:tw-mb-[15px] !tw-pr-[48px] max-lg:!tw-pr-0">
            {% if section.settings.heading != blank %}
              <h2 class="heading tw-m-0 tw-text-[40px] max-md:tw-text-[32px] tw-font-semibold tw-leading-[1.3] tw-font-dm-sans tw-text-darkblack !tw-capitalize">
                {{ section.settings.heading }}
              </h2>
            {% endif %}
            {% if section.settings.sub_heading != blank %}
              <div class="sub-heading tw-text-[20px] max-md:tw-text-[15px] tw-text-darkblack tw-font-dm-sans tw-font-medium tw-leading-[1.3]">
                {{ section.settings.sub_heading }}
              </div>
            {% endif %}
          </div>
        {% endif %}
        <div class="cutting-edge-container tw-flex max-lg:tw-flex-col tw-gap-[23px] max-[1200px]:tw-gap-[20px] tw-items-center">
          {% if section.settings.image != blank
            or section.settings.title != blank
            or section.settings.sub_title != blank
            or section.settings.desc != blank
            or section.blocks.size > 0
          %}
            <div class="cutting-edge-info tw-max-w-[431px] max-lg:tw-max-w-full max-[1200px]:tw-max-w-[380px] tw-w-full">
              {% if section.settings.image != blank
                or section.settings.title != blank
                or section.settings.sub_title != blank
                or section.settings.desc != blank
              %}
                <div
                  class="cutting-edge-image-info tw-relative tw-rounded-[40px] tw-overflow-hidden tw-flex after:tw-content-[''] after:tw-absolute after:tw-top-0 after:tw-left-0 after:tw-w-full after:tw-h-full tw-z-[1]"
                >
                  {% if section.settings.image != blank %}
                    <img
                      src="{{ section.settings.image | image_url : width: 5760 }}"
                      class="right-img tw-w-full tw-h-full tw-object-cover"
                      alt="{{ section.settings.image.alt }}"
                    >
                  {% endif %}
                  {% if section.settings.title != blank
                    or section.settings.sub_title != blank
                    or section.settings.desc != blank
                  %}
                    <div class="btm-info tw-absolute tw-bottom-0 tw-left-0 tw-p-[56px_38px] max-[1200px]:tw-p-[50px_30px] max-md:tw-p-[35px_30px] tw-w-full tw-z-[2]">
                      {% if section.settings.title != blank %}
                        <h2 class="title tw-text-[64px] max-md:tw-text-[55px] tw-font-semibold tw-leading-[1.3] tw-font-dm-sans tw-text-[#F5F3EF] tw-mt-0 tw-mb-[10px] max-md:tw-mb-0">
                          {{ section.settings.title }}
                        </h2>
                      {% endif %}
                      {% if section.settings.sub_title != blank %}
                        <h4 class="sub-title tw-text-[24px] tw-text-[#F5F3EF] tw-font-dm-sans tw-font-medium tw-leading-none tw-capitalize tw-mt-0 tw-mb-[15px] max-md:tw-mb-[8px] tw-tracking-normal">
                          {{ section.settings.sub_title }}
                        </h4>
                      {% endif %}
                      {% if section.settings.desc != blank %}
                        <div class="description tw-text-[16px] max-md:tw-text-[15px] tw-text-[#F5F3EF] tw-font-dm-sans tw-font-medium tw-leading-[1.3] *:tw-mb-0">
                          {{ section.settings.desc }}
                        </div>
                      {% endif %}
                    </div>
                  {% endif %}
                </div>
              {% endif %}
            </div>
          {% endif %}
          {% if section.blocks.size > 0 %}
            <div class="swiper cutting-edge-slider tw-max-w-[calc(100%-451px)] max-[1200px]:tw-max-w-[calc(100%-400px)] max-lg:tw-w-[calc(100%+96px)] max-md:tw-w-[calc(100%+32px)] !tw-pr-[48px] max-lg:!tw-px-[48px] max-md:!tw-px-[16px] max-lg:!tw-ml-[-48px] max-md:!tw-ml-[-16px] max-lg:tw-max-w-[unset] ">
              <div class="swiper-wrapper">
                {% for block in section.blocks %}
                  {% if block.settings.block_image != blank
                    or block.settings.block_title != blank
                    or block.settings.block_desc != blank
                  %}
                    <div class="edge-slider swiper-slide tw-p-[50px_40px] max-[1200px]:tw-p-[30px] tw-bg-[#F7F7F7] tw-rounded-[30px] tw-flex tw-flex-col tw-items-center tw-justify-center">
                      {% if block.settings.block_image != blank %}
                        <div
                          class="
                            img-container tw-mb-[44px] tw-flex tw-items-center tw-justify-center tw-relative tw-h-[365px] max-[1200px]:tw-h-[
                            300px] max-md:tw-h-[241px]
                          "
                        >
                          <img
                            src="{{ block.settings.block_image | image_url : width: 5760 }}"
                            class="right-img tw-h-auto tw-object-contain tw-max-h-full"
                            alt="{{ block.settings.block_image.alt }}"
                          >
                        </div>
                      {% endif %}
                      {% if block.settings.block_title != blank or block.settings.block_desc != blank %}
                        {% if block.settings.block_title != blank %}
                          <h4 class="block-title tw-text-[24px] max-md:tw-text-[20px] tw-text-darkblack tw-font-dm-sans tw-font-semibold tw-leading-[1.3] tw-capitalize tw-mb-[15px] max-md:tw-mb-[10px] tw-text-center tw-tracking-normal">
                            {{ block.settings.block_title }}
                          </h4>
                        {% endif %}
                        {% if block.settings.block_desc != blank %}
                          <div class="block-desc tw-text-[18px] max-md:tw-text-[15px] tw-text-darkblack tw-font-dm-sans tw-font-medium tw-leading-[1.3] tw-text-center">
                            {{ block.settings.block_desc }}
                          </div>
                        {% endif %}
                      {% endif %}
                    </div>
                  {% endif %}
                {% endfor %}
              </div>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
    <div class="swiper-button-main tw-flex tw-items-center tw-justify-end tw-gap-[10px] tw-mt-[50px] tw-max-w-[1340px] tw-w-full tw-mx-auto !tw-px-[48px] max-md:!tw-px-[16px] max-lg:tw-hidden">
      <div
        class="cutting-edge-swiper-button-prev swiper-button-prev after:tw-hidden !tw-w-[52px] !tw-h-[52px] !tw-mt-0"
        style="position: unset;"
      >
        {{ 'icon-previous-btn.svg' | inline_asset_content }}
      </div>
      <div
        class="cutting-edge-swiper-button-next swiper-button-next after:tw-hidden !tw-w-[52px] !tw-h-[52px] !tw-mt-0"
        style="position: unset;"
      >
        {{ 'icon-next-btn.svg' | inline_asset_content }}
      </div>
    </div>
    <div
      class="cutting-edge-swiper-pagination tw-hidden max-lg:tw-flex tw-flex-wrap tw-gap-[8px] tw-items-center tw-justify-center max-lg:!tw-px-[48px] max-md:!tw-px-[16px] tw-mt-[20px]"
      style="position: unset;"
    ></div>
  </div>
{% endif %}
{% schema %}
{
  "name": "Oddit Cutting innovation",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Section Full Width",
      "default": false
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading"
    },
    {
      "type": "richtext",
      "id": "sub_heading",
      "label": "Sub Heading"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "text",
      "id": "sub_title",
      "label": "Sub Title"
    },
    {
      "type": "richtext",
      "id": "desc",
      "label": "description"
    }
  ],
  "blocks": [
    {
      "type": "cutting-edge-slider",
      "name": "Cutting edge slider",
      "settings": [
        {
          "type": "image_picker",
          "id": "block_image",
          "label": "Image"
        },
        {
          "type": "text",
          "id": "block_title",
          "label": "Title"
        },
        {
          "type": "text",
          "id": "block_desc",
          "label": "Description"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Oddit Cutting innovation"
    }
  ]
}
{% endschema %}
