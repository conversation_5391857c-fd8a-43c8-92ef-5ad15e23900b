{%- liquid
  assign featured_media = product.selected_or_first_available_variant.featured_media | default: product.featured_media
  assign first_3d_model = product.media | where: 'media_type', 'model' | first
-%}
{%- liquid
  assign product_tags = product.tags | join: ','
  assign has_custom_label = false
  if product.metafields.theme.label and product.metafields.theme.label != blank
    assign has_custom_label = true
    assign custom_label = product.metafields.theme.label.value
  elsif product_tags contains '_label_'
    for tag in product.tags
      if tag contains '_label_'
        assign tag_starts_with = tag | slice: 0
        if tag_starts_with == '_'
          assign has_custom_label = true
          assign custom_label = tag | replace: '_label_', ''
        endif
      endif
    endfor
  endif
-%}
{%- unless product.empty? -%}
  <div
    data-product-images
    data-zoom="{{ product_zoom_enable }}"
    data-has-slideshow="{% if product.media.size > 1 %}true{% else %}false{% endif %}"
  >
    <div class="product__photos product__photos-{{ section_id }} product__photos--{{ thumbnail_position }}">
      {% if template.name == 'product' and template.suffix contains 'oddit' %}
        <div class="tw-hidden max-lg:tw-block">
      {% endif %}
      <div class="product__main-photos max-md:!tw-mb-[15px] max-md:!tw-mx-0" data-aos data-product-single-media-group>
        {% if template.name == 'product'
          and template.suffix contains 'oddit'
          and section.settings.gallery_icon != blank
          or section.settings.gallery_text != blank
        %}
          <div class="gallary-icon tw-flex tw-items-center tw-gap-x-[10px] tw-absolute tw-bottom-[27px] tw-right-0 tw-z-[9]">
            {% if section.settings.gallery_icon != blank %}
              <div class="image tw-flex">
                <img
                  class="tw-h-[30px] tw-max-w-full !tw-block"
                  src="{{ section.settings.gallery_icon | img_url: 'master' }}"
                  alt="{{ section.settings.gallery_icon.alt }}"
                >
              </div>
            {% endif %}
            {% if section.settings.gallery_text != blank %}
              <div class="gallery-text tw-text-[13px] tw-font-medium tw-text-darkblack tw-font-dm-sans tw-leading-[normal] tw-tracking-normal tw-max-w-[110px] tw-w-full">
                {{ section.settings.gallery_text }}
              </div>
            {% endif %}
          </div>
        {% endif %}
        <div
          data-product-photos
          data-zoom="{{ product_zoom_enable }}"
          class="product-slideshow"
          id="ProductPhotos-{{ section_id }}"
        >
          {%- for media in product.media -%}
            {%- render 'media',
              section_id: section_id,
              media: media,
              featured_media: featured_media,
              loopIndex0: forloop.index0,
              loopIndex: forloop.index,
              product_zoom_enable: product_zoom_enable,
              product_zoom_size: product_zoom_size,
              product_image_size: product_image_size,
              isModal: isModal,
              video_looping: video_looping,
              video_style: video_style
            -%}
          {%- endfor -%}
        </div>

        {%- if first_3d_model -%}
          <button
            aria-label="{{ 'products.product.view_in_space_label' | t }}"
            class="product-single__view-in-space"
            data-shopify-xr
            data-shopify-model3d-id="{{ first_3d_model.id }}"
            data-shopify-title="{{ product.title }}"
            data-shopify-xr-hidden
          >
            <svg
              aria-hidden="true"
              focusable="false"
              role="presentation"
              class="icon icon-3d"
              viewBox="18.24 17.35 24.52 28.3"
            >
              <path fill="#3A3A3A" d="M30.5 17.35l-12.26 7.07v14.16l12.26 7.07 12.26-7.08V24.42L30.5 17.35zM20.24 37.42V25.58l10.26-5.93 10.13 5.85-10.13 5.88v12l-10.26-5.96z"/>
            </svg>
            <span class="product-single__view-in-space-text">
              {{ 'products.product.view_in_space' | t }}
            </span>
          </button>
        {%- endif -%}

        {% if section.settings.badge_label != blank %}
          <div class="grid-product__tag grid-product__tag--custom black_friday_label !tw-py-[4px] !tw-px-[11px] !tw-bg-[#EF4444] tw-rounded-[38px] tw-font-dm-sans !tw-text-[15px] tw-font-bold tw-text-white tw-uppercase !tw-leading-[normal] tw-tracking-normal !tw-left-[14px] max-lg:!tw-left-[20px] !tw-top-[30px] max-lg:!tw-top-[20px]">
            {{ section.settings.badge_label }}
          </div>
        {%- elsif has_custom_label -%}
          {% if custom_label contains 'Black Friday'
            or custom_label contains 'BLACK FRIDAY'
            or custom_label contains 'NEW YEAR SALE'
            or custom_label contains "SUMMER SALE"
          %}
            <div class="grid-product__tag grid-product__tag--custom black_friday_label">
              {{ custom_label }}
            </div>
          {% endif %}
        {%- endif -%}
      </div>
      <div
        data-product-thumbs
        class="product__thumbs tw-overflow-hidden tw-px-[16px] product__thumbs--{{ thumbnail_position }} product__thumbs-placement--{{ image_position }}{% if product.media.size == 1 %} medium-up--hide{% endif %} small--hide max-lg:!tw-block"
        data-position="{{ thumbnail_position }}"
        data-arrows="{{ thumbnail_arrows }}"
        data-aos
      >
        {%- if thumbnail_arrows -%}
          <button type="button" class="product__thumb-arrow product__thumb-arrow--prev hide">
            <svg
              aria-hidden="true"
              focusable="false"
              role="presentation"
              class="icon icon-chevron-left"
              viewBox="0 0 284.49 498.98"
            >
              <path d="M249.49 0a35 35 0 0 1 24.75 59.75L84.49 249.49l189.75 189.74a35.002 35.002 0 1 1-49.5 49.5L10.25 274.24a35 35 0 0 1 0-49.5L224.74 10.25A34.89 34.89 0 0 1 249.49 0z"/>
            </svg>
          </button>
        {%- endif -%}

        <div class="product__thumbs--scroller tw-rounded-tl-[20px] tw-rounded-br-[20px]">
          {%- if product.media.size > 1 -%}
            {%- for media in product.media -%}
              {%- liquid
                assign image_set = false
                assign image_set_group = ''
                if media.alt contains '#'
                  assign image_set_full = media.alt | split: '#' | last
                  if image_set_full contains '_'
                    assign image_set = true
                    assign image_set_group = image_set_full | split: '_' | first
                  endif
                endif
              -%}
              <div
                class="product__thumb-item !tw-mr-[10px] tw-rounded-[4px] tw-overflow-hidden !tw-border-none"
                data-index="{{ forloop.index0 }}"
                {% if image_set %}
                  data-set-name="{{image_set_group}}"
                  data-group="{{image_set_full}}"
                {% endif %}
              >
                <div
                  class="image-wrap !tw-bg-[#F6F6F6]"
                  style="height: 0; padding-bottom: {{ 100 | divided_by: media.preview_image.aspect_ratio }}%;"
                >
                  <a
                    href="{{ media.preview_image | img_url: product_zoom_size }}"
                    data-product-thumb
                    class="product__thumb before:!tw-hidden"
                    data-index="{{ forloop.index0 }}"
                    data-id="{{ media.id }}"
                  >
                    {%- if media.media_type == 'video' or media.media_type == 'external_video' -%}
                      <span class="product__thumb-icon">
                        <svg
                          aria-hidden="true"
                          focusable="false"
                          role="presentation"
                          class="icon icon-play"
                          viewBox="18.24 17.35 24.52 28.3"
                        >
                          <path fill="#323232" d="M22.1 19.151v25.5l20.4-13.489-20.4-12.011z"/>
                        </svg>
                      </span>
                    {%- endif -%}
                    {%- if media.media_type == 'model' -%}
                      <span class="product__thumb-icon">
                        <svg
                          aria-hidden="true"
                          focusable="false"
                          role="presentation"
                          class="icon icon-3d"
                          viewBox="18.24 17.35 24.52 28.3"
                        >
                          <path fill="#3A3A3A" d="M30.5 17.35l-12.26 7.07v14.16l12.26 7.07 12.26-7.08V24.42L30.5 17.35zM20.24 37.42V25.58l10.26-5.93 10.13 5.85-10.13 5.88v12l-10.26-5.96z"/>
                        </svg>
                      </span>
                    {%- endif -%}

                    {%- assign img_url = media.preview_image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}
                    <img
                      class="animation-delay-{{ forloop.index | times: 3 }} lazyload"
                      data-src="{{ img_url }}"
                      data-widths="[120, 360, 540, 720]"
                      data-aspectratio="{{ media.preview_image.aspect_ratio }}"
                      data-sizes="auto"
                      alt="{{ media.alt | escape | split: '#' | first }}"
                    >

                    <noscript>
                      <img
                        class="lazyloaded"
                        src="{{ media | img_url: '400x' }}"
                        alt="{{ media.alt | escape }}"
                      >
                    </noscript>
                  </a>
                </div>
              </div>
            {%- endfor -%}
          {%- endif -%}
        </div>

        {%- if thumbnail_arrows -%}
          <button type="button" class="product__thumb-arrow product__thumb-arrow--next">
            <svg
              aria-hidden="true"
              focusable="false"
              role="presentation"
              class="icon icon-chevron-right"
              viewBox="0 0 284.49 498.98"
            >
              <path d="M35 498.98a35 35 0 0 1-24.75-59.75l189.74-189.74L10.25 59.75a35.002 35.002 0 0 1 49.5-49.5l214.49 214.49a35 35 0 0 1 0 49.5L59.75 488.73A34.89 34.89 0 0 1 35 498.98z"/>
            </svg>
          </button>
        {%- endif -%}
      </div>
      {% if template.name == 'product' and template.suffix contains 'oddit' %}
        </div>
      {% endif %}
      {%- if template.suffix == 'oddit' -%}
        <div class="desktop-images-list mobile-hide max-lg:tw-hidden">
          <div class="image-container tw-grid tw-grid-cols-2 tw-gap-[15px]">
            {%- for media in product.media -%}
              {%- render 'oddit-media',
                section_id: section_id,
                media: media,
                featured_media: featured_media,
                loopIndex0: forloop.index0,
                loopIndex: forloop.index,
                product_zoom_enable: product_zoom_enable,
                product_zoom_size: product_zoom_size,
                product_image_size: product_image_size,
                isModal: isModal,
                video_looping: video_looping,
                video_style: video_style
              -%}
            {%- endfor -%}
          </div>
        </div>
      {%- endif -%}
    </div>
  </div>

  {% if thumbnail_height == 'fixed' %}
    {% style %}
      .product__photos-{{ section_id }} .product__thumbs:not(.product__thumbs--below) {
        min-height: 400px;
        max-height: 400px;
      }

      @media screen and (max-width: 798px) {
        .product__photos-{{ section_id }} .product__thumbs:not(.product__thumbs--below) {
          min-height: 300px;
          max-height: 300px;
        }
      }
    {% endstyle %}
  {% endif %}

  <script type="application/json" id="ModelJson-{{ section_id }}">
    {{ product.media | where: 'media_type', 'model' | json }}
  </script>
{%- else -%}
  <div class="product__photos product__photos-{{ section_id }}">
    <div class="product__main-photos" style="width: 100%">
      <div data-product-photos>
        <div class="product-main-slide" data-index="{{ forloop.index0 }}">
          <a href="#">
            {{ 'product-1' | placeholder_svg_tag: 'placeholder-svg' }}
          </a>
        </div>
      </div>
    </div>
  </div>
{%- endunless -%}
