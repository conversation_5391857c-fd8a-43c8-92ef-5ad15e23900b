
{%- comment -%}
{
  "pack_name": "FAQ - simple",
  "pack_author": "Design Packs for Wonderment | 2023"
{%- endcomment -%}

{%- comment -%} Layout: has user entered measurement value into max-width field? {%- endcomment -%}
{%- if section.settings.max_width contains 'px' or section.settings.max_width contains '%' -%}
  {%- assign max_width = section.settings.max_width -%}
{%- else -%}
  {%- assign max_width = section.settings.max_width | append: 'px' -%}
{%- endif -%}

{%- comment -%} Text: custom font_picker values {%- endcomment -%}
{%- capture font_settings_list -%}
{{ section.settings.main_font | font_face: font_display: 'swap' }}~
{{ section.settings.main_font | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
{{ section.settings.main_font | font_modify: 'style', 'italic' | font_face: font_display: 'swap' }}~
{{ section.settings.main_font | font_modify: 'style', 'italic' | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}
{{ section.settings.heading_font | font_face: font_display: 'swap' }}~
{{ section.settings.heading_font | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}~
{{ section.settings.heading_font | font_modify: 'style', 'italic' | font_face: font_display: 'swap' }}~
{{ section.settings.heading_font | font_modify: 'style', 'italic' | font_modify: 'weight', 'bolder' | font_face: font_display: 'swap' }}
{%- endcapture -%}
{%- assign font_array = font_settings_list | split: '~' -%}

{%- capture minify -%}
{%- comment -%} CSS {%- endcomment -%}
<style>
{%- if section.settings.override_theme_font != blank -%}
  {%- for font in font_array -%}
    {%- unless font contains 'error' -%}{{ font }}{%- endunless -%}
  {%- endfor -%}
  #WS--{{ section.id }} {
    --main-font: {{ section.settings.main_font.family }}, {{ section.settings.main_font.fallback_families }};
    --main-font-weight: var(--main-font-weight);
    --main-font-style: var(--main-font-style);
    --heading-font: {{ section.settings.heading_font.family }}, {{ section.settings.heading_font.fallback_families }};
    --heading-font-weight: var(--heading-font-weight);
    --heading-font-style: var(--heading-font-style);
  }
{%- endif -%}

#WS--{{ section.id }} {
  color: {{ section.settings.text_color }};
  background: {{ section.settings.background_color }};
  {%- if section.settings.override_theme_font != blank -%}
  font-family: var(--main-font);
  font-weight: var(--main-font-weight);
  font-style: var(--main-font-style);
  {%- endif -%}
  margin-top: {{ section.settings.outer_margin }}px;
  margin-bottom: {{ section.settings.outer_margin }}px;
}

#WS--{{ section.id }} .wndr-sxn__sizer {
  padding-top: {{ section.settings.inner_padding }}px;
  padding-bottom: {{ section.settings.inner_padding }}px;
  width: {{ section.settings.base_width }}%;
  max-width: {{ max_width }};
  margin: 0 auto;
  flex-wrap: wrap;
}

#WS--{{ section.id }} [aria-expanded="true"] .vert {
  display: none;
}

#WS--{{ section.id }} [aria-expanded] rect {
  fill: currentColor;
}

#WS--{{ section.id }} .wndr-sxn__heading {
  margin: 0;
  padding: 1em 0;
  color: {{ section.settings.heading_color }};
  line-height: calc(3px + 2ex + 3px);
  font-size: calc(var(--dp-g-heading-size, var(--dp-heading-size, 35px)) * {{ section.settings.text_size | times: 0.01 }});
  {%- if section.settings.override_theme_font != blank -%}
  font-family: var(--heading-font);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  {%- endif -%}
}

#WS--{{ section.id }} .wndr-sxn__questions {
  flex-direction: column;
}

#WS--{{ section.id }} .wndr-sxn__question-block {
  padding-top: calc(1em + 3px);
  padding-bottom: calc(1em + 3px);
  border-top: 2px solid {{ section.settings.border_color }};
}

#WS--{{ section.id }} .wndr-sxn__faq__expanded-true button svg {
  display: none;
}

#WS--{{ section.id }} .wndr-sxn__faq__expanded-true .wndr-sxn__question button {
  cursor: default;
}

#WS--{{ section.id }} .wndr-sxn__faq__expanded-true .wndr-sxn__text {
  height: auto;
}

#WS--{{ section.id }} .wndr-sxn__question {
  margin: 0;
  color: {{ section.settings.heading_color }};
  font-size: calc(var(--dp-g-small-heading-size, var(--dp-small-heading-size, 24px)) * {{ section.settings.text_size | times: 0.01 }});
  {%- if section.settings.override_theme_font != blank -%}
  font-family: var(--heading-font);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  {%- endif -%}
}

#WS--{{ section.id }} .wndr-sxn__question button {
  color: inherit;
  background: transparent;
  border: none;
  font-family: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  cursor: pointer;
  text-align: left;
  text-transform: none;
  margin: 0;
  line-height: calc(3px + 2ex + 3px);
  font-size: calc(var(--dp-g-small-heading-size, var(--dp-small-heading-size, 20px)) * {{ section.settings.text_size | times: 0.01 }});
  {%- if section.settings.override_theme_font != blank -%}
  font-family: var(--heading-font);
  font-weight: var(--heading-font-weight);
  font-style: var(--heading-font-style);
  {%- endif -%}
}

#WS--{{ section.id }} .wndr-sxn__question button:focus {
  outline: none;
  /* background-color: rgba(255,255,255,0.25);
  box-shadow: 0px 0px 0px 5px rgba(255,255,255,0.25); */
}

#WS--{{ section.id }} .wndr-sxn__question svg {
  width: 20px;
  min-width: 20px;
  margin-left: 5px;
}

#WS--{{ section.id }} .wndr-sxn__text {
  width: 100%;
  color: inherit;
  font-family: inherit;
  height: 0px;
  transition: height 0.5s ease;
  overflow: hidden;
}

#WS--{{ section.id }} .wndr-sxn__text *:first-child {
  margin-top: 1em;
}

#WS--{{ section.id }} .wndr-sxn__text p {
  margin-bottom: 1em;
  line-height: calc(3px + 3ex + 3px);
  font-size: calc(var(--dp-g-body-size, var(--dp-body-size, 18px)) * {{ section.settings.text_size | times: 0.01 }});
  {%- if section.settings.override_theme_font != blank -%}
  font-family: var(--main-font);
  font-weight: var(--main-font-weight);
  font-style: var(--main-font-style);
  {%- endif -%}
}

#WS--{{ section.id }} .wndr-sxn__additional-content {
  width: 100%;
  padding-top: 1em;
  margin-top: 2.5em;
  margin-bottom: 1em;
  border-top: thin solid {{ section.settings.border_color }};
  color: {{ section.settings.text_color }};
  line-height: calc(3px + 3ex + 3px);
  font-size: calc(var(--dp-g-body-size, var(--dp-body-size, 18px)) * {{ section.settings.text_size | times: 0.01 }});
  {%- if section.settings.override_theme_font != blank -%}
  font-family: var(--main-font);
  font-weight: var(--main-font-weight);
  font-style: var(--main-font-style);
  {%- endif -%}
}

{%- if section.settings.custom_css != blank -%}
  {%- assign custom_section_declarations = section.settings.custom_css | split: '}' -%}
  {%- for declaration in custom_section_declarations -%}
    {%- if declaration contains '{' -%}
      #WS--{{ section.id }} {{ declaration }} }
    {%- endif -%}
  {%- endfor -%}
{%- endif -%}

/* Landscape phones and down */
@media (max-width: 480px) {
  #WS--{{ section.id }} {
    margin-top: {{ section.settings.outer_margin | divided_by: 2 }}px;
    margin-bottom: {{ section.settings.outer_margin | divided_by: 2 }}px;
  }

  #WS--{{ section.id }} .wndr-sxn__sizer {
    min-width: 70%;
    padding-top: {{ section.settings.inner_padding | divided_by: 2 }}px;
    padding-bottom: {{ section.settings.inner_padding | divided_by: 2 }}px;
  }

  {%- if section.settings.mobile_custom_css != blank -%}
    {%- assign mobile_custom_declarations = section.settings.mobile_custom_css | split: '}' -%}
    {%- for declaration in mobile_custom_declarations -%}
      {%- if declaration contains '{' -%}
        #WS--{{ section.id }} {{ declaration }} }
      {%- endif -%}
    {%- endfor -%}
  {%- endif -%}
}
</style>

{%- comment -%} HTML {%- endcomment -%}
<section id="WS--{{ section.id }}"
        class="WS--{{ section.id }} wndr-sxn__faq wndr-sxn__section">
  <div class="wndr-sxn__sizer
              wndr-sxn__faq__expanded-{{ section.settings.show_answers }}">
    {%- for block in section.blocks -%}
      <div id="WS--{{ block.id }}" class="wndr-sxn__block wndr-sxn__block--{{ block.type }}" {{ block.shopify_attributes }}>
        {%- if block.type == "section_heading" -%}
          <div class="wndr-sxn__heading-area wndr-sxn__justify-{{ section.settings.alignment }}">
            <h2 class="wndr-sxn__heading" id="{{ block.settings.title | handleize }}">{{ block.settings.title }}</h2>
          </div>
        {%- elsif block.type == "question" -%}
          <div class="wndr-sxn__question-block">
            <h3 class="wndr-sxn__question">
              <button aria-expanded="{{ section.settings.show_answers }}" accordion-trigger>
                {{ block.settings.title }}
                <svg aria-hidden="true" focusable="false" viewBox="0 0 10 10">
                  <rect class="vert" height="8" width="1" y="1" x="4.5"/>
                  <rect height="1" width="8" y="4.5" x="1"/>
                </svg>
              </button>
            </h3>
            <div class="wndr-sxn__text wndr-sxn__rte">
              {{ block.settings.text }}
            </div>
          </div>
        {%- endif -%}
      </div>
    {%- endfor -%}
  </div>
</section>
{%- endcapture -%}
{{ minify | strip_newlines }}

{% schema %}
{
  "name": "📦 FAQ",
  "class": "WS__faq-simple",
  "settings": [
    {
      "type": "header",
      "content": "✏️ Text appearance"
    },
    {
      "type": "checkbox",
      "id": "show_answers",
      "label": "Show answers expanded",
      "default": false
    },
    {
      "type": "range",
      "id": "text_size",
      "label": "Text size",
      "min": 80,
      "max": 150,
      "step": 5,
      "default": 100,
      "unit": "%"
    },
    {
      "type": "select",
      "id": "alignment",
      "label": "Heading alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        }
      ],
      "default": "center"
    },
    {
      "type": "header",
      "content": "🎨 Design"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "#121212"
    },
    {
      "type": "color_background",
      "id": "background_color",
      "label": "Background",
      "default": "linear-gradient(184deg, rgba(255, 255, 255, 1) 7%, rgbargba(255, 255, 255, 1) 94%)"
    },
    {
      "type": "color",
      "id": "border_color",
      "label": "Borders",
      "default": "#121212"
    },
    {
      "type": "header",
      "content": "🖼 Layout"
    },
    {
      "type": "range",
      "id": "base_width",
      "label": "Size",
      "min": 50,
      "max": 100,
      "step": 5,
      "default": 80,
      "unit": "%"
    },
    {
      "type": "text",
      "id": "max_width",
      "label": "Maximum width",
      "placeholder": "eg. 1200px",
      "info": "Sets width constraint for content."
    },
    {
      "type": "range",
      "id": "inner_padding",
      "label": "Inner padding",
      "info": "Only applies to top and bottom.",
      "min": 0,
      "max": 100,
      "default": 100,
      "step": 5,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "outer_margin",
      "label": "Outer margin",
      "info": "Only applies to top and bottom.",
      "min": 0,
      "max": 100,
      "default": 0,
      "step": 5,
      "unit": "px"
    },
    {
      "type": "header",
      "content": "🚨 Advanced"
    },
    {
      "type": "html",
      "id": "custom_css",
      "label": "CSS"
    },
    {
      "type": "html",
      "id": "mobile_custom_css",
      "label": "Mobile CSS",
      "info": "Applied on screens less than 480px."
    },
    {
      "type": "checkbox",
      "id": "override_theme_font",
      "label": "Override default theme font",
      "default": false
    },
    {
      "type": "font_picker",
      "id": "heading_font",
      "label": "Headings and questions",
      "default": "serif"
    },
    {
      "type": "font_picker",
      "id": "main_font",
      "label": "Answers",
      "default": "sans-serif"
    }
  ],
  "blocks": [
    {
      "type": "section_heading",
      "name": "Section heading",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Heading"
        }
      ]
    },
    {
      "type": "question",
      "name": "Question block",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Question",
          "default": "Do you ship internationally?"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Answer",
          "default": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Mauris nibh enim, ornare at consequat sed, placerat quis libero. Curabitur id nulla ut nunc aliquam sodales.</p>"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "📦 FAQ",
      "settings": {
      },
      "blocks": [
        {
          "type": "section_heading",
          "settings": {
            "title": "Getting started"
          }
        },
        {
          "type": "question",
          "settings": {
            "title": "Where are your products manufactured?",
            "text": "<p>We are a local company and all merchandise is made with love here on the moon.</p>"
          }
        },
        {
          "type": "question",
          "settings": {
            "title": "How do your sizes work?",
            "text": "<p>Please take a look at our size chart for more information and how to take accurate measurements.</p>"
          }
        },
        {
          "type": "section_heading",
          "settings": {
            "title": "Shipping"
          }
        },
        {
          "type": "question",
          "settings": {
            "title": "Do you ship internationally?",
            "text": "<p>Currently we are only shipping to USA and Canada.</p>"
          }
        },
        {
          "type": "question",
          "settings": {
            "title": "How much is shipping?",
            "text": "<p>If you spend over $100, shipping is always free. For any other purchases, shipping will be calculated at checkout.</p>"
          }
        },
        {
          "type": "question",
          "settings": {
            "title": "What is your return policy?",
            "text": "<p>Merchandise that is unworn and in the original packaging can be returned within 30 days with the packing slip included with the product.</p>"
          }
        }
      ]
    }
  ]
}
{% endschema %}
{%- comment -%} JSON Settings {%- endcomment -%}
<script type="application/json" data-wndr-sxn="{{ section.id }}">
  {
    "id": {{ section.id | json }},
    "show_answers": {{ section.settings.show_answers | json }}
  }
</script>

{%- comment -%} JavaScript {%- endcomment -%}
<script data-wndr-sxn-js="{{ section.id }}" type="module" defer>
(function(){

  const wonderSection = {
    settings: {},
    accordion: function (event) {
      const btn = event.target.closest('[accordion-trigger]');
      const target = btn.parentNode.nextElementSibling;

      if (btn.getAttribute('aria-expanded') === 'false') {
        target.classList.add('wndr-sxn__active');
        target.style.height = "auto";

        var height = target.clientHeight + "px";

        target.style.height = "0px";

        setTimeout(() => {
          target.style.height = height;
        }, 0)

        btn.setAttribute('aria-expanded', 'true');

        target.addEventListener('transitionend', () => {
          target.classList.remove('wndr-sxn__active')
        }, {once: true})

      } else {
        target.style.height = "0px";
        btn.setAttribute('aria-expanded', 'false');
      }

    },
    load: function(section){
      if (wonderSection.settings.show_answers == true){
        return false;
      }

      section.addEventListener('click', (event) => {
        this.accordion(event);
      });

    },
    unload: function(section){
      section.removeEventListener('click', (event) => {
        this.accordion(event);
      });
    }
  }

  window.addEventListener('shopify:section:unload', function (e) {
    const settings = document.querySelector(`[data - wndr - sxn= "${e.detail.sectionId}"]`);
    const sectionId = e.detail.sectionId;
    const section = document.querySelector(`#WS--${ e.detail.sectionId } `);
    if (sectionId == wonderSection.settings.id){
      wonderSection.unload(section);
    }
  });

  window.addEventListener('shopify:section:load', function (e) {
    const section = document.querySelector(`#WS--${ e.detail.sectionId } `);

    // Load external JS file
    const sectionJS = document.querySelector(`[data - wndr - sxn - js= "${e.detail.sectionId}"]`);
    const sectionExternalJS = document.querySelector(`[data - wndr - sxn - external - js= "${e.detail.sectionId}"]`);

    const loadJavaScriptBlock = () => {
      if (sectionJS) {
        const sectionScript = sectionJS.innerHTML;
        const newScript = document.createElement('script');
        const inlineScript = document.createTextNode(sectionScript);
        newScript.appendChild(inlineScript);
        section.appendChild(newScript);
      }
    }

    if (sectionExternalJS) {
      const newScript = document.createElement('script');
      const externalPath = sectionExternalJS.src;
      newScript.src = externalPath;
      section.appendChild(newScript);
      newScript.onload = () => {
        loadJavaScriptBlock();
      }
    } else {
      loadJavaScriptBlock();
    }

  });


  wonderSection.settings = JSON.parse(document.querySelector('[data-wndr-sxn="{{ section.id }}"]').innerHTML);
  const sectionId = {{ section.id | json }};
  const section = document.querySelector(`#WS--${ sectionId } `);

  if (sectionId == wonderSection.settings.id){
    wonderSection.load(section);
  }
})()
</script>

{%- comment -%} SEO {%- endcomment -%}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {%- for block in section.blocks -%}
      {%- if block.type == "question" -%}
        {
          "@type": "Question",
          "name": "{{ block.settings.title | escape }}",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "{{ block.settings.text | escape }}"
          }
        }{%- if forloop.last != true -%},{%- endif -%}
      {%- endif -%}
    {%- endfor -%}
  ]
}
</script>
