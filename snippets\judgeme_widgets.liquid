{% comment %} Please do not edit this file. It is automatically updated by Judge.me{% endcomment %}


{% if widget_type == 'judgeme_review_widget' %}
<div style='clear:both'></div>
<div id='judgeme_product_reviews' class='jdgm-widget jdgm-review-widget' data-product-title='{{ product.title | escape }}' data-id='{{ product.id }}'
data-from-snippet='{% if concierge_install != nil  %}{{ concierge_install }}{% else %}true{% endif %}'
data-auto-install='{% if auto_install != nil %}{{ auto_install }}{% else %}false{% endif %}'>
  {{ product.metafields.judgeme.widget }}
</div>
{% endif %}

{% if widget_type == 'judgeme_verified_reviews_count_badge' %}
<div style='clear:both'></div>
<div style='text-align: center'>
  <a class="jdgm-verified-count-badget" href="javascript:void(0)" data-from-snippet='{% if concierge_install != nil %}{{ concierge_install }}{% else %}true{% endif %}'>
    {{ shop.metafields.judgeme.verified_badge }}
  </a>
</div>
{% endif %}

{% if widget_type == 'judgeme_all_reviews_text' %}
<div class='jdgm-widget jdgm-all-reviews-text' data-from-snippet='{% if concierge_install != nil  %}{{ concierge_install }}{% else %}true{% endif %}' >
  <a href="javascript:void(0)">
    <span class='jdgm-all-reviews-text__text' data-score="{{ shop.metafields.judgeme.all_reviews_rating }}" data-number-of-reviews="{{ shop.metafields.judgeme.all_reviews_count }}" data-locale="nl">
      Klanten beoordelen ons {{ shop.metafields.judgeme.all_reviews_rating | round: 1 }}/5 gebaseerd op {{ shop.metafields.judgeme.all_reviews_count }} - recensies.
    </span>
  </a>
</div>
{% endif %}

{% if widget_type == 'judgeme_medals' %}
  {{ shop.metafields.judgeme.medals }}
{% endif %}

{% if widget_type == 'judgeme_floating_tab' %}
<section class='jdgm-widget jdgm-revs-tab' role='complementary'>
  {% assign jm_metafields = shop.metafields.judgeme %}
  <div class='jdgm-revs-tab-btn btn' position="none" tabindex="0" aria-label="Click to open Judge.me floating reviews tab" role='button'>★ Judge.me beoordelingen</div>
  <div class='jdgm-revs-tab__header'>
    <a class='jdgm-close-ico'></a>
    <h3 class='jdgm-revs-tab__title'>Laat klanten voor ons spreken</h3>
    <a class='jdgm-revs-tab__url' href='https://gardpro.nl/pages/reviews'>
      <div data-score='{{ jm_metafields.all_reviews_rating }}' class='jdgm-all-reviews-rating' aria-label='Average rating is {{ jm_metafields.all_reviews_rating }}' role='img'></div>{{ jm_metafields.all_reviews_count }} reviews
    </a>
  </div>
  {{ jm_metafields.reviews_tab }}
</section>
{% endif %}

{% if widget_type == 'judgeme_featured_carousel' %}
<div class='jdgm-carousel-wrapper'
data-from-snippet='{% if concierge_install != nil %}{{ concierge_install }}{% else %}true{% endif %}'
data-auto-install='{% if auto_install != nil %}{{ auto_install }}{% else %}false{% endif %}' >
  {% assign jm_metafields = shop.metafields.judgeme %}  
  <div class="jdgm-carousel-title-and-link">
    <h2 class='jdgm-carousel-title'>Laat klanten voor ons spreken</h2>
    <span class="jdgm-all-reviews-rating-wrapper" href="javascript:void(0)">
      <span style="display:block"  data-score='{{ jm_metafields.all_reviews_rating }}' class='jdgm-all-reviews-rating' aria-label='{{ jm_metafields.all_reviews_rating }} stars' tabindex='0' role='img'></span>
      <span style="display: block" class='jdgm-carousel-number-of-reviews' data-number-of-reviews='{{ jm_metafields.all_reviews_count }}'>
        van {{ shop.metafields.judgeme.all_reviews_count }} recensies
      </span>
</span>  </div>
  {{ jm_metafields.featured_carousel }}
</div>
{% endif %}

{% if widget_type == 'judgeme_preview_badge' %}
<div style='{{ jm_style }}' class='jdgm-widget jdgm-preview-badge' data-id='{{ product.id }}'
data-template='{% if concierge_install == true or concierge_install == nil %}{{ template }}{% else %}manual-installation{% endif %}'
data-auto-install='{% if auto_install != nil %}{{ auto_install }}{% else %}false{% endif %}'>
  {{ product.metafields.judgeme.badge }}
</div>
{% endif %}

{% if widget_type == 'judgeme_ugc_media_grid' %}
<div class='jdgm-widget jdgm-ugc-media-wrapper'
    data-from-snippet='{% if concierge_install != nil %}{{ concierge_install }}{% else %}true{% endif %}'
    data-auto-install='{% if auto_install != nil %}{{ auto_install }}{% else %}false{% endif %}' >
  {{ shop.metafields.judgeme.ugc_media_grid }}
</div>
{% endif %}

