<section class="image_text_6"  style="background:{{section.settings.bg_color}}; {% if section.settings.bg_image != blank %}background-image: url({{ section.settings.bg_image | img_url: 'master' }}); background-size: cover; background-repeat: no-repeat; background-position: center;{% endif %};">
  <div class="page_wrapper">
    <div class="wrapper">
      <div class="text_block">

        {% if section.settings.heading != blank %}
        <h2 class="heading">{{ section.settings.heading }}</h2>
        {% endif %}
  
        <div class="timmer_wrap">
           {% for block in section.blocks %}
               {% if block.settings.image != blank %}
          <div class="timmer_item">
        <img src="{{ block.settings.image |img_url :'master' }}">     
          </div>
            {% endif %}
          {% endfor %}
        </div>

<div class="swiper-container timmer_swiper" style="display: none;">
  <div class="swiper-wrapper timmer_wrap">
    {% for block in section.blocks %}
      {% if block.settings.image != blank %}
        <div class="swiper-slide timmer_item">
          <img src="{{ block.settings.image | img_url: 'master' }}">
        </div>
      {% endif %}
    {% endfor %}
  </div>
  <div class="swiper-pagination"></div>
</div>


        
          <div class="icon_wrap">
           {% for block in section.blocks %}
          <div class="icon_item">
         {% if block.settings.icon != blank %}
        <img src="{{ block.settings.icon |img_url :'master' }}">
        {% endif %}
          </div>
          {% endfor %}
          {% if section.settings.more_btn !=blank %}
          <div class="icon_item more_btn">{{ section.settings.more_btn }}</div>
            {% endif %}
        </div>
      </div>
   
      
    </div>
  </div>
</section>

<style>


section.image_text_6 .wrapper .text_block {
    width: 100%;
    display: flex;
      gap: 40px;
    flex-direction: column;

}
section.image_text_6 .wrapper .text_block h2.heading {
    font-size: 74px;
    font-family: Helvetica-Bold;
    margin: 0;
  color:#2a2b2b;

      text-transform: none;
}
  section.image_text_6 .wrapper .text_block h2.heading p{
      margin: 0;
  }
section.image_text_6 .wrapper .text_block .content p {
    margin: 0;
    font-size: 20px;
    font-family: Helvetica-Bold;
    color: #676666;
}
  section.image_text_6 .wrapper .image_block {
    width: 100%;
    font-size: 0;
}
  section.image_text_6 .wrapper .image_block img{
    width: 100%;
    }
  /* .image_text_6 .wrapper {

    display: grid;
        align-items: center;
    gap:50px;
} */
  section.image_text_6 .wrapper .text_block .content {
    width: 100%;

}
  section.image_text_6 .wrapper .text_block .sub_heading {
    margin: 0;
    font-size: 20px;
    font-family: Helvetica-Bold;
    color: #676666;
}

section.image_text_6 .wrapper .text_block .timmer_wrap {
    display: grid
;
    grid-template-columns: repeat(3, auto);
      gap: 100px;
}
  section.image_text_6 .wrapper .text_block .timmer_wrap .timmer_item {
    width: 100%;
}
  section.image_text_6 .wrapper .text_block .timmer_wrap .timmer_item img{
    width: 100%;
}
  section.image_text_6 .wrapper .text_block .icon_wrap {
    display: grid
;
    grid-template-columns: repeat(14, auto);
        gap: 20px;
  }
  section.image_text_6 .wrapper .text_block .icon_wrap .icon_item {
    width: 100%;
    max-width: 50px;
}
  section.image_text_6 .wrapper .text_block .icon_wrap .icon_item img {
    width: 100%;
}
section.image_text_6 .wrapper .text_block .icon_wrap .icon_item.more_btn {
    display: flex !important
;
    justify-content: left;
    align-items: center;
    max-width: 100%;
    font-size: 15px;
    font-family: Helvetica-Bold;
    color: #999c9e;
  font-weight:700;
      cursor: pointer;
}


    section.image_text_6 {
    padding: 50px 150px;
}
  section.image_text_6 .wrapper .text_block .icon_wrap .icon_item.more_btn.clicked {
    display: none !important;
}

  @media only screen and (min-width: 2600px) {
 section.image_text_6 .wrapper .text_block h2.heading {
    font-size: 135px;
 }
    section.image_text_6 .wrapper .text_block .icon_wrap .icon_item {
    width: 100%;
    max-width: 100px;
}
    section.image_text_6 .wrapper .text_block .icon_wrap .icon_item.more_btn {
    font-size: 31px;

    }
  }
   @media only screen and (max-width: 1600px) {
  section.image_text_6 {
    padding: 25px 60px;
}
   }
  @media only screen and (max-width: 1280px) {
section.image_text_6 .wrapper .text_block h2.heading {
    font-size: 48px;
}
  }
  @media only screen and (max-width: 1024px) {
  section.image_text_6 .wrapper .text_block .timmer_wrap {
    gap: 50px;
}
    section.image_text_6 .wrapper .text_block .icon_wrap {

    grid-template-columns: repeat(10, auto);
    }
  }
  @media only screen and (max-width: 840px) {
        section.image_text_6 {
        padding: 30px 20px;
    }
        section.image_text_6 .wrapper .text_block .icon_wrap {
    grid-template-columns: repeat(8, auto);
    gap: 15px;
}
  }
  @media only screen and (max-width: 480px) {
    section.image_text_6 .wrapper .text_block .timmer_wrap{
      display:none;
    }
  section.image_text_6 .wrapper .text_block h2.heading {
    font-size: 38px;
}
    section.image_text_6 .wrapper .text_block .icon_wrap .icon_item{
          max-width: 45px;
    }
    section.image_text_6 .wrapper .text_block .icon_wrap .icon_item.more_btn{
      max-width: 45px;
    text-align: center;
    }
    section.image_text_6 .wrapper .text_block .icon_wrap {
    grid-template-columns: repeat(5, auto);
    gap: 10px;
}
    section.image_text_6 .wrapper .text_block .swiper-container {
    display: block !important;
              overflow: hidden;
              padding-bottom: 20px;
      position:relative;
}
      .image_text_6 span.swiper-pagination-bullet {
    width: 6px;
    height: 6px;
    background-color: currentColor;
    opacity: 0.4;
}
    .image_text_6 .swiper-pagination{
      bottom:0;
    }
    .image_text_6 .swiper-pagination-bullet.swiper-pagination-bullet-active {
    opacity: 1;
    background-color: currentColor;
    width: 9px;
    height: 9px;
}
section.image_text_6 .wrapper .text_block .swiper-container .timmer_wrap{

        display: flex;
        gap: 0;
    }


  }


  
</style>

<script>
 $(document).ready(function() {
  // Hide all icon_items after the 14th one (index starts from 0)
  $('.image_text_6 .icon_item').slice(13).hide();

  // On click of .more_btn, show the hidden items and add a class
  $('.image_text_6 .more_btn').on('click', function() {
    $('.image_text_6 .icon_item').show(); // Show all items
    $(this).addClass('clicked').hide(); // Add class and hide "More" button
  });
});


 

  $(document).ready(function () {
    var mySwiper = new Swiper('.image_text_6 .timmer_swiper', {
      loop: true,
      slidesPerView: 1,
      spaceBetween: 0,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      effect: 'slide' ,
      pagination: {
      el: '.image_text_6 .swiper-pagination',
      clickable: true,
    }
    });
  });



</script>


  

{% schema %}
{
  "name": "Image Text 6",
  "settings": [
    {
      "type": "color",
      "id": "bg_color",
      "default": "transparent",
      "label": "Backgroud Color"
    },
       {
      "type": "image_picker",
      "id": "bg_image",
      "label": "Background Image"
    },
    

      {
          "type": "richtext",
          "id": "heading",
          "label": "Heading"
        },
      {
          "type": "text",
          "id": "more_btn",
          "label": "More Button"
        },

      
  ],

    "blocks": [
    {
      "type": "block",
      "name": "Block",
      "settings": [

        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
                {
          "type": "image_picker",
          "id": "icon",
          "label": "Icon"
        }
  
    
    
      
      ]
    }
  ],
  
  "presets": [
    {
      "name": "Image Text 6",
      "blocks": []
    }
  ]
}
{% endschema %}

