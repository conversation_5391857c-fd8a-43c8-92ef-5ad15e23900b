{% comment %} Please do not edit this file. This file will be updated frequently so any manual changes will be discarded {% endcomment %}
<article class='jdgm-widget jdgm-all-reviews-widget'>
  <style class='jdgm-temp-hiding-style'>.jdgm-all-reviews-widget{ display: none }</style>
  {{ shop.metafields.judgeme.all_reviews_header }}
  <div class='jdgm-all-reviews__body'>
    {% assign prefix = 'all_reviews_' %}
    {% for count in (0..65535) %}
      {% assign metafield_key = prefix | append: count %}
      {% assign current_metafield = shop.metafields.judgeme[metafield_key] %}
      {% unless current_metafield %} {% break %} {% endunless %}
      {{ current_metafield }}
    {% endfor %}
  </div>
</article>
