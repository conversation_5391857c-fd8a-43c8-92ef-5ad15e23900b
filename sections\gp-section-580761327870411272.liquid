
    
    <style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-580761327870411272.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-580761327870411272.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-580761327870411272.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-580761327870411272.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-580761327870411272.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-580761327870411272.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-580761327870411272.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-580761327870411272.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-580761327870411272.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-580761327870411272.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-580761327870411272.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-580761327870411272.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-580761327870411272.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-580761327870411272.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-580761327870411272.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-580761327870411272.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-580761327870411272.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-580761327870411272.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-580761327870411272.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-580761327870411272.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-580761327870411272.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-580761327870411272.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-580761327870411272.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-580761327870411272.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-580761327870411272.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-580761327870411272 .gp-relative{position:relative}.gps-580761327870411272 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-580761327870411272 .gp-mb-0{margin-bottom:0}.gps-580761327870411272 .gp-flex{display:flex}.gps-580761327870411272 .gp-grid{display:grid}.gps-580761327870411272 .\!gp-hidden{display:none!important}.gps-580761327870411272 .gp-hidden{display:none}.gps-580761327870411272 .gp-max-w-full{max-width:100%}.gps-580761327870411272 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-580761327870411272 .gp-flex-col{flex-direction:column}.gps-580761327870411272 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-580761327870411272 .gp-duration-200{transition-duration:.2s}.gps-580761327870411272 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-580761327870411272 .tablet\:\!gp-hidden{display:none!important}.gps-580761327870411272 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-580761327870411272 .mobile\:\!gp-hidden{display:none!important}.gps-580761327870411272 .mobile\:gp-hidden{display:none}}.gps-580761327870411272 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}</style>
    
    
    

    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}

    
        <section
          class="gp-mx-auto gp-max-w-full [&_*]:gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
          style="--w:calc(var(--g-ct-w, 1200px) + 2 * var(--g-ct-p, 15px));--w-tablet:calc(var(--g-ct-w, 100%) + 2 * var(--g-ct-p, 15px));--w-mobile:calc(var(--g-ct-w, 100%) + 2 * var(--g-ct-p, 15px))"
        >
          
    <gp-row gp-data='{"background":{"desktop":{"type":"color","color":"transparent","image":{"src":"","width":0,"height":0},"size":"cover","position":{"x":50,"y":50},"repeat":"no-repeat","attachment":"scroll"}},"uid":"i2l51b0113cca"}' data-id="i2l51b0113cca" id="i2l51b0113cca" data-same-height-subgrid-container class="i2l51b0113cca gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--pt:auto;--pb:auto;--d:grid;--d-tablet:grid;--d-mobile:grid;--b:solid;--bc:#121212;--bw:0px;--radius:var(--g-radius-none);--shadow:none;--op:100%;--cg:32px;--gtc:minmax(0, 12fr);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start;--d-mobile:none"
      class="i2l43706d8d30 gp-relative gp-flex gp-flex-col"
    >
      
    </div>

      
    </gp-row>
  
        </section>
      
  
    
    
{% schema %}
  {
    
    "name": "Section 13",
    "tag": "section",
    "class": "gps-580761327870411272 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=578718746651132539&sectionId=580761327870411272)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
  