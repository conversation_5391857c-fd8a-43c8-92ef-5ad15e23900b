<div class="page-width page-content">

  {% if settings.show_breadcrumbs %}
    <nav class="breadcrumb{% if settings.type_headers_align_text %} text-center{% endif %}" role="navigation" aria-label="breadcrumbs">
      <a href="{{ routes.account_url }}">{{ 'customer.account.title' | t }}</a>
      <span class="divider" aria-hidden="true">/</span>
      {{ 'customer.orders.title' | t }}
    </nav>
  {% endif %}

  <header class="section-header">
    <h1 class="section-header__title">{{ 'customer.order.title' | t: name: order.name }}</h1>
  </header>

  <div class="grid">

    <div class="grid__item medium-up--two-thirds">
      <p>
        {%- assign order_date = order.created_at | time_tag: format: "date_at_time" -%}
        {{ 'customer.order.date_html' | t: date: order_date }}
      </p>

      {% if order.cancelled %}
        <div class="errors">
          {%- assign cancelled_at = order.cancelled_at | time_tag: format: "date_at_time" -%}
          <p class="h5">{{ 'customer.order.cancelled_html' | t: date: cancelled_at }}</p>
          <p>{{ 'customer.order.cancelled_reason' | t: reason: order.cancel_reason }}</p>
        </div>
      {% endif %}

      <table class="table--responsive table--small-text">
        <thead>
          <tr>
            <th>{{ 'customer.order.product' | t }}</th>
            <th>{{ 'customer.order.sku' | t }}</th>
            <th>{{ 'customer.order.price' | t }}</th>
            <th class="text-right">{{ 'customer.order.quantity' | t }}</th>
            <th class="text-right table--small-hide">{{ 'customer.order.total' | t }}</th>
          </tr>
        </thead>
        <tbody>
          {% for line_item in order.line_items %}
          <tr id="{{ line_item.key }}" class="table__section">
            <td data-label="{{ 'customer.order.product' | t }}">
              {{ line_item.title | link_to: line_item.product.url }}
              {%- assign property_size = line_item.properties | size -%}
              {% unless line_item.selling_plan_allocation == nil and property_size == 0 %}
                <p>
                  {% unless line_item.selling_plan_allocation == nil %}
                    {{ line_item.selling_plan_allocation.selling_plan.name }}
                  {% endunless %}
                  {% if property_size != 0 %}
                    {% for property in line_item.properties %}
                      {% assign property_first_char = property.first | slice: 0 %}
                      {% if property.last != blank and property_first_char != '_' %}
                        {{ property.first }}:
                        {% if property.last contains '/uploads/' %}
                          <a href="{{ property.last }}">{{ property.last | split: '/' | last }}</a>
                        {% else %}
                          {{ property.last }}
                        {% endif %}
                      {% endif %}
                    {% endfor %}
                  {% endif %}
                </p>
              {% endunless %}
              {%- if line_item.line_level_discount_allocations != blank -%}
                {%- for discount_allocation in line_item.line_level_discount_allocations -%}
                  <p class="cart__discount">
                    {{ discount_allocation.discount_application.title }} (-{{ discount_allocation.amount | money }})
                  </p>
                {%- endfor -%}
              {%- endif -%}
              {% if line_item.fulfillment %}
                <div class="note">
                  {%- assign created_at = line_item.fulfillment.created_at | time_tag: format: 'date' -%}
                  {{ 'customer.order.fulfilled_at_html' | t: date: created_at }}
                  {% if line_item.fulfillment.tracking_number %}
                    <a href="{{ line_item.fulfillment.tracking_url }}">{{ line_item.fulfillment.tracking_company }} #{{ line_item.fulfillment.tracking_number}}</a>
                  {% endif %}
                </div>
              {% endif %}
            </td>
            <td data-label="{{ 'customer.order.sku' | t }}">{{ line_item.sku }}</td>
            <td data-label="{{ 'customer.order.price' | t }}">
              {%- if line_item.original_price != line_item.final_price -%}
                <span class="visually-hidden">{{ 'products.general.regular_price' | t }}</span>
                <span class="cart__price cart__price--strikethrough">{{ line_item.original_price | money }}</span>
                <span class="visually-hidden">{{ 'products.general.sale_price' | t }}</span>
                <span class="cart__discount">{{ line_item.final_price | money }}</span>
              {%- else -%}
                {{ line_item.original_price | money }}
              {%- endif -%}

              {%- if line_item.unit_price_measurement -%}
                {%- capture unit_price_base_unit -%}
                  <span class="unit-price-base">
                    {%- if line_item.unit_price_measurement -%}
                      {%- if line_item.unit_price_measurement.reference_value != 1 -%}
                        {{ line_item.unit_price_measurement.reference_value }}
                      {%- endif -%}
                      {{ line_item.unit_price_measurement.reference_unit }}
                    {%- endif -%}
                  </span>
                {%- endcapture -%}

                <div class="product__unit-price">{{ line_item.unit_price | money }}/{{ unit_price_base_unit }}</div>
              {%- endif -%}
            </td>
            <td data-label="{{ 'customer.order.quantity' | t }}" class="text-right">{{ line_item.quantity }}</td>
            <td class="table--small-hide text-right">
              {%- if line_item.original_line_price != line_item.final_line_price -%}
                <span class="visually-hidden">{{ 'products.general.regular_price' | t }}</span>
                <span class="cart__price cart__price--strikethrough">{{ line_item.original_line_price | money }}</span>
                <span class="visually-hidden">{{ 'products.general.sale_price' | t }}</span>
                <span class="cart__discount">{{ line_item.final_line_price | money }}</span>
              {%- else -%}
                {{ line_item.original_line_price | money }}
              {%- endif -%}
            </td>
          </tr>
          {% endfor %}
        </tbody>
        <tfoot>
          <tr>
            <td colspan="4" class="table--small-hide">{{ 'customer.order.subtotal' | t }}</td>
            <td data-label="{{ 'customer.order.subtotal' | t }}" class="text-right">{{ order.line_items_subtotal_price | money }}</td>
          </tr>

          {%- if order.cart_level_discount_applications != blank -%}
            <tr>
              {%- for discount_application in order.cart_level_discount_applications -%}
                <th scope="row" colspan="4" class="small--hide">
                  {{ 'customer.order.discount' | t }} | {{ discount_application.title }}
                </th>
                <td class="text-right" data-label="{{ 'customer.order.discount' | t }}">
                  <div class="cart__discount">
                    <span class="medium-up--hide">
                      {{ discount_application.title }}
                    </span>
                    <span class="cart__discount">-{{ discount_application.total_allocated_amount | money }}</span>
                  </div>
                </td>
              {%- endfor -%}
            </tr>
          {%- endif -%}

          {% for shipping_method in order.shipping_methods %}
          <tr>
            <td colspan="4" class="table--small-hide">{{ 'customer.order.shipping' | t }} ({{ shipping_method.title }})</td>
            <td data-label="{{ 'customer.order.shipping' | t }}" class="text-right">{{ shipping_method.price | money }}</td>
          </tr>
          {% endfor %}

          {% for tax_line in order.tax_lines %}
            <tr>
              <td colspan="4" class="table--small-hide">{{ 'customer.order.tax' | t }} ({{ tax_line.title }} {{ tax_line.rate | times: 100 }}%)</td>
              <td data-label="{{ 'customer.order.tax' | t }}" class="text-right">{{ tax_line.price | money }}</td>
            </tr>
          {% endfor %}

          <tr>
            <td colspan="4" class="table__title table--small-hide">{{ 'customer.order.total' | t }}</td>
            <td data-label="{{ 'customer.order.total' | t }}" class="table__title text-right">{{ order.total_price | money }} {{ order.currency }}</td>
          </tr>
        </tfoot>
      </table>

      <hr class="hr--small hr--clear">

    </div>

    <div class="grid__item medium-up--one-third">

      <h3>{{ 'customer.order.billing_address' | t }}</h3>

      <p><strong>{{ 'customer.order.payment_status' | t }}:</strong> {{ order.financial_status_label }}</p>

      <p class="h5">{{ order.billing_address.name }}</p>
      <p>
        {% if order.billing_address.company != blank %}
          {{ order.billing_address.company }}<br>
        {% endif %}
        {{ order.billing_address.street }}<br>
        {{ order.billing_address.city }}<br>
        {% if order.billing_address.province != blank %}
          {{ order.billing_address.province }}<br>
        {% endif %}
        {{ order.billing_address.zip | upcase }}<br>
        {{ order.billing_address.country }}<br>
        {{ order.billing_address.phone }}
      </p>

      <hr class="hr--medium">

      {%- if order.shipping_address != blank -%}
        <h3>{{ 'customer.order.shipping_address' | t }}</h3>
      {%- endif -%}

      <p><strong>{{ 'customer.order.fulfillment_status' | t }}:</strong> {{ order.fulfillment_status_label }}</p>

      {%- if order.shipping_address != blank -%}
        <p class="h5">{{ order.shipping_address.name }}</p>
        <p>
          {% if order.shipping_address.company != blank %}
            {{ order.shipping_address.company }}<br>
          {% endif %}
          {{ order.shipping_address.street }}<br>
          {{ order.shipping_address.city }}<br>
          {% if order.shipping_address.province != blank %}
            {{ order.shipping_address.province }}<br>
          {% endif %}
          {{ order.shipping_address.zip | upcase }}<br>
          {{ order.shipping_address.country }}<br>
          {{ order.shipping_address.phone }}
        </p>
      {%- endif -%}

    </div>

  </div>
</div>
