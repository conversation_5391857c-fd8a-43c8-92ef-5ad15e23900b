{% comment %}
  Arguments
  - product: product object
  - [per_row]: number of items per row
  - [quick_shop_enable]: boolean
  - [collection]: collection object
{% endcomment %}

{%- liquid
  unless per_row
    assign per_row = 4
  endunless

  case per_row
    when 1
      assign grid_item_width = ''
    when 2
      assign grid_item_width = 'medium-up--one-half'
    when 3
      assign grid_item_width = 'small--one-half medium-up--one-third'
    when 4
      assign grid_item_width = 'small--one-half medium-up--one-quarter'
    when 5
      assign grid_item_width = 'small--one-half medium-up--one-fifth'
    when 6
      assign grid_item_width = 'small--one-half medium-up--one-sixth'
  endcase

  assign on_sale = false
  if product.compare_at_price > product.price
    assign on_sale = true
  endif

  assign product_tags = product.tags | join: ','
  assign has_custom_label = false
  if product.metafields.theme.label and product.metafields.theme.label != blank
    assign has_custom_label = true
    assign custom_label = product.metafields.theme.label.value
  elsif product_tags contains '_label_'
    for tag in product.tags
      if tag contains '_label_'
        assign tag_starts_with = tag | slice: 0
        if tag_starts_with == '_'
          assign has_custom_label = true
          assign custom_label = tag | replace: '_label_', ''
        endif
      endif
    endfor
  endif
-%}

<div
  class="grid__item grid-product {{ grid_item_width }}{% if quick_shop_enable %} grid-product__has-quick-shop{% endif %} !tw-pl-[30px] max-md:!tw-pl-[15px]"
  data-aos="row-of-{{ per_row }}"
  data-product-handle="{{ product.handle }}"
  data-product-id="{{ product.id }}"
>
  <div class="grid-product__content">
    {%- if has_custom_label -%}
      {% if custom_label contains 'Black Friday'
        or custom_label contains 'BLACK FRIDAY'
        or custom_label contains 'NEW YEAR SALE'
        or custom_label contains "SUMMER SALE"
      %}
        <div class="grid-product__tag grid-product__tag--custom black_friday_label">
          {{ custom_label }}
        </div>
      {% else %}
        <div class="grid-product__tag grid-product__tag--custom">
          {{ custom_label }}
        </div>
      {% endif %}
    {%- else -%}
      {%- unless product.available -%}
        <div class="grid-product__tag grid-product__tag--sold-out">
          {{ 'products.product.sold_out' | t }}
        </div>
      {%- endunless -%}
      {%- if on_sale and product.available -%}
        <div class="grid-product__tag grid-product__tag--sale">
          {{ 'products.general.sale' | t }}
        </div>
      {%- endif -%}
    {%- endif -%}

    {%- liquid
      assign fixed_aspect_ratio = false
      unless settings.product_grid_image_size == 'natural'
        assign fixed_aspect_ratio = true
      endunless

      assign preview_image = product.featured_media.preview_image
      assign img_url = preview_image | img_url: '1x1' | replace: '_1x1.', '_{width}x.'
    -%}

    <a href="{{ product.url | within: collection }}" class="grid-product__link">
      <div class="grid-product__image-mask">
        {%- if quick_shop_enable -%}
          <div class="quick-product__btn quick-product__btn--not-ready js-modal-open-quick-modal-{{ product.id }} small--hide">
            <span class="quick-product__label">{{ settings.quick_shop_text }}</span>
          </div>
        {%- endif -%}
        {%- if fixed_aspect_ratio -%}
          <div
            class="grid__image-ratio grid__image-ratio--{{ settings.product_grid_image_size }}"
          >
            <img
              class="lazyload{% unless settings.product_grid_image_fill %} grid__image-contain{% endunless %}"
              data-src="{{ img_url }}"
              data-widths="[360, 540, 720, 900, 1080]"
              data-aspectratio="{{ preview_image.aspect_ratio }}"
              data-sizes="auto"
              alt="{{ preview_image.alt | escape }}"
            >
          </div>
        {%- else -%}
          <div
            class="image-wrap tw-rounded-[26px] !tw-bg-[#FFFDFB]"
            style="height: 0; padding-bottom: {{ 100 | divided_by: preview_image.aspect_ratio }}%;"
          >
            <img
              class="grid-product__image lazyload"
              data-src="{{ img_url }}"
              data-widths="[360, 540, 720, 900, 1080]"
              data-aspectratio="{{ preview_image.aspect_ratio }}"
              data-sizes="auto"
              alt="{{ preview_image.alt | escape }}"
            >
            <noscript>
              <img
                class="grid-product__image lazyloaded"
                src="{{ preview_image | img_url: '400x' }}"
                alt="{{ preview_image.alt | escape }}"
              >
            </noscript>
          </div>
        {%- endif -%}

        {%- if settings.product_hover_image and product.media.size > 1 -%}
          {%- for media in product.media offset: 1 limit: 1 -%}
            {%- assign second_image = media.preview_image -%}
          {%- endfor -%}
          <div class="grid-product__secondary-image tw-rounded-[26px] !tw-bg-[#FFFDFB] tw-overflow-hidden small--hide">
            {%- assign img_url = second_image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}
            <img
              class="lazyload"
              data-src="{{ img_url }}"
              data-widths="[360, 540, 720, 1000]"
              data-aspectratio="{{ second_image.aspect_ratio }}"
              data-sizes="auto"
              alt="{{ second_image.alt }}"
            >
          </div>
        {%- endif -%}

        {%- if settings.enable_swatches -%}
          {%- assign swatch_trigger = 'products.general.color_swatch_trigger' | t | downcase -%}
          {%- for option in product.options_with_values -%}
            {%- liquid
              assign option_name = option.name | downcase
              assign is_color = false
              if option_name contains swatch_trigger
                assign is_color = true
              elsif swatch_trigger == 'color' and option_name contains 'colour' or option_name contains 'colore'
                assign is_color = true
              endif
            -%}
            {%- if is_color -%}
              {%- assign option_index = forloop.index0 -%}
              {%- assign values = '' -%}
              {%- for variant in product.variants -%}
                {%- assign value = variant.options[option_index] %}
                {%- unless values contains value -%}
                  {%- liquid
                    assign values = values | join: ',' | append: ',' | append: value | split: ','
                  -%}

                  {%- if variant.image -%}
                    <div
                      class="grid-product__color-image grid-product__color-image--{{ variant.id }} small--hide"
                    ></div>
                  {%- endif -%}
                {%- endunless -%}
              {%- endfor -%}
            {%- endif -%}
          {%- endfor -%}
        {%- endif -%}
      </div>

      <div class="grid-product__meta !tw-pt-[23px] max-md:!tw-pt-[15px] !tw-pb-[16px] !tw-text-left">
        <div
          class="loox-rating oddit-pdp hide tw-mb-[15px] max-md:tw-mb-[12px]"
          data-id="{{ product.id }}"
          data-rating="{{ product.metafields.loox.avg_rating }}"
          data-raters="{{ product.metafields.loox.num_reviews }}"
        ></div>
        <div class="grid-product__title grid-product__title--{{ settings.type_product_style }} !tw-text-[24px] max-md:!tw-text-[16px] !tw-font-bold !tw-font-dm-sans !tw-text-darkblack tw-tracking-normal !tw-capitalize !tw-mb-[16px] max-md:!tw-mb-[12px]">
          {{ product.title }}
        </div>
        {% if product.metafields.custom.product_subtext != blank %}
          <p class="oddit-pdp hide max-md:tw-mb-[12px] tw-text-[15px] max-md:tw-text-[13px] tw-font-normal tw-text-darkblack tw-font-dm-sans tw-leading-[1] tw-tracking-normal">
            {{ product.metafields.custom.product_subtext }}
          </p>
        {% endif %}
        {%- if settings.vendor_enable -%}
          <div class="grid-product__vendor">{{ product.vendor }}</div>
        {%- endif -%}
        <div class="grid-product__price">
          <div class="hide oddit-pdp tw-flex tw-items-center tw-gap-x-[10px] max-md:tw-gap-x-[8px]">
            <div class="tw-text-[24px] max-md:tw-text-[16px] tw-font-semibold tw-font-dm-sans tw-text-[#008001] tw-leading-[1] tw-tracking-normal">
              {%- if product.price_varies -%}
                {%- assign price = product.price_min | money -%}
                {{ 'products.general.from_text_html' | t: price: price }}
              {%- else -%}
                {{ product.price | money }}
              {%- endif -%}
            </div>
            {%- if on_sale -%}
              <span class="visually-hidden">{{ 'products.general.regular_price' | t }}</span>
              <span class="grid-product__price--original tw-text-[20px] max-md:tw-text-[16px] tw-font-normal tw-font-dm-sans tw-text-[#80868B] tw-leading-[1] tw-tracking-normal">
                {% if template.name == 'product' and template.suffix contains 'oddit' %}
                  {{- product.compare_at_price | money_without_trailing_zeros -}}
                {% else %}
                  {{- product.compare_at_price | money -}}
                {% endif %}
              </span>
              <span class="visually-hidden">{{ 'products.general.sale_price' | t }}</span>
            {%- endif -%}
            {%- liquid
              assign compare_at_price = product.compare_at_price
              assign price = product.price | default: 1999

              if compare_at_price > price
                assign sale_percentage = compare_at_price | minus: price | times: 100.0 | divided_by: compare_at_price | money_without_currency | times: 100 | remove: '.0'
              endif
            -%}
            {%- if sale_percentage -%}
              <span class="tw-text-[15px] max-md:tw-text-[12px] tw-text-white tw-font-bold tw-font-dm-sans tw-uppercase tw-leading-[normal] tw-bg-[#008001] tw-rounded-[20px] tw-px-[7px] tw-py-[2px] tw-min-w-max"
                >SAVE {{ sale_percentage }}%</span
              >
            {%- endif -%}
          </div>

          <div class="oddit-pdp-hide">
            {%- if on_sale -%}
              <span class="visually-hidden">{{ 'products.general.regular_price' | t }}</span>
              <span class="grid-product__price--original">
                {% if template.name == 'product' and template.suffix contains 'oddit' %}
                  {{- product.compare_at_price | money_without_trailing_zeros -}}
                {% else %}
                  {{- product.compare_at_price | money -}}
                {% endif %}
              </span>
              <span class="visually-hidden">{{ 'products.general.sale_price' | t }}</span>
            {%- endif -%}
            {%- if product.price_varies -%}
              {% if template.name == 'product' and template.suffix contains 'oddit' %}
                {%- assign price = product.price_min | money_without_trailing_zeros -%}
              {% else %}
                {%- assign price = product.price_min | money -%}
              {% endif %}
              {{ 'products.general.from_text_html' | t: price: price }}
            {%- else -%}
              {% if template.name == 'product' and template.suffix contains 'oddit' %}
                {{ product.price | money_without_trailing_zeros }}
              {% else %}
                {{ product.price | money }}
              {% endif %}
            {%- endif -%}
          </div>
          {%- if on_sale -%}
            {%- if settings.product_save_amount -%}
              {%- if settings.product_save_type == 'dollar' -%}
                {%- capture saved_amount -%}{{ product.compare_at_price | minus: product.price | money }}{%- endcapture -%}
              {%- else -%}
                {%- capture saved_amount -%}{{ product.compare_at_price | minus: product.price | times: 100.0 | divided_by: product.compare_at_price | round }}%{%- endcapture -%}
              {%- endif -%}
              <span class="grid-product__price--savings">
                {{ 'products.general.save_html' | t: saved_amount: saved_amount }}
              </span>
            {%- endif -%}
          {%- endif -%}

          {%- assign product_variant = product.selected_or_first_available_variant -%}
          {%- if product_variant.unit_price_measurement -%}
            <div class="product__unit-price">
              {%- capture unit_price_base_unit -%}
                {%- if product_variant.unit_price_measurement -%}
                  {%- if product_variant.unit_price_measurement.reference_value != 1 -%}
                    {{ product_variant.unit_price_measurement.reference_value }}
                  {%- endif -%}
                  {{ product_variant.unit_price_measurement.reference_unit }}
                {%- endif -%}
              {%- endcapture -%}

              {{ product_variant.unit_price | money }}/{{ unit_price_base_unit }}
            </div>
          {%- endif -%}
        </div>
      </div>
    </a>
  </div>

  {%- if product.variants.size > 0 -%}
    <div class="oddit-pdp hide variant-swatch-main tw-flex tw-flex-wrap tw-gap-[8px] tw-items-center tw-mb-2">
      {% assign variant_show_limit = 6 %}
      {%- for variant in product.variants -%}
        {%- if variant.image != blank -%}
          <a
            href="{{ variant.url }}"
            class="color-swatch color-swatch--small color-swatch--with-image{% if forloop.index > variant_show_limit %} hide{% endif %} !tw-min-w-[50px] !tw-h-[50px] max-md:!tw-min-w-[34px] max-md:!tw-h-[34px] !tw-m-0 before:!tw-hidden !tw-rounded-[11px]"
            style="background-image: url({{ variant.image | img_url: '112x112' }});"
          >
          </a>
          {%- assign forloopLen = forloop.length -%}
        {%- endif -%}
      {%- endfor -%}
      {%- if forloopLen > variant_show_limit -%}
        <div class="more-variant tw-text-[14px] tw-text-black tw-font-normal tw-font-dm-sans tw-leading-[1]">
          + {{ forloopLen | minus: variant_show_limit }}
        </div>
      {%- endif -%}
    </div>
  {%- endif -%}

  {%- if settings.enable_swatches -%}
    {%- liquid
      assign swatch_trigger = 'products.general.color_swatch_trigger' | t | downcase
      assign swatch_file_extension = 'png'
      assign color_count = 0
    -%}

    {%- for option in product.options_with_values -%}
      {%- liquid
        assign option_name = option.name | downcase
        assign is_color = false
        if option_name contains swatch_trigger
          assign is_color = true
        elsif swatch_trigger == 'color' and option_name contains 'colour' or option_name contains 'colore'
          assign is_color = true
        endif
      -%}
      {%- if is_color -%}
        {%- assign option_index = forloop.index0 -%}
        {%- assign values = '' -%}
        <div class="oddit-pdp-hide grid-product__colors grid-product__colors--{{ product.id }}">
          {%- for variant in product.variants -%}
            {%- assign value = variant.options[option_index] %}
            {%- unless values contains value -%}
              {%- liquid
                assign values = values | join: ',' | append: ',' | append: value | split: ','

                assign color_file_name = value | handle | append: '.' | append: swatch_file_extension
                assign color_image = color_file_name | file_img_url: '50x50' | prepend: 'https:' | split: '?' | first
                assign color_swatch_fallback = value | split: ' ' | last | handle
                assign color_count = color_count | plus: 1
              -%}

              <a
                href="{{ variant.url | within: collection }}"
                class="color-swatch color-swatch--small color-swatch--{{ value | handle }}{% if variant.image %} color-swatch--with-image{% endif %}"
                {% if variant.image %}
                  data-variant-id="{{ variant.id }}"
                  data-variant-image="{{ variant.image | img_url: '400x' }}"
                {% endif %}
                aria-label="{{ product.title }} - {{ value }}"
                style="background-color: {{ color_swatch_fallback }};{% if images[color_file_name] != blank %}  background-image: url({{ color_image }});{% endif %}"
              >
                <span class="visually-hidden">{{ value }}</span>
              </a>
            {%- endunless -%}
          {%- endfor -%}
        </div>
        {%- if color_count < 2 -%}
          {%- style -%}
            .grid-product__colors--{{ product.id }} {
              display: none;
            }
          {%- endstyle -%}
        {%- endif -%}
      {%- endif -%}
    {%- endfor -%}
  {%- endif -%}

  {%- if settings.enable_product_reviews -%}
    <span class="shopify-product-reviews-badge" data-id="{{ product.id }}"></span>
  {%- endif -%}

  {%- if quick_shop_enable -%}
    {%- render 'quick-shop-modal', product: product -%}
  {%- endif -%}
</div>
