

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-555482666422567746.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-555482666422567746.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-555482666422567746.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-555482666422567746.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-555482666422567746.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-555482666422567746.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-555482666422567746.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-555482666422567746.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-555482666422567746.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-555482666422567746.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-555482666422567746.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-555482666422567746.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-555482666422567746.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-555482666422567746.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-555482666422567746.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-555482666422567746.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-555482666422567746.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-555482666422567746.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-555482666422567746.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-555482666422567746.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-555482666422567746.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-555482666422567746.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-555482666422567746.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-555482666422567746.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-555482666422567746.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-555482666422567746.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-555482666422567746.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-555482666422567746.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-555482666422567746.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-555482666422567746.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-555482666422567746.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-555482666422567746.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-555482666422567746.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-555482666422567746.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-555482666422567746.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-555482666422567746.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-555482666422567746.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-555482666422567746.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-555482666422567746.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-555482666422567746.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-555482666422567746.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-555482666422567746.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-555482666422567746.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-555482666422567746.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-555482666422567746.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-555482666422567746.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-555482666422567746.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-555482666422567746.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-555482666422567746.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-555482666422567746.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-555482666422567746.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-555482666422567746.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-555482666422567746.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-555482666422567746.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-555482666422567746.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-555482666422567746.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-555482666422567746.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-555482666422567746.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-555482666422567746.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-555482666422567746.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-555482666422567746.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-555482666422567746.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-555482666422567746.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-555482666422567746.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-555482666422567746.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-555482666422567746.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-555482666422567746.gps.gpsil [style*="--o-mobile:"]{order:var(--o-mobile)}.gps-555482666422567746.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-555482666422567746.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-555482666422567746.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-555482666422567746.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-555482666422567746.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-555482666422567746.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-555482666422567746 .gp-relative{position:relative}.gps-555482666422567746 .gp-z-1{z-index:1}.gps-555482666422567746 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-555482666422567746 .gp-mb-0{margin-bottom:0}.gps-555482666422567746 .gp-flex{display:flex}.gps-555482666422567746 .gp-inline-flex{display:inline-flex}.gps-555482666422567746 .gp-grid{display:grid}.gps-555482666422567746 .gp-contents{display:contents}.gps-555482666422567746 .\!gp-hidden{display:none!important}.gps-555482666422567746 .gp-hidden{display:none}.gps-555482666422567746 .gp-h-full{height:100%}.gps-555482666422567746 .gp-w-full{width:100%}.gps-555482666422567746 .gp-max-w-full{max-width:100%}.gps-555482666422567746 .gp-flex-col{flex-direction:column}.gps-555482666422567746 .gp-items-center{align-items:center}.gps-555482666422567746 .gp-justify-center{justify-content:center}.gps-555482666422567746 .gp-gap-y-0{row-gap:0}.gps-555482666422567746 .gp-overflow-hidden{overflow:hidden}.gps-555482666422567746 .gp-break-words{overflow-wrap:break-word}.gps-555482666422567746 .gp-rounded-none{border-radius:0}.gps-555482666422567746 .gp-text-center{text-align:center}.gps-555482666422567746 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-555482666422567746 .gp-no-underline{text-decoration-line:none}.gps-555482666422567746 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555482666422567746 .gp-duration-200{transition-duration:.2s}.gps-555482666422567746 .gp-duration-300{transition-duration:.3s}.gps-555482666422567746 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555482666422567746 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-555482666422567746 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-555482666422567746 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-555482666422567746 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-555482666422567746 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-555482666422567746 .tablet\:\!gp-hidden{display:none!important}.gps-555482666422567746 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-555482666422567746 .mobile\:\!gp-hidden{display:none!important}.gps-555482666422567746 .mobile\:gp-hidden{display:none}}.gps-555482666422567746 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-555482666422567746 .\[\&_p\]\:gp-inline p{display:inline}.gps-555482666422567746 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-555482666422567746 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-555482666422567746 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      enableLazyloadImage="true" id="g3c0mqt8KT" data-id="g3c0mqt8KT"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:0px;--cg-tablet:0px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g3c0mqt8KT gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:center;--o-mobile:1"
      class="gL1qnohml3 gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="g33_rquOFd" data-id="g33_rquOFd"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--pl:15px;--pr:15px;--pt-mobile:var(--g-s-3xl);--pl-mobile:15px;--pb-mobile:var(--g-s-3xl);--pr-mobile:15px;--pt-tablet:var(--g-s-3xl);--pl-tablet:15px;--pb-tablet:var(--g-s-3xl);--pr-tablet:15px;--cg:30px;--pc:start;--gtc:minmax(0, 12fr);--w:500px;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g33_rquOFd gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="grXLdvOcdt gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gyAxVweGV0">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gyAxVweGV0 "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-l);--pl-mobile:auto;--pr-mobile:auto"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:600;--ls:normal;--size:37px;--size-tablet:40px;--size-mobile:35px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggyAxVweGV0_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gPoDRKi8ng">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gPoDRKi8ng "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-3xl);--mt-mobile:var(--g-s-l);--mb-mobile:42px"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:18px;--size-tablet:23px;--size-mobile:20px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggPoDRKi8ng_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    
  >
    <style>
    .gtBeWNd9Vu.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      
    }

    .gtBeWNd9Vu:hover::before {
      
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .gtBeWNd9Vu:hover .gp-button-icon {
      color: undefined;
    }

     .gtBeWNd9Vu .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gtBeWNd9Vu:hover .gp-button-price {
      color: undefined;
    }

    .gtBeWNd9Vu .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gtBeWNd9Vu .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gtBeWNd9Vu:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#" target="_self" data-id="gtBeWNd9Vu" aria-label="EXPLORE NOW"
      
      data-state="idle"
      class="gtBeWNd9Vu gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center"
      style="--hvr-bg:rgba(36, 36, 36, 0.8);--bg:#096de3;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:32px;--pr:32px;--pt:12px;--pb:12px;--pl-tablet:32px;--pr-tablet:32px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:32px;--pr-mobile:32px;--pt-mobile:12px;--pb-mobile:12px;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--fs:normal;--weight:600;--ls:1px;--lh-tablet:150%"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="group-hover/button:!gp-text-inherit gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggtBeWNd9Vu_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
    </div>
   
    
    </div><div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:center;--o-mobile:0"
      class="gC9AolBsBj gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="g9j9W4bJAx"
    role="presentation"
    class="gp-group/image g9j9W4bJAx"
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    >
      <div
        class="pointer-events-auto h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/85_33eb3610-9b17-4896-b2d2-37ad8847266d.png?v=1732745331" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxMDgwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xMDgwLTEwODAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjEwODAiIGhlaWdodD0iMTA4MCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxMDgwIiBmaWxsPSJ1cmwoI2ctMTA4MC0xMDgwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTA4MCIgdG89IjEwODAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/85_33eb3610-9b17-4896-b2d2-37ad8847266d.png?v=1732745331" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxMDgwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xMDgwLTEwODAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjEwODAiIGhlaWdodD0iMTA4MCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxMDgwIiBmaWxsPSJ1cmwoI2ctMTA4MC0xMDgwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTA4MCIgdG89IjEwODAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxMDgwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xMDgwLTEwODAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjEwODAiIGhlaWdodD0iMTA4MCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxMDgwIiBmaWxsPSJ1cmwoI2ctMTA4MC0xMDgwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTA4MCIgdG89IjEwODAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/85_33eb3610-9b17-4896-b2d2-37ad8847266d.png?v=1732745331"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 1",
    "tag": "section",
    "class": "gps-555482666422567746 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555482666321838914&sectionId=555482666422567746)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggyAxVweGV0_text","label":"ggyAxVweGV0_text","default":"FEEL YOUR BEST, EVERY SINGLE DAY WITH A GARDPRO SMARTWATCH"},{"type":"html","id":"ggPoDRKi8ng_text","label":"ggPoDRKi8ng_text","default":"<p>Discover our range of smartwatches for women—where style meets advanced health tracking. From fitness tracker watches for women to ladies' sports watches, stay on top of your wellness journey with heart rate, sleep, and activity monitoring designed to keep you moving.</p>"},{"type":"html","id":"ggtBeWNd9Vu_label","label":"ggtBeWNd9Vu_label","default":"EXPLORE NOW"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
