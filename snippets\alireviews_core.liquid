{% assign template_support = "product.brand-story,product.gift-card,product,product.modal,product.preorder,product.product-landing" %}
{% if template_support contains request.page_type %}
 
 
        <script>
            var alireviews_page_type = "{{ template }}";
            var alireviews_collection_id = "{{ collection.id }}";
            var alireviews_theme_id = "{{ theme.id }}";
            {% assign alireviews_avg_order_value = 0 %}
            {% if customer.orders_count != 0 %}
                {% assign alireviews_avg_order_value = customer.total_spent  | divided_by: customer.orders_count | divided_by:100 %}
            {% endif %}
            var alireviews_avg_order_value = "{{ alireviews_avg_order_value }}";
            var alireviews_customer_id = "{{ customer.id }}";
            {% assign  alireviews_product_in_cart = "" | split: "" %}
            {% for item in cart.items %}
                {% assign alireviews_tmp_product_id = item.product_id | split: "_" %}
                {% assign alireviews_product_in_cart = alireviews_product_in_cart | concat: alireviews_tmp_product_id %}
            {% endfor %}
            {% assign alireviews_product_in_cart = alireviews_product_in_cart | join: "," %}
            var alireviews_product_in_cart = "{{ alireviews_product_in_cart }}";
            var alireviews_product_id = "{{ product.id }}";
            var alireviews_total_order_values = "{{customer.total_spent | divided_by:100 }}";
            {% assign alireviews_tags = customer.tags | join: "," %}
            var alireviews_tags = "{{ alireviews_tags }}";
            var alireviews_last_purchase = "{{ customer.last_order.created_at | date: "%Y-%m-%d %H:%M" }}";
        </script>
 <script src="https://cdn.alireviews.io/box/js/frontend/45/iframe.js?version=5.4.9" defer="defer"></script>
<script type="text/javascript">
function loadCSS() {
  let head = document.getElementsByTagName("head")[0];
  let link = document.createElement("link");
  link.rel = "stylesheet";
  link.type = "text/css";
  link.href = "https://cdn.alireviews.io/box/css/frontend/45/vendor.css?version=5.4.9";
  link.media = "all";
  head.appendChild(link);
};
window.addEventListener("load", loadCSS(), false);
</script>

<script type="text/javascript"> 
  const EL_IFRAME_AR = 'iframe[widget-id][class=aliReviewsFrame]:not([src])';
  function renderIframe(iframes){
    iframes.length && iframes.forEach((iframe, index) => {
      let iframeSrc = iframe.getAttribute('data-ar-src') || iframe.getAttribute('data-src');
      iframe.setAttribute('src', iframeSrc || '');
    });
  };
  document.addEventListener("DOMContentLoaded", function () { 
    let iframes = document.querySelectorAll(EL_IFRAME_AR);
    renderIframe(iframes);
  });
</script>


{% endif %}