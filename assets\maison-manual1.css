:root {
  --primary-blue: #0071e6;
  --primary-blue-hover: #2784e8;
  --text-primary: #282521;
  --text-secondary: rgb(17 17 17 / 79%);
  --text-tertiary: rgba(17, 17, 17, 0.32);
  --bg-primary: #f2f1f0;
  --bg-white: #ffffff;
  --bg-secondary: #f5f5f5;
  --bg-tertiary: #fafafa;
  --border-color: rgba(17, 17, 17, 0.12);
  --border-light: rgba(17, 17, 17, 0.08);
  --shadow-sm:
    0px 1px 2px 0px rgba(16, 24, 40, 0.06),
    0px 1px 3px 0px rgba(16, 24, 40, 0.1);
  --shadow-md: 0px 4px 12px rgba(0, 0, 0, 0.1);
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-full: 999px;
}

[data-theme="dark"] {
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --text-tertiary: rgba(255, 255, 255, 0.4);
  --bg-primary: #111111;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #222222;
  --border-color: rgba(255, 255, 255, 0.12);
  --border-light: rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .card,
[data-theme="dark"] .feature-card,
[data-theme="dark"] .quick-ref-card,
[data-theme="dark"] .troubleshoot-card,
[data-theme="dark"] .model-card,
[data-theme="dark"] .nav-dropdown {
  background-color: #35322d;
  border-color: rgba(255, 255, 255, 0.1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family:
    "Aspekta",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    sans-serif;
}

body {
  font-family:
    "Aspekta",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
  line-height: 1.5;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 80px;
}

@media (max-width: 1024px) {
  .container {
    padding: 0 40px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 12px;
  }

  .nav-section {
    top: 64px;
    z-index: 90;
    padding: 2px;
  }
}

/* Typography */
h1 {
  font-size: 32px;
  font-weight: 600;
  line-height: 1.3;
  text-transform: uppercase;
}

h2 {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.4;
  text-transform: uppercase;
}

h3 {
  font-size: 18px;
  font-weight: 600;
  line-height: 1.5;
  text-transform: uppercase;
}

.text-sm {
  font-size: 14px;
  line-height: 1.5;
}

/* Header */
.header {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
}

.logo {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-blue);
  letter-spacing: -0.02em;
}

.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.theme-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: var(--radius-sm);
  transition: background-color 0.2s;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle:hover {
  background-color: var(--bg-secondary);
}

/* Product Selector */
.product-selector {
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
}

.product-selector-content {
  padding: 32px 0;
}

.series-tabs {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.series-tab {
  padding: 8px 16px;
  border: none;
  background-color: transparent;
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  border-radius: var(--radius-full);
  transition: all 0.2s;
  width: 100%;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.series-tab:hover {
  background-color: var(--bg-secondary);
}

.series-tab.active {
  background-color: var(--primary-blue);
  color: white;
}

.model-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
}

.model-card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-light);
  padding: 16px;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
}

.model-card:hover {
  border-color: var(--primary-blue);
  box-shadow: var(--shadow-sm);
  transform: translateY(-2px);
}

.model-card.selected {
  border-color: var(--primary-blue);
  box-shadow: var(--shadow-sm);
}

.model-card.selected::before {
  content: "";
  position: absolute;
  top: 12px;
  right: 12px;
  width: 20px;
  height: 20px;
  background-color: var(--primary-blue);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url("data:image/svg+xml,%3Csvg width='12' height='10' viewBox='0 0 12 10' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 5L4.5 8.5L11 1' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

.model-name {
  font-weight: 600;
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 12px;
}

.model-badge {
  display: inline-block;
  padding: 4px 8px;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
}

/* Search Section */
.search-section {
  padding: 48px 0 32px;
}

.search-container {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  padding: 16px 48px 16px 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 16px;
  transition: all 0.2s;
  outline: none;
}

.search-input:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(0, 113, 227, 0.1);
}

.search-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
  pointer-events: none;
}

.popular-searches {
  margin-top: 16px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.popular-tag {
  padding: 8px 16px;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-full);
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid var(--border-light);
}

.popular-tag:hover {
  border-color: var(--primary-blue);
  color: var(--primary-blue);
  background-color: rgba(0, 113, 227, 0.05);
  transform: translateY(-1px);
}

/* Navigation */
.nav-section {
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  position: sticky;
  top: 64px;
  z-index: 90;
}

.nav-tabs {
  display: flex;
  gap: 32px;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
}

.nav-tabs::-webkit-scrollbar {
  display: none;
}

.nav-tab {
  padding: 16px 0;
  border: none;
  background: none;
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  position: relative;
  white-space: nowrap;
  transition: color 0.2s;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.nav-tab:hover {
  color: var(--text-primary);
}

.nav-tab.active {
  color: var(--primary-blue);
}

.nav-tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--primary-blue);
}

/* Content */
.content-section {
  padding: 48px 0 64px;
  display: none;
}

.content-section.active {
  display: block;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.section-header {
  margin-bottom: 32px;
}

.section-subtitle {
  color: var(--text-secondary);
  margin-top: 8px;
}

/* Cards */
.card {
  background-color: var(--bg-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: 24px;
  margin-bottom: 16px;
  transition: all 0.2s;
}

.card:hover {
  box-shadow: var(--shadow-sm);
}

/* Feature Grid */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin: 32px 0;
}

.feature-card {
  background-color: var(--bg-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: 24px;
  transition: all 0.2s;
}

.feature-card:hover {
  box-shadow: var(--shadow-sm);
  transform: translateY(-2px);
}

.feature-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(
    180deg,
    var(--bg-primary) 0%,
    var(--bg-secondary) 100%
  );
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  font-size: 24px;
}

.feature-title {
  font-weight: 600;
  margin-bottom: 8px;
}

.feature-description {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
}

/* Steps */
.steps-container {
  margin: 32px 0;
}

.step {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.step-number {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  background-color: var(--primary-blue);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.step-content {
  flex: 1;
}

.step-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.step-description {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
}

/* Button */
.btn {
  padding: 16px 32px;
  border: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  box-shadow: var(--shadow-sm);
}

.btn-primary {
  background-color: var(--primary-blue);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-blue-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--bg-tertiary);
  transform: translateY(-1px);
}

/* Quick Reference CaArd */
.quick-ref-card {
  background: radial-gradient(100% 100% at 50% 100%, #053669 0%, #282521 85%);
  color: white;
  border-radius: var(--radius-lg);
  padding: 32px;
  margin: 32px 0;
  box-shadow: var(--shadow-md);
}

.quick-ref-grid > .quick-ref-item > p {
  margin-bottom: 0px;
  opacity: 0.7;
}

.quick-ref-content h2 {
  color: white;
  margin-bottom: 8px;
}

.quick-ref-content p {
  opacity: 0.9;
  margin-bottom: 24px;
}

.quick-ref-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 16px;
}

.quick-ref-item {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 16px;
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
}

.quick-ref-item strong {
  display: block;
  margin-bottom: 4px;
}

ol {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
  margin-left: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.feature-description-title {
  color: var(--text-secondary);
  font-size: 12px;
  line-height: 1.5;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin: 12px 0px;
  font-weight: 600;
}

/* Watch Demo */
.watch-demo {
  background: linear-gradient(
    180deg,
    var(--bg-secondary) 0%,
    var(--bg-tertiary) 100%
  );
  border-radius: var(--radius-lg);
  padding: 48px 32px;
  text-align: center;
  margin: 32px 0;
}

.watch-screen {
  width: 240px;
  height: 240px;
  background-color: #000;
  border-radius: 32px;
  margin: 32px auto;
  position: relative;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.watch-face {
  color: white;
  font-size: 16px;
  font-weight: 500;
  opacity: 0.8;
}

/* Troubleshooting */
.troubleshoot-card {
  background-color: var(--bg-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  margin-bottom: 12px;
  overflow: hidden;
}

.troubleshoot-header {
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.troubleshoot-header:hover {
  background-color: var(--bg-secondary);
}

.troubleshoot-title {
  font-weight: 600;
}

.troubleshoot-icon {
  transition: transform 0.2s;
  color: var(--text-secondary);
}

.troubleshoot-icon.rotate {
  transform: rotate(180deg);
}

.troubleshoot-content {
  padding: 0 24px;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.troubleshoot-content.expanded {
  padding: 0 24px 24px;
  max-height: 500px;
}

/* Search Results */
.search-results {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  right: 0;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  max-height: 400px;
  overflow-y: auto;
  box-shadow: var(--shadow-md);
  display: none;
}

.search-results.active {
  display: block;
}

.search-result {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-light);
  cursor: pointer;
  display: flex;
  gap: 12px;
  align-items: center;
  transition: background-color 0.2s;
}

.search-result:hover {
  background-color: var(--bg-secondary);
}

.search-result:last-child {
  border-bottom: none;
}

.search-result-icon {
  width: 32px;
  height: 32px;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: var(--primary-blue);
}

.search-result-content {
  flex: 1;
}

.search-result-title {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 2px;
}

.search-result-description {
  font-size: 12px;
  color: var(--text-secondary);
}

/* Trust Signals */
.trust-signals {
  background-color: var(--bg-tertiary);
  padding: 48px 0;
  margin-top: 64px;
  border-top: 1px solid var(--border-color);
}

.trust-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
}

.trust-item {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.trust-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(
    180deg,
    var(--bg-primary) 0%,
    var(--bg-secondary) 100%
  );
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: var(--shadow-sm);
}

.trust-content h3 {
  margin-bottom: 4px;
}

.trust-content p {
  color: var(--text-secondary);
  font-size: 14px;
}

/* Responsive */
@media (max-width: 768px) {
  .series-tab {
    padding: 8px 2px;
  }
  h1 {
    font-size: 22px;
  }

  h2 {
    font-size: 20px;
  }

  .nav-tabs {
    gap: 24px;
    padding: 0 20px;
  }

  .feature-grid,
  .trust-grid {
    grid-template-columns: 1fr;
  }

  .quick-ref-grid {
    grid-template-columns: repeat(1, 1fr);
  }

  .quick-ref-card {
    padding: 40px 16px;
  }

  .header-actions {
    gap: 8px;
  }

  .btn {
    padding: 12px 24px;
    font-size: 14px;
  }

  .watch-screen {
    width: 200px;
    height: 200px;
  }
}

/* Search Overlay Styles */
.search-overlay-container {
  position: relative;
  z-index: 999;
}

.search-backdrop {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
  /* Transitions will be handled by AlpineJS */
}

.search-panel-outer-container {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  display: flex;
  max-width: 100%;
}

.search-panel-inner-container {
  width: 100vw;
  max-width: 28rem; /* 448px, equivalent to max-w-md */
  /* Transitions will be handled by AlpineJS */
}

.search-panel-content-wrapper {
  display: flex;
  height: 100%;
  flex-direction: column;
  background-color: var(--bg-primary);
  box-shadow:
    0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1); /* shadow-xl */
}

.search-panel-header {
  border-bottom: 1px solid var(--border-color);
  padding: 1.25rem 1rem; /* px-4 py-5 */
}

@media (min-width: 640px) {
  /* sm: breakpoint */
  .search-panel-header {
    padding-left: 1.5rem; /* sm:px-6 */
    padding-right: 1.5rem; /* sm:px-6 */
  }
}

.search-panel-header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.search-panel-title {
  font-size: 1rem; /* text-base */
  font-weight: 600; /* font-semibold */
  line-height: 1.5rem; /* leading-6 */
}

.search-panel-close-button {
  border-radius: 0.375rem; /* rounded-md */
  color: var(--text-secondary);
  background-color: transparent; /* Added for explicitness */
  border: none; /* Added for explicitness */
  cursor: pointer; /* Added for default button behavior */
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
}

.search-panel-close-button:hover {
  color: var(--text-primary);
}

.search-panel-close-button:focus {
  outline: 2px solid transparent; /* focus:outline-none, but usually you'd add focus rings */
  outline-offset: 2px;
}

.search-panel-close-icon {
  height: 1.5rem; /* h-6 */
  width: 1.5rem; /* w-6 */
}

.search-panel-input-section {
  margin-top: 1rem; /* mt-4 */
}

.search-panel-input-relative-wrapper {
  position: relative;
}

.search-panel-input {
  display: block;
  width: 100%;
  border-radius: 0.375rem; /* rounded-md */
  border-width: 0;
  padding-top: 0.75rem; /* py-3 */
  padding-bottom: 0.75rem; /* py-3 */
  padding-left: 2.5rem; /* pl-10 */
  padding-right: 1rem; /* pr-4 */
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  font-size: 1rem; /* text-base */
  line-height: 1.5rem; /* default from text-base, can be more specific if needed */
}

.search-panel-input::placeholder {
  color: var(--text-tertiary);
}

.search-panel-input:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
    var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
    calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow:
    var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
    var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: var(--primary-blue);
  --tw-ring-inset: inset; /* Ensure this is defined or handled if you use focus:ring-inset */
}

@media (min-width: 640px) {
  /* sm: breakpoint */
  .search-panel-input {
    font-size: 0.875rem; /* sm:text-sm */
    line-height: 1.5rem; /* sm:leading-6, might need to adjust if different from text-sm default */
  }
}

.search-panel-input-icon-wrapper {
  position: absolute;
  inset-inline-start: 0; /* left-0 */
  inset-block-start: 0; /* top-0 */
  inset-block-end: 0; /* bottom-0 */
  display: flex;
  align-items: center;
  padding-left: 0.75rem; /* pl-3 */
  pointer-events: none;
}

.search-panel-input-icon {
  height: 1.25rem; /* h-5 */
  width: 1.25rem; /* w-5 */
  color: var(--text-tertiary);
}

/* Panel content area */
.search-panel-body {
  flex: 1 1 0%; /* flex-1 */
  overflow-y: auto;
}

.search-panel-body-padding {
  padding: 1.25rem 1rem; /* px-4 py-5 */
}

@media (min-width: 640px) {
  /* sm: breakpoint */
  .search-panel-body-padding {
    padding-left: 1.5rem; /* sm:px-6 */
    padding-right: 1.5rem; /* sm:px-6 */
  }
}

/* Popular Searches */
.popular-searches-container {
  /* space-y-4 direct children will have margin-top, handled by x-show or direct styling on children */
}
.popular-searches-title,
.search-results-title {
  font-size: 0.75rem; /* text-xs */
  font-weight: 500; /* font-medium */
  text-transform: uppercase;
  letter-spacing: 0.05em; /* tracking-wide */
  color: var(--text-tertiary);
}
.popular-searches-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem; /* gap-2 */
}
.popular-search-tag {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px; /* rounded-full */
  padding: 0.25rem 0.75rem; /* px-3 py-1 */
  font-size: 0.875rem; /* text-sm */
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  transition-property: all; /* transition-all */
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  border: none; /* Added */
  cursor: pointer; /* Added */
}
.popular-search-tag:hover {
  color: var(--primary-blue);
  background-color: rgba(
    var(--primary-blue-rgb, 0, 113, 230),
    0.1
  ); /* Assuming --primary-blue-rgb is defined or use direct rgba */
}

/* Search Results */
.search-results-container {
  margin-top: 2rem; /* mt-8 */
  /* space-y-4 handled by children or direct styling */
}
.search-results-list {
  /* space-y-2 handled by children or direct styling */
}
.search-result-button {
  width: 100%;
  border-radius: 0.5rem; /* rounded-lg */
  padding: 1rem; /* p-4 */
  text-align: left;
  transition-property: all; /* transition-all */
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  background-color: transparent; /* Added */
  border: none; /* Added */
  cursor: pointer; /* Added */
}
.search-result-button:hover {
  background-color: var(--bg-secondary);
}
.search-result-flex-container {
  display: flex;
  gap: 1rem; /* gap-4 */
}
.search-result-icon-container {
  flex-shrink: 0;
  margin-top: 0.25rem; /* mt-1 */
}
.search-result-icon-wrapper {
  display: flex;
  height: 2rem; /* h-8 */
  width: 2rem; /* w-8 */
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem; /* rounded-lg */
  background-color: rgba(
    var(--primary-blue-rgb, 0, 113, 230),
    0.1
  ); /* Assuming --primary-blue-rgb for opacity */
  color: var(--primary-blue);
}
.search-result-icon {
  height: 1.25rem; /* h-5 */
  width: 1.25rem; /* w-5 */
}
.search-result-text-container {
  flex: 1 1 0%; /* flex-1 */
  min-width: 0;
}
.search-result-title {
  font-size: 0.875rem; /* text-sm */
  font-weight: 500; /* font-medium */
  color: var(--text-primary);
  overflow: hidden; /* truncate */
  text-overflow: ellipsis; /* truncate */
  white-space: nowrap; /* truncate */
}
.search-result-description {
  margin-top: 0.25rem; /* mt-1 */
  font-size: 0.875rem; /* text-sm */
  color: var(--text-secondary);
  /* line-clamp-2 requires -webkit-box-orient, display: -webkit-box, etc. or JS */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* No Results */
.no-results-container {
  margin-top: 2rem; /* mt-8 */
  text-align: center;
}
.no-results-icon-wrapper {
  display: inline-flex; /* inline-flex */
  align-items: center;
  justify-content: center;
  width: 3rem; /* w-12 */
  height: 3rem; /* h-12 */
  border-radius: 9999px; /* rounded-full */
  background-color: var(--bg-secondary);
  margin-bottom: 1rem; /* mb-4 */
}
.no-results-icon {
  /* width and height already set on SVG */
  color: var(--text-tertiary);
}
.no-results-text {
  font-size: 0.875rem; /* text-sm */
  color: var(--text-secondary);
}

.search-overlay {
  background-color: var(--bg-primary);
  transition: transform 0.3s ease-in-out;
}

.search-result-card {
  transition: all 0.2s ease-in-out;
}

.search-result-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.search-input:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(0, 113, 227, 0.1);
}

/* Transition Classes */
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.scale-105 {
  transform: scale(1.05);
}

.transition {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.ease-in-out {
  transition-timing-function: ease-in-out;
}
.duration-300 {
  transition-duration: 300ms;
}
.translate-x-full {
  transform: translateX(100%);
}
.translate-x-0 {
  transform: translateX(0);
}
.opacity-0 {
  opacity: 0;
}
.opacity-100 {
  opacity: 1;
}
/* Mobile nav dropdown styles */
.nav-dropdown {
  display: none;
  width: 100%;
  margin: 12px 0px;
  padding: 12px;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  font-size: 16px;
  background: var(--bg-white);
  color: var(--text-primary);
}
@media (max-width: 768px) {
  .nav-tabs {
    display: none;
  }
  .nav-dropdown {
    display: block;
  }
}
