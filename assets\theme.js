document.addEventListener('DOMContentLoaded', function () {
  document.querySelectorAll('.custom-video-container .play-icon').forEach(function (playIcon) {
    playIcon.addEventListener('click', function () {
      var videoId = playIcon.getAttribute('data-video-id');

      var allVideoContainers = document.querySelectorAll('.video-container');

      var videoContainer = document.querySelector('#video-container-' + videoId.split('-')[1]);

      if (videoContainer) {
        var videoElement = videoContainer.querySelector('video');

        document.querySelectorAll('.video-container video').forEach(function (video) {
          video.pause();
          document.querySelectorAll('.custom-video-container .play-icon').forEach(function (icon) {
            icon.style.display = 'block';
          });
        });

        if (videoElement) {
          if (playIcon.style.display === 'none') {
            videoElement.pause();
            playIcon.style.display = 'block';
          } else {
            videoElement.play();
            playIcon.style.display = 'none';
          }

          const videoEnded = (event) => {
            playIcon.style.display = 'block';
            videoElement.removeEventListener('ended', videoEnded);
          };

          videoElement.addEventListener('ended', videoEnded);
        }
      }
    });
  });

  document.querySelectorAll('.custom-video-container video').forEach(function (video) {
    video.addEventListener('click', function () {
      this.pause();
      this.closest('.custom-video-container').querySelector('.play-icon').style.display = 'block';
    });
  });

  const titles = document.querySelectorAll('.text-tab-title');
  let currentIndex = 0;
  let autoRotateInterval;
  let isAutoRotating = true;

  if (titles.length > 0) {
    const firstTitle = titles[0];
    const firstBlockId = firstTitle.getAttribute('data-block-id');

    const firstDescription = document.querySelector(`.text-tab-info[data-block-id="${firstBlockId}"]`);
    const firstImage = document.querySelector(`.text-tab-img[data-block-id="${firstBlockId}"]`);
    const sectionImage = document.querySelector(`.text-tab-img.section-image[data-block-id="${firstBlockId}"]`);

    if (firstDescription) {
      firstDescription.classList.add('active');
    }
    if (firstImage) {
      firstImage.classList.add('active');
    } else if (sectionImage) {
      sectionImage.style.display = 'block';
    }
  }

  function startAutoRotation() {
    if (isAutoRotating) {
      autoRotateInterval = setInterval(function () {
        moveToNextTab();
      }, 5000);
    }
  }

  function stopAutoRotation() {
    clearInterval(autoRotateInterval);
    isAutoRotating = false;
  }

  function moveToNextTab() {
    if (titles.length === 0) {
      return;
    }

    const allDescriptions = document.querySelectorAll('.text-tab-info');
    const allImages = document.querySelectorAll('.text-tab-img');
    const sectionImages = document.querySelectorAll('.text-tab-img.section-image');

    allDescriptions.forEach((desc) => {
      desc.classList.remove('active');
    });

    allImages.forEach((img) => {
      img.classList.remove('active');
      img.style.display = 'none';
    });

    sectionImages.forEach((img) => {
      img.style.display = 'none';
    });

    currentIndex = (currentIndex + 1) % titles.length;

    const newBlockId = titles[currentIndex].getAttribute('data-block-id');
    const newDescription = document.querySelector(`.text-tab-info[data-block-id="${newBlockId}"]`);
    const newImage = document.querySelector(`.text-tab-img[data-block-id="${newBlockId}"]`);
    const newSectionImage = document.querySelector(`.text-tab-img.section-image[data-block-id="${newBlockId}"]`);

    if (newDescription) {
      newDescription.classList.add('active');
    }
    if (newImage) {
      newImage.classList.add('active');
      newImage.style.display = 'block';
    } else if (newSectionImage) {
      newSectionImage.style.display = 'block';
    }
  }

  titles.forEach((title, index) => {
    title.addEventListener('click', function () {
      if (isAutoRotating) {
        stopAutoRotation();
      }

      const blockId = this.getAttribute('data-block-id');

      const allDescriptions = document.querySelectorAll('.text-tab-info');
      const allImages = document.querySelectorAll('.text-tab-img');
      const sectionImages = document.querySelectorAll('.text-tab-img.section-image');

      allDescriptions.forEach((desc) => {
        desc.classList.remove('active');
      });

      allImages.forEach((img) => {
        img.classList.remove('active');
        img.style.display = 'none';
      });

      sectionImages.forEach((img) => {
        img.style.display = 'none';
      });

      const description = document.querySelector(`.text-tab-info[data-block-id="${blockId}"]`);
      const image = document.querySelector(`.text-tab-img[data-block-id="${blockId}"]`);
      const sectionImage = document.querySelector(`.text-tab-img.section-image[data-block-id="${blockId}"]`);

      if (description) {
        description.classList.add('active');
      }
      if (image) {
        image.classList.add('active');
        image.style.display = 'block';
      } else if (sectionImage) {
        sectionImage.style.display = 'block';
      }

      currentIndex = index;
    });
  });

  if (titles.length > 0) {
    startAutoRotation();
  }

  new Swiper('.gard-pro-in-action .gard-pro-action', {
    slidesPerView: 1.2,
    centeredSlides: true,
    spaceBetween: 10,
    loop: true,
    breakpoints: {
      640: {
        slidesPerView: 1.2,
      },
      768: {
        slidesPerView: 3.8,
      },
      1440: {
        slidesPerView: 3.8,
      },
    },
  });

  new Swiper('.cutting-edge-slider', {
    slidesPerView: 2.3,
    spaceBetween: 15,
    navigation: {
      nextEl: '.cutting-edge-swiper-button-next',
      prevEl: '.cutting-edge-swiper-button-prev',
    },
    pagination: {
      el: '.cutting-edge-swiper-pagination',
      clickable: true,
    },
    breakpoints: {
      0: {
        slidesPerView: 1.3,
        spaceBetween: 15,
      },
      767: {
        slidesPerView: 2.3,
        spaceBetween: 15,
      },
      1024: {
        slidesPerView: 1.3,
        spaceBetween: 15,
      },
      1200: {
        slidesPerView: 2.3,
        spaceBetween: 15,
      },
    },
  });

  new Swiper('.advanced-health-monitor .product-list-slider', {
    slidesPerView: 3,
    loop: true,
    spaceBetween: 20,
    navigation: {
      nextEl: '.advanced-health-monitor .swiper-button-next',
      prevEl: '.advanced-health-monitor .swiper-button-prev',
    },
    breakpoints: {
      0: {
        slidesPerView: 1.3,
        spaceBetween: 12,
      },
      767: {
        slidesPerView: 2,
        spaceBetween: 15,
      },
      1024: {
        slidesPerView: 3,
        spaceBetween: 15,
      },
    },
  });
});

document.addEventListener('DOMContentLoaded', function () {
  new Swiper('.advanced-health-monitor .product-list-slider', {
    slidesPerView: 3,
    loop: true,
    spaceBetween: 20,
    navigation: {
      nextEl: '.advanced-health-monitor .swiper-button-next',
      prevEl: '.advanced-health-monitor .swiper-button-prev',
    },
    breakpoints: {
      0: {
        slidesPerView: 1.3,
        spaceBetween: 12,
      },
      767: {
        slidesPerView: 3,
        spaceBetween: 15,
      },
      1024: {
        slidesPerView: 3,
        spaceBetween: 15,
      },
    },
  });
});
/*
@license
  Impulse by Archetype Themes (https://archetypethemes.co)
  Access unminified JS in assets/theme.js

  Use this event listener to run your own JS outside of this file.
  Documentation - https://archetypethemes.co/blogs/impulse/javascript-events-for-developers

  document.addEventListener('page:loaded', function() {
    // Page has loaded and theme assets are ready
  });
*/ (window.theme = window.theme || {}),
  (window.Shopify = window.Shopify || {}),
  (theme.config = {
    bpSmall: !1,
    hasSessionStorage: !0,
    hasLocalStorage: !0,
    mediaQuerySmall: 'screen and (max-width: 769px)',
    youTubeReady: !1,
    vimeoReady: !1,
    vimeoLoading: !1,
    isTouch:
      !!('ontouchstart' in window || (window.DocumentTouch && window.document instanceof DocumentTouch)) ||
      !!window.navigator.maxTouchPoints ||
      !!window.navigator.msMaxTouchPoints,
    stickyHeader: !1,
    rtl: 'rtl' == document.documentElement.getAttribute('dir'),
  }),
  theme.config.isTouch && (document.documentElement.className += ' supports-touch'),
  console &&
    console.log &&
    console.log(
      'Impulse theme (' + theme.settings.themeVersion + ') by ARCHΞTYPE | Learn more at https://archetypethemes.co'
    ),
  (theme.recentlyViewed = { recent: {}, productInfo: {} }),
  (window.lazySizesConfig = window.lazySizesConfig || {}),
  (lazySizesConfig.expFactor = 4),
  (function () {
    'use strict';
    var e, t, i, s, n, a, o, r;
    (theme.delegate = {
      on: function (e, t, i) {
        return (
          this.namespaces || (this.namespaces = {}),
          (this.namespaces[e] = t),
          (i = i || !1),
          this.addEventListener(e.split('.')[0], t, i),
          this
        );
      },
      off: function (e) {
        if (this.namespaces)
          return this.removeEventListener(e.split('.')[0], this.namespaces[e]), delete this.namespaces[e], this;
      },
    }),
      (window.on = Element.prototype.on = theme.delegate.on),
      (window.off = Element.prototype.off = theme.delegate.off),
      (theme.utils = {
        defaultTo: function (e, t) {
          return null == e || e != e ? t : e;
        },
        wrap: function (e, t) {
          e.parentNode.insertBefore(t, e), t.appendChild(e);
        },
        debounce: function (e, t, i) {
          var s;
          return function () {
            var n = this,
              a = arguments,
              o = function () {
                (s = null), i || t.apply(n, a);
              },
              r = i && !s;
            clearTimeout(s), (s = setTimeout(o, e)), r && t.apply(n, a);
          };
        },
        throttle: function (e, t) {
          var i = !1;
          return function () {
            i ||
              (t.apply(this, arguments),
              (i = !0),
              setTimeout(function () {
                i = !1;
              }, e));
          };
        },
        prepareTransition: function (e, t) {
          function i(t) {
            e.classList.remove('is-transitioning'), e.removeEventListener('transitionend', i);
          }
          e.addEventListener('transitionend', i),
            e.classList.add('is-transitioning'),
            e.offsetWidth,
            'function' == typeof t && t();
        },
        compact: function (e) {
          for (var t = -1, i = null == e ? 0 : e.length, s = 0, n = []; ++t < i; ) {
            var a = e[t];
            a && (n[s++] = a);
          }
          return n;
        },
        serialize: function (e) {
          var t = [];
          return (
            Array.prototype.slice.call(e.elements).forEach(function (e) {
              if (!(!e.name || e.disabled || ['file', 'reset', 'submit', 'button'].indexOf(e.type) > -1)) {
                if ('select-multiple' === e.type) {
                  Array.prototype.slice.call(e.options).forEach(function (i) {
                    i.selected && t.push(encodeURIComponent(e.name) + '=' + encodeURIComponent(i.value));
                  });
                  return;
                }
                (!(['checkbox', 'radio'].indexOf(e.type) > -1) || e.checked) &&
                  t.push(encodeURIComponent(e.name) + '=' + encodeURIComponent(e.value));
              }
            }),
            t.join('&')
          );
        },
      }),
      (theme.a11y = {
        trapFocus: function (e) {
          var t = {
              focusin: e.namespace ? 'focusin.' + e.namespace : 'focusin',
              focusout: e.namespace ? 'focusout.' + e.namespace : 'focusout',
              keydown: e.namespace ? 'keydown.' + e.namespace : 'keydown.handleFocus',
            },
            i = e.container.querySelectorAll(
              'button, [href], input, select, textarea, [tabindex]:not([tabindex^="-"])'
            ),
            s = [].slice.call(i).filter((e) => null !== e.offsetParent),
            n = s[0],
            a = s[s.length - 1];
          e.elementToFocus || (e.elementToFocus = e.container),
            e.container.setAttribute('tabindex', '-1'),
            e.elementToFocus.focus(),
            document.documentElement.off('focusin'),
            document.documentElement.on(t.focusout, function () {
              document.documentElement.off(t.keydown);
            }),
            document.documentElement.on(t.focusin, function (e) {
              (e.target === a || e.target === n) &&
                document.documentElement.on(t.keydown, function (e) {
                  var t;
                  (t = e), 9 === t.keyCode && t.target === n && t.shiftKey && (t.preventDefault(), a.focus());
                });
            });
        },
        removeTrapFocus: function (e) {
          var t = e.namespace ? 'focusin.' + e.namespace : 'focusin';
          e.container && e.container.removeAttribute('tabindex'), document.documentElement.off(t);
        },
        lockMobileScrolling: function (e, t) {
          var i = t || document.documentElement;
          document.documentElement.classList.add('lock-scroll'),
            i.on('touchmove' + e, function () {
              return !0;
            });
        },
        unlockMobileScrolling: function (e, t) {
          document.documentElement.classList.remove('lock-scroll'),
            (t || document.documentElement).off('touchmove' + e);
        },
      }),
      document.documentElement.on('keyup.tab', function (e) {
        9 === e.keyCode &&
          (document.documentElement.classList.add('tab-outline'), document.documentElement.off('keyup.tab'));
      }),
      (theme.Currency =
        ((e = theme && theme.settings && theme.settings.superScriptPrice),
        {
          formatMoney: function t(i, s) {
            s || (s = theme.settings.moneyFormat), 'string' == typeof i && (i = i.replace('.', ''));
            var n = '',
              a = /\{\{\s*(\w+)\s*\}\}/,
              o = s || '${{amount}}';
            function r(e, t, i, s) {
              if (
                ((t = theme.utils.defaultTo(t, 2)),
                (i = theme.utils.defaultTo(i, ',')),
                (s = theme.utils.defaultTo(s, '.')),
                isNaN(e) || null == e)
              )
                return 0;
              var n,
                a = (e = (e / 100).toFixed(t)).split('.');
              return a[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g, '$1' + i) + (a[1] ? s + a[1] : '');
            }
            switch (o.match(a)[1]) {
              case 'amount':
                (n = r(i, 2)), e && n && n.includes('.') && (n = n.replace('.', '<sup>') + '</sup>');
                break;
              case 'amount_no_decimals':
                n = r(i, 0);
                break;
              case 'amount_with_comma_separator':
                (n = r(i, 2, '.', ',')), e && n && n.includes(',') && (n = n.replace(',', '<sup>') + '</sup>');
                break;
              case 'amount_no_decimals_with_comma_separator':
                n = r(i, 0, '.', ',');
                break;
              case 'amount_no_decimals_with_space_separator':
                n = r(i, 0, ' ');
            }
            return o.replace(a, n);
          },
          getBaseUnit: function e(t) {
            if (t && t.unit_price_measurement && t.unit_price_measurement.reference_value)
              return 1 === t.unit_price_measurement.reference_value
                ? t.unit_price_measurement.reference_unit
                : t.unit_price_measurement.reference_value + t.unit_price_measurement.reference_unit;
          },
        })),
      (theme.Images = {
        imageSize: function e(t) {
          if (!t) return '620x';
          var i = t.match(/.+_((?:pico|icon|thumb|small|compact|medium|large|grande)|\d{1,4}x\d{0,4}|x\d{1,4})[_\.@]/);
          return null !== i ? i[1] : null;
        },
        getSizedImageUrl: function e(t, i) {
          if (!t || null == i) return t;
          if ('master' === i) return this.removeProtocol(t);
          var s = t.match(/\.(jpg|jpeg|gif|png|bmp|bitmap|tiff|tif)(\?v=\d+)?$/i);
          if (null != s) {
            var n = t.split(s[0]),
              a = s[0];
            return this.removeProtocol(n[0] + '_' + i + a);
          }
          return null;
        },
        removeProtocol: function e(t) {
          return t.replace(/http(s)?:/, '');
        },
        lazyloadImagePath: function e(t) {
          var i;
          return null !== t && (i = t.replace(/(\.[^.]*)$/, '_{width}x$1')), i;
        },
      }),
      (theme.loadImageSection = function (e) {
        function t() {
          e.classList.remove('loading', 'loading--delayed'), e.classList.add('loaded');
        }
        function i() {
          return e.querySelector('.lazyloaded');
        }
        if (e.querySelector('svg') || i()) {
          t();
          return;
        }
        var s = setInterval(function () {
          i() && (clearInterval(s), t());
        }, 25);
      }),
      (theme.initWhenVisible = function (e) {
        var t = e.threshold ? e.threshold : 0;
        new IntersectionObserver(
          (t, i) => {
            t.forEach((t) => {
              t.isIntersecting && 'function' == typeof e.callback && (e.callback(), i.unobserve(t.target));
            });
          },
          { rootMargin: '0px 0px ' + t + 'px 0px' }
        ).observe(e.element);
      }),
      (theme.LibraryLoader =
        ((i = { requested: 'requested', loaded: 'loaded' }),
        (s = 'https://cdn.shopify.com/shopifycloud/'),
        (n = {
          youtubeSdk: {
            tagId: 'youtube-sdk',
            src: 'https://www.youtube.com/iframe_api',
            type: (t = { link: 'link', script: 'script' }).script,
          },
          vimeo: { tagId: 'vimeo-api', src: 'https://player.vimeo.com/api/player.js', type: t.script },
          shopifyXr: {
            tagId: 'shopify-model-viewer-xr',
            src: s + 'shopify-xr-js/assets/v1.0/shopify-xr.en.js',
            type: t.script,
          },
          modelViewerUi: {
            tagId: 'shopify-model-viewer-ui',
            src: s + 'model-viewer-ui/assets/v1.0/model-viewer-ui.en.js',
            type: t.script,
          },
          modelViewerUiStyles: {
            tagId: 'shopify-model-viewer-ui-styles',
            src: s + 'model-viewer-ui/assets/v1.0/model-viewer-ui.css',
            type: t.link,
          },
        }),
        {
          load: function e(s, a) {
            var o,
              r = n[s];
            if (r && r.status !== i.requested) {
              if (((a = a || function () {}), r.status === i.loaded)) {
                a();
                return;
              }
              switch (((r.status = i.requested), r.type)) {
                case t.script:
                  o =
                    ((c = r),
                    (l = a),
                    (d = document.createElement('script')),
                    (d.src = c.src),
                    d.addEventListener('load', function () {
                      (c.status = i.loaded), l();
                    }),
                    d);
                  break;
                case t.link:
                  o =
                    ((h = r),
                    (u = a),
                    (p = document.createElement('link')),
                    (p.href = h.src),
                    (p.rel = 'stylesheet'),
                    (p.type = 'text/css'),
                    p.addEventListener('load', function () {
                      (h.status = i.loaded), u();
                    }),
                    p);
              }
              (o.id = r.tagId), (r.element = o);
              var c,
                l,
                d,
                h,
                u,
                p,
                f = document.getElementsByTagName(r.type)[0];
              f.parentNode.insertBefore(o, f);
            }
          },
        })),
      (theme.rteInit = function () {
        function e(e) {
          e.src = e.src;
          var t = document.createElement('div');
          t.classList.add('video-wrapper'), theme.utils.wrap(e, t);
        }
        document.querySelectorAll('.rte table').forEach((e) => {
          var t = document.createElement('div');
          t.classList.add('table-wrapper'), theme.utils.wrap(e, t);
        }),
          document.querySelectorAll('.rte iframe[src*="youtube.com/embed"]').forEach((t) => {
            e(t);
          }),
          document.querySelectorAll('.rte iframe[src*="player.vimeo"]').forEach((t) => {
            e(t);
          }),
          document.querySelectorAll('.rte a img').forEach((e) => {
            e.parentNode.classList.add('rte__image');
          });
      }),
      (theme.Sections = function e() {
        (this.constructors = {}),
          (this.instances = []),
          document.addEventListener('shopify:section:load', this._onSectionLoad.bind(this)),
          document.addEventListener('shopify:section:unload', this._onSectionUnload.bind(this)),
          document.addEventListener('shopify:section:select', this._onSelect.bind(this)),
          document.addEventListener('shopify:section:deselect', this._onDeselect.bind(this)),
          document.addEventListener('shopify:block:select', this._onBlockSelect.bind(this)),
          document.addEventListener('shopify:block:deselect', this._onBlockDeselect.bind(this));
      }),
      (theme.Sections.prototype = Object.assign({}, theme.Sections.prototype, {
        _createInstance: function (e, t, i) {
          var s = e.getAttribute('data-section-id'),
            n = e.getAttribute('data-section-type');
          if (void 0 !== (t = t || this.constructors[n])) {
            i && this._findInstance(s) && this._removeInstance(s);
            try {
              var a = Object.assign(new t(e), { id: s, type: n, container: e });
              this.instances.push(a);
            } catch (o) {
              console.error(o);
            }
          }
        },
        _findInstance: function (e) {
          for (var t = 0; t < this.instances.length; t++) if (this.instances[t].id === e) return this.instances[t];
        },
        _removeInstance: function (e) {
          for (var t, i = this.instances.length; i--; )
            if (this.instances[i].id === e) {
              (t = this.instances[i]), this.instances.splice(i, 1);
              break;
            }
          return t;
        },
        _onSectionLoad: function (e, t, i) {
          window.AOS && AOS.refreshHard(), theme && theme.initGlobals && theme.initGlobals();
          var s = t || e.target,
            n = t || e.target.querySelector('[data-section-id]');
          if (n) {
            this._createInstance(n);
            var a = t ? i : this._findInstance(e.detail.sectionId);
            s.querySelectorAll('[data-subsection]').length && this.loadSubSections(s),
              a && 'function' == typeof a.onLoad && a.onLoad(e),
              setTimeout(function () {
                window.dispatchEvent(new Event('scroll'));
              }, 200);
          }
        },
        _onSectionUnload: function (e) {
          this.instances = this.instances.filter(function (t) {
            var i = t.id === e.detail.sectionId;
            return i && 'function' == typeof t.onUnload && t.onUnload(e), !i;
          });
        },
        loadSubSections: function (e) {
          e &&
            e.querySelectorAll('[data-section-id]').forEach((e) => {
              this._onSectionLoad(null, e, e.dataset.sectionId);
            });
        },
        _onSelect: function (e) {
          var t = this._findInstance(e.detail.sectionId);
          void 0 !== t && 'function' == typeof t.onSelect && t.onSelect(e);
        },
        _onDeselect: function (e) {
          var t = this._findInstance(e.detail.sectionId);
          void 0 !== t && 'function' == typeof t.onDeselect && t.onDeselect(e);
        },
        _onBlockSelect: function (e) {
          var t = this._findInstance(e.detail.sectionId);
          void 0 !== t && 'function' == typeof t.onBlockSelect && t.onBlockSelect(e);
        },
        _onBlockDeselect: function (e) {
          var t = this._findInstance(e.detail.sectionId);
          void 0 !== t && 'function' == typeof t.onBlockDeselect && t.onBlockDeselect(e);
        },
        register: function (e, t, i) {
          this.constructors[e] = t;
          var s = document.querySelectorAll('[data-section-type="' + e + '"]');
          i && (s = i.querySelectorAll('[data-section-type="' + e + '"]')),
            s.forEach(
              function (e) {
                this._createInstance(e, t, i);
              }.bind(this)
            );
        },
        reinit: function (e) {
          for (var t = 0; t < this.instances.length; t++) {
            var i = this.instances[t];
            i.type === e && 'function' == typeof i.forceReload && i.forceReload();
          }
        },
      })),
      (theme.Variants = (function () {
        function e(e) {
          (this.container = e.container),
            (this.variants = e.variants),
            (this.singleOptionSelector = e.singleOptionSelector),
            (this.originalSelectorId = e.originalSelectorId),
            (this.enableHistoryState = e.enableHistoryState),
            (this.dynamicVariantsEnabled = e.dynamicVariantsEnabled),
            (this.currentlySelectedValues = this._getCurrentOptions()),
            (this.currentVariant = this._getVariantFromOptions()),
            this.container.querySelectorAll(this.singleOptionSelector).forEach((e) => {
              e.addEventListener('change', this._onSelectChange.bind(this));
            });
        }
        return (
          (e.prototype = Object.assign({}, e.prototype, {
            _getCurrentOptions: function () {
              var e = [];
              return (
                this.container.querySelectorAll(this.singleOptionSelector).forEach((t) => {
                  var i = t.getAttribute('type');
                  'radio' === i || 'checkbox' === i
                    ? t.checked && e.push({ value: t.value, index: t.dataset.index })
                    : e.push({ value: t.value, index: t.dataset.index });
                }),
                (e = theme.utils.compact(e))
              );
            },
            _numberFromOptionKey: function (e) {
              return parseInt(e.substr(-1));
            },
            _getWeightedOptionMatchCount: function (e) {
              return this._getCurrentOptions().reduce((t, { value: i, index: s }) => {
                let n = this._numberFromOptionKey(s);
                return e[s] === i ? t + (4 - n) : t;
              }, 0);
            },
            _getFullMatch(e) {
              let t = this._getCurrentOptions(),
                i = this.variants;
              return i.find((i) => {
                let s = t.every(({ value: e, index: t }) => i[t] === e);
                return e ? s && i.available : s;
              });
            },
            _getClosestAvailableMatch: function (e) {
              if (!e) return null;
              let t = this._getCurrentOptions(),
                i = this.variants,
                s =
                  e &&
                  i.filter(
                    (i) =>
                      t
                        .filter(
                          ({ value: t, index: i }) => this._numberFromOptionKey(i) <= this._numberFromOptionKey(e.index)
                        )
                        .every(({ value: e, index: t }) => i[t] === e) && i.available
                  );
              return s.reduce((t, i) => {
                if (null === t) return i;
                let s = this._getWeightedOptionMatchCount(t, e),
                  n = this._getWeightedOptionMatchCount(i, e);
                return n > s ? i : t;
              }, null);
            },
            _getVariantFromOptions: function (e) {
              let t = this._getFullMatch(!0),
                i = this._getClosestAvailableMatch(e),
                s = this._getFullMatch(!1);
              return this.dynamicVariantsEnabled ? t || i || s || null : s || null;
            },
            _updateInputState: function (e, t) {
              return (t) => {
                if (null === e) return;
                let i = t.dataset.index,
                  s = t.value,
                  n = t.getAttribute('type');
                'radio' === n || 'checkbox' === n ? (t.checked = e[i] === s) : (t.value = e[i]);
              };
            },
            _onSelectChange: function ({ srcElement: e }) {
              let t = this.container.querySelectorAll(this.singleOptionSelector),
                i = this._getVariantFromOptions({ index: e.dataset.index, value: e.value });
              t.forEach(this._updateInputState(i, e));
              let s = (this.currentlySelectedValues = this._getCurrentOptions()),
                n = { variant: i, currentlySelectedValues: s, value: e.value, index: e.parentElement.dataset.index };
              this.container.dispatchEvent(new CustomEvent('variantChange', { detail: n })),
                document.dispatchEvent(new CustomEvent('variant:change', { detail: n })),
                i &&
                  (this._updateMasterSelect(i),
                  this._updateImages(i),
                  this._updatePrice(i),
                  this._updateUnitPrice(i),
                  this._updateSKU(i),
                  (this.currentVariant = i),
                  this.enableHistoryState && this._updateHistoryState(i));

              if (document.querySelectorAll('.product-block .variant-wrapper')) {
                document.querySelectorAll('.product-block .variant-wrapper').forEach((item) => {
                  const checkedInput = item.querySelector('input:checked');
                  if (checkedInput) {
                    const value = checkedInput.value;
                    const span = item.querySelector('.oddit-template span');
                    if (span) {
                      span.textContent = value;
                    }
                  }
                });
              }
            },
            _updateImages: function (e) {
              var t = e.featured_image || {},
                i = (this.currentVariant && this.currentVariant.featured_image) || {};
              e.featured_image &&
                t.src !== i.src &&
                this.container.dispatchEvent(new CustomEvent('variantImageChange', { detail: { variant: e } }));
            },
            _updatePrice: function (e) {
              (!this.currentVariant ||
                e.price !== this.currentVariant.price ||
                e.compare_at_price !== this.currentVariant.compare_at_price) &&
                this.container.dispatchEvent(new CustomEvent('variantPriceChange', { detail: { variant: e } }));
            },
            _updateUnitPrice: function (e) {
              (!this.currentVariant || e.unit_price !== this.currentVariant.unit_price) &&
                this.container.dispatchEvent(new CustomEvent('variantUnitPriceChange', { detail: { variant: e } }));
            },
            _updateSKU: function (e) {
              (!this.currentVariant || e.sku !== this.currentVariant.sku) &&
                this.container.dispatchEvent(new CustomEvent('variantSKUChange', { detail: { variant: e } }));
            },
            _updateHistoryState: function (e) {
              if (history.replaceState && e) {
                var t =
                  window.location.protocol +
                  '//' +
                  window.location.host +
                  window.location.pathname +
                  '?variant=' +
                  e.id;
                window.history.replaceState({ path: t }, '', t);
              }
            },
            _updateMasterSelect: function (e) {
              let t = this.container.querySelector(this.originalSelectorId);
              t && ((t.value = e.id), t.dispatchEvent(new Event('change', { bubbles: !0 })));
            },
          })),
          e
        );
      })()),
      (window.vimeoApiReady = function () {
        var e, t;
        (theme.config.vimeoLoading = !0),
          new Promise((i, s) => {
            (e = setInterval(function () {
              Vimeo && (clearInterval(e), clearTimeout(t), i());
            }, 500)),
              (t = setTimeout(function () {
                clearInterval(e), s();
              }, 4e3));
          }).then(function () {
            (theme.config.vimeoReady = !0),
              (theme.config.vimeoLoading = !1),
              document.dispatchEvent(new CustomEvent('vimeoReady'));
          });
      }),
      (theme.VimeoPlayer = (function () {
        var e = { loading: 'loading', loaded: 'loaded', interactable: 'video-interactable' },
          t = { byline: !1, loop: !0, muted: !0, playsinline: !0, portrait: !1, title: !1 };
        function i(e, t, i) {
          (this.divId = e),
            (this.el = document.getElementById(e)),
            (this.videoId = t),
            (this.iframe = null),
            (this.options = i),
            this.options && this.options.videoParent && (this.parent = this.el.closest(this.options.videoParent)),
            this.setAsLoading(),
            theme.config.vimeoReady
              ? this.init()
              : (theme.LibraryLoader.load('vimeo', window.vimeoApiReady),
                document.addEventListener('vimeoReady', this.init.bind(this)));
        }
        return (
          (i.prototype = Object.assign({}, i.prototype, {
            init: function () {
              var e = t;
              (e.id = this.videoId),
                (this.videoPlayer = new Vimeo.Player(this.el, e)),
                this.videoPlayer.ready().then(this.playerReady.bind(this));
            },
            playerReady: function () {
              (this.iframe = this.el.querySelector('iframe')),
                this.iframe.setAttribute('tabindex', '-1'),
                'false' === this.options.loop && this.videoPlayer.setLoop(!1),
                'sound' === this.options.style ? this.videoPlayer.setVolume(1) : this.videoPlayer.setVolume(0),
                this.setAsLoaded(),
                new IntersectionObserver(
                  (e, t) => {
                    e.forEach((e) => {
                      e.isIntersecting ? this.play() : this.pause();
                    });
                  },
                  { rootMargin: '0px 0px 50px 0px' }
                ).observe(this.iframe);
            },
            setAsLoading: function () {
              this.parent && this.parent.classList.add(e.loading);
            },
            setAsLoaded: function () {
              this.parent &&
                (this.parent.classList.remove(e.loading),
                this.parent.classList.add(e.loaded),
                this.parent.classList.add(e.interactable),
                Shopify && Shopify.designMode && window.AOS && AOS.refreshHard());
            },
            enableInteraction: function () {
              this.parent && this.parent.classList.add(e.interactable);
            },
            play: function () {
              this.videoPlayer && 'function' == typeof this.videoPlayer.play && this.videoPlayer.play();
            },
            pause: function () {
              this.videoPlayer && 'function' == typeof this.videoPlayer.pause && this.videoPlayer.pause();
            },
            destroy: function () {
              this.videoPlayer && 'function' == typeof this.videoPlayer.destroy && this.videoPlayer.destroy();
            },
          })),
          i
        );
      })()),
      (window.onYouTubeIframeAPIReady = function () {
        (theme.config.youTubeReady = !0), document.dispatchEvent(new CustomEvent('youTubeReady'));
      }),
      (theme.YouTube = (function () {
        var e = { loading: 'loading', loaded: 'loaded', interactable: 'video-interactable' },
          t = {
            width: 1280,
            height: 720,
            playerVars: {
              autohide: 0,
              autoplay: 1,
              cc_load_policy: 0,
              controls: 0,
              fs: 0,
              iv_load_policy: 3,
              modestbranding: 1,
              playsinline: 1,
              rel: 0,
            },
          };
        function i(e, i) {
          (this.divId = e),
            (this.iframe = null),
            (this.attemptedToPlay = !1),
            (t.events = {
              onReady: this.onVideoPlayerReady.bind(this),
              onStateChange: this.onVideoStateChange.bind(this),
            }),
            (this.options = Object.assign({}, t, i)),
            this.options &&
              (this.options.videoParent &&
                (this.parent = document.getElementById(this.divId).closest(this.options.videoParent)),
              this.options.autoplay || (this.options.playerVars.autoplay = this.options.autoplay),
              'sound' === this.options.style &&
                ((this.options.playerVars.controls = 1), (this.options.playerVars.autoplay = 0))),
            this.setAsLoading(),
            theme.config.youTubeReady
              ? this.init()
              : (theme.LibraryLoader.load('youtubeSdk'),
                document.addEventListener('youTubeReady', this.init.bind(this)));
        }
        return (
          (i.prototype = Object.assign({}, i.prototype, {
            init: function () {
              this.videoPlayer = new YT.Player(this.divId, this.options);
            },
            onVideoPlayerReady: function (e) {
              (this.iframe = document.getElementById(this.divId)),
                this.iframe.setAttribute('tabindex', '-1'),
                'sound' !== this.options.style && e.target.mute(),
                new IntersectionObserver(
                  (e, t) => {
                    e.forEach((e) => {
                      e.isIntersecting ? this.play() : this.pause();
                    });
                  },
                  { rootMargin: '0px 0px 50px 0px' }
                ).observe(this.iframe);
            },
            onVideoStateChange: function (e) {
              switch (e.data) {
                case -1:
                  this.attemptedToPlay && (this.setAsLoaded(), this.enableInteraction());
                  break;
                case 0:
                  this.play(e);
                  break;
                case 1:
                  this.setAsLoaded();
                  break;
                case 3:
                  this.attemptedToPlay = !0;
              }
            },
            setAsLoading: function () {
              this.parent && this.parent.classList.add(e.loading);
            },
            setAsLoaded: function () {
              this.parent &&
                (this.parent.classList.remove(e.loading),
                this.parent.classList.add(e.loaded),
                Shopify && Shopify.designMode && window.AOS && AOS.refreshHard());
            },
            enableInteraction: function () {
              this.parent && this.parent.classList.add(e.interactable);
            },
            play: function () {
              this.videoPlayer && 'function' == typeof this.videoPlayer.playVideo && this.videoPlayer.playVideo();
            },
            pause: function () {
              this.videoPlayer && 'function' == typeof this.videoPlayer.pauseVideo && this.videoPlayer.pauseVideo();
            },
            destroy: function () {
              this.videoPlayer && 'function' == typeof this.videoPlayer.destroy && this.videoPlayer.destroy();
            },
          })),
          i
        );
      })()),
      (o = !1),
      document.body.addEventListener('touchstart', function (e) {
        if (!e.target.closest('.flickity-slider')) return (o = !1);
        (o = !0), (a = { x: e.touches[0].pageX, y: e.touches[0].pageY });
      }),
      document.body.addEventListener(
        'touchmove',
        function (e) {
          o &&
            e.cancelable &&
            Math.abs({ x: e.touches[0].pageX - a.x, y: e.touches[0].pageY - a.y }.x) >
              Flickity.defaults.dragThreshold &&
            e.preventDefault();
        },
        { passive: !1 }
      ),
      (theme.AjaxRenderer = (function () {
        function e({ sections: e, onReplace: t, debug: i } = {}) {
          (this.sections = e || []), (this.cachedSections = []), (this.onReplace = t), (this.debug = Boolean(i));
        }
        return (
          (e.prototype = Object.assign({}, e.prototype, {
            renderPage: function (e, t, i = !0) {
              let s = new URLSearchParams(window.location.search),
                n = this.getUpdatedParams(s, t),
                a = this.sections.map((t) => {
                  let i = `${e}?section_id=${t.sectionId}&${n.toString()}`,
                    s = (e) => e.url === i;
                  return this.cachedSections.some(s)
                    ? this.renderSectionFromCache(s, t)
                    : this.renderSectionFromFetch(i, t);
                });
              return i && this.updateURLHash(n), Promise.all(a);
            },
            renderSectionFromCache: function (e, t) {
              let i = this.cachedSections.find(e);
              return (
                this.log(`[AjaxRenderer] rendering from cache: url=${i.url}`),
                this.renderSection(i.html, t),
                Promise.resolve(t)
              );
            },
            renderSectionFromFetch: function (e, t) {
              return (
                this.log(`[AjaxRenderer] redering from fetch: url=${e}`),
                new Promise((i, s) => {
                  fetch(e)
                    .then((e) => e.text())
                    .then((s) => {
                      let n = s;
                      (this.cachedSections = [...this.cachedSections, { html: n, url: e }]),
                        this.renderSection(n, t),
                        i(t);
                    })
                    .catch((e) => s(e));
                })
              );
            },
            renderSection: function (e, t) {
              this.log(`[AjaxRenderer] rendering section: section=${JSON.stringify(t)}`);
              let i = new DOMParser().parseFromString(e, 'text/html');
              if (this.onReplace) this.onReplace(i, t);
              else if ('string' == typeof t.nodeId) {
                var s = i.getElementById(t.nodeId);
                if (!s) return;
                document.getElementById(t.nodeId).innerHTML = s.innerHTML;
              } else
                t.nodeId.forEach((e) => {
                  document.getElementById(e).innerHTML = i.getElementById(e).innerHTML;
                });
              return t;
            },
            getUpdatedParams: function (e, t) {
              let i = new URLSearchParams(e),
                s = ['sort_by', 'q', 'options[prefix]', 'type'];
              for (let [n, a] of i.entries()) t.getAll(n).includes(a) || s.includes(n) || i.delete(n);
              for (let [o, r] of t.entries()) i.getAll(o).includes(r) || '' === r || i.append(o, r);
              return i;
            },
            updateURLHash: function (e) {
              history.pushState({}, '', `${window.location.pathname}${e && '?'.concat(e)}`);
            },
            log: function (...e) {
              this.debug && console.log(...e);
            },
          })),
          e
        );
      })()),
      window.Shopify &&
        window.Shopify.theme &&
        navigator &&
        navigator.sendBeacon &&
        window.Shopify.designMode &&
        navigator.sendBeacon(
          'https://api.archetypethemes.co/api/beacon',
          new URLSearchParams({
            shop: window.Shopify.shop,
            themeName:
              window.theme &&
              window.theme.settings &&
              `${window.theme.settings.themeName} v${window.theme.settings.themeVersion}`,
            role: window.Shopify.theme.role,
            route: window.location.pathname,
            themeId: window.Shopify.theme.id,
            themeStoreId: window.Shopify.theme.theme_store_id || 0,
            isThemeEditor: !!window.Shopify.designMode,
          })
        ),
      (theme.cart = {
        getCart: function () {
          return fetch(''.concat(theme.routes.cart, '?t=').concat(Date.now()), {
            credentials: 'same-origin',
            method: 'GET',
          }).then((e) => e.json());
        },
        getCartProductMarkup: function () {
          var e = ''.concat(theme.routes.cartPage, '?t=').concat(Date.now());
          return fetch((e = -1 === e.indexOf('?') ? e + '?view=ajax' : e + '&view=ajax'), {
            credentials: 'same-origin',
            method: 'GET',
          }).then(function (e) {
            return e.text();
          });
        },
        changeItem: function (e, t) {
          return this._updateCart({
            url: ''.concat(theme.routes.cartChange, '?t=').concat(Date.now()),
            data: JSON.stringify({ id: e, quantity: t }),
          });
        },
        _updateCart: function (e) {
          return fetch(e.url, {
            method: 'POST',
            body: e.data,
            credentials: 'same-origin',
            headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
          })
            .then((e) => e.json())
            .then(function (e) {
              return e;
            });
        },
        updateAttribute: function (e, t) {
          return this._updateCart({
            url: '/cart/update.js',
            data: JSON.stringify({ attributes: { [e]: theme.cart.attributeToString(t) } }),
          });
        },
        updateNote: function (e) {
          return this._updateCart({
            url: '/cart/update.js',
            data: JSON.stringify({ note: theme.cart.attributeToString(e) }),
          });
        },
        attributeToString: function (e) {
          return 'string' != typeof e && 'undefined' == (e += '') && (e = ''), e.trim();
        },
      }),
      (theme.CartForm = (function () {
        var e = {
            products: '[data-products]',
            qtySelector: '.js-qty__wrapper',
            discounts: '[data-discounts]',
            savings: '[data-savings]',
            subTotal: '[data-subtotal]',
            cartBubble: '.cart-link__bubble',
            cartNote: '[name="note"]',
            termsCheckbox: '.cart__terms-checkbox',
            checkoutBtn: '.cart__checkout',
          },
          t = { btnLoading: 'btn--loading' },
          i = { requiresTerms: !1 };
        function s(t) {
          t &&
            ((this.form = t),
            (this.wrapper = t.parentNode),
            (this.location = t.dataset.location),
            (this.namespace = '.cart-' + this.location),
            (this.products = t.querySelector(e.products)),
            (this.submitBtn = t.querySelector(e.checkoutBtn)),
            (this.discounts = t.querySelector(e.discounts)),
            (this.savings = t.querySelector(e.savings)),
            (this.subtotal = t.querySelector(e.subTotal)),
            (this.termsCheckbox = t.querySelector(e.termsCheckbox)),
            (this.noteInput = t.querySelector(e.cartNote)),
            this.termsCheckbox && (i.requiresTerms = !0),
            this.init());
        }
        return (
          (s.prototype = Object.assign({}, s.prototype, {
            init: function () {
              this.initQtySelectors(),
                document.addEventListener('cart:quantity' + this.namespace, this.quantityChanged.bind(this)),
                this.form.on('submit' + this.namespace, this.onSubmit.bind(this)),
                this.noteInput &&
                  this.noteInput.addEventListener('change', function () {
                    var e = this.value;
                    theme.cart.updateNote(e);
                  }),
                document.addEventListener(
                  'cart:build',
                  function () {
                    this.buildCart();
                  }.bind(this)
                );
            },
            reInit: function () {
              this.initQtySelectors();
            },
            onSubmit: function (e) {
              if ((this.submitBtn.classList.add(t.btnLoading), i.requiresTerms && !this.termsCheckbox.checked))
                return (
                  alert(theme.strings.cartTermsConfirmation),
                  this.submitBtn.classList.remove(t.btnLoading),
                  e.preventDefault(),
                  !1
                );
            },
            _parseProductHTML: function (e) {
              var t = new DOMParser().parseFromString(e, 'text/html');
              return { items: t.querySelector('.cart__items'), discounts: t.querySelector('.cart__discounts') };
            },
            buildCart: function () {
              theme.cart.getCartProductMarkup().then(this.cartMarkup.bind(this));
            },
            cartMarkup: function (e) {
              var t = this._parseProductHTML(e),
                i = t.items,
                s = parseInt(i.dataset.count),
                n = i.dataset.cartSubtotal,
                a = i.dataset.cartSavings;
              this.updateCartDiscounts(t.discounts),
                this.updateSavings(a),
                s > 0 ? this.wrapper.classList.remove('is-empty') : this.wrapper.classList.add('is-empty'),
                this.updateCount(s),
                (this.products.innerHTML = ''),
                this.products.append(i),
                (this.subtotal.innerHTML = theme.Currency.formatMoney(n, theme.settings.moneyFormat)),
                this.reInit(),
                window.AOS && AOS.refreshHard(),
                Shopify && Shopify.StorefrontExpressButtons && Shopify.StorefrontExpressButtons.initialize();
            },
            updateCartDiscounts: function (e) {
              this.discounts && ((this.discounts.innerHTML = ''), this.discounts.append(e));
            },
            initQtySelectors: function () {
              this.form.querySelectorAll(e.qtySelector).forEach((e) => {
                new theme.QtySelector(e, { namespace: this.namespace, isCart: !0 });
              });
            },
            quantityChanged: function (e) {
              var t = e.detail[0],
                i = e.detail[1],
                s = e.detail[2];
              t &&
                i &&
                (s && s.classList.add('is-loading'),
                theme.cart
                  .changeItem(t, i)
                  .then(
                    function (e) {
                      e.item_count > 0
                        ? this.wrapper.classList.remove('is-empty')
                        : this.wrapper.classList.add('is-empty'),
                        this.buildCart(),
                        document.dispatchEvent(new CustomEvent('cart:updated', { detail: { cart: e } }));
                    }.bind(this)
                  )
                  .catch(function (e) {}));
            },
            updateSubtotal: function (t) {
              this.form.querySelector(e.subTotal).innerHTML = theme.Currency.formatMoney(t, theme.settings.moneyFormat);
            },
            updateSavings: function (e) {
              if (this.savings) {
                if (e > 0) {
                  var t = theme.Currency.formatMoney(e, theme.settings.moneyFormat);
                  this.savings.classList.remove('hide'),
                    (this.savings.innerHTML = theme.strings.cartSavings.replace('[savings]', t));
                } else this.savings.classList.add('hide');
              }
            },
            updateCount: function (t) {
              var i = document.querySelectorAll('.cart-link__bubble-num');
              i.length &&
                i.forEach((e) => {
                  e.innerText = t;
                });
              var s = document.querySelectorAll(e.cartBubble);
              s.length &&
                (t > 0
                  ? s.forEach((e) => {
                      e.classList.add('cart-link__bubble--visible');
                    })
                  : s.forEach((e) => {
                      e.classList.remove('cart-link__bubble--visible');
                    }));
            },
          })),
          s
        );
      })()),
      (theme.collapsibles = (function () {
        var e = {
            trigger: '.collapsible-trigger',
            module: '.collapsible-content',
            moduleInner: '.collapsible-content__inner',
            tabs: '.collapsible-trigger--tab',
          },
          t = {
            hide: 'hide',
            open: 'is-open',
            autoHeight: 'collapsible--auto-height',
            tabs: 'collapsible-trigger--tab',
          },
          i = '.collapsible',
          s = !1;
        function n(n) {
          if (!s) {
            s = !0;
            var o,
              r = n.currentTarget,
              c = r.classList.contains(t.open),
              l = r.classList.contains(t.tabs),
              d = r.getAttribute('aria-controls'),
              h = document.getElementById(d);
            if ((d || (d = r.dataset.controls), d)) {
              if (
                (!h &&
                  document.querySelectorAll('[data-id="' + d + '"]').length > 0 &&
                  (h = r.parentNode.querySelector('[data-id="' + d + '"]')),
                !h)
              ) {
                s = !1;
                return;
              }
              var u = h.querySelector(e.moduleInner).offsetHeight,
                p = h.classList.contains(t.autoHeight),
                f = h.parentNode.closest(e.module),
                m = u;
              if (l) {
                if (c) {
                  s = !1;
                  return;
                }
                document.querySelectorAll(e.tabs + '[data-id="' + r.dataset.id + '"]').forEach((e) => {
                  e.classList.remove(t.open),
                    a((o = document.querySelector('#' + e.getAttribute('aria-controls'))), 0, !0);
                });
              }
              if (
                (c &&
                  p &&
                  setTimeout(function () {
                    a(h, (u = 0), c, p);
                  }, 0),
                c && !p && (u = 0),
                r.setAttribute('aria-expanded', !c),
                c ? r.classList.remove(t.open) : r.classList.add(t.open),
                a(h, u, c, p),
                f)
              ) {
                var g = f.style.height;
                c && 'auto' === g && (m = 0);
                var v = c ? f.offsetHeight - m : u + f.offsetHeight;
                a(f, v, !1, !1);
              }
              if (window.SPR) {
                var y = h.querySelector('.spr-summary-actions-newreview');
                if (!y) return;
                y.off('click' + i),
                  y.on('click' + i, function () {
                    (u = h.querySelector(e.moduleInner).offsetHeight), a(h, u, c, p);
                  });
              }
            }
          }
        }
        function a(e, i, n, a) {
          if (
            (e.classList.remove(t.hide),
            theme.utils.prepareTransition(e, function () {
              (e.style.height = i + 'px'), n ? e.classList.remove(t.open) : e.classList.add(t.open);
            }),
            !n && a)
          ) {
            var o = e;
            window.setTimeout(function () {
              o.css('height', 'auto'), (s = !1);
            }, 500);
          } else s = !1;
        }
        return {
          init: function s(a) {
            (a || document).querySelectorAll(e.trigger).forEach((e) => {
              var s = e.classList.contains(t.open);
              e.setAttribute('aria-expanded', s), e.off('click' + i), e.on('click' + i, n);
            });
          },
        };
      })()),
      (theme.Disclosure = (function () {
        var e = {
            disclosureForm: '[data-disclosure-form]',
            disclosureList: '[data-disclosure-list]',
            disclosureToggle: '[data-disclosure-toggle]',
            disclosureInput: '[data-disclosure-input]',
            disclosureOptions: '[data-disclosure-option]',
          },
          t = { listVisible: 'disclosure-list--visible' };
        function i(e) {
          (this.container = e), this._cacheSelectors(), this._setupListeners();
        }
        return (
          (i.prototype = Object.assign({}, i.prototype, {
            _cacheSelectors: function () {
              this.cache = {
                disclosureForm: this.container.closest(e.disclosureForm),
                disclosureList: this.container.querySelector(e.disclosureList),
                disclosureToggle: this.container.querySelector(e.disclosureToggle),
                disclosureInput: this.container.querySelector(e.disclosureInput),
                disclosureOptions: this.container.querySelectorAll(e.disclosureOptions),
              };
            },
            _setupListeners: function () {
              (this.eventHandlers = this._setupEventHandlers()),
                this.cache.disclosureToggle.addEventListener('click', this.eventHandlers.toggleList),
                this.cache.disclosureOptions.forEach(function (e) {
                  e.addEventListener('click', this.eventHandlers.connectOptions);
                }, this),
                this.container.addEventListener('keyup', this.eventHandlers.onDisclosureKeyUp),
                this.cache.disclosureList.addEventListener('focusout', this.eventHandlers.onDisclosureListFocusOut),
                this.cache.disclosureToggle.addEventListener('focusout', this.eventHandlers.onDisclosureToggleFocusOut),
                document.body.addEventListener('click', this.eventHandlers.onBodyClick);
            },
            _setupEventHandlers: function () {
              return {
                connectOptions: this._connectOptions.bind(this),
                toggleList: this._toggleList.bind(this),
                onBodyClick: this._onBodyClick.bind(this),
                onDisclosureKeyUp: this._onDisclosureKeyUp.bind(this),
                onDisclosureListFocusOut: this._onDisclosureListFocusOut.bind(this),
                onDisclosureToggleFocusOut: this._onDisclosureToggleFocusOut.bind(this),
              };
            },
            _connectOptions: function (e) {
              e.preventDefault(), this._submitForm(e.currentTarget.dataset.value);
            },
            _onDisclosureToggleFocusOut: function (e) {
              !1 === this.container.contains(e.relatedTarget) && this._hideList();
            },
            _onDisclosureListFocusOut: function (e) {
              var i = e.currentTarget.contains(e.relatedTarget);
              this.cache.disclosureList.classList.contains(t.listVisible) && !i && this._hideList();
            },
            _onDisclosureKeyUp: function (e) {
              27 === e.which && (this._hideList(), this.cache.disclosureToggle.focus());
            },
            _onBodyClick: function (e) {
              var i = this.container.contains(e.target);
              this.cache.disclosureList.classList.contains(t.listVisible) && !i && this._hideList();
            },
            _submitForm: function (e) {
              (this.cache.disclosureInput.value = e), this.cache.disclosureForm.submit();
            },
            _hideList: function () {
              this.cache.disclosureList.classList.remove(t.listVisible),
                this.cache.disclosureToggle.setAttribute('aria-expanded', !1);
            },
            _toggleList: function () {
              var e = 'true' === this.cache.disclosureToggle.getAttribute('aria-expanded');
              this.cache.disclosureList.classList.toggle(t.listVisible),
                this.cache.disclosureToggle.setAttribute('aria-expanded', !e);
            },
            destroy: function () {
              this.cache.disclosureToggle.removeEventListener('click', this.eventHandlers.toggleList),
                this.cache.disclosureOptions.forEach(function (e) {
                  e.removeEventListener('click', this.eventHandlers.connectOptions);
                }, this),
                this.container.removeEventListener('keyup', this.eventHandlers.onDisclosureKeyUp),
                this.cache.disclosureList.removeEventListener('focusout', this.eventHandlers.onDisclosureListFocusOut),
                this.cache.disclosureToggle.removeEventListener(
                  'focusout',
                  this.eventHandlers.onDisclosureToggleFocusOut
                ),
                document.body.removeEventListener('click', this.eventHandlers.onBodyClick);
            },
          })),
          i
        );
      })()),
      (theme.Drawers = (function () {
        function e(e, t) {
          (this.config = {
            id: e,
            close: '.js-drawer-close',
            open: '.js-drawer-open-' + t,
            openClass: 'js-drawer-open',
            closingClass: 'js-drawer-closing',
            activeDrawer: 'drawer--is-open',
            namespace: '.drawer-' + t,
          }),
            (this.nodes = { page: document.querySelector('#MainContent') }),
            (this.drawer = document.querySelector('#' + e)),
            (this.isOpen = !1),
            this.drawer && this.init();
        }
        return (
          (e.prototype = Object.assign({}, e.prototype, {
            init: function () {
              document.querySelectorAll(this.config.open).forEach((e) => {
                e.setAttribute('aria-expanded', 'false'), e.addEventListener('click', this.open.bind(this));
              }),
                this.drawer.querySelector(this.config.close).addEventListener('click', this.close.bind(this)),
                document.addEventListener(
                  'modalOpen',
                  function () {
                    this.close();
                  }.bind(this)
                );
            },
            open: function (e, t) {
              e && e.preventDefault(),
                !this.isOpen &&
                  (e && e.stopPropagation
                    ? (e.stopPropagation(),
                      e.currentTarget.setAttribute('aria-expanded', 'true'),
                      (this.activeSource = e.currentTarget))
                    : t && (t.setAttribute('aria-expanded', 'true'), (this.activeSource = t)),
                  theme.utils.prepareTransition(
                    this.drawer,
                    function () {
                      this.drawer.classList.add(this.config.activeDrawer);
                    }.bind(this)
                  ),
                  document.documentElement.classList.add(this.config.openClass),
                  (this.isOpen = !0),
                  theme.a11y.trapFocus({ container: this.drawer, namespace: 'drawer_focus' }),
                  document.dispatchEvent(new CustomEvent('drawerOpen')),
                  document.dispatchEvent(new CustomEvent('drawerOpen.' + this.config.id)),
                  this.bindEvents());
            },
            close: function (e) {
              if (this.isOpen) {
                if (e) {
                  if (e.target.closest('.js-drawer-close'));
                  else if (e.target.closest('.drawer')) return;
                }
                document.activeElement.blur(),
                  theme.utils.prepareTransition(
                    this.drawer,
                    function () {
                      this.drawer.classList.remove(this.config.activeDrawer);
                    }.bind(this)
                  ),
                  document.documentElement.classList.remove(this.config.openClass),
                  document.documentElement.classList.add(this.config.closingClass),
                  window.setTimeout(
                    function () {
                      document.documentElement.classList.remove(this.config.closingClass),
                        this.activeSource &&
                          this.activeSource.getAttribute('aria-expanded') &&
                          (this.activeSource.setAttribute('aria-expanded', 'false'), this.activeSource.focus());
                    }.bind(this),
                    500
                  ),
                  (this.isOpen = !1),
                  theme.a11y.removeTrapFocus({ container: this.drawer, namespace: 'drawer_focus' }),
                  this.unbindEvents();
              }
            },
            bindEvents: function () {
              window.on(
                'click' + this.config.namespace,
                function (e) {
                  this.close(e);
                }.bind(this)
              ),
                window.on(
                  'keyup' + this.config.namespace,
                  function (e) {
                    27 === e.keyCode && this.close();
                  }.bind(this)
                ),
                theme.a11y.lockMobileScrolling(this.config.namespace, this.nodes.page);
            },
            unbindEvents: function () {
              window.off('click' + this.config.namespace),
                window.off('keyup' + this.config.namespace),
                theme.a11y.unlockMobileScrolling(this.config.namespace, this.nodes.page);
            },
          })),
          e
        );
      })()),
      (theme.Modals = (function () {
        function e(e, t, i) {
          if (((this.id = e), (this.modal = document.getElementById(e)), !this.modal)) return !1;
          (this.modalContent = this.modal.querySelector('.modal__inner')),
            (this.config = Object.assign(
              {
                close: '.js-modal-close',
                open: '.js-modal-open-' + t,
                openClass: 'modal--is-active',
                closingClass: 'modal--is-closing',
                bodyOpenClass: ['modal-open'],
                bodyOpenSolidClass: 'modal-open--solid',
                bodyClosingClass: 'modal-closing',
                closeOffContentClick: !0,
              },
              i
            )),
            (this.modalIsOpen = !1),
            (this.focusOnOpen = this.config.focusIdOnOpen
              ? document.getElementById(this.config.focusIdOnOpen)
              : this.modal),
            (this.isSolid = this.config.solid),
            this.init();
        }
        return (
          (e.prototype.init = function () {
            document.querySelectorAll(this.config.open).forEach((e) => {
              e.setAttribute('aria-expanded', 'false'), e.addEventListener('click', this.open.bind(this));
            }),
              this.modal.querySelectorAll(this.config.close).forEach((e) => {
                e.addEventListener('click', this.close.bind(this));
              }),
              document.addEventListener(
                'drawerOpen',
                function () {
                  this.close();
                }.bind(this)
              );
          }),
          (e.prototype.open = function (e) {
            var t = !1;
            !this.modalIsOpen &&
              (e ? e.preventDefault() : (t = !0),
              e &&
                e.stopPropagation &&
                (e.stopPropagation(), (this.activeSource = e.currentTarget.setAttribute('aria-expanded', 'true'))),
              this.modalIsOpen && !t && this.close(),
              this.modal.classList.add(this.config.openClass),
              document.documentElement.classList.add(...this.config.bodyOpenClass),
              this.isSolid && document.documentElement.classList.add(this.config.bodyOpenSolidClass),
              (this.modalIsOpen = !0),
              theme.a11y.trapFocus({
                container: this.modal,
                elementToFocus: this.focusOnOpen,
                namespace: 'modal_focus',
              }),
              document.dispatchEvent(new CustomEvent('modalOpen')),
              document.dispatchEvent(new CustomEvent('modalOpen.' + this.id)),
              this.bindEvents());
          }),
          (e.prototype.close = function (e) {
            if (this.modalIsOpen) {
              if (e) {
                if (e.target.closest('.js-modal-close'));
                else if (e.target.closest('.modal__inner')) return;
              }
              document.activeElement.blur(),
                this.modal.classList.remove(this.config.openClass),
                this.modal.classList.add(this.config.closingClass),
                document.documentElement.classList.remove(...this.config.bodyOpenClass),
                document.documentElement.classList.add(this.config.bodyClosingClass),
                window.setTimeout(
                  function () {
                    document.documentElement.classList.remove(this.config.bodyClosingClass),
                      this.modal.classList.remove(this.config.closingClass),
                      this.activeSource &&
                        this.activeSource.getAttribute('aria-expanded') &&
                        this.activeSource.setAttribute('aria-expanded', 'false').focus();
                  }.bind(this),
                  500
                ),
                this.isSolid && document.documentElement.classList.remove(this.config.bodyOpenSolidClass),
                (this.modalIsOpen = !1),
                theme.a11y.removeTrapFocus({ container: this.modal, namespace: 'modal_focus' }),
                document.dispatchEvent(new CustomEvent('modalClose.' + this.id)),
                this.unbindEvents();
            }
          }),
          (e.prototype.bindEvents = function () {
            window.on(
              'keyup.modal',
              function (e) {
                27 === e.keyCode && this.close();
              }.bind(this)
            ),
              this.config.closeOffContentClick && this.modal.on('click.modal', this.close.bind(this));
          }),
          (e.prototype.unbindEvents = function () {
            document.documentElement.off('.modal'), this.config.closeOffContentClick && this.modal.off('.modal');
          }),
          e
        );
      })());
    class c extends HTMLElement {
      constructor() {
        super(),
          (this.parallaxImage = this.querySelector('[data-parallax-image]')),
          (this.windowInnerHeight = window.innerHeight),
          (this.isActive = !1),
          (this.timeout = null),
          (this.directionMap = { right: 0, top: 90, left: 180, bottom: 270 }),
          (this.directionMultipliers = { 0: [1, 0], 90: [0, -1], 180: [-1, 0], 270: [0, 1] }),
          this.init(),
          window.addEventListener('scroll', () => this.scrollHandler());
      }
      getParallaxInfo() {
        let { width: e, height: t, top: i } = this.parallaxImage.getBoundingClientRect(),
          s = this.parallaxImage,
          n,
          { angle: a, movement: o } = s.dataset,
          r = 'top' === a ? Math.ceil(t * (parseFloat(o) / 100)) : Math.ceil(e * (parseFloat(o) / 100));
        (a = this.directionMap[a] ?? parseFloat(a)) != a && (a = 270),
          r != r && (r = 100),
          (a %= 360) < 0 && (a += 360);
        let c = a > 90 && a < 270,
          l = a < 180;
        if (((s.style[c ? 'left' : 'right'] = 0), (s.style[l ? 'top' : 'bottom'] = 0), a % 90)) {
          let d = (a * Math.PI) / 180;
          n = [Math.cos(d), -1 * Math.sin(d)];
        } else n = this.directionMultipliers[a];
        return (
          n[0] && (s.style.width = `calc(100% + ${r * Math.abs(n[0])}px)`),
          n[1] && (s.style.height = `calc(100% + ${r * Math.abs(n[1])}px)`),
          { element: s, movementPixels: r, multipliers: n, top: i, height: t }
        );
      }
      init() {
        let { element: e, movementPixels: t, multipliers: i, top: s, height: n } = this.getParallaxInfo(),
          a = this.windowInnerHeight - s,
          o = this.windowInnerHeight + n,
          r = a / o;
        if (r > -0.1 && r < 1.1) {
          let c = Math.min(Math.max(r, 0), 1) * t;
          e.style.transform = `translate3d(${c * i[0]}px, ${c * i[1]}px, 0)`;
        }
        this.isActive && requestAnimationFrame(this.init.bind(this));
      }
      scrollHandler() {
        this.isActive
          ? clearTimeout(this.timeout)
          : ((this.isActive = !0), requestAnimationFrame(this.init.bind(this))),
          (this.timeout = setTimeout(() => (this.isActive = !1), 20));
      }
    }
    if ((customElements.define('parallax-image', c), void 0 === window.noUiSlider))
      throw Error('theme.PriceRange is missing vendor noUiSlider: // =require vendor/nouislider.js');
    (theme.PriceRange = (function () {
      var e = {
        priceRange: '.price-range',
        priceRangeSlider: '.price-range__slider',
        priceRangeInputMin: '.price-range__input-min',
        priceRangeInputMax: '.price-range__input-max',
        priceRangeDisplayMin: '.price-range__display-min',
        priceRangeDisplayMax: '.price-range__display-max',
      };
      function t(e, { onChange: t, onUpdate: i, ...s } = {}) {
        return (
          (this.container = e), (this.onChange = t), (this.onUpdate = i), (this.sliderOptions = s || {}), this.init()
        );
      }
      return (
        (t.prototype = Object.assign({}, t.prototype, {
          init: function () {
            if (!this.container.classList.contains('price-range'))
              throw Error('You must instantiate PriceRange with a valid container');
            return (
              (this.formEl = this.container.closest('form')),
              (this.sliderEl = this.container.querySelector(e.priceRangeSlider)),
              (this.inputMinEl = this.container.querySelector(e.priceRangeInputMin)),
              (this.inputMaxEl = this.container.querySelector(e.priceRangeInputMax)),
              (this.displayMinEl = this.container.querySelector(e.priceRangeDisplayMin)),
              (this.displayMaxEl = this.container.querySelector(e.priceRangeDisplayMax)),
              (this.minRange = parseFloat(this.container.dataset.min) || 0),
              (this.minValue = parseFloat(this.container.dataset.minValue) || 0),
              (this.maxRange = parseFloat(this.container.dataset.max) || 100),
              (this.maxValue = parseFloat(this.container.dataset.maxValue) || this.maxRange),
              this.createPriceRange()
            );
          },
          createPriceRange: function () {
            this.sliderEl &&
              this.sliderEl.noUiSlider &&
              'function' == typeof this.sliderEl.noUiSlider.destroy &&
              this.sliderEl.noUiSlider.destroy();
            var e = noUiSlider.create(this.sliderEl, {
              connect: !0,
              step: 10,
              ...this.sliderOptions,
              start: [this.minValue, this.maxValue],
              range: { min: this.minRange, max: this.maxRange },
            });
            return (
              e.on('update', (e) => {
                (this.displayMinEl.innerHTML = theme.Currency.formatMoney(e[0], theme.settings.moneyFormat)),
                  (this.displayMaxEl.innerHTML = theme.Currency.formatMoney(e[1], theme.settings.moneyFormat)),
                  this.onUpdate && this.onUpdate(e);
              }),
              e.on('change', (e) => {
                if (((this.inputMinEl.value = e[0]), (this.inputMaxEl.value = e[1]), this.onChange)) {
                  let t = new FormData(this.formEl);
                  this.onChange(t);
                }
              }),
              e
            );
          },
        })),
        t
      );
    })()),
      (theme.AjaxProduct = (function () {
        var e = { loading: !1 };
        function t(e, t, i) {
          (this.form = e),
            (this.args = i),
            this.form &&
              ((this.addToCart = e.querySelector(t || '.add-to-cart')),
              this.form.addEventListener('submit', this.addItemFromForm.bind(this)));
        }
        return (
          (t.prototype = Object.assign({}, t.prototype, {
            addItemFromForm: function (t, i) {
              if ((t.preventDefault(), !e.loading)) {
                this.addToCart.classList.add('btn--loading'), (e.loading = !0);
                var s = theme.utils.serialize(this.form);
                fetch(theme.routes.cartAdd, {
                  method: 'POST',
                  body: s,
                  credentials: 'same-origin',
                  headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest',
                  },
                })
                  .then((e) => e.json())
                  .then(
                    function (t) {
                      422 === t.status ? this.error(t) : this.success(t),
                        (e.loading = !1),
                        this.addToCart.classList.remove('btn--loading'),
                        document.body.classList.contains('template-cart') && (window.scrollTo(0, 0), location.reload());
                    }.bind(this)
                  );
              }
            },
            success: function (e) {
              var t = this.form.querySelector('.errors');
              t && t.remove(),
                document.dispatchEvent(
                  new CustomEvent('ajaxProduct:added', { detail: { product: e, addToCartBtn: this.addToCart } })
                ),
                this.args &&
                  this.args.scopedEventId &&
                  document.dispatchEvent(
                    new CustomEvent('ajaxProduct:added:' + this.args.scopedEventId, {
                      detail: { product: e, addToCartBtn: this.addToCart },
                    })
                  );
            },
            error: function (e) {
              if (!e.description) {
                console.warn(e);
                return;
              }
              var t = this.form.querySelector('.errors');
              t && t.remove();
              var i = document.createElement('div');
              i.classList.add('errors', 'text-center'),
                (i.textContent = e.description),
                this.form.append(i),
                document.dispatchEvent(
                  new CustomEvent('ajaxProduct:error', { detail: { errorMessage: e.description } })
                ),
                this.args &&
                  this.args.scopedEventId &&
                  document.dispatchEvent(
                    new CustomEvent('ajaxProduct:error:' + this.args.scopedEventId, {
                      detail: { errorMessage: e.description },
                    })
                  );
            },
          })),
          t
        );
      })()),
      (theme.ProductMedia = (function () {
        var e = {},
          t = {},
          i = {},
          s = { mediaGroup: '[data-product-single-media-group]', xrButton: '[data-shopify-xr]' };
        function n(e) {
          if (!e) {
            for (var i in t)
              if (t.hasOwnProperty(i)) {
                var s = t[i];
                !s.modelViewerUi && Shopify && (s.modelViewerUi = new Shopify.ModelViewerUI(s.element)), a(s);
              }
          }
        }
        function a(e) {
          var t = i[e.sectionId];
          e.container.addEventListener('mediaVisible', function () {
            t.element.setAttribute('data-shopify-model3d-id', e.modelId),
              theme.config.isTouch || e.modelViewerUi.play();
          }),
            e.container.addEventListener('mediaHidden', function () {
              t.element.setAttribute('data-shopify-model3d-id', t.defaultId), e.modelViewerUi.pause();
            }),
            e.container.addEventListener('xrLaunch', function () {
              e.modelViewerUi.pause();
            });
        }
        return {
          init: function a(o, r) {
            (e[r] = { loaded: !1 }),
              o.forEach(function (e, n) {
                var a = e.dataset.mediaId,
                  o = e.querySelector('model-viewer'),
                  c = o.dataset.modelId;
                if (0 === n) {
                  var l = e.closest(s.mediaGroup).querySelector(s.xrButton);
                  i[r] = { element: l, defaultId: c };
                }
                t[a] = { modelId: c, sectionId: r, container: e, element: o };
              }),
              window.Shopify.loadFeatures([
                {
                  name: 'shopify-xr',
                  version: '1.0',
                  onLoad: function t(i) {
                    if (!i) {
                      if (!window.ShopifyXR) {
                        document.addEventListener('shopify_xr_initialized', function () {
                          t();
                        });
                        return;
                      }
                      for (var s in e)
                        if (e.hasOwnProperty(s)) {
                          var n = e[s];
                          if (n.loaded) continue;
                          var a = document.querySelector('#ModelJson-' + s);
                          window.ShopifyXR.addModels(JSON.parse(a.innerHTML)), (n.loaded = !0);
                        }
                      window.ShopifyXR.setupXRElements();
                    }
                  },
                },
                { name: 'model-viewer-ui', version: '1.0', onLoad: n },
              ]),
              theme.LibraryLoader.load('modelViewerUiStyles');
          },
          removeSectionModels: function i(s) {
            for (var n in t) t.hasOwnProperty(n) && t[n].sectionId === s && delete t[n];
            delete e[s];
          },
        };
      })()),
      (theme.QtySelector = (function () {
        var e = { input: '.js-qty__num', plus: '.js-qty__adjust--plus', minus: '.js-qty__adjust--minus' };
        function t(t, i) {
          (this.wrapper = t),
            (this.plus = t.querySelector(e.plus)),
            (this.minus = t.querySelector(e.minus)),
            (this.input = t.querySelector(e.input)),
            (this.minValue = this.input.getAttribute('min') || 1);
          var s = { namespace: null, isCart: !1, key: this.input.dataset.id };
          (this.options = Object.assign({}, s, i)), this.init();
        }
        return (
          (t.prototype = Object.assign({}, t.prototype, {
            init: function () {
              this.plus.addEventListener(
                'click',
                function () {
                  var e = this._getQty();
                  this._change(e + 1);
                }.bind(this)
              ),
                this.minus.addEventListener(
                  'click',
                  function () {
                    var e = this._getQty();
                    this._change(e - 1);
                  }.bind(this)
                ),
                this.input.addEventListener(
                  'change',
                  function (e) {
                    this._change(this._getQty());
                  }.bind(this)
                );
            },
            _getQty: function () {
              var e = this.input.value;
              return (parseFloat(e) != parseInt(e) || isNaN(e)) && (e = 1), parseInt(e);
            },
            _change: function (e) {
              e <= this.minValue && (e = this.minValue),
                (this.input.value = e),
                this.options.isCart &&
                  document.dispatchEvent(
                    new CustomEvent('cart:quantity' + this.options.namespace, {
                      detail: [this.options.key, e, this.wrapper],
                    })
                  );
            },
          })),
          t
        );
      })()),
      (theme.initQuickShop = function () {
        var e = document.querySelectorAll('.grid-product');
        function t(e) {
          var i = e.currentTarget;
          if (!theme.config.bpSmall) {
            if ((i.removeEventListener('mouseover', t), !i || !i.dataset.productId)) return;
            var s = i.dataset.productId,
              n = i.dataset.productHandle,
              a = i.querySelector('.quick-product__btn');
            theme.preloadProductModal(n, s, a);
          }
        }
        e.length &&
          theme.settings.quickView &&
          e.forEach((e) => {
            e.addEventListener('mouseover', t);
          });
      }),
      (theme.preloadProductModal = function (e, t, i) {
        var s = document.getElementById('QuickShopHolder-' + e),
          n = theme.routes.home + '/products/' + e + '?view=modal';
        fetch((n = n.replace('//', '/')))
          .then(function (e) {
            return e.text();
          })
          .then(function (n) {
            var a = new DOMParser()
              .parseFromString(n, 'text/html')
              .querySelector('.product-section[data-product-handle="' + e + '"]');
            s &&
              ((s.innerHTML = ''),
              s.append(a),
              new theme.Modals('QuickShopModal-' + t, 'quick-modal-' + t),
              theme.sections.register('product', theme.Product, s),
              theme.collapsibles.init(),
              theme.videoModal(),
              i && i.classList.remove('quick-product__btn--not-ready'));
          });
      }),
      (theme.Slideshow = (function () {
        var e = { animateOut: 'animate-out', isPaused: 'is-paused', isActive: 'is-active' },
          t = {
            allSlides: '.slideshow__slide',
            currentSlide: '.is-selected',
            wrapper: '.slideshow-wrapper',
            pauseButton: '.slideshow__pause',
          },
          i = {
            thumb: '.product__thumb-item:not(.hide)',
            links: '.product__thumb-item:not(.hide) a',
            arrow: '.product__thumb-arrow',
          },
          s = {
            adaptiveHeight: !1,
            autoPlay: !1,
            avoidReflow: !1,
            childNav: null,
            childNavScroller: null,
            childVertical: !1,
            dragThreshold: 7,
            fade: !1,
            friction: 0.8,
            initialIndex: 0,
            pageDots: !1,
            pauseAutoPlayOnHover: !1,
            prevNextButtons: !1,
            rightToLeft: theme.config.rtl,
            selectedAttraction: 0.14,
            setGallerySize: !0,
            wrapAround: !0,
          };
        function n(e, n) {
          if (
            ((this.el = e),
            (this.args = Object.assign({}, s, n)),
            (this.args.on = {
              ready: this.init.bind(this),
              change: this.slideChange.bind(this),
              settle: this.afterChange.bind(this),
            }),
            this.args.childNav &&
              ((this.childNavEls = this.args.childNav.querySelectorAll(i.thumb)),
              (this.childNavLinks = this.args.childNav.querySelectorAll(i.links)),
              (this.arrows = this.args.childNav.querySelectorAll(i.arrow)),
              this.childNavLinks.length && this.initChildNav()),
            this.args.avoidReflow &&
              (function e(t) {
                if (t.id) {
                  for (var i = t.firstChild; null != i && 3 == i.nodeType; ) i = i.nextSibling;
                  var s = document.createElement('style');
                  (s.innerHTML = `#${t.id} .flickity-viewport{height:${i.offsetHeight}px}`),
                    document.head.appendChild(s);
                }
              })(e),
            (this.slideshow = new Flickity(e, this.args)),
            e.dataset.zoom &&
              'true' === e.dataset.zoom &&
              (this.slideshow.on('dragStart', () => {
                (this.slideshow.slider.style.pointerEvents = 'none'),
                  this.slideshow.options.fade &&
                    (this.slideshow.slider.querySelector('.is-selected').style.pointerEvents = 'none');
              }),
              this.slideshow.on('dragEnd', () => {
                (this.slideshow.slider.style.pointerEvents = 'auto'),
                  this.slideshow.options.fade &&
                    (this.slideshow.slider.querySelector('.is-selected').style.pointerEvents = 'auto');
              })),
            this.args.autoPlay)
          ) {
            var a = e.closest(t.wrapper);
            (this.pauseBtn = a.querySelector(t.pauseButton)),
              this.pauseBtn && this.pauseBtn.addEventListener('click', this._togglePause.bind(this));
          }
          window.on(
            'resize',
            theme.utils.debounce(
              300,
              function () {
                this.resize();
              }.bind(this)
            )
          );
        }
        return (
          (n.prototype = Object.assign({}, n.prototype, {
            init: function (e) {
              (this.currentSlide = this.el.querySelector(t.currentSlide)),
                this.args.callbacks &&
                  this.args.callbacks.onInit &&
                  'function' == typeof this.args.callbacks.onInit &&
                  this.args.callbacks.onInit(this.currentSlide),
                window.AOS && AOS.refresh();
            },
            slideChange: function (t) {
              this.args.fade &&
                this.currentSlide &&
                (this.currentSlide.classList.add(e.animateOut),
                this.currentSlide.addEventListener(
                  'transitionend',
                  function () {
                    this.currentSlide.classList.remove(e.animateOut);
                  }.bind(this)
                )),
                this.args.childNav && this.childNavGoTo(t),
                this.args.callbacks &&
                  this.args.callbacks.onChange &&
                  'function' == typeof this.args.callbacks.onChange &&
                  this.args.callbacks.onChange(t),
                this.arrows &&
                  this.arrows.length &&
                  (this.arrows[0].classList.toggle('hide', 0 === t),
                  this.arrows[1].classList.toggle('hide', t === this.childNavLinks.length - 1));
            },
            afterChange: function (i) {
              this.args.fade &&
                this.el.querySelectorAll(t.allSlides).forEach((t) => {
                  t.classList.remove(e.animateOut);
                }),
                (this.currentSlide = this.el.querySelector(t.currentSlide)),
                this.args.childNav && this.childNavGoTo(this.slideshow.selectedIndex);
            },
            destroy: function () {
              this.args.childNav &&
                this.childNavLinks.length &&
                this.childNavLinks.forEach((t) => {
                  t.classList.remove(e.isActive);
                }),
                this.slideshow.destroy();
            },
            reposition: function () {
              this.slideshow.reposition();
            },
            _togglePause: function () {
              this.pauseBtn.classList.contains(e.isPaused)
                ? (this.pauseBtn.classList.remove(e.isPaused), this.slideshow.playPlayer())
                : (this.pauseBtn.classList.add(e.isPaused), this.slideshow.pausePlayer());
            },
            resize: function () {
              this.slideshow.resize();
            },
            play: function () {
              this.slideshow.playPlayer();
            },
            pause: function () {
              this.slideshow.pausePlayer();
            },
            goToSlide: function (e) {
              this.slideshow.select(e);
            },
            setDraggable: function (e) {
              (this.slideshow.options.draggable = e), this.slideshow.updateDraggable();
            },
            initChildNav: function () {
              this.childNavLinks[this.args.initialIndex].classList.add('is-active'),
                this.childNavLinks.forEach((e, t) => {
                  e.setAttribute('data-index', t),
                    e.addEventListener(
                      'click',
                      function (e) {
                        e.preventDefault(), this.goToSlide(this.getChildIndex(e.currentTarget));
                      }.bind(this)
                    ),
                    e.addEventListener(
                      'focus',
                      function (e) {
                        this.goToSlide(this.getChildIndex(e.currentTarget));
                      }.bind(this)
                    ),
                    e.addEventListener(
                      'keydown',
                      function (e) {
                        13 === e.keyCode && this.goToSlide(this.getChildIndex(e.currentTarget));
                      }.bind(this)
                    );
                }),
                this.arrows.length &&
                  this.arrows.forEach((e) => {
                    e.addEventListener('click', this.arrowClick.bind(this));
                  });
            },
            getChildIndex: function (e) {
              return parseInt(e.dataset.index);
            },
            childNavGoTo: function (t) {
              this.childNavLinks.forEach((t) => {
                t.blur(), t.classList.remove(e.isActive);
              });
              var i = this.childNavLinks[t];
              if ((i.classList.add(e.isActive), this.args.childNavScroller)) {
                if (this.args.childVertical) {
                  var s = i.offsetTop;
                  this.args.childNavScroller.scrollTop = s - 100;
                } else {
                  var n = i.offsetLeft;
                  this.args.childNavScroller.scrollLeft = n - 100;
                }
              }
            },
            arrowClick: function (e) {
              e.currentTarget.classList.contains('product__thumb-arrow--prev')
                ? this.slideshow.previous()
                : this.slideshow.next();
            },
          })),
          n
        );
      })()),
      (theme.VariantAvailability = (function () {
        var e = { disabled: 'disabled' };
        function t(e) {
          (this.type = e.type),
            (this.variantsObject = e.variantsObject),
            (this.currentVariantObject = e.currentVariantObject),
            (this.container = e.container),
            (this.namespace = e.namespace),
            this.init();
        }
        return (
          (t.prototype = Object.assign({}, t.prototype, {
            init: function () {
              this.container.on('variantChange' + this.namespace, this.setAvailability.bind(this)),
                this.setInitialAvailability();
            },
            createAvailableOptionsTree: (e, t) =>
              e.reduce(
                (e, i) => (
                  Object.keys(e).forEach((s) => {
                    if (null === i[s]) return;
                    let n = e[s].find((e) => e.value === i[s]);
                    void 0 === n && ((n = { value: i[s], soldOut: !0 }), e[s].push(n));
                    let a = t.find(({ value: e, index: t }) => 'option1' === t),
                      o = t.find(({ value: e, index: t }) => 'option2' === t);
                    switch (s) {
                      case 'option1':
                        n.soldOut = (!n.soldOut || !i.available) && n.soldOut;
                        break;
                      case 'option2':
                        a && i.option1 === a.value && (n.soldOut = (!n.soldOut || !i.available) && n.soldOut);
                      case 'option3':
                        a &&
                          i.option1 === a.value &&
                          o &&
                          i.option2 === o.value &&
                          (n.soldOut = (!n.soldOut || !i.available) && n.soldOut);
                    }
                  }),
                  e
                ),
                { option1: [], option2: [], option3: [] }
              ),
            setInitialAvailability: function () {
              this.container.querySelectorAll('.variant-input-wrap').forEach((e) => {
                this.disableVariantGroup(e);
              });
              let e = this.currentVariantObject.options.map((e, t) => ({ value: e, index: `option${t + 1}` })),
                t = this.createAvailableOptionsTree(this.variantsObject, e, this.currentVariantObject);
              for (var [i, s] of Object.entries(t)) this.manageOptionState(i, s);
            },
            setAvailability: function (e) {
              let { value: t, index: i, currentlySelectedValues: s, variant: n } = e.detail,
                a = this.createAvailableOptionsTree(this.variantsObject, s, n, i, t);
              for (var [o, r] of Object.entries(a)) this.manageOptionState(o, r, t);
            },
            manageOptionState: function (e, t) {
              var i = this.container.querySelector('.variant-input-wrap[data-index="' + e + '"]');
              t.forEach((e) => {
                this.enableVariantOption(i, e);
              });
            },
            enableVariantOption: function (t, i) {
              var s = i.value.replace(/([ #;&,.+*~\':"!^$[\]()=>|\/@])/g, '\\$1');
              if ('dropdown' === this.type)
                i.soldOut
                  ? (t.querySelector('option[value="' + s + '"]').disabled = !0)
                  : (t.querySelector('option[value="' + s + '"]').disabled = !1);
              else {
                var n = t.querySelector('.variant-input[data-value="' + s + '"]'),
                  a = n.querySelector('input'),
                  o = n.querySelector('label');
                a.classList.remove(e.disabled),
                  o.classList.remove(e.disabled),
                  i.soldOut && (a.classList.add(e.disabled), o.classList.add(e.disabled));
              }
            },
            disableVariantGroup: function (t) {
              'dropdown' === this.type
                ? t.querySelectorAll('option').forEach((e) => {
                    e.disabled = !0;
                  })
                : (t.querySelectorAll('input').forEach((t) => {
                    t.classList.add(e.disabled);
                  }),
                  t.querySelectorAll('label').forEach((t) => {
                    t.classList.add(e.disabled);
                  }));
            },
          })),
          t
        );
      })()),
      (theme.videoModal = function () {
        var e,
          t,
          i = 'VideoHolder',
          s = {
            youtube: 'a[href*="youtube.com/watch"], a[href*="youtu.be/"]',
            vimeo: 'a[href*="player.vimeo.com/player/"], a[href*="vimeo.com/"]',
            mp4Trigger: '.product-video-trigger--mp4',
            mp4Player: '.product-video-mp4-sound',
          },
          n = document.querySelectorAll(s.youtube),
          a = document.querySelectorAll(s.vimeo),
          o = document.querySelectorAll(s.mp4Trigger);
        if (n.length || a.length || o.length) {
          var r = document.getElementById(i);
          n.length && theme.LibraryLoader.load('youtubeSdk'),
            a.length && theme.LibraryLoader.load('vimeo', window.vimeoApiReady);
          var c = new theme.Modals('VideoModal', 'video-modal', {
            closeOffContentClick: !0,
            bodyOpenClass: ['modal-open', 'video-modal-open'],
            solid: !0,
          });
          n.forEach((e) => {
            e.addEventListener('click', l);
          }),
            a.forEach((e) => {
              e.addEventListener('click', d);
            }),
            o.forEach((e) => {
              e.addEventListener('click', h);
            }),
            document.addEventListener('modalClose.VideoModal', function i() {
              e && 'function' == typeof e.destroy
                ? e.destroy()
                : t && 'function' == typeof t.destroy
                ? t.destroy()
                : p();
            });
        }
        function l(t) {
          if (theme.config.youTubeReady) {
            t.preventDefault(), p(), c.open(t);
            var s,
              n,
              a =
                ((s = t.currentTarget.getAttribute('href')),
                (n = s.match(/^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#\&\?]*).*/)),
                !!n && 11 == n[7].length && n[7]);
            e = new theme.YouTube(i, { videoId: a, style: 'sound', events: { onReady: u } });
          }
        }
        function d(e) {
          if (theme.config.vimeoReady) {
            e.preventDefault(), p(), c.open(e);
            var s = e.currentTarget.dataset.videoId,
              n = e.currentTarget.dataset.videoLoop;
            t = new theme.VimeoPlayer(i, s, { style: 'sound', loop: n });
          }
        }
        function h(e) {
          p();
          var t = e.currentTarget.parentNode.querySelector(s.mp4Player).cloneNode(!0);
          t.classList.remove('hide'), r.append(t), c.open(e), r.querySelector('video').play();
        }
        function u(e) {
          e.target.unMute(), e.target.playVideo();
        }
        function p() {
          r.innerHTML = '';
        }
      });
    class l extends HTMLElement {
      constructor() {
        super(),
          (this.el = this),
          (this.inner = this.querySelector('[data-tool-tip-inner]')),
          (this.closeButton = this.querySelector('[data-tool-tip-close]')),
          (this.toolTipContent = this.querySelector('[data-tool-tip-content]')),
          (this.toolTipTitle = this.querySelector('[data-tool-tip-title]')),
          (this.triggers = document.querySelectorAll('[data-tool-tip-trigger]')),
          document.addEventListener('tooltip:open', (e) => {
            this._open(e.detail.context, e.detail.content);
          });
      }
      _open(e, t) {
        (this.toolTipContent.innerHTML = t),
          'store-availability' != e && this.toolTipTitle.remove(),
          this._lockScrolling(),
          this.closeButton &&
            this.closeButton.on('click.tooltip-close', () => {
              this._close();
            }),
          document.documentElement.on('click.tooltip-outerclick', (e) => {
            'true' !== this.el.dataset.toolTipOpen || this.inner.contains(e.target) || this._close();
          }),
          document.documentElement.on('keydown.tooltip-esc', (e) => {
            'Escape' === e.code && this._close();
          }),
          (this.el.dataset.toolTipOpen = !0),
          (this.el.dataset.toolTip = e);
      }
      _close() {
        (this.toolTipContent.innerHTML = ''),
          (this.el.dataset.toolTipOpen = 'false'),
          (this.el.dataset.toolTip = ''),
          this._unlockScrolling(),
          this.closeButton.off('click.tooltip-close'),
          document.documentElement.off('click.tooltip-outerclick'),
          document.documentElement.off('keydown.tooltip-esc');
      }
      _lockScrolling() {
        theme.a11y.trapFocus({ container: this.el, namespace: 'tooltip_focus' }),
          theme.a11y.lockMobileScrolling(),
          document.documentElement.classList.add('modal-open');
      }
      _unlockScrolling() {
        theme.a11y.removeTrapFocus({ container: this.el, namespace: 'tooltip_focus' }),
          theme.a11y.unlockMobileScrolling(),
          document.documentElement.classList.remove('modal-open');
      }
    }
    customElements.define('tool-tip', l);
    class d extends HTMLElement {
      constructor() {
        super(), (this.el = this), (this.toolTipContent = this.querySelector('[data-tool-tip-content]')), this.init();
      }
      init() {
        let e = new CustomEvent('tooltip:open', {
          detail: { context: this.dataset.toolTip, content: this.toolTipContent.innerHTML },
          bubbles: !0,
        });
        this.el.addEventListener('click', (t) => {
          t.stopPropagation(), this.dispatchEvent(e);
        });
      }
    }
    customElements.define('tool-tip-trigger', d);
    class h extends HTMLElement {
      constructor() {
        super(),
          (this.closeBtn = this.querySelector('[data-close-button]')),
          (this.popupTrigger = this.querySelector('[data-message]')),
          (this.id = this.dataset.sectionId),
          (this.newsletterId = `NewsletterPopup-${this.id}`),
          (this.cookie = Cookies.get(`newsletter-${this.id}`)),
          (this.cookieName = `newsletter-${this.id}`),
          (this.secondsBeforeShow = this.dataset.delaySeconds),
          (this.expiry = parseInt(this.dataset.delayDays)),
          (this.modal = new theme.Modals(`NewsletterPopup-${this.newsletterId}`, 'newsletter-popup-modal')),
          this.init();
      }
      init() {
        document.addEventListener('shopify:block:select', (e) => {
          e.detail.sectionId === this.id && this.show(0, !0);
        }),
          document.addEventListener('shopify:block:deselect', (e) => {
            e.detail.sectionId === this.id && this.hide();
          }),
          document.addEventListener(`modalOpen.${this.newsletterId}`, () => this.hide()),
          document.addEventListener(`modalClose.${this.newsletterId}`, () => this.show()),
          document.addEventListener('newsletter:openReminder', () => this.show(0)),
          this.closeBtn.addEventListener('click', () => {
            this.hide(), Cookies.set(this.cookieName, 'opened', { path: '/', expires: this.expiry });
          }),
          this.popupTrigger.addEventListener('click', () => {
            let e = new CustomEvent('reminder:openNewsletter', { bubbles: !0 });
            this.dispatchEvent(e), this.hide();
          });
      }
      show(e = this.secondsBeforeShow, t = !1) {
        let i = 'true' === sessionStorage.getItem('reminderAppeared');
        i ||
          setTimeout(() => {
            (this.dataset.enabled = 'true'), sessionStorage.setItem('reminderAppeared', !0);
          }, 1e3 * e);
      }
      hide() {
        this.dataset.enabled = 'false';
      }
    }
    customElements.define('newsletter-reminder', h),
      (theme.announcementBar = (function () {
        var e,
          t,
          i = { autoPlay: 5e3, avoidReflow: !0, cellAlign: theme.config.rtl ? 'right' : 'left' };
        function s() {
          t = new theme.Slideshow(e, i);
        }
        function n() {
          t && 'function' == typeof t.destroy && t.destroy();
        }
        return {
          init: function t() {
            if ((e = document.getElementById('AnnouncementSlider')))
              n(),
                1 !== e.dataset.blockCount &&
                  ((theme.config.bpSmall || 'true' === e.dataset.compact) && s(),
                  document.addEventListener('matchSmall', function () {
                    n(), s();
                  }),
                  document.addEventListener('unmatchSmall', function () {
                    n(), 'true' === e.dataset.compact && s();
                  }));
          },
          onBlockSelect: function i(s) {
            var n = parseInt(e.querySelector('#AnnouncementSlide-' + s).dataset.index);
            t && 'function' == typeof t.pause && (t.goToSlide(n), t.pause());
          },
          onBlockDeselect: function e() {
            t && 'function' == typeof t.play && t.play();
          },
          unload: n,
        };
      })()),
      (theme.customerTemplates = function () {
        var e, t, i, s;
        function n() {
          document.getElementById('RecoverPasswordForm').classList.toggle('hide'),
            document.getElementById('CustomerLoginForm').classList.toggle('hide');
        }
        '#recover' === window.location.hash && n(),
          (e = document.getElementById('RecoverPassword')) &&
            e.addEventListener('click', function (e) {
              e.preventDefault(), n();
            }),
          (t = document.getElementById('HideRecoverPasswordLink')) &&
            t.addEventListener('click', function (e) {
              e.preventDefault(), n();
            }),
          document.querySelector('.reset-password-success') &&
            document.getElementById('ResetSuccess').classList.remove('hide'),
          (i = document.getElementById('AddressNewForm')),
          (s = document.querySelectorAll('.js-address-form')),
          i &&
            s.length &&
            (setTimeout(function () {
              document.querySelectorAll('.js-address-country').forEach((e) => {
                var t = e.dataset.countryId,
                  i = e.dataset.provinceId,
                  s = e.dataset.provinceContainerId;
                new Shopify.CountryProvinceSelector(t, i, { hideElement: s });
              });
            }, 1e3),
            document.querySelectorAll('.address-new-toggle').forEach((e) => {
              e.addEventListener('click', function () {
                i.classList.toggle('hide');
              });
            }),
            document.querySelectorAll('.address-edit-toggle').forEach((e) => {
              e.addEventListener('click', function (e) {
                var t = e.currentTarget.dataset.formId;
                document.getElementById('EditAddress_' + t).classList.toggle('hide');
              });
            }),
            document.querySelectorAll('.address-delete').forEach((e) => {
              e.addEventListener('click', function (e) {
                var t = e.currentTarget.dataset.formId;
                confirm(e.currentTarget.dataset.confirmMessage || 'Are you sure you wish to delete this address?') &&
                  Shopify &&
                  Shopify.postLink('/account/addresses/' + t, { parameters: { _method: 'delete' } });
              });
            }));
      }),
      (theme.CartDrawer = (function () {
        var e = { drawer: '#CartDrawer', form: '#CartDrawerForm' };
        function t() {
          (this.form = document.querySelector(e.form)),
            (this.drawer = new theme.Drawers('CartDrawer', 'cart')),
            this.init();
        }
        return (
          (t.prototype = Object.assign({}, t.prototype, {
            init: function () {
              (this.cartForm = new theme.CartForm(this.form)),
                this.cartForm.buildCart(),
                document.addEventListener(
                  'ajaxProduct:added',
                  function (e) {
                    this.cartForm.buildCart(), this.open();
                  }.bind(this)
                ),
                document.addEventListener('cart:open', this.open.bind(this)),
                document.addEventListener('cart:close', this.close.bind(this));
            },
            open: function () {
              this.drawer.open();
            },
            close: function () {
              this.drawer.close();
            },
          })),
          t
        );
      })()),
      (theme.headerNav = (function () {
        var e,
          t,
          i = {
            wrapper: '#HeaderWrapper',
            siteHeader: '#SiteHeader',
            searchBtn: '.js-search-header',
            closeSearch: '#SearchClose',
            searchContainer: '.site-header__search-container',
            logo: '#LogoContainer img',
            megamenu: '.megamenu',
            navItems: '.site-nav__item',
            navLinks: '.site-nav__link',
            navLinksWithDropdown: '.site-nav__link--has-dropdown',
            navDropdownLinks: '.site-nav__dropdown-link--second-level',
          },
          s = {
            hasDropdownClass: 'site-nav--has-dropdown',
            hasSubDropdownClass: 'site-nav__deep-dropdown-trigger',
            dropdownActive: 'is-focused',
          },
          n = {
            namespace: '.siteNav',
            wrapperOverlayed: !1,
            overlayedClass: 'is-light',
            overlayEnabledClass: 'header-wrapper--sticky',
            stickyEnabled: !1,
            stickyActive: !1,
            stickyClass: 'site-header--stuck',
            stickyHeaderWrapper: 'StickyHeaderWrap',
            openTransitionClass: 'site-header--opening',
            lastScroll: 0,
          };
        function a() {
          if (n.stickyEnabled) {
            var e = t.offsetHeight;
            t.classList.contains('site-header--stuck') &&
              !theme.config.bpSmall &&
              (e += 2 * parseFloat(window.getComputedStyle(t, null).getPropertyValue('padding-top'))),
              (document.querySelector('#' + n.stickyHeaderWrapper).style.height = e + 'px');
          }
        }
        function o() {
          n.stickyEnabled && !n.forceStopSticky && (requestAnimationFrame(r), (n.lastScroll = window.scrollY));
        }
        function r() {
          if (window.scrollY > 250)
            !n.stickyActive &&
              ((n.stickyActive = !0),
              t.classList.add(n.stickyClass),
              n.wrapperOverlayed && e.classList.remove(n.overlayedClass),
              setTimeout(function () {
                t.classList.add(n.openTransitionClass);
              }, 100));
          else {
            if (!n.stickyActive) return;
            (n.stickyActive = !1),
              t.classList.remove(n.openTransitionClass),
              t.classList.remove(n.stickyClass),
              n.wrapperOverlayed && e.classList.add(n.overlayedClass);
          }
        }
        function c(e) {
          e.preventDefault(), e.stopImmediatePropagation();
          var t = document.querySelector(i.searchContainer);
          theme.utils.prepareTransition(
            t,
            function () {
              t.classList.add('is-active');
            }.bind(this)
          ),
            document.documentElement.classList.add('js-drawer-open', 'js-drawer-open--search'),
            setTimeout(function () {
              theme.a11y.trapFocus({
                container: t,
                namespace: 'header_search',
                elementToFocus: t.querySelector('.site-header__search-input'),
              });
            }, 100),
            theme.config.bpSmall && n.stickyEnabled && n.lastScroll < 300 && window.scrollTo(0, 0),
            theme.a11y.lockMobileScrolling(n.namespace),
            (function e() {
              window.on(
                'keyup' + n.namespace,
                function (e) {
                  27 === e.keyCode && l();
                }.bind(this)
              ),
                document.documentElement.on(
                  'click' + n.namespace,
                  function (e) {
                    l(e);
                  }.bind(this)
                );
            })();
        }
        function l(e) {
          if (e) {
            for (var t = e.path || (e.composedPath && e.composedPath()), s = 0; s < t.length; s++)
              if (t[s].classList) {
                if (t[s].classList.contains('site-header__search-btn')) break;
                if (t[s].classList.contains('site-header__search-container')) return;
              }
          }
          document.activeElement.blur(),
            document.documentElement.classList.add('js-drawer-closing'),
            document.documentElement.classList.remove('js-drawer-open', 'js-drawer-open--search'),
            window.setTimeout(
              function () {
                document.documentElement.classList.remove('js-drawer-closing');
              }.bind(this),
              500
            );
          var a = document.querySelector(i.searchContainer);
          theme.utils.prepareTransition(
            a,
            function () {
              a.classList.remove('is-active');
            }.bind(this)
          ),
            theme.a11y.removeTrapFocus({ container: a, namespace: 'header_search' }),
            theme.a11y.unlockMobileScrolling(n.namespace),
            window.off('keyup' + n.namespace),
            document.documentElement.off('click' + n.namespace);
        }
        function d(e) {
          document.querySelectorAll(i.logo).forEach((e) => {
            var t = e.clientWidth,
              i = e.closest('.header-item').clientWidth;
            t > i ? (e.style.maxWidth = i) : e.removeAttribute('style');
          });
        }
        return {
          init: function r() {
            var h, u;
            (e = document.querySelector(i.wrapper)),
              (t = document.querySelector(i.siteHeader)),
              (n.stickyEnabled = 'true' === t.dataset.sticky),
              n.stickyEnabled &&
                ((n.wrapperOverlayed = e.classList.contains(n.overlayedClass)),
                (theme.config.stickyHeader =
                  ((h = 0),
                  t.querySelectorAll(i.megamenu).forEach((e) => {
                    var t = e.offsetHeight;
                    t > h && (h = t);
                  }),
                  !(window.innerHeight < h + 120))),
                theme.config.stickyHeader
                  ? ((n.forceStopSticky = !1),
                    (n.lastScroll = 0),
                    ((u = document.createElement('div')).id = n.stickyHeaderWrapper),
                    theme.utils.wrap(t, u),
                    a(),
                    window.on('resize' + n.namespace, theme.utils.debounce(50, a)),
                    window.on('scroll' + n.namespace, theme.utils.throttle(20, o)),
                    Shopify &&
                      Shopify.designMode &&
                      setTimeout(function () {
                        a();
                      }, 250))
                  : (n.forceStopSticky = !0)),
              (theme.settings.overlayHeader = 'true' === t.dataset.overlay),
              theme.settings.overlayHeader &&
                Shopify &&
                Shopify.designMode &&
                document.body.classList.contains('template-collection') &&
                !document.querySelector('.collection-hero') &&
                this.disableOverlayHeader(),
              (function e() {
                var a = !1,
                  o = !1,
                  r = !1;
                function c(e) {
                  a && u(), o && p(), l(e.currentTarget);
                }
                function l(e) {
                  var t = e.parentNode;
                  if (
                    (t.classList.contains(s.hasDropdownClass) && (t.classList.add(s.dropdownActive), (a = !0)),
                    !theme.config.isTouch && !r)
                  ) {
                    var i = theme.config.isTouch ? 'touchend' : 'click';
                    (r = !0),
                      document.documentElement.on(
                        i + n.namespace,
                        function () {
                          h(), document.documentElement.off(i + n.namespace), (r = !1);
                        }.bind(this)
                      );
                  }
                }
                function d(e, t) {
                  var i = e.parentNode;
                  (i.classList.contains(s.hasSubDropdownClass) || t) && (i.classList.add(s.dropdownActive), (o = !0));
                }
                function h() {
                  u(), p();
                }
                function u() {
                  document.querySelectorAll(i.navItems).forEach((e) => {
                    e.classList.remove(s.dropdownActive);
                  });
                }
                function p() {
                  document.querySelectorAll(i.navDropdownLinks).forEach((e) => {
                    e.parentNode.classList.remove(s.dropdownActive);
                  });
                }
                theme.config.isTouch &&
                  document.querySelectorAll(i.navLinksWithDropdown).forEach((e) => {
                    e.on('touchend' + n.namespace, function (e) {
                      e.currentTarget.parentNode.classList.contains(s.dropdownActive)
                        ? window.location.replace(e.currentTarget.getAttribute('href'))
                        : (e.preventDefault(), h(), l(e.currentTarget));
                    });
                  }),
                  document.querySelectorAll(i.navLinks).forEach((e) => {
                    e.on('focusin' + n.namespace, c),
                      e.on('mouseover' + n.namespace, c),
                      e.on('mouseleave' + n.namespace, h);
                  }),
                  document.querySelectorAll(i.navDropdownLinks).forEach((e) => {
                    theme.config.isTouch &&
                      e.on('touchend' + n.namespace, function (e) {
                        var t = e.currentTarget.parentNode;
                        t.classList.contains(s.hasSubDropdownClass)
                          ? t.classList.contains(s.dropdownActive)
                            ? window.location.replace(e.currentTarget.getAttribute('href'))
                            : (e.preventDefault(), p(), d(e.currentTarget))
                          : window.location.replace(e.currentTarget.getAttribute('href'));
                      }),
                      e.on('focusin' + n.namespace, function (e) {
                        p(), d(e.currentTarget, !0);
                      });
                  }),
                  theme.config.isTouch &&
                    (document.body.on('touchend' + n.namespace, function () {
                      h();
                    }),
                    t.querySelectorAll(i.megamenu).forEach((e) => {
                      e.on('touchend' + n.namespace, function (e) {
                        e.stopImmediatePropagation();
                      });
                    }));
              })(),
              document.querySelectorAll(i.searchBtn).forEach((e) => {
                e.addEventListener('click', c);
              }),
              document.querySelector(i.closeSearch).addEventListener('click', l),
              window.on('load' + n.namespace, d),
              window.on('resize' + n.namespace, theme.utils.debounce(150, d));
          },
          disableOverlayHeader: function t() {
            e.classList.remove(n.overlayEnabledClass, n.overlayedClass),
              (n.wrapperOverlayed = !1),
              (theme.settings.overlayHeader = !1);
          },
        };
      })()),
      (window.onpageshow = function (e) {
        e.persisted &&
          (document.body.classList.remove('unloading'),
          document.querySelectorAll('.cart__checkout').forEach((e) => {
            e.classList.remove('btn--loading');
          }));
      }),
      (theme.predictiveSearch = (function () {
        var e,
          t = '',
          i = !1,
          s = '.predictive',
          n = {
            form: '#HeaderSearchForm',
            input: 'input[type="search"]',
            wrapper: '#PredictiveWrapper',
            resultDiv: '#PredictiveResults',
            searchButton: '[data-predictive-search-button]',
          },
          a = {},
          o = { imageSize: 'square' },
          r = { up_arrow: 38, down_arrow: 40, tab: 9 };
        function c() {
          a.wrapper.classList.add('hide'), (a.results.innerHTML = ''), clearTimeout(e);
        }
        function l() {
          a.form.submit();
        }
        function d(e) {
          e.preventDefault ? e.preventDefault() : (e.returnValue = !1);
          var t = {},
            i = new FormData(e.target);
          for (var s of i.keys()) t[s] = i.get(s);
          t.q && (t.q += '*');
          var n = u(t);
          return (window.location.href = `${theme.routes.search}?${n}`), !1;
        }
        function h(s) {
          s.keyCode !== r.up_arrow &&
            s.keyCode !== r.down_arrow &&
            s.keyCode !== r.tab &&
            (function s() {
              var n = a.input.value;
              if ('' === n) {
                c();
                return;
              }
              var r,
                l = ((r = n), 'string' != typeof r ? null : r.trim().replace(/\ /g, '-').toLowerCase());
              clearTimeout(e),
                (e = setTimeout(
                  function () {
                    (function e(s) {
                      if (!i && t !== s) {
                        var n;
                        (t = s),
                          (i = !0),
                          fetch(
                            '/search/suggest.json?' +
                              u({
                                q: s,
                                'resources[type]': theme.settings.predictiveSearchType,
                                'resources[limit]': 4,
                                'resources[options][unavailable_products]': 'last',
                                'resources[options][fields]': 'title,product_type,variants.title,vendor',
                              })
                          )
                            .then((e) => e.json())
                            .then((e) => {
                              i = !1;
                              var t = {},
                                s = 0;
                              a.wrapper.classList.remove('hide');
                              var n = Object.entries(e.resources.results);
                              if (
                                (Object.keys(n).forEach(function (e) {
                                  var i,
                                    a,
                                    r,
                                    c,
                                    l,
                                    d,
                                    h,
                                    u,
                                    p,
                                    f = n[e],
                                    m = f[0],
                                    g = f[1];
                                  switch (((s += g.length), m)) {
                                    case 'products':
                                      t[m] =
                                        ((i = g),
                                        (a = ''),
                                        (r = []),
                                        i.forEach((e) => {
                                          var t = {
                                            title: e.title,
                                            url: e.url,
                                            image_responsive_url: theme.Images.lazyloadImagePath(e.image),
                                            image_aspect_ratio: e.featured_image.aspect_ratio,
                                            vendor: e.vendor,
                                            price_min: e.price_min,
                                          };
                                          r.push(t);
                                        }),
                                        r.length &&
                                          (a = `
          <div data-type-products>
            <div class="grid grid--uniform">
              ${theme.buildProductGridItem(r, 'small--one-half medium-up--one-quarter', 4, o.imageSize)}
            </div>
          </div>
        `),
                                        a);
                                      break;
                                    case 'collections':
                                      t[m] =
                                        ((c = g),
                                        (l = ''),
                                        c.length &&
                                          (l = `
          <div data-type-collections>
            <p class="h6 predictive__label">${theme.strings.searchCollections}</p>
            <ul class="no-bullets">
              ${theme.buildCollectionItem(c)}
            </ul>
          </div>
        `),
                                        l);
                                      break;
                                    case 'pages':
                                      t[m] =
                                        ((d = g),
                                        (h = ''),
                                        d.length &&
                                          (h = `
          <div data-type-pages>
            <p class="h6 predictive__label">${theme.strings.searchPages}</p>
            <ul class="no-bullets">
              ${theme.buildPageItem(d)}
            </ul>
          </div>
        `),
                                        h);
                                      break;
                                    case 'articles':
                                      t[m] =
                                        ((u = g),
                                        (p = ''),
                                        u.forEach((e) => {
                                          e.image &&
                                            (e.image = theme.Images.getSizedImageUrl(e.image, '200x200_crop_center'));
                                        }),
                                        u.length &&
                                          (p = `
          <div data-type-articles>
            <p class="h6 predictive__label">${theme.strings.searchArticles}</p>
            <div class="grid grid--uniform">
              ${theme.buildArticleItem(u, o.imageSize)}
            </div>
          </div>
        `),
                                        p);
                                  }
                                }),
                                0 === s)
                              ) {
                                c();
                                return;
                              }
                              var r,
                                l,
                                d =
                                  ((r = t),
                                  (l = ''),
                                  r.products && '' !== r.products && (l += r.products),
                                  r.collections && '' !== r.collections && (l += r.collections),
                                  r.pages && '' !== r.pages && (l += r.pages),
                                  r.articles && '' !== r.articles && (l += r.articles),
                                  l);
                              (a.results.innerHTML = ''), (a.results.innerHTML = d);
                            });
                      }
                    })(l);
                  }.bind(this),
                  500
                ));
            })();
        }
        function u(e) {
          return Object.keys(e)
            .map(function (t) {
              return t + '=' + encodeURIComponent(e[t]);
            })
            .join('&');
        }
        return {
          init: function e() {
            if (
              !document.getElementById('shopify-features') ||
              JSON.parse(document.getElementById('shopify-features').innerHTML).predictiveSearch
            )
              (a.wrapper = document.querySelector(n.wrapper)),
                a.wrapper &&
                  ((o.imageSize = a.wrapper.dataset.imageSize),
                  (a.form = document.querySelector(n.form)),
                  a.form.setAttribute('autocomplete', 'off'),
                  a.form.on('submit' + s, d),
                  (a.input = a.form.querySelector(n.input)),
                  a.input.on('keyup' + s, h),
                  (a.submit = a.wrapper.querySelector(n.searchButton)),
                  a.submit.on('click' + s, l),
                  (a.results = document.querySelector(n.resultDiv)));
          },
        };
      })()),
      (theme.buildProductGridItem = function (e, t, i, s) {
        var n = '';
        return (
          e.forEach((e) => {
            var a = theme.buildProductImage(e, s);
            let o = '',
              r = '';
            theme.settings.predictiveSearchPrice &&
              (o = `<div class="grid-product__price">${theme.strings.productFrom}${theme.Currency.formatMoney(
                e.price_min,
                theme.moneyFormat
              )}</div>`),
              theme.settings.predictiveSearchVendor && (r = `<div class="grid-product__vendor">${e.vendor}</div>`),
              (n += `
        <div class="grid__item grid-product ${t} aos-animate" data-aos="row-of-${i}">
          <div class="grid-product__content">
            <a href="${e.url}" class="grid-product__link">
              <div class="grid-product__image-mask">
                ${a}
              </div>
              <div class="grid-product__meta">
                <div class="grid-product__title">${e.title}</div>
                ${o}
                ${r}
              </div>
            </a>
          </div>
        </div>
      `);
          }),
          n
        );
      }),
      (theme.buildProductImage = function (e, t) {
        var i = t || theme.settings.productImageSize,
          s = '';
        if ('natural' === i)
          s = `
        <div class="image-wrap" style="height: 0; padding-bottom: ${e.image_aspect_ratio}%;">
          <img class="grid-product__image lazyload"
            data-src="${e.image_responsive_url}"
            data-widths="[180, 360, 540, 720, 900]"
            data-aspectratio="${e.image_aspect_ratio}"
            data-sizes="auto"
            alt="${e.title}">
        </div>`;
        else {
          var n = 'lazyload';
          theme.settings.productImageCover || (n += ' grid__image-contain'),
            (s = `
        <div class="grid__image-ratio grid__image-ratio--${i}">
          <img class="${n}"
              data-src="${e.image_responsive_url}"
              data-widths="[360, 540, 720, 900, 1080]"
              data-aspectratio="${e.aspect_ratio}"
              data-sizes="auto"
              alt="${e.title}">
        </div>
      `);
        }
        return s;
      }),
      (theme.buildCollectionItem = function (e) {
        var t = '';
        return (
          e.forEach((e) => {
            t += `
        <li>
          <a href="${e.url}">
            ${e.title}
          </a>
        </li>
      `;
          }),
          t
        );
      }),
      (theme.buildPageItem = function (e) {
        var t = '';
        return (
          e.forEach((e) => {
            t += `
        <li>
          <a href="${e.url}">
            ${e.title}
          </a>
        </li>
      `;
          }),
          t
        );
      }),
      (theme.buildArticleItem = function (e, t) {
        var i = '';
        return (
          e.forEach((e) => {
            i += `
        <div class="grid__item grid-product small--one-half medium-up--one-quarter" data-aos="row-of-4">
          <a href="${e.url}" class="grid-product__link grid-product__link--inline">
            <div class="grid-product__image-mask">
              <div
                class="grid__image-ratio grid__image-ratio--object grid__image-ratio--${t}">
                <div class="predictive__image-wrap">
                  ${theme.buildPredictiveImage(e)}
                </div>
              </div>
            </div>
            <div class="grid-product__meta">
              ${e.title}
            </div>
          </a>
        </div>
      `;
          }),
          i
        );
      }),
      (theme.buildPredictiveImage = function (e) {
        var t = '';
        return (
          e.image &&
            (t = `<img class="lazyload"
            data-src="${e.image}"
            data-widths="[360, 540, 720]"
            data-sizes="auto">`),
          t
        );
      });
    class u extends HTMLElement {
      constructor() {
        if (
          (super(),
          (this.cookieName = this.id),
          (this.cookie = Cookies.get(this.cookieName)),
          (this.classes = {
            activeContent: 'age-verification-popup__content--active',
            inactiveContent: 'age-verification-popup__content--inactive',
            inactiveDeclineContent: 'age-verification-popup__decline-content--inactive',
            activeDeclineContent: 'age-verification-popup__decline-content--active',
          }),
          (this.declineButton = this.querySelector('[data-age-verification-popup-decline-button]')),
          (this.declineContent = this.querySelector('[data-age-verification-popup-decline-content]')),
          (this.content = this.querySelector('[data-age-verification-popup-content]')),
          (this.returnButton = this.querySelector('[data-age-verification-popup-return-button]')),
          (this.exitButton = this.querySelector('[data-age-verification-popup-exit-button]')),
          (this.backgroundImage = this.querySelector('[data-background-image]')),
          (this.mobileBackgroundImage = this.querySelector('[data-mobile-background-image]')),
          Shopify.designMode &&
            (document.addEventListener('shopify:section:select', (e) => {
              e.detail.sectionId === this.dataset.sectionId && this.init();
            }),
            document.addEventListener('shopify:section:load', (e) => {
              if (e.detail.sectionId === this.dataset.sectionId) {
                this.init(), 'true' === this.dataset.testMode && this.cookie && Cookies.remove(this.cookieName);
                let t = sessionStorage.getItem(this.id);
                t && this.showDeclineContent();
              }
            })),
          (this.cookie || 'false' === this.dataset.enabled) && 'false' === this.dataset.testMode)
        )
          return;
        this.init();
      }
      init() {
        (this.modal = new theme.Modals(this.id, 'age-verification-popup-modal', { closeOffContentClick: !1 })),
          this.backgroundImage && (this.backgroundImage.style.display = 'block'),
          theme.config.bpSmall && this.mobileBackgroundImage && (this.mobileBackgroundImage.style.display = 'block'),
          this.modal.open(),
          theme.a11y.lockMobileScrolling(`#${this.id}`, document.querySelector('#MainContent')),
          this.declineButton &&
            this.declineButton.addEventListener('click', (e) => {
              e.preventDefault(),
                this.showDeclineContent(),
                Shopify.designMode && sessionStorage.setItem(this.id, 'second-view');
            }),
          this.returnButton &&
            this.returnButton.addEventListener('click', (e) => {
              e.preventDefault(), this.hideDeclineContent();
              let t = sessionStorage.getItem(this.id);
              Shopify.designMode && t && sessionStorage.removeItem(this.id);
            }),
          this.exitButton &&
            this.exitButton.addEventListener('click', (e) => {
              e.preventDefault(),
                'false' === this.dataset.testMode && Cookies.set(this.cookieName, 'entered', { expires: 30 }),
                this.backgroundImage && (this.backgroundImage.style.display = 'none'),
                theme.config.bpSmall &&
                  this.mobileBackgroundImage &&
                  (this.mobileBackgroundImage.style.display = 'none'),
                this.modal.close(),
                theme.a11y.unlockMobileScrolling(`#${this.id}`, document.querySelector('#MainContent'));
            });
      }
      showDeclineContent() {
        this.declineContent.classList.remove(this.classes.inactiveDeclineContent),
          this.declineContent.classList.add(this.classes.activeDeclineContent),
          this.content.classList.add(this.classes.inactiveContent),
          this.content.classList.remove(this.classes.activeContent);
      }
      hideDeclineContent() {
        this.declineContent.classList.add(this.classes.inactiveDeclineContent),
          this.declineContent.classList.remove(this.classes.activeDeclineContent),
          this.content.classList.remove(this.classes.inactiveContent),
          this.content.classList.add(this.classes.activeContent);
      }
    }
    customElements.define('age-verification-popup', u),
      (theme.Maps = (function () {
        var e = { zoom: 14 },
          t = null,
          i = [],
          s = {},
          n = { section: '[data-section-type="map"]', map: '[data-map]', mapOverlay: '.map-section__overlay' };
        function a(e) {
          (this.container = e),
            (this.sectionId = this.container.getAttribute('data-section-id')),
            (this.namespace = '.map-' + this.sectionId),
            (this.map = e.querySelector(n.map)),
            (this.key = this.map.dataset.apiKey),
            (s = {
              addressNoResults: theme.strings.addressNoResults,
              addressQueryLimit: theme.strings.addressQueryLimit,
              addressError: theme.strings.addressError,
              authError: theme.strings.authError,
            }),
            this.key &&
              theme.initWhenVisible({ element: this.container, callback: this.prepMapApi.bind(this), threshold: 20 });
        }
        return (
          (window.gm_authFailure = function () {
            Shopify.designMode &&
              (document.querySelectorAll(n.section).forEach((e) => {
                e.classList.add('map-section--load-error');
              }),
              document.querySelectorAll(n.map).forEach((e) => {
                e.parentNode.removeChild(e);
              }),
              window.mapError(theme.strings.authError));
          }),
          (window.mapError = function (e) {
            var t = document.createElement('div');
            t.classList.add('map-section__error', 'errors', 'text-center'),
              (t.innerHTML = e),
              document.querySelectorAll(n.mapOverlay).forEach((e) => {
                e.parentNode.prepend(t);
              }),
              document.querySelectorAll('.map-section__link').forEach((e) => {
                e.classList.add('hide');
              });
          }),
          (a.prototype = Object.assign({}, a.prototype, {
            prepMapApi: function () {
              if ('loaded' === t) this.createMap();
              else if (
                (i.push(this),
                'loading' !== t && ((t = 'loading'), void 0 === window.google || void 0 === window.google.maps))
              ) {
                var e = document.createElement('script');
                (e.onload = function () {
                  (t = 'loaded'),
                    i.forEach((e) => {
                      e.createMap();
                    });
                }),
                  (e.src = 'https://maps.googleapis.com/maps/api/js?key=' + this.key),
                  document.head.appendChild(e);
              }
            },
            createMap: function () {
              var t = this.map;
              return (function e(t) {
                var i = new google.maps.Geocoder();
                if (t) {
                  var s = t.dataset.addressSetting;
                  return new Promise((e, t) => {
                    i.geocode({ address: s }, function (i, s) {
                      s !== google.maps.GeocoderStatus.OK && t(s), e(i);
                    });
                  });
                }
              })(t)
                .then(
                  function (i) {
                    var s = {
                        zoom: e.zoom,
                        backgroundColor: 'none',
                        center: i[0].geometry.location,
                        draggable: !1,
                        clickableIcons: !1,
                        scrollwheel: !1,
                        disableDoubleClickZoom: !0,
                        disableDefaultUI: !0,
                      },
                      n = (this.map = new google.maps.Map(t, s)),
                      a = (this.center = n.getCenter());
                    new google.maps.Marker({ map: n, position: n.getCenter() }),
                      google.maps.event.addDomListener(
                        window,
                        'resize',
                        theme.utils.debounce(250, function () {
                          google.maps.event.trigger(n, 'resize'), n.setCenter(a), t.removeAttribute('style');
                        })
                      ),
                      Shopify.designMode && window.AOS && AOS.refreshHard();
                  }.bind(this)
                )
                .catch(function (e) {
                  var t;
                  switch (e) {
                    case 'ZERO_RESULTS':
                      t = s.addressNoResults;
                      break;
                    case 'OVER_QUERY_LIMIT':
                      t = s.addressQueryLimit;
                      break;
                    case 'REQUEST_DENIED':
                      t = s.authError;
                      break;
                    default:
                      t = s.addressError;
                  }
                  Shopify.designMode && window.mapError(t);
                });
            },
            onUnload: function () {
              0 !== this.map.length &&
                google &&
                google.maps &&
                google.maps.event &&
                google.maps.event.clearListeners(this.map, 'resize');
            },
          })),
          a
        );
      })()),
      (theme.NewsletterPopup = (function () {
        function e(e) {
          this.container = e;
          var t = this.container.getAttribute('data-section-id');
          if (
            ((this.cookieName = 'newsletter-' + t),
            (this.cookie = Cookies.get(this.cookieName)),
            e && '/challenge' !== window.location.pathname && '/password' !== window.location.pathname)
          ) {
            (this.data = {
              secondsBeforeShow: e.dataset.delaySeconds,
              daysBeforeReappear: e.dataset.delayDays,
              hasReminder: e.dataset.hasReminder,
              testMode: e.dataset.testMode,
              isEnabled: e.dataset.enabled,
            }),
              (this.modal = new theme.Modals('NewsletterPopup-' + t, 'newsletter-popup-modal'));
            var i = e.querySelector('.popup-cta a');
            if (
              (i &&
                i.addEventListener(
                  'click',
                  function () {
                    this.closePopup(!0);
                  }.bind(this)
                ),
              (e.querySelector('.errors') || e.querySelector('.note--success')) && this.modal.open(),
              e.querySelector('.note--success'))
            ) {
              this.closePopup(!0);
              return;
            }
            document.addEventListener('modalClose.' + e.id, this.closePopup.bind(this)),
              this.cookie || 'true' !== this.data.isEnabled || this.initPopupDelay(),
              document.addEventListener('reminder:openNewsletter', () => {
                this.modal.open();
              });
          }
        }
        return (
          (e.prototype = Object.assign({}, e.prototype, {
            initPopupDelay: function () {
              'true' !== this.data.testMode &&
                setTimeout(
                  function () {
                    let e = 'true' === sessionStorage.getItem('newsletterAppeared');
                    if (e) {
                      let t = new CustomEvent('newsletter:openReminder', { bubbles: !0 });
                      this.container.dispatchEvent(t);
                    } else this.modal.open(), sessionStorage.setItem('newsletterAppeared', !0);
                  }.bind(this),
                  1e3 * this.data.secondsBeforeShow
                );
            },
            closePopup: function (e) {
              if ('true' === this.data.testMode) {
                Cookies.remove(this.cookieName, { path: '/' });
                return;
              }
              var t = e ? 200 : this.data.daysBeforeReappear,
                i = 'true' === this.data.hasReminder,
                s = 'true' === sessionStorage.getItem('reminderAppeared');
              i && s
                ? Cookies.set(this.cookieName, 'opened', { path: '/', expires: t })
                : i || Cookies.set(this.cookieName, 'opened', { path: '/', expires: t });
            },
            onLoad: function () {
              this.modal.open();
            },
            onSelect: function () {
              this.modal.open();
            },
            onDeselect: function () {
              this.modal.close();
            },
            onBlockSelect: function () {
              this.modal.close();
            },
            onBlockDeselect: function () {
              this.modal.open();
            },
          })),
          e
        );
      })()),
      (theme.PasswordHeader = (function () {
        function e() {
          this.init();
        }
        return (
          (e.prototype = Object.assign({}, e.prototype, {
            init: function () {
              if (document.querySelector('#LoginModal')) {
                var e = new theme.Modals('LoginModal', 'login-modal', { focusIdOnOpen: 'password', solid: !0 });
                document.querySelectorAll('.errors').length && e.open();
              }
            },
          })),
          e
        );
      })()),
      (theme.Photoswipe = (function () {
        var e = {
          trigger: '.js-photoswipe__zoom',
          images: '.photoswipe__image',
          slideshowTrack: '.flickity-viewport ',
          activeImage: '.is-selected',
        };
        function t(e, t) {
          (this.container = e),
            (this.sectionId = t),
            (this.namespace = '.photoswipe-' + this.sectionId),
            this.gallery,
            this.images,
            this.items,
            (this.inSlideshow = !1),
            e && 'false' !== e.dataset.zoom && this.init();
        }
        return (
          (t.prototype = Object.assign({}, t.prototype, {
            init: function () {
              this.container.querySelectorAll(e.trigger).forEach((e) => {
                e.on('click' + this.namespace, this.triggerClick.bind(this));
              });
            },
            triggerClick: function (t) {
              this.container.dataset && 'true' === this.container.dataset.hasSlideshow
                ? (this.inSlideshow = !0)
                : (this.inSlideshow = !1),
                (this.items = this.getImageData());
              var i = this.inSlideshow ? this.container.querySelector(e.activeImage) : t.currentTarget,
                s = this.inSlideshow ? this.getChildIndex(i) : i.dataset.index;
              this.initGallery(this.items, s);
            },
            getChildIndex: function (e) {
              for (var t = 0; null != (e = e.previousSibling); ) t++;
              return t + 1;
            },
            getImageData: function () {
              this.images = this.inSlideshow
                ? this.container.querySelectorAll(e.slideshowTrack + e.images)
                : this.container.querySelectorAll(e.images);
              var t = [];
              return (
                this.images.forEach((e) => {
                  var i = {
                    msrc: e.currentSrc || e.src,
                    src: e.getAttribute('data-photoswipe-src'),
                    w: e.getAttribute('data-photoswipe-width'),
                    h: e.getAttribute('data-photoswipe-height'),
                    el: e,
                    initialZoomLevel: 0.5,
                  };
                  t.push(i);
                }),
                t
              );
            },
            initGallery: function (e, t) {
              var i = document.querySelectorAll('.pswp')[0];
              (this.gallery = new PhotoSwipe(i, PhotoSwipeUI_Default, e, {
                allowPanToNext: !1,
                captionEl: !1,
                closeOnScroll: !1,
                counterEl: !1,
                history: !1,
                index: t - 1,
                pinchToClose: !1,
                preloaderEl: !1,
                scaleMode: 'zoom',
                shareEl: !1,
                tapToToggleControls: !1,
                getThumbBoundsFn: function (t) {
                  var i = window.pageYOffset || document.documentElement.scrollTop,
                    s = e[t].el.getBoundingClientRect();
                  return { x: s.left, y: s.top + i, w: s.width };
                },
              })),
                this.gallery.listen('afterChange', this.afterChange.bind(this)),
                this.gallery.init(),
                this.preventiOS15Scrolling();
            },
            afterChange: function () {
              var e = this.gallery.getCurrentIndex();
              this.container.dispatchEvent(new CustomEvent('photoswipe:afterChange', { detail: { index: e } }));
            },
            syncHeight: function () {
              document.documentElement.style.setProperty('--window-inner-height', `${window.innerHeight}px`);
            },
            preventiOS15Scrolling: function () {
              let e;
              /iPhone|iPad|iPod/i.test(window.navigator.userAgent) &&
                (this.syncHeight(),
                (e = window.scrollY),
                document.documentElement.classList.add('pswp-open-in-ios'),
                window.addEventListener('resize', this.syncHeight),
                this.gallery.listen('destroy', () => {
                  document.documentElement.classList.remove('pswp-open-in-ios'), window.scrollTo(0, e);
                }));
            },
          })),
          t
        );
      })()),
      (theme.Recommendations = (function () {
        var e = {
          placeholder: '.product-recommendations-placeholder',
          sectionClass: ' .product-recommendations',
          productResults: '.grid-product',
        };
        function t(t) {
          (this.container = t),
            (this.sectionId = t.getAttribute('data-section-id')),
            (this.url = t.dataset.url),
            (e.recommendations = 'Recommendations-' + this.sectionId),
            theme.initWhenVisible({ element: t, callback: this.init.bind(this), threshold: 500 });
        }
        return (
          (t.prototype = Object.assign({}, t.prototype, {
            init: function () {
              var t = document.getElementById(e.recommendations);
              if (t && 'false' !== t.dataset.enable) {
                var i = this.url;
                if (Shopify.designMode) {
                  var s = t.querySelector(e.sectionClass);
                  s && (s.innerHTML = '');
                }
                fetch(i)
                  .then(function (e) {
                    return e.text();
                  })
                  .then(
                    function (i) {
                      var s = new DOMParser().parseFromString(i, 'text/html').querySelector(e.sectionClass),
                        n = t.querySelector(e.placeholder);
                      if (n) {
                        if (((n.innerHTML = ''), !s)) {
                          this.container.classList.add('hide'), AOS && AOS.refreshHard();
                          return;
                        }
                        n.appendChild(s),
                          theme.reinitProductGridItem(t),
                          document.dispatchEvent(new CustomEvent('recommendations:loaded', { detail: { section: t } })),
                          0 === s.querySelectorAll(e.productResults).length && this.container.classList.add('hide');
                      }
                    }.bind(this)
                  );
              }
            },
          })),
          t
        );
      })()),
      (theme.SlideshowSection = (function () {
        function e(e) {
          this.container = e;
          var t = e.getAttribute('data-section-id');
          if (
            ((this.slideshow = e.querySelector('#Slideshow-' + t)),
            (this.namespace = '.' + t),
            (this.initialIndex = 0),
            this.slideshow)
          ) {
            var i = e.parentElement;
            0 === [].indexOf.call(i.parentElement.children, i)
              ? this.init()
              : theme.initWhenVisible({ element: this.container, callback: this.init.bind(this) });
          }
        }
        return (
          (e.prototype = Object.assign({}, e.prototype, {
            init: function () {
              var e = this.slideshow.querySelectorAll('.slideshow__slide');
              if (
                (this.container.hasAttribute('data-immediate-load')
                  ? (this.slideshow.classList.remove('loading', 'loading--delayed'),
                    this.slideshow.classList.add('loaded'))
                  : theme.loadImageSection(this.slideshow),
                e.length > 1)
              ) {
                var t = {
                  prevNextButtons: this.slideshow.hasAttribute('data-arrows'),
                  pageDots: this.slideshow.hasAttribute('data-dots'),
                  fade: !0,
                  setGallerySize: !1,
                  initialIndex: this.initialIndex,
                  autoPlay: 'true' === this.slideshow.dataset.autoplay && parseInt(this.slideshow.dataset.speed),
                };
                this.flickity = new theme.Slideshow(this.slideshow, t);
              } else e[0].classList.add('is-selected');
            },
            forceReload: function () {
              this.onUnload(), this.init();
            },
            onUnload: function () {
              this.flickity && 'function' == typeof this.flickity.destroy && this.flickity.destroy();
            },
            onDeselect: function () {
              this.flickity && 'function' == typeof this.flickity.play && this.flickity.play();
            },
            onBlockSelect: function (e) {
              var t = parseInt(this.slideshow.querySelector('.slideshow__slide--' + e.detail.blockId).dataset.index);
              this.flickity && 'function' == typeof this.flickity.pause
                ? (this.flickity.goToSlide(t), this.flickity.pause())
                : ((this.initialIndex = t),
                  setTimeout(
                    function () {
                      this.flickity && 'function' == typeof this.flickity.pause && this.flickity.pause();
                    }.bind(this),
                    1e3
                  ));
            },
            onBlockDeselect: function () {
              this.flickity &&
                'function' == typeof this.flickity.play &&
                this.flickity.args.autoPlay &&
                this.flickity.play();
            },
          })),
          e
        );
      })()),
      (theme.StoreAvailability = (function () {
        var e = {
          drawerOpenBtn: '.js-drawer-open-availability',
          modalOpenBtn: '.js-modal-open-availability',
          productTitle: '[data-availability-product-title]',
        };
        function t(e) {
          (this.container = e), (this.baseUrl = e.dataset.baseUrl), (this.productTitle = e.dataset.productName);
        }
        return (
          (t.prototype = Object.assign({}, t.prototype, {
            updateContent: function (t) {
              var i = this.baseUrl + '/variants/' + t + '/?section_id=store-availability',
                s = this;
              fetch(i)
                .then(function (e) {
                  return e.text();
                })
                .then(function (t) {
                  if ('' === t.trim()) {
                    this.container.innerHTML = '';
                    return;
                  }
                  (s.container.innerHTML = t),
                    (s.container.innerHTML = s.container.firstElementChild.innerHTML),
                    s.container.querySelector(e.drawerOpenBtn) &&
                      (s.drawer = new theme.Drawers('StoreAvailabilityDrawer', 'availability')),
                    s.container.querySelector(e.modalOpenBtn) &&
                      (s.modal = new theme.Modals('StoreAvailabilityModal', 'availability'));
                  var i = s.container.querySelector(e.productTitle);
                  i && (i.textContent = s.productTitle);
                });
            },
          })),
          t
        );
      })()),
      (theme.VideoSection = (function () {
        var e = { videoParent: '.video-parent-section' };
        function t(e) {
          (this.container = e),
            (this.sectionId = e.getAttribute('data-section-id')),
            (this.namespace = '.video-' + this.sectionId),
            this.videoObject,
            theme.initWhenVisible({ element: this.container, callback: this.init.bind(this), threshold: 500 });
        }
        return (
          (t.prototype = Object.assign({}, t.prototype, {
            init: function () {
              var e = this.container.querySelector('.video-div');
              if (e) {
                var t = e.dataset.type;
                switch (t) {
                  case 'youtube':
                    var i = e.dataset.videoId;
                    this.initYoutubeVideo(i);
                    break;
                  case 'vimeo':
                    var i = e.dataset.videoId;
                    this.initVimeoVideo(i);
                    break;
                  case 'mp4':
                    this.initMp4Video();
                }
              }
            },
            initYoutubeVideo: function (t) {
              this.videoObject = new theme.YouTube('YouTubeVideo-' + this.sectionId, {
                videoId: t,
                videoParent: e.videoParent,
              });
            },
            initVimeoVideo: function (t) {
              this.videoObject = new theme.VimeoPlayer('Vimeo-' + this.sectionId, t, { videoParent: e.videoParent });
            },
            initMp4Video: function () {
              var t = 'Mp4Video-' + this.sectionId,
                i = document.getElementById(t),
                s = i.closest(e.videoParent);
              if (i) {
                s.classList.add('loaded');
                var n = document.querySelector('#' + t).play();
                void 0 !== n &&
                  n
                    .then(function () {})
                    .catch(function () {
                      i.setAttribute('controls', ''), s.classList.add('video-interactable');
                    });
              }
            },
            onUnload: function (e) {
              e.target.id.replace('shopify-section-', ''),
                this.videoObject && 'function' == typeof this.videoObject.destroy && this.videoObject.destroy();
            },
          })),
          t
        );
      })());
    class p extends HTMLElement {
      constructor() {
        super(),
          (this.el = this),
          (this.display = this.querySelector('[data-time-display]')),
          (this.block = this.closest('.countdown__block--timer')),
          (this.year = this.el.dataset.year),
          (this.month = this.el.dataset.month),
          (this.day = this.el.dataset.day),
          (this.hour = this.el.dataset.hour),
          (this.minute = this.el.dataset.minute),
          (this.daysPlaceholder = this.querySelector('[date-days-placeholder]')),
          (this.hoursPlaceholder = this.querySelector('[date-hours-placeholder]')),
          (this.minutesPlaceholder = this.querySelector('[date-minutes-placeholder]')),
          (this.secondsPlaceholder = this.querySelector('[date-seconds-placeholder]')),
          (this.messagePlaceholder = this.querySelector('[data-message-placeholder]')),
          (this.hideTimerOnComplete = this.el.dataset.hideTimer),
          (this.completeMessage = this.el.dataset.completeMessage),
          (this.timerComplete = !1),
          this.init();
      }
      init() {
        setInterval(() => {
          this.timerComplete || this._calculate();
        }, 1e3);
      }
      _calculate() {
        let e =
          +new Date(`${this.year}-${this.month}-${this.day} ${this.hour}:${this.minute}:00`).getTime() -
          +new Date().getTime();
        if (e > 0) {
          let t = {
            days: Math.floor(e / 864e5),
            hours: Math.floor((e / 36e5) % 24),
            minutes: Math.floor((e / 1e3 / 60) % 60),
            seconds: Math.floor((e / 1e3) % 60),
          };
          (this.daysPlaceholder.innerHTML = t.days),
            (this.hoursPlaceholder.innerHTML = t.hours),
            (this.minutesPlaceholder.innerHTML = t.minutes),
            (this.secondsPlaceholder.innerHTML = t.seconds);
        } else
          this.completeMessage &&
            this.messagePlaceholder &&
            this.messagePlaceholder.classList.add('countdown__timer-message--visible'),
            'true' === this.hideTimerOnComplete &&
              (this.display.classList.remove('countdown__display--visible'),
              this.display.classList.add('countdown__display--hidden')),
            this.completeMessage ||
              'true' !== this.hideTimerOnComplete ||
              this.block.classList.add('countdown__block--hidden'),
            (this.timerComplete = !0);
      }
    }
    customElements.define('countdown-timer', p);
    class f extends HTMLElement {
      constructor() {
        super(),
          (this.el = this),
          (this.buttons = this.querySelectorAll('[data-button]')),
          (this.hotspotBlocks = this.querySelectorAll('[data-hotspot-block]')),
          (this.blockContainer = this.querySelector('[data-block-container]')),
          (this.colorImages = this.querySelectorAll('.grid-product__color-image')),
          (this.colorSwatches = this.querySelectorAll('.color-swatch--with-image')),
          this._bindEvents(),
          this._setupQuickShop(),
          this.colorImages.length && this._colorSwatchHovering();
      }
      _colorSwatchHovering() {
        this.colorSwatches.forEach((e) => {
          e.addEventListener(
            'mouseenter',
            function () {
              this._setActiveColorImage(e);
            }.bind(this)
          ),
            e.addEventListener(
              'touchstart',
              function (t) {
                t.preventDefault(), this._setActiveColorImage(e);
              }.bind(this),
              { passive: !0 }
            ),
            e.addEventListener(
              'mouseleave',
              function () {
                this._removeActiveColorImage(e);
              }.bind(this)
            );
        });
      }
      _setActiveColorImage(e) {
        var t = e.dataset.variantId,
          i = e.dataset.variantImage;
        this.colorImages.forEach((e) => {
          e.classList.remove('is-active');
        }),
          this.colorSwatches.forEach((e) => {
            e.classList.remove('is-active');
          });
        var s = this.el.querySelector('.grid-product__color-image--' + t);
        (s.style.backgroundImage = 'url(' + i + ')'), s.classList.add('is-active'), e.classList.add('is-active');
        var n = e.dataset.url,
          a = e.closest('.grid-item__link');
        a && a.setAttribute('href', n);
      }
      _removeActiveColorImage(e) {
        let t = e.dataset.variantId;
        this.querySelector(`.grid-product__color-image--${t}`).classList.remove('is-active');
      }
      _bindEvents() {
        this.buttons.forEach((e) => {
          let t = e.dataset.button;
          e.on('click', (e) => {
            e.preventDefault(), e.stopPropagation(), this._showContent(t);
          });
        }),
          document.addEventListener('shopify:block:select', (e) => {
            let t = e.detail.blockId;
            this._showContent(`${t}`), this._setupQuickShop();
          });
      }
      _showContent(e) {
        this.hotspotBlocks.forEach((t) => {
          t.dataset.hotspotBlock === e ? t.classList.add('is-active') : t.classList.remove('is-active');
        });
      }
      _setupQuickShop() {
        this.querySelectorAll('[data-block-type="product"]').length > 0 &&
          ('function' == typeof theme.QuickShop
            ? new theme.QuickShop(this.blockContainer)
            : 'function' == typeof theme.initQuickShop && theme.initQuickShop(),
          'function' == typeof theme.QuickAdd && new theme.QuickAdd(this.blockContainer));
      }
    }
    customElements.define('hot-spots', f);
    class m extends HTMLElement {
      constructor() {
        super(),
          (this.el = this),
          (this.sectionId = this.dataset.sectionId),
          (this.button = this.querySelector('[data-button]')),
          (this.draggableContainer = this.querySelector('[data-draggable]')),
          (this.primaryImage = this.querySelector('[data-primary-image]')),
          (this.secondaryImage = this.querySelector('[data-secondary-image]')),
          this.calculateSizes(),
          (this.active = !1),
          (this.currentX = 0),
          (this.initialX = 0),
          (this.xOffset = 0),
          (this.buttonOffset = this.button.offsetWidth / 2),
          this.el.addEventListener('touchstart', this.dragStart, !1),
          this.el.addEventListener('touchend', this.dragEnd, !1),
          this.el.addEventListener('touchmove', this.drag, !1),
          this.el.addEventListener('mousedown', this.dragStart, !1),
          this.el.addEventListener('mouseup', this.dragEnd, !1),
          this.el.addEventListener('mousemove', this.drag, !1),
          window.on(
            'resize',
            theme.utils.debounce(250, () => {
              this.calculateSizes(!0);
            })
          ),
          document.addEventListener('shopify:section:load', (e) => {
            e.detail.sectionId === this.sectionId && this.calculateSizes();
          });
      }
      calculateSizes(e = !1) {
        (this.active = !1),
          (this.currentX = 0),
          (this.initialX = 0),
          (this.xOffset = 0),
          (this.buttonOffset = this.button.offsetWidth / 2),
          (this.elWidth = this.el.offsetWidth),
          (this.button.style.transform = `translate(-${this.buttonOffset}px, -50%)`),
          (this.primaryImage.style.width = `${this.elWidth}px`),
          e && (this.draggableContainer.style.width = `${this.elWidth / 2}px`);
      }
      dragStart(e) {
        'touchstart' === e.type
          ? (this.initialX = e.touches[0].clientX - this.xOffset)
          : (this.initialX = e.clientX - this.xOffset),
          e.target === this.button && (this.active = !0);
      }
      dragEnd(e) {
        (this.initialX = this.currentX), (this.active = !1);
      }
      drag(e) {
        this.active &&
          (e.preventDefault(),
          'touchmove' === e.type
            ? (this.currentX = e.touches[0].clientX - this.initialX)
            : (this.currentX = e.clientX - this.initialX),
          (this.xOffset = this.currentX),
          this.setTranslate(this.currentX, this.button));
      }
      setTranslate(e, t) {
        let i = e - this.buttonOffset,
          s = this.elWidth / 2 + e,
          n = -((this.elWidth / 2 + this.buttonOffset) * 1),
          a = this.elWidth / 2 - this.buttonOffset;
        i < n + 50 ? ((i = n + 50), (s = 50)) : i > a - 50 && ((i = a - 50), (s = this.elWidth - 50)),
          (t.style.transform = `translate(${i}px, -50%)`),
          (this.draggableContainer.style.width = `${s}px`);
      }
    }
    customElements.define('image-compare', m),
      (theme.Blog = (function () {
        function e(e) {
          this.tagFilters();
        }
        return (
          (e.prototype = Object.assign({}, e.prototype, {
            tagFilters: function () {
              var e = document.getElementById('BlogTagFilter');
              e &&
                e.addEventListener('change', function () {
                  location.href = e.value;
                });
            },
          })),
          e
        );
      })()),
      (theme.CollectionHeader = (function () {
        var e = !1;
        function t(t) {
          this.namespace = '.collection-header';
          var i = t.querySelector('.collection-hero');
          i
            ? (e && this.checkIfNeedReload(), theme.loadImageSection(i))
            : theme.settings.overlayHeader && theme.headerNav.disableOverlayHeader(),
            (e = !0);
        }
        return (
          (t.prototype = Object.assign({}, t.prototype, {
            checkIfNeedReload: function () {
              Shopify.designMode &&
                theme.settings.overlayHeader &&
                (document.querySelector('.header-wrapper').classList.contains('header-wrapper--overlay') ||
                  location.reload());
            },
          })),
          t
        );
      })()),
      (theme.CollectionSidebar = (function () {
        var e = !1,
          t = { sidebar: '#CollectionSidebar' };
        function i(e) {
          this.container = e.querySelector(t.sidebar);
        }
        return (
          (i.prototype = Object.assign({}, i.prototype, {
            init: function () {
              this.container &&
                (this.onUnload(),
                (e = 'drawer' === this.container.dataset.style),
                (theme.FilterDrawer = new theme.Drawers('FilterDrawer', 'collection-filters', !0)));
            },
            forceReload: function () {
              this.init();
            },
            onSelect: function () {
              if (theme.FilterDrawer) {
                if (!e) {
                  theme.FilterDrawer.close();
                  return;
                }
                (e || theme.config.bpSmall) && theme.FilterDrawer.open();
              }
            },
            onDeselect: function () {
              theme.FilterDrawer && theme.FilterDrawer.close();
            },
            onUnload: function () {
              theme.FilterDrawer && theme.FilterDrawer.close();
            },
          })),
          i
        );
      })()),
      (theme.Collection = (function () {
        var e = !1,
          t = {
            sortSelect: '#SortBy',
            colorSwatchImage: '.grid-product__color-image',
            colorSwatch: '.color-swatch--with-image',
            collectionGrid: '.collection-grid__wrapper',
            trigger: '.collapsible-trigger',
            sidebar: '#CollectionSidebar',
            filterSidebar: '.collapsible-content--sidebar',
            activeTagList: '.tag-list--active-tags',
            tags: '.tag-list input',
            activeTags: '.tag-list a',
            tagsForm: '.filter-form',
            filters: '.collection-filter',
            priceRange: '.price-range',
          },
          i = {
            activeTag: 'tag--active',
            removeTagParent: 'tag--remove',
            filterSidebar: 'collapsible-content--sidebar',
            isOpen: 'is-open',
          };
        function s(e) {
          (this.container = e),
            (this.sectionId = e.getAttribute('data-section-id')),
            (this.namespace = '.collection-' + this.sectionId),
            (this.sidebar = new theme.CollectionSidebar(e)),
            (this.ajaxRenderer = new theme.AjaxRenderer({
              sections: [{ sectionId: this.sectionId, nodeId: 'CollectionAjaxContent' }],
              onReplace: this.onReplaceAjaxContent.bind(this),
            })),
            this.init();
        }
        return (
          (s.prototype = Object.assign({}, s.prototype, {
            init: function () {
              this.initSort(),
                this.colorSwatchHovering(),
                this.initFilters(),
                this.initPriceRange(),
                this.sidebar.init();
            },
            initSort: function () {
              (this.sortSelect = document.querySelector(t.sortSelect)),
                this.sortSelect &&
                  ((this.defaultSort = this.getDefaultSortValue()),
                  this.sortSelect.on('change' + this.namespace, this.onSortChange.bind(this)));
            },
            getSortValue: function () {
              return this.sortSelect.value || this.defaultSort;
            },
            getDefaultSortValue: function () {
              return this.sortSelect.getAttribute('data-default-sortby');
            },
            onSortChange: function () {
              (this.queryParams = new URLSearchParams(window.location.search)),
                this.queryParams.set('sort_by', this.getSortValue()),
                this.queryParams.delete('page'),
                (window.location.search = this.queryParams.toString());
            },
            colorSwatchHovering: function () {
              this.container.querySelectorAll(t.colorSwatchImage).length &&
                this.container.querySelectorAll(t.colorSwatch).forEach((e) => {
                  e.addEventListener('mouseenter', function () {
                    var t = e.dataset.variantId,
                      i = e.dataset.variantImage,
                      s = document.querySelector('.grid-product__color-image--' + t);
                    (s.style.backgroundImage = 'url(' + i + ')'), s.classList.add('is-active');
                  }),
                    e.addEventListener('mouseleave', function () {
                      var t = e.dataset.variantId;
                      document.querySelector('.grid-product__color-image--' + t).classList.remove('is-active');
                    });
                });
            },
            initFilters: function () {
              document.querySelectorAll(t.tags).length &&
                (this.bindBackButton(),
                theme.config.stickyHeader &&
                  (this.setFilterStickyPosition(),
                  window.on('resize', theme.utils.debounce(500, this.setFilterStickyPosition))),
                document.querySelectorAll(t.activeTags).forEach((e) => {
                  e.addEventListener('click', this.tagClick.bind(this));
                }),
                document.querySelectorAll(t.tagsForm).forEach((e) => {
                  e.addEventListener('input', this.onFormSubmit.bind(this));
                }));
            },
            initPriceRange: function () {
              let e = document.querySelectorAll(t.priceRange);
              e.forEach((e) => new theme.PriceRange(e, { onChange: this.renderFromFormData.bind(this) }));
            },
            tagClick: function (t) {
              var i = t.currentTarget;
              if (
                (theme.FilterDrawer && theme.FilterDrawer.close(),
                i.classList.contains('no-ajax') || (t.preventDefault(), e))
              )
                return;
              e = !0;
              let s = i.parentNode,
                n = new URL(i.href);
              this.renderActiveTag(s, i),
                this.updateScroll(!0),
                this.startLoading(),
                this.renderCollectionPage(n.searchParams);
            },
            onFormSubmit: function (t) {
              var i = t.target;
              if (
                (theme.FilterDrawer && theme.FilterDrawer.close(),
                i.classList.contains('no-ajax') || (t.preventDefault(), e))
              )
                return;
              e = !0;
              let s = i.closest('li'),
                n = i.closest('form'),
                a = new FormData(n);
              this.renderActiveTag(s, i), this.updateScroll(!0), this.startLoading(), this.renderFromFormData(a);
            },
            fetchOpenCollasibleFilters: function () {
              return Array.from(document.querySelectorAll(`${t.sidebar} ${t.trigger}.${i.isOpen}`)).map(
                (e) => e.dataset.collapsibleId
              );
            },
            renderActiveTag: function (e, s) {
              let n = e.querySelector('.tag__text');
              e.classList.contains(i.activeTag)
                ? e.classList.remove(i.activeTag)
                : (e.classList.add(i.activeTag),
                  s.closest('li').classList.contains(i.removeTagParent)
                    ? e.remove()
                    : document.querySelectorAll(t.activeTagList).forEach((e) => {
                        let t = document.createElement('li'),
                          i = document.createElement('a');
                        t.classList.add('tag', 'tag--remove'),
                          i.classList.add('btn', 'btn--small'),
                          (i.innerText = n.innerText),
                          t.appendChild(i),
                          e.appendChild(t);
                      }));
            },
            renderFromFormData: function (e) {
              let t = new URLSearchParams(e);
              this.renderCollectionPage(t);
            },
            onReplaceAjaxContent: function (e, t) {
              let i = this.fetchOpenCollasibleFilters();
              i.forEach((t) => {
                e.querySelectorAll(`[data-collapsible-id=${t}]`).forEach(this.openCollapsible);
              });
              var s = e.getElementById(t.nodeId);
              s && (document.getElementById(t.nodeId).innerHTML = s.innerHTML);
            },
            openCollapsible: function (e) {
              e.classList.contains(i.filterSidebar) && (e.style.height = 'auto'), e.classList.add(i.isOpen);
            },
            renderCollectionPage: function (t, i = !0) {
              this.ajaxRenderer.renderPage(window.location.pathname, t, i).then(() => {
                theme.sections.reinit('collection-grid'),
                  this.updateScroll(!1),
                  this.initPriceRange(),
                  theme.reinitProductGridItem(),
                  document.dispatchEvent(new CustomEvent('collection:reloaded')),
                  (e = !1);
              });
            },
            bindBackButton: function () {
              window.off('popstate' + this.namespace),
                window.on(
                  'popstate' + this.namespace,
                  function (e) {
                    if (e) {
                      let t = new URL(window.location.href);
                      this.renderCollectionPage(t.searchParams, !1);
                    }
                  }.bind(this)
                );
            },
            updateScroll: function (e) {
              var t = document.querySelector('[data-scroll-to]'),
                i = t && t.offsetTop;
              theme.config.bpSmall || (i -= 15),
                theme.config.stickyHeader && (i -= document.querySelector('.site-header').offsetHeight),
                e ? window.scrollTo({ top: i, behavior: 'smooth' }) : window.scrollTo({ top: i });
            },
            setFilterStickyPosition: function () {
              var e = document.querySelector('.site-header').offsetHeight;
              document.querySelector(t.filters).style.top = e + 10 + 'px';
              var i = document.querySelector('.grid__item--sidebar');
              i && (i.style.top = e + 10 + 'px');
            },
            forceReload: function () {
              this.init();
            },
            startLoading: function () {
              document.querySelector(t.collectionGrid).classList.add('unload');
            },
          })),
          s
        );
      })()),
      (theme.FooterSection = (function () {
        var e = { locale: '[data-disclosure-locale]', currency: '[data-disclosure-currency]' };
        function t(e) {
          (this.container = e), (this.localeDisclosure = null), (this.currencyDisclosure = null), this.init();
        }
        return (
          (t.prototype = Object.assign({}, t.prototype, {
            init: function () {
              var t = this.container.querySelector(e.locale),
                i = this.container.querySelector(e.currency);
              t && (this.localeDisclosure = new theme.Disclosure(t)),
                i && (this.currencyDisclosure = new theme.Disclosure(i));
              var s = document.querySelector('.footer__newsletter-input');
              s &&
                s.addEventListener('keyup', function () {
                  s.classList.add('footer__newsletter-input--active');
                }),
                theme.collapsibles.init(this.container);
            },
            onUnload: function () {
              this.localeDisclosure && this.localeDisclosure.destroy(),
                this.currencyDisclosure && this.currencyDisclosure.destroy();
            },
          })),
          t
        );
      })()),
      (theme.HeaderSection = (function () {
        var e = { locale: '[data-disclosure-locale]', currency: '[data-disclosure-currency]' };
        function t(e) {
          (this.container = e), (this.sectionId = this.container.getAttribute('data-section-id')), this.init();
        }
        return (
          (t.prototype = Object.assign({}, t.prototype, {
            init: function () {
              Shopify &&
                Shopify.designMode &&
                (theme.sections.reinit('slideshow-section'),
                setTimeout(function () {
                  window.dispatchEvent(new Event('resize'));
                }, 500)),
                this.initDrawers(),
                this.initDisclosures(),
                theme.headerNav.init(),
                theme.announcementBar.init();
            },
            initDisclosures: function () {
              var t = this.container.querySelector(e.locale),
                i = this.container.querySelector(e.currency);
              t && (this.localeDisclosure = new theme.Disclosure(t)),
                i && (this.currencyDisclosure = new theme.Disclosure(i));
            },
            initDrawers: function () {
              (theme.NavDrawer = new theme.Drawers('NavDrawer', 'nav')),
                'drawer' !== theme.settings.cartType ||
                  document.body.classList.contains('template-cart') ||
                  new theme.CartDrawer(),
                theme.collapsibles.init(document.getElementById('NavDrawer'));
            },
            onBlockSelect: function (e) {
              theme.announcementBar.onBlockSelect(e.detail.blockId);
            },
            onBlockDeselect: function () {
              theme.announcementBar.onBlockDeselect();
            },
            onUnload: function () {
              theme.NavDrawer.close(),
                theme.announcementBar.unload(),
                this.localeDisclosure && this.localeDisclosure.destroy(),
                this.currencyDisclosure && this.currencyDisclosure.destroy();
            },
          })),
          t
        );
      })()),
      (theme.Product = (function () {
        var e = {},
          t = {
            onSale: 'on-sale',
            disabled: 'disabled',
            isModal: 'is-modal',
            loading: 'loading',
            loaded: 'loaded',
            hidden: 'hide',
            interactable: 'video-interactable',
            visuallyHide: 'visually-invisible',
          },
          i = {
            productVideo: '.product__video',
            videoParent: '.product__video-wrapper',
            slide: '.product-main-slide',
            currentSlide: '.is-selected',
            startingSlide: '.starting-slide',
            variantType: '.variant-wrapper',
            blocks: '[data-product-blocks]',
            blocksHolder: '[data-blocks-holder]',
            dynamicVariantsEnabled: '[data-dynamic-variants-enabled]',
          };
        function s(e) {
          this.container = e;
          var t = (this.sectionId = e.getAttribute('data-section-id')),
            i = (this.productId = e.getAttribute('data-product-id'));
          (this.inModal = 'true' === e.dataset.modal),
            this.modal,
            (this.settings = {
              enableHistoryState: e.dataset.history || !1,
              namespace: '.product-' + t,
              inventory: !1,
              inventoryThreshold: 10,
              modalInit: !1,
              hasImages: !0,
              imageSetName: null,
              imageSetIndex: null,
              currentImageSet: null,
              imageSize: '620x',
              currentSlideIndex: 0,
              videoLooping: e.dataset.videoLooping,
            }),
            this.inModal &&
              ((this.settings.enableHistoryState = !1),
              (this.settings.namespace = '.product-' + t + '-modal'),
              (this.modal = document.getElementById('QuickShopModal-' + i))),
            (this.selectors = {
              variantsJson: '[data-variant-json]',
              currentVariantJson: '[data-current-variant-json]',
              form: '.product-single__form',
              media: '[data-product-media-type-model]',
              closeMedia: '.product-single__close-media',
              photoThumbs: '[data-product-thumb]',
              thumbSlider: '[data-product-thumbs]',
              thumbScroller: '.product__thumbs--scroller',
              mainSlider: '[data-product-photos]',
              imageContainer: '[data-product-images]',
              productImageMain: '[data-product-image-main]',
              priceWrapper: '[data-product-price-wrap]',
              price: '[data-product-price]',
              comparePrice: '[data-compare-price]',
              savePrice: '[data-save-price]',
              priceA11y: '[data-a11y-price]',
              comparePriceA11y: '[data-compare-price-a11y]',
              unitWrapper: '[data-unit-price-wrapper]',
              unitPrice: '[data-unit-price]',
              unitPriceBaseUnit: '[data-unit-base]',
              sku: '[data-sku]',
              inventory: '[data-product-inventory]',
              incomingInventory: '[data-incoming-inventory]',
              colorLabel: '[data-variant-color-label]',
              addToCart: '[data-add-to-cart]',
              addToCartText: '[data-add-to-cart-text]',
              originalSelectorId: '[data-product-select]',
              singleOptionSelector: '[data-variant-input]',
              variantColorSwatch: '.variant__input--color-swatch',
              dynamicVariantsEnabled: '[data-dynamic-variants-enabled]',
              availabilityContainer: '[data-store-availability-holder]',
            }),
            this.cacheElements(),
            (this.firstProductImage = this.cache.mainSlider.querySelector('img')),
            this.firstProductImage || (this.settings.hasImages = !1);
          var s = this.cache.mainSlider.querySelector('[data-set-name]');
          s && (this.settings.imageSetName = s.dataset.setName), this.init();
        }
        return (
          (s.prototype = Object.assign({}, s.prototype, {
            init: function () {
              this.inModal &&
                (this.container.classList.add(t.isModal),
                document.addEventListener(
                  'modalOpen.QuickShopModal-' + this.productId,
                  this.openModalProduct.bind(this)
                ),
                document.addEventListener(
                  'modalClose.QuickShopModal-' + this.productId,
                  this.closeModalProduct.bind(this)
                )),
                this.inModal ||
                  (this.formSetup(),
                  this.productSetup(),
                  this.videoSetup(),
                  this.initProductSlider(),
                  this.customMediaListners(),
                  this.addIdToRecentlyViewed());
            },
            cacheElements: function () {
              this.cache = {
                form: this.container.querySelector(this.selectors.form),
                mainSlider: this.container.querySelector(this.selectors.mainSlider),
                thumbSlider: this.container.querySelector(this.selectors.thumbSlider),
                thumbScroller: this.container.querySelector(this.selectors.thumbScroller),
                productImageMain: this.container.querySelector(this.selectors.productImageMain),
                priceWrapper: this.container.querySelector(this.selectors.priceWrapper),
                comparePriceA11y: this.container.querySelector(this.selectors.comparePriceA11y),
                comparePrice: this.container.querySelector(this.selectors.comparePrice),
                price: this.container.querySelector(this.selectors.price),
                savePrice: this.container.querySelector(this.selectors.savePrice),
                priceA11y: this.container.querySelector(this.selectors.priceA11y),
              };
            },
            formSetup: function () {
              this.initQtySelector(),
                this.initAjaxProductForm(),
                this.availabilitySetup(),
                this.initVariants(),
                this.settings.imageSetName && this.updateImageSet();
            },
            availabilitySetup: function () {
              var e = this.container.querySelector(this.selectors.availabilityContainer);
              e && (this.storeAvailability = new theme.StoreAvailability(e));
            },
            productSetup: function () {
              this.setImageSizes(),
                this.initImageZoom(),
                this.initModelViewerLibraries(),
                this.initShopifyXrLaunch(),
                window.SPR && (SPR.initDomEls(), SPR.loadBadges());
            },
            setImageSizes: function () {
              if (this.settings.hasImages) {
                var e = this.firstProductImage.currentSrc;
                e && (this.settings.imageSize = theme.Images.imageSize(e));
              }
            },
            addIdToRecentlyViewed: function () {
              var e = this.container.getAttribute('data-product-handle'),
                t = this.container.getAttribute('data-product-url'),
                i = this.container.getAttribute('data-aspect-ratio'),
                s = this.container.getAttribute('data-img-url');
              theme.recentlyViewed.recent.hasOwnProperty(e) && delete theme.recentlyViewed.recent[e],
                (theme.recentlyViewed.recent[e] = { url: t, aspectRatio: i, featuredImage: s }),
                theme.config.hasLocalStorage &&
                  window.localStorage.setItem('theme-recent', JSON.stringify(theme.recentlyViewed.recent));
            },
            initVariants: function () {
              var e = this.container.querySelector(this.selectors.variantsJson);
              if (e) {
                this.variantsObject = JSON.parse(e.innerHTML);
                var t = !!this.container.querySelector(i.dynamicVariantsEnabled),
                  s = {
                    container: this.container,
                    enableHistoryState: this.settings.enableHistoryState,
                    singleOptionSelector: this.selectors.singleOptionSelector,
                    originalSelectorId: this.selectors.originalSelectorId,
                    variants: this.variantsObject,
                    dynamicVariantsEnabled: t,
                  };
                console.log(s);
                var n = this.container.querySelectorAll(this.selectors.variantColorSwatch);

                if (
                  (n.length &&
                    n.forEach((e) => {
                      e.addEventListener(
                        'change',
                        function (t) {
                          var i = e.dataset.colorName,
                            s = e.dataset.colorIndex;
                          this.updateColorName(i, s);
                        }.bind(this)
                      );
                    }),
                  (this.variants = new theme.Variants(s)),
                  this.storeAvailability)
                ) {
                  var a = this.variants.currentVariant ? this.variants.currentVariant.id : this.variants.variants[0].id;
                  this.storeAvailability.updateContent(a),
                    this.container.on('variantChange' + this.settings.namespace, this.updateAvailability.bind(this));
                }
                this.container.on('variantChange' + this.settings.namespace, this.updateCartButton.bind(this)),
                  this.container.on('variantImageChange' + this.settings.namespace, this.updateVariantImage.bind(this)),
                  this.container.on('variantPriceChange' + this.settings.namespace, this.updatePrice.bind(this)),
                  this.container.on(
                    'variantUnitPriceChange' + this.settings.namespace,
                    this.updateUnitPrice.bind(this)
                  ),
                  this.container.querySelector(this.selectors.sku) &&
                    this.container.on('variantSKUChange' + this.settings.namespace, this.updateSku.bind(this));
                var o = this.container.querySelector(this.selectors.inventory);
                if (
                  (o &&
                    ((this.settings.inventory = !0),
                    (this.settings.inventoryThreshold = o.dataset.threshold),
                    this.container.on('variantChange' + this.settings.namespace, this.updateInventory.bind(this))),
                  t)
                ) {
                  var r = this.container.querySelector(this.selectors.currentVariantJson);
                  if (r) {
                    var c = this.container.querySelector(i.variantType);
                    c &&
                      new theme.VariantAvailability({
                        container: this.container,
                        namespace: this.settings.namespace,
                        type: c.dataset.type,
                        variantsObject: this.variantsObject,
                        currentVariantObject: JSON.parse(r.innerHTML),
                      });
                  }
                }
                if (this.settings.imageSetName) {
                  var l = this.container.querySelector(
                    '.variant-input-wrap[data-handle="' + this.settings.imageSetName + '"]'
                  );
                  l
                    ? ((this.settings.imageSetIndex = l.dataset.index),
                      this.container.on('variantChange' + this.settings.namespace, this.updateImageSet.bind(this)))
                    : (this.settings.imageSetName = null);
                }
              }
            },
            initQtySelector: function () {
              this.container.querySelectorAll('.js-qty__wrapper').forEach((e) => {
                new theme.QtySelector(e, { namespace: '.product' });
              });
            },
            initAjaxProductForm: function () {
              'drawer' === theme.settings.cartType && new theme.AjaxProduct(this.cache.form);
            },
            updateColorName: function (e, t) {
              this.container.querySelector(this.selectors.colorLabel + `[data-index="${t}"`).textContent = e;
            },
            updateCartButton: function (e) {
              var i = e.detail.variant,
                s = this.container.querySelector(this.selectors.addToCart),
                n = this.container.querySelector(this.selectors.addToCartText);
              if (s) {
                if (i) {
                  if (i.available) {
                    s.classList.remove(t.disabled), (s.disabled = !1);
                    var a = n.dataset.defaultText;
                    n.textContent = a;
                  } else s.classList.add(t.disabled), (s.disabled = !0), (n.textContent = theme.strings.soldOut);
                } else s.classList.add(t.disabled), (s.disabled = !0), (n.textContent = theme.strings.unavailable);
              }
            },
            updatePrice: function (e) {
              var i = e.detail.variant;
              if (i) {
                if (
                  ((this.cache.price.innerHTML = theme.Currency.formatMoney(i.price, theme.settings.moneyFormat)),
                  i.compare_at_price > i.price)
                ) {
                  (this.cache.comparePrice.innerHTML = theme.Currency.formatMoney(
                    i.compare_at_price,
                    theme.settings.moneyFormat
                  )),
                    this.cache.priceWrapper.classList.remove(t.hidden),
                    this.cache.price.classList.add(t.onSale),
                    this.cache.comparePriceA11y.setAttribute('aria-hidden', 'false'),
                    this.cache.priceA11y.setAttribute('aria-hidden', 'false');
                  var s = i.compare_at_price - i.price;
                  (s =
                    'percent' == theme.settings.saveType
                      ? Math.round((100 * s) / i.compare_at_price) + '%'
                      : theme.Currency.formatMoney(s, theme.settings.moneyFormat)),
                    this.cache.savePrice.classList.remove(t.hidden),
                    (this.cache.savePrice.innerHTML = theme.strings.savePrice.replace('[saved_amount]', s));
                } else
                  this.cache.priceWrapper && this.cache.priceWrapper.classList.add(t.hidden),
                    this.cache.savePrice.classList.add(t.hidden),
                    this.cache.price.classList.remove(t.onSale),
                    this.cache.comparePriceA11y && this.cache.comparePriceA11y.setAttribute('aria-hidden', 'true'),
                    this.cache.priceA11y.setAttribute('aria-hidden', 'true');
              }
            },
            updateUnitPrice: function (e) {
              var i = e.detail.variant;
              i && i.unit_price
                ? ((this.container.querySelector(this.selectors.unitPrice).innerHTML = theme.Currency.formatMoney(
                    i.unit_price,
                    theme.settings.moneyFormat
                  )),
                  (this.container.querySelector(this.selectors.unitPriceBaseUnit).innerHTML =
                    theme.Currency.getBaseUnit(i)),
                  this.container.querySelector(this.selectors.unitWrapper).classList.remove(t.hidden))
                : this.container.querySelector(this.selectors.unitWrapper).classList.add(t.hidden);
            },
            imageSetArguments: function (e) {
              var e = e || (this.variants ? this.variants.currentVariant : null);
              if (e) {
                var t = (this.settings.currentImageSet = this.getImageSetName(e[this.settings.imageSetIndex])),
                  i = this.settings.imageSetName + '_' + t;
                return (
                  (this.settings.currentSlideIndex = 0),
                  {
                    cellSelector: '[data-group="' + i + '"]',
                    imageSet: i,
                    initialIndex: this.settings.currentSlideIndex,
                  }
                );
              }
            },
            updateImageSet: function (e) {
              var t = e ? e.detail.variant : this.variants ? this.variants.currentVariant : null;
              if (t) {
                var i = this.getImageSetName(t[this.settings.imageSetIndex]);
                this.settings.currentImageSet !== i && this.initProductSlider(t);
              }
            },
            updateImageSetThumbs: function (e) {
              this.cache.thumbSlider.querySelectorAll('.product__thumb-item').forEach((i) => {
                i.classList.toggle(t.hidden, i.dataset.group !== e);
              });
            },
            getImageSetName: function (e) {
              return e
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/-$/, '')
                .replace(/^-/, '');
            },
            updateSku: function (e) {
              var t = e.detail.variant,
                i = '';
              t && (t.sku && (i = t.sku), (this.container.querySelector(this.selectors.sku).textContent = i));
            },
            updateInventory: function (e) {
              var t = e.detail.variant;
              if (!t || !t.inventory_management || 'continue' === t.inventory_policy) {
                this.toggleInventoryQuantity(t, !1), this.toggleIncomingInventory(!1);
                return;
              }
              if ('shopify' === t.inventory_management && window.inventories && window.inventories[this.productId]) {
                var i = window.inventories[this.productId][t.id],
                  s = i.quantity,
                  n = !0,
                  a = !1;
                (s <= 0 || s > this.settings.inventoryThreshold || 'continue' === i.policy) && (n = !1),
                  this.toggleInventoryQuantity(t, i),
                  n && 'true' === i.incoming && s <= this.settings.inventoryThreshold && (a = !0),
                  this.toggleIncomingInventory(a, t.available, i.next_incoming_date);
              }
            },
            updateAvailability: function (e) {
              var t = e.detail.variant;
              t && this.storeAvailability.updateContent(t.id);
            },
            toggleInventoryQuantity: function (e, i) {
              let { quantity: s, policy: n } = i || {};
              if (this.settings.inventory) {
                var a = this.container.querySelector(this.selectors.inventory),
                  o = a.closest('.product-block');
                parseInt(s) <= parseInt(this.settings.inventoryThreshold) && 'continue' !== n
                  ? (a.parentNode.classList.add('inventory--low'),
                    s > 1
                      ? (a.textContent = theme.strings.otherStockLabel.replace('[count]', s))
                      : (a.textContent = theme.strings.oneStockLabel.replace('[count]', s)))
                  : (a.parentNode.classList.remove('inventory--low'), (a.textContent = theme.strings.inStockLabel)),
                  e && e.available
                    ? (a.parentNode.classList.remove(t.hidden), o && o.classList.remove(t.hidden))
                    : (a.parentNode.classList.add(t.hidden), o && o.classList.add(t.hidden));
              }
            },
            toggleIncomingInventory: function (e, i, s) {
              var n = this.container.querySelector(this.selectors.incomingInventory);
              if (n) {
                var a = n.closest('.product-block'),
                  o = n.querySelector('.js-incoming-text');
                if (e) {
                  var r = i
                    ? theme.strings.willNotShipUntil.replace('[date]', s)
                    : theme.strings.willBeInStockAfter.replace('[date]', s);
                  s || (r = theme.strings.waitingForStock),
                    n.classList.remove(t.hidden),
                    a && a.classList.remove(t.hidden),
                    (o.textContent = r);
                } else n.classList.add(t.hidden);
              }
            },
            videoSetup: function () {
              var e = this.cache.mainSlider.querySelectorAll(i.productVideo);
              if (!e.length) return !1;
              e.forEach((e) => {
                var t = e.dataset.videoType;
                'youtube' === t
                  ? this.initYoutubeVideo(e)
                  : 'vimeo' === t
                  ? this.initVimeoVideo(e)
                  : 'mp4' === t && this.initMp4Video(e);
              });
            },
            initYoutubeVideo: function (t) {
              e[t.id] = new theme.YouTube(t.id, {
                videoId: t.dataset.videoId,
                videoParent: i.videoParent,
                autoplay: !1,
                style: t.dataset.videoStyle,
                loop: t.dataset.videoLoop,
                events: {
                  onReady: this.youtubePlayerReady.bind(this),
                  onStateChange: this.youtubePlayerStateChange.bind(this),
                },
              });
            },
            initVimeoVideo: function (t) {
              e[t.id] = new theme.VimeoPlayer(t.id, t.dataset.videoId, {
                videoParent: i.videoParent,
                autoplay: !1,
                style: t.dataset.videoStyle,
                loop: t.dataset.videoLoop,
              });
            },
            youtubePlayerReady: function (t) {
              var i = t.target.getIframe().id;
              if (e[i]) {
                var s = e[i],
                  n = s.videoPlayer;
                'sound' !== s.options.style && n.mute(),
                  s.parent.classList.remove('loading'),
                  s.parent.classList.add('loaded'),
                  s.parent.classList.add('video-interactable'),
                  this._isFirstSlide(i) && 'sound' !== s.options.style && n.playVideo();
              }
            },
            _isFirstSlide: function (e) {
              return this.cache.mainSlider.querySelector(i.startingSlide + ' #' + e);
            },
            youtubePlayerStateChange: function (t) {
              var i = e[t.target.getIframe().id];
              switch (t.data) {
                case -1:
                  i.attemptedToPlay && i.parent.classList.add('video-interactable');
                  break;
                case 0:
                  i && 'true' === i.options.loop && i.videoPlayer.playVideo();
                  break;
                case 3:
                  i.attemptedToPlay = !0;
              }
            },
            initMp4Video: function (t) {
              (e[t.id] = { id: t.id, type: 'mp4' }), this._isFirstSlide(t.id) && this.playMp4Video(t.id);
            },
            stopVideos: function () {
              for (var [t, i] of Object.entries(e))
                i.videoPlayer
                  ? 'function' == typeof i.videoPlayer.stopVideo && i.videoPlayer.stopVideo()
                  : 'mp4' === i.type && this.stopMp4Video(i.id);
            },
            _getVideoType: function (e) {
              return e.getAttribute('data-video-type');
            },
            _getVideoDivId: function (e) {
              return e.id;
            },
            playMp4Video: function (e) {
              var t = this.container.querySelector('#' + e),
                s = t.play();
              void 0 !== s &&
                s
                  .then(function () {})
                  .catch(function (e) {
                    t.setAttribute('controls', ''),
                      t.closest(i.videoParent).setAttribute('data-video-style', 'unmuted');
                  });
            },
            stopMp4Video: function (e) {
              var t = this.container.querySelector('#' + e);
              t && 'function' == typeof t.pause && t.pause();
            },
            initImageZoom: function () {
              var e = this.container.querySelector(this.selectors.imageContainer);
              e &&
                (new theme.Photoswipe(e, this.sectionId),
                e.addEventListener(
                  'photoswipe:afterChange',
                  function (e) {
                    this.flickity && this.flickity.goToSlide(e.detail.index);
                  }.bind(this)
                ));
            },
            getThumbIndex: function (e) {
              return e.dataset.index;
            },
            updateVariantImage: function (e) {
              var t = e.detail.variant;
              theme.Images.getSizedImageUrl(t.featured_media.preview_image.src, this.settings.imageSize);
              var i = this.container.querySelector('.product__thumb[data-id="' + t.featured_media.id + '"]'),
                s = this.getThumbIndex(i);
              void 0 !== s && this.flickity && this.flickity.goToSlide(s);
            },
            initProductSlider: function (e) {
              if (this.cache.mainSlider.querySelectorAll(i.slide).length <= 1) {
                var t = this.cache.mainSlider.querySelector(i.slide);
                t && t.classList.add('is-selected');
                return;
              }
              if ((this.flickity && 'function' == typeof this.flickity.destroy && this.flickity.destroy(), !e)) {
                var s = this.cache.mainSlider.querySelector(i.startingSlide);
                this.settings.currentSlideIndex = this._slideIndex(s);
              }
              var n = {
                dragThreshold: 25,
                adaptiveHeight: !0,
                avoidReflow: !0,
                initialIndex: this.settings.currentSlideIndex,
                childNav: this.cache.thumbSlider,
                childNavScroller: this.cache.thumbScroller,
                childVertical: 'beside' === this.cache.thumbSlider.dataset.position,
                pageDots: !0,
                wrapAround: !0,
                callbacks: { onInit: this.onSliderInit.bind(this), onChange: this.onSlideChange.bind(this) },
              };
              this.settings.imageSetName &&
                ((n = Object.assign({}, n, this.imageSetArguments(e))), this.updateImageSetThumbs(n.imageSet)),
                (this.flickity = new theme.Slideshow(this.cache.mainSlider, n));
            },
            onSliderInit: function (e) {
              this.settings.imageSetName && this.prepMediaOnSlide(e);
            },
            onSlideChange: function (e) {
              if (this.flickity) {
                var t = this.cache.mainSlider.querySelector(
                    '.product-main-slide[data-index="' + this.settings.currentSlideIndex + '"]'
                  ),
                  i = this.settings.imageSetName
                    ? this.cache.mainSlider.querySelectorAll('.flickity-slider .product-main-slide')[e]
                    : this.cache.mainSlider.querySelector('.product-main-slide[data-index="' + e + '"]');
                t.setAttribute('tabindex', '-1'),
                  i.setAttribute('tabindex', 0),
                  this.stopMediaOnSlide(t),
                  this.prepMediaOnSlide(i),
                  (this.settings.currentSlideIndex = e);
              }
            },
            stopMediaOnSlide(t) {
              var s = t.querySelector(i.productVideo);
              if (s) {
                var n = this._getVideoType(s),
                  a = this._getVideoDivId(s);
                if ('youtube' === n) {
                  if (e[a].videoPlayer) {
                    e[a].videoPlayer.stopVideo();
                    return;
                  }
                } else if ('mp4' === n) {
                  this.stopMp4Video(a);
                  return;
                }
              }
              var o = t.querySelector(this.selectors.media);
              o && o.dispatchEvent(new CustomEvent('mediaHidden', { bubbles: !0, cancelable: !0 }));
            },
            prepMediaOnSlide(t) {
              var s = t.querySelector(i.productVideo);
              if (s) {
                this.flickity.reposition();
                var n = this._getVideoType(s),
                  a = this._getVideoDivId(s);
                if ('youtube' === n) {
                  if (e[a].videoPlayer && 'sound' !== e[a].options.style) {
                    e[a].videoPlayer.playVideo();
                    return;
                  }
                } else 'mp4' === n && this.playMp4Video(a);
              }
              var o = t.querySelector(this.selectors.media);
              o &&
                (o.dispatchEvent(new CustomEvent('mediaVisible', { bubbles: !0, cancelable: !0 })),
                t.querySelector('.shopify-model-viewer-ui__button').setAttribute('tabindex', 0),
                t.querySelector('.product-single__close-media').setAttribute('tabindex', 0));
            },
            _slideIndex: function (e) {
              return e.getAttribute('data-index');
            },
            openModalProduct: function () {
              var e = !1;
              this.settings.modalInit
                ? (e = !0)
                : ((this.blocksHolder = this.container.querySelector(i.blocksHolder)),
                  fetch(this.blocksHolder.dataset.url)
                    .then(function (e) {
                      return e.text();
                    })
                    .then(
                      function (e) {
                        var t = new DOMParser().parseFromString(e, 'text/html').querySelector(i.blocks);
                        t.querySelectorAll('[id]').forEach((e) => {
                          var i = e.getAttribute('id');
                          e.setAttribute('id', i + '-modal');
                          var s = t.querySelector(`[for="${i}"]`);
                          s && s.setAttribute('for', i + '-modal');
                          var n = t.querySelector(`[aria-controls="${i}"]`);
                          n && n.setAttribute('aria-controls', i + '-modal');
                        });
                        var s = t.querySelector(this.selectors.form);
                        if (s) {
                          var n = s.getAttribute('id');
                          t.querySelectorAll('[form]').forEach((e) => {
                            e.setAttribute('form', n);
                          });
                        }
                        (this.blocksHolder.innerHTML = ''),
                          this.blocksHolder.append(t),
                          this.blocksHolder.classList.add('product-form-holder--loaded'),
                          this.cacheElements(),
                          this.formSetup(),
                          this.updateModalProductInventory(),
                          Shopify && Shopify.PaymentButton && Shopify.PaymentButton.init(),
                          theme.collapsibles.init(this.container),
                          document.dispatchEvent(
                            new CustomEvent('quickview:loaded', { detail: { productId: this.productId } })
                          );
                      }.bind(this)
                    ),
                  this.productSetup(),
                  this.videoSetup(),
                  this.settings.imageSetName
                    ? this.variants
                      ? this.initProductSlider()
                      : document.addEventListener(
                          'quickview:loaded',
                          function (e) {
                            e.detail.productId === this.productId && this.initProductSlider();
                          }.bind(this)
                        )
                    : this.initProductSlider(),
                  this.customMediaListners(),
                  this.addIdToRecentlyViewed(),
                  (this.settings.modalInit = !0)),
                AOS.refreshHard(),
                document.dispatchEvent(
                  new CustomEvent('quickview:open', { detail: { initialized: e, productId: this.productId } })
                );
            },
            updateModalProductInventory: function () {
              (window.inventories = window.inventories || {}),
                this.container.querySelectorAll('.js-product-inventory-data').forEach((e) => {
                  var t = e.dataset.productId;
                  (window.inventories[t] = {}),
                    e.querySelectorAll('.js-variant-inventory-data').forEach((e) => {
                      window.inventories[t][e.dataset.id] = {
                        quantity: e.dataset.quantity,
                        policy: e.dataset.policy,
                        incoming: e.dataset.incoming,
                        next_incoming_date: e.dataset.date,
                      };
                    });
                });
            },
            closeModalProduct: function () {
              this.stopVideos();
            },
            initModelViewerLibraries: function () {
              var e = this.container.querySelectorAll(this.selectors.media);
              e.length < 1 || theme.ProductMedia.init(e, this.sectionId);
            },
            initShopifyXrLaunch: function () {
              document.addEventListener(
                'shopify_xr_launch',
                function () {
                  this.container
                    .querySelector(this.selectors.productMediaWrapper + ':not(.' + self.classes.hidden + ')')
                    .dispatchEvent(new CustomEvent('xrLaunch', { bubbles: !0, cancelable: !0 }));
                }.bind(this)
              );
            },
            customMediaListners: function () {
              document.querySelectorAll(this.selectors.closeMedia).forEach((e) => {
                e.addEventListener(
                  'click',
                  function () {
                    var e = this.cache.mainSlider.querySelector(i.currentSlide).querySelector(this.selectors.media);
                    e && e.dispatchEvent(new CustomEvent('mediaHidden', { bubbles: !0, cancelable: !0 }));
                  }.bind(this)
                );
              });
              var e = this.container.querySelectorAll('model-viewer');
              e.length &&
                e.forEach((e) => {
                  e.addEventListener(
                    'shopify_model_viewer_ui_toggle_play',
                    function (e) {
                      this.mediaLoaded(e);
                    }.bind(this)
                  ),
                    e.addEventListener(
                      'shopify_model_viewer_ui_toggle_pause',
                      function (e) {
                        this.mediaUnloaded(e);
                      }.bind(this)
                    );
                });
            },
            mediaLoaded: function (e) {
              this.container.querySelectorAll(this.selectors.closeMedia).forEach((e) => {
                e.classList.remove(t.hidden);
              }),
                this.flickity && this.flickity.setDraggable(!1);
            },
            mediaUnloaded: function (e) {
              this.container.querySelectorAll(this.selectors.closeMedia).forEach((e) => {
                e.classList.add(t.hidden);
              }),
                this.flickity && this.flickity.setDraggable(!0);
            },
            onUnload: function () {
              theme.ProductMedia.removeSectionModels(this.sectionId),
                this.flickity && 'function' == typeof this.flickity.destroy && this.flickity.destroy();
            },
          })),
          s
        );
      })()),
      (theme.RecentlyViewed = (function () {
        var e = !1;
        function t(e) {
          e &&
            ((this.container = e),
            (this.sectionId = this.container.getAttribute('data-section-id')),
            (this.namespace = '.recently-viewed' + this.sectionId),
            (this.gridItemWidth = this.container.getAttribute('data-grid-item-class')),
            (this.rowOf = this.container.getAttribute('data-row-of')),
            theme.initWhenVisible({ element: this.container, callback: this.init.bind(this), threshold: 600 }));
        }
        return (
          (t.prototype = Object.assign({}, t.prototype, {
            init: function () {
              if (!e) {
                if (
                  ((e = !0),
                  0 === Object.keys(theme.recentlyViewed.recent).length &&
                    theme.recentlyViewed.recent.constructor === Object)
                ) {
                  this.container.classList.add('hide');
                  return;
                }
                (this.outputContainer = document.getElementById('RecentlyViewed-' + this.sectionId)),
                  (this.handle = this.container.getAttribute('data-product-handle'));
                var t = [];
                Object.keys(theme.recentlyViewed.recent).forEach(
                  function (e) {
                    'undefined' !== e && t.push(this.getProductInfo(e));
                  }.bind(this)
                ),
                  Promise.all(t).then(
                    function (e) {
                      this.setupOutput(e), this.captureProductDetails(e);
                    }.bind(this)
                  );
              }
            },
            getProductInfo: function (e) {
              return new Promise(function (t, i) {
                theme.recentlyViewed.productInfo.hasOwnProperty(e)
                  ? t(theme.recentlyViewed.productInfo[e])
                  : fetch('/products/' + e + '.js')
                      .then(function (e) {
                        return e.text();
                      })
                      .then(function (e) {
                        t(e);
                      });
              });
            },
            setupOutput: function (e) {
              var t = [],
                i = this.container.getAttribute('data-recent-count'),
                s = 0;
              if (
                (Object.keys(e).forEach(
                  function (i) {
                    if (e[i]) {
                      var n = JSON.parse(e[i]);
                      if (n.handle !== this.handle && void 0 !== n.handle) {
                        s++,
                          (n.url = theme.recentlyViewed.recent[n.handle]
                            ? theme.recentlyViewed.recent[n.handle].url
                            : n.url),
                          (n.image_responsive_url = theme.recentlyViewed.recent[n.handle].featuredImage),
                          (n.image_aspect_ratio = theme.recentlyViewed.recent[n.handle].aspectRatio);
                        var a = n.variants[0];
                        if (a && a.unit_price) {
                          var o = '';
                          a.unit_price_measurement &&
                            (1 != a.unit_price_measurement.reference_value &&
                              (o += a.unit_price_measurement.reference_value + ' '),
                            (o += a.unit_price_measurement.reference_unit)),
                            (n.unit_price = theme.Currency.formatMoney(a.unit_price)),
                            '' != o && (n.unit_price += '/' + o);
                        }
                        t.unshift(n);
                      }
                    }
                  }.bind(this)
                ),
                0 === t.length)
              ) {
                this.container.classList.add('hide');
                return;
              }
              var n = theme.buildProductGridItem(t.slice(0, i), this.gridItemWidth, this.rowOf);
              (this.outputContainer.innerHTML = n), AOS && AOS.refreshHard();
            },
            captureProductDetails: function (e) {
              for (var t = 0; t < e.length; t++) {
                var i = e[t];
                theme.recentlyViewed.productInfo[i.handle] = i;
              }
              theme.config.hasSessionStorage &&
                sessionStorage.setItem('recent-products', JSON.stringify(theme.recentlyViewed.productInfo));
            },
            onUnload: function () {
              e = !1;
            },
          })),
          t
        );
      })()),
      (theme.Testimonials = (function () {
        var e = { adaptiveHeight: !0, avoidReflow: !0, pageDots: !0, prevNextButtons: !1 };
        function t(e) {
          (this.container = e), this.timeout;
          var t = e.getAttribute('data-section-id');
          (this.slideshow = e.querySelector('#Testimonials-' + t)),
            (this.namespace = '.testimonial-' + t),
            this.slideshow &&
              theme.initWhenVisible({ element: this.container, callback: this.init.bind(this), threshold: 600 });
        }
        return (
          (t.prototype = Object.assign({}, t.prototype, {
            init: function () {
              this.slideshow.dataset.count <= 3 && (e.wrapAround = !1),
                (this.flickity = new theme.Slideshow(this.slideshow, e)),
                this.slideshow.dataset.count > 2 &&
                  (this.timeout = setTimeout(
                    function () {
                      this.flickity.goToSlide(1);
                    }.bind(this),
                    1e3
                  ));
            },
            onUnload: function () {
              this.flickity && 'function' == typeof this.flickity.destroy && this.flickity.destroy();
            },
            onDeselect: function () {
              this.flickity && 'function' == typeof this.flickity.play && this.flickity.play();
            },
            onBlockSelect: function (e) {
              var t = parseInt(this.slideshow.querySelector('.testimonials-slide--' + e.detail.blockId).dataset.index);
              clearTimeout(this.timeout),
                this.flickity &&
                  'function' == typeof this.flickity.pause &&
                  (this.flickity.goToSlide(t), this.flickity.pause());
            },
            onBlockDeselect: function () {
              this.flickity && 'function' == typeof this.flickity.play && this.flickity.play();
            },
          })),
          t
        );
      })()),
      (theme.isStorageSupported = function (e) {
        if (window.self !== window.top) return !1;
        var t,
          i = 'test';
        'session' === e && (t = window.sessionStorage), 'local' === e && (t = window.localStorage);
        try {
          return t.setItem(i, '1'), t.removeItem(i), !0;
        } catch (s) {
          return !1;
        }
      }),
      (theme.reinitProductGridItem = function (e) {
        AOS && AOS.refreshHard(),
          theme.initQuickShop(),
          window.SPR && (SPR.initDomEls(), SPR.loadBadges()),
          theme.collapsibles.init();
      }),
      (theme.config.hasSessionStorage = theme.isStorageSupported('session')),
      (theme.config.hasLocalStorage = theme.isStorageSupported('local')),
      AOS.init({ easing: 'ease-out-quad', once: !0, offset: 60, disableMutationObserver: !0 }),
      theme.config.hasLocalStorage &&
        ((theme.recentlyViewed.localStorage = window.localStorage.getItem('theme-recent')),
        theme.recentlyViewed.localStorage &&
          (theme.recentlyViewed.recent = JSON.parse(theme.recentlyViewed.localStorage))),
      (theme.recentlyViewed.productInfo =
        theme.config.hasSessionStorage && sessionStorage['recent-products']
          ? JSON.parse(sessionStorage['recent-products'])
          : {}),
      (theme.config.bpSmall = matchMedia(theme.config.mediaQuerySmall).matches),
      matchMedia(theme.config.mediaQuerySmall).addListener(function (e) {
        e.matches
          ? ((theme.config.bpSmall = !0), document.dispatchEvent(new CustomEvent('matchSmall')))
          : ((theme.config.bpSmall = !1), document.dispatchEvent(new CustomEvent('unmatchSmall')));
      }),
      (theme.initGlobals = function () {
        theme.collapsibles.init(), theme.videoModal();
      }),
      (r = function () {
        if (
          ((theme.sections = new theme.Sections()),
          theme.sections.register('slideshow-section', theme.SlideshowSection),
          theme.sections.register('header', theme.HeaderSection),
          theme.sections.register('product', theme.Product),
          theme.sections.register('blog', theme.Blog),
          theme.sections.register('password-header', theme.PasswordHeader),
          theme.sections.register('photoswipe', theme.Photoswipe),
          theme.sections.register('product-recommendations', theme.Recommendations),
          theme.sections.register('background-image', theme.BackgroundImage),
          theme.sections.register('testimonials', theme.Testimonials),
          theme.sections.register('video-section', theme.VideoSection),
          theme.sections.register('map', theme.Maps),
          theme.sections.register('footer-section', theme.FooterSection),
          theme.sections.register('store-availability', theme.StoreAvailability),
          theme.sections.register('recently-viewed', theme.RecentlyViewed),
          theme.sections.register('newsletter-popup', theme.NewsletterPopup),
          theme.sections.register('collection-header', theme.CollectionHeader),
          theme.sections.register('collection-grid', theme.Collection),
          theme.initGlobals(),
          theme.initQuickShop(),
          theme.rteInit(),
          document.body.classList.contains('template-cart'))
        ) {
          var e = document.getElementById('CartPageForm');
          e && new theme.CartForm(e);
        }
        theme.settings.predictiveSearch && theme.predictiveSearch.init(),
          theme.settings.isCustomerTemplate && theme.customerTemplates(),
          document.dispatchEvent(new CustomEvent('page:loaded'));
      }),
      'loading' != document.readyState ? r() : document.addEventListener('DOMContentLoaded', r);
  })();

// custom slider js.
$('.custom-image-slider').slick({
  dots: false,
  //arrows:true,
  infinite: true,
  autoplay: true,
  autoplaySpeed: 3000,
  speed: 1000,
  slidesToShow: 3,
  slidesToScroll: 1,
  responsive: [
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
        infinite: true,
        dots: true,
      },
    },
    {
      breakpoint: 600,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      },
    },
    {
      breakpoint: 480,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      },
    },
    // You can unslick at a given breakpoint now by adding:
    // settings: "unslick"
    // instead of a settings object
  ],
});

class AddItem extends HTMLElement {
  constructor() {
    super();
    this.handleClick = this.handleClick.bind(this);
  }

  connectedCallback() {
    this.addEventListener('click', this.handleClick);
  }

  disconnectedCallback() {
    this.removeEventListener('click', this.handleClick);
  }

  handleClick(event) {
    let newId = this.dataset.id;
    this.classList.add('has-loading');
    fetch('/cart/add.js', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id: newId,
        quantity: 1,
      }),
    })
      .then((response) => response.json())
      .then((product) => {
        document.dispatchEvent(
          new CustomEvent('ajaxProduct:added', {
            detail: {
              product: product,
              addToCartBtn: this.addToCart,
            },
          })
        );
        this.classList.remove('has-loading');
      })
      .catch((error) => console.error('Error:', error));
  }
}
customElements.define('add-item', AddItem);
