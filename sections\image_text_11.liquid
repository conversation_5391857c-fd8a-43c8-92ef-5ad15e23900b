<section class="image_text_11"  style="background:{{section.settings.bg_color}}; {% if section.settings.bg_image != blank %}background-image: url({{ section.settings.bg_image | img_url: 'master' }}); background-size: cover; background-repeat: no-repeat; background-position: center;{% endif %};">
  <div class="page_wrapper">
    <div class="wrapper">
      <div class="text_block">

        {% if section.settings.heading != blank %}
        <h2 class="heading">{{ section.settings.heading }}</h2>
        {% endif %}
  
      

<div class="swiper-container timmer_swiper" style="display: ;">
  <div class="swiper-wrapper timmer_wrap">
    {% for block in section.blocks %}
      {% if block.settings.image != blank %}
        <div class="swiper-slide timmer_item">
          <div class="image">
          <img src="{{ block.settings.image | img_url: 'master' }}">
        </div>
        </div>
      {% endif %}
    {% endfor %}
  </div>
</div>


        
      
      </div>
   
      
    </div>
  </div>
</section>

<style>


section.image_text_11 .wrapper .text_block {
    width: 100%;
    display: flex;
      gap: 60px;
    flex-direction: column;

}
section.image_text_11 .wrapper .text_block h2.heading {
    font-size: 74px;
    font-family: Helvetica-Bold;
    margin: 0;
  color:#2a2b2b;

      text-transform: none;
          margin: 0 auto;
        text-align: center;
      width: 100%;
    max-width: 1000px;
}
  section.image_text_11 .wrapper .text_block h2.heading p{
      margin: 0;
  }
section.image_text_11 .wrapper .text_block .content p {
    margin: 0;
    font-size: 20px;
    font-family: Helvetica-Bold;
    color: #676666;
}
  section.image_text_11 .wrapper .image_block {
    width: 100%;
    font-size: 0;
}
  section.image_text_11 .wrapper .image_block img{
    width: 100%;
    }
  /* .image_text_11 .wrapper {

    display: grid;
        align-items: center;
    gap:50px;
} */
  section.image_text_11 .wrapper .text_block .content {
    width: 100%;

}
  section.image_text_11 .wrapper .text_block .sub_heading {
    margin: 0;
    font-size: 20px;
    font-family: Helvetica-Bold;
    color: #676666;
}

/* section.image_text_11 .wrapper .text_block .timmer_wrap {
    display: grid
;
    grid-template-columns: repeat(3, auto);
      gap: 100px;
} */
  section.image_text_11 .wrapper .text_block .timmer_wrap .timmer_item {
    width: 100%;
}
  section.image_text_11 .wrapper .text_block .timmer_wrap .timmer_item img{
    width: 100%;
}
  section.image_text_11 .wrapper .text_block .icon_wrap {
    display: grid
;
    grid-template-columns: repeat(14, auto);
        gap: 20px;
  }
  section.image_text_11 .wrapper .text_block .icon_wrap .icon_item {
    width: 100%;
    max-width: 50px;
}
  section.image_text_11 .wrapper .text_block .icon_wrap .icon_item img {
    width: 100%;
}
section.image_text_11 .wrapper .text_block .icon_wrap .icon_item.more_btn {
    display: flex !important
;
    justify-content: left;
    align-items: center;
    max-width: 100%;
    font-size: 15px;
    font-family: Helvetica-Bold;
    color: #999c9e;
  font-weight:700;
}


    section.image_text_11 {
    padding: 50px 150px;
}
  section.image_text_11 .wrapper .text_block .icon_wrap .icon_item.more_btn.clicked {
    display: none !important;
}
    section.image_text_11 .wrapper .text_block .swiper-container {
    display: block !important;
              overflow: hidden;
          position: relative;
          padding: 5.5% 0;
}
  section.image_text_11 .wrapper .text_block .swiper-container::before {
    content: '';
    background: url(https://cdn.shopify.com/s/files/1/0733/7908/6636/files/unnamed_49.png?v=1752190617);
    position: absolute;
    inset: 0;

    width: 100%;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
}
  section.image_text_11 .wrapper .text_block .timmer_wrap .timmer_item .image {
    font-size: 0;
}
  @media only screen and (min-width: 2600px) {
section.image_text_11 .wrapper .text_block h2.heading {
        font-size: 135px;
        width: 100%;
        max-width: 1810px;
}
    section.image_text_11 .wrapper .text_block .icon_wrap .icon_item {
    width: 100%;
    max-width: 100px;
}
    section.image_text_11 .wrapper .text_block .icon_wrap .icon_item.more_btn {
    font-size: 31px;

    }
  }
   @media only screen and (max-width: 1600px) {
  section.image_text_11 {
    padding: 25px 60px;
}
   }
  @media only screen and (max-width: 1280px) {
section.image_text_11 .wrapper .text_block h2.heading {
    font-size: 48px;
 max-width: 730px;
}
    section.image_text_11 .wrapper .text_block {
    gap: 40px;
    }
  }
  @media only screen and (max-width: 1024px) {
  /* section.image_text_11 .wrapper .text_block .timmer_wrap {
    gap: 50px;
} */
    section.image_text_11 .wrapper .text_block .swiper-container {
  
    padding: 5% 0;
}
    section.image_text_11 .wrapper .text_block .icon_wrap {

    grid-template-columns: repeat(10, auto);
    }
  }
  @media only screen and (max-width: 840px) {
        section.image_text_11 {
        padding: 30px 20px;
    }
        section.image_text_11 .wrapper .text_block .icon_wrap {
    grid-template-columns: repeat(8, auto);
    gap: 15px;
}
    section.image_text_11 .wrapper .text_block .swiper-container {
    padding: 9% 0;
}
  }
  @media only screen and (max-width: 480px) {

  section.image_text_11 .wrapper .text_block h2.heading {
    font-size: 38px;
}
    section.image_text_11 .wrapper .text_block .icon_wrap {
    grid-template-columns: repeat(5, auto);
    gap: 10px;
}

section.image_text_11 .wrapper .text_block .swiper-container .timmer_wrap{

        display: flex;
        gap: 0;
    }

section.image_text_11 .wrapper .text_block .timmer_wrap .timmer_item .image {
    max-width: 70%;
    margin: 0 auto;
}
        section.image_text_11 .wrapper .text_block .swiper-container {
        padding: 23% 0;
    }
  }


  
</style>

<script>

$(document).ready(function () {
  var mySwiper = new Swiper('.image_text_11 .timmer_swiper', {
    loop: true,
    slidesPerView: 1,
    spaceBetween: 0,
    autoplay: {
      delay: 3000,
      disableOnInteraction: false,
    },
    effect: 'slide', // use 'fade' if you want fading
    breakpoints: {
      // when window width is >= 640px
       0: {
        slidesPerView: 1,
        spaceBetween: 50
      },
      481: {
        slidesPerView: 3,
        spaceBetween: 50
      },
    
      // when window width is >= 1024px
      841: {
        slidesPerView: 5,
        spaceBetween: 60
      }
    }
  });
});




</script>


  

{% schema %}
{
  "name": "Image Text 11",
  "settings": [
    {
      "type": "color",
      "id": "bg_color",
      "default": "transparent",
      "label": "Backgroud Color"
    },
       {
      "type": "image_picker",
      "id": "bg_image",
      "label": "Background Image"
    },
    

      {
          "type": "richtext",
          "id": "heading",
          "label": "Heading"
        },
  

      
  ],

    "blocks": [
    {
      "type": "block",
      "name": "Block",
      "settings": [

        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
               
    
    
      
      ]
    }
  ],
  
  "presets": [
    {
      "name": "Image Text 11",
      "blocks": []
    }
  ]
}
{% endschema %}

