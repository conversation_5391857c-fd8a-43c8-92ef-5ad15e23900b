
    
      {%- assign gpBkProduct = product -%}
      
      {%- liquid
        if request.page_type == 'product'
          if 'static' == 'static'
            if '14922687119742' == 'latest'
              paginate collections.all.products by 100000
                assign product = collections.all.products | sort: 'created_at' | reverse | first
              endpaginate
            else
              assign product = all_products['gard-pro-ultra-2-plus2']
              assign productId = '14922687119742' | times: 1
              if product == empty or product == null
                paginate collections.all.products by 100000
                  for item in collections.all.products
                    if item.id == productId
                      assign product = item
                    endif
                  endfor
                endpaginate
              endif
            endif
          endif
        else
          if '14922687119742' == 'latest'
            paginate collections.all.products by 100000
              assign product = collections.all.products | sort: 'created_at'| reverse | first
            endpaginate
          else
            assign product = all_products['gard-pro-ultra-2-plus2']
            assign productId = '14922687119742' | times: 1
            if product == empty or product == null
              paginate collections.all.products by 100000
                for item in collections.all.products
                  if item.id == productId
                    assign product = item
                  endif
                endfor
              endpaginate
            endif
          endif
        endif
      -%}
    
    
    {% if product != empty and product != null %}
      
      {%- assign initVariantId =  -%}
      {%- assign product_form_id = 'product-form-' | append: "gWprtcqsZJ" -%}
      {%- assign variant = product.selected_or_first_available_variant -%}
      {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%-if productSelectedVariant == empty or productSelectedVariant == null -%}
        {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      {%-if variant == empty or variant == null -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
      {%- endif -%}
    
      <gp-product
        data-uid="gWprtcqsZJ"
        data-id="gWprtcqsZJ"
        
        
        gp-context='{"productId": {{ product.id }}, "preSelectedOptionIds": [], "isSyncProduct": "true", "variantSelected": {{ variant | json | escape }},  "inventory_management": {{ variant.inventory_management | json | escape  }}, "inventory_policy": {{ variant.inventory_policy | json | escape }}, "inventoryQuantity": {{ variant.inventory_quantity }}, "quantity": 1, "formId": "{{ product_form_id }}" }'
        gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }}}'
      >
        <product-form class="product-form">
          {%- form 'product', product, id: product_form_id, class: 'form contents', data-type: 'add-to-cart-form', autocomplete: 'off'  -%}
            <input type="hidden" name="id" value="{{ variant.id }}" />
            <input type="hidden" name="quantity" value="{{ quantity }}" />
            <button type="submit" onclick="return false;" style="display:none;"></button>
            
    

    

    
    <gp-row gp-data='{"uid":"gWprtcqsZJ"}' data-id="gWprtcqsZJ-row" id="gWprtcqsZJ" data-same-height-subgrid-container class="gWprtcqsZJ gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--op-mobile:100%;--op-tablet:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:0px;--pt:0px;--pl:0px;--pb:0px;--pr:0px;--mb-tablet:0px;--pt-tablet:0px;--pl-tablet:0px;--pb-tablet:0px;--pr-tablet:0px;--mb-mobile:0px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--cg:30px;--rg:0px;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="gXrtLvXRPz gp-relative gp-flex gp-flex-col"
    >
      
  {% if product.media.size > 1 %}<style>
    .gem-slider-item-g3QvEhuucd-{{product.id}}.gp-gallery-image-item::after, .gem-slider-item-gp-gallery-g3QvEhuucd-{{product.id}}.gp-gallery-image-item::after {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      z-index: 999;
      top: 0;
      left: 0;
      border-style: solid;
  border-width: 1px 1px 1px 1px;
  border-color: #575757;
  
      
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      
    }
    .gem-slider-item-g3QvEhuucd-{{product.id}}.gp-gallery-image-item[data-outline=active]:after, .gem-slider-item-gp-gallery-g3QvEhuucd-{{product.id}}.gp-gallery-image-item[data-outline=active]:after {
      pointer-events: none;
    }
  </style>{% endif %}
  
    {% assign featured_image = product.featured_image %}
    {% if variant != null and variant.featured_image != null %}{% assign featured_image = variant.featured_image %}{% endif %}
  
    <gp-product-images
      gp-data='
    {
      "id":"g3QvEhuucd",
      "pageContext": {"pageType":"GP_STATIC","sectionName":"","isPreviewing":false,"isOptimizePlan":true,"isTranslateWithLocale":false,"isLazyLoadSection":true,"isLazyLoadImage":true,"hasCollectionHandle":true,"numberOfProductOfProductList":0,"pageBackground":"","fontType":"google","primaryLocale":"","showPriceCurrency":false,"enableLazyLoadImage":true},
      "setting":{"arrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","arrowIconColor":"#000000","arrowNavBorder":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"},"arrowNavRadius":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"borderActive":{"border":"solid","borderType":"none","borderWidth":"1px","color":"#575757","isCustom":false,"width":"1px 1px 1px 1px"},"clickOpenLightBox":{"desktop":false},"dragToScroll":true,"ftArrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","ftArrowIconColor":"#000000","ftArrowNavBorder":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"},"ftArrowNavRadius":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"ftClickOpenLightBox":{"desktop":"none"},"ftDotActiveColor":{"desktop":"line-3"},"ftDotColor":{"desktop":"bg-1"},"ftDotGapToCarousel":{"desktop":16},"ftDotSize":{"desktop":12},"ftDotStyle":{"desktop":"none"},"ftDragToScroll":true,"ftLoop":{"desktop":false},"ftNavigationPosition":{"desktop":"none"},"ftPauseOnHover":true,"ftSpeed":1,"galleryHoverEffect":"none","galleryZoom":150,"galleryZoomType":"default","hoverEffect":"none","loop":{"desktop":true},"navigationPosition":{"desktop":"none"},"otherImage":0,"pauseOnHover":true,"preDisplay":"1st-available-variant","preload":false,"qualityPercent":{"desktop":100},"qualityType":{"desktop":"finest"},"speed":1,"type":{"desktop":"slider"},"typeDisplay":"all-images","zoom":150,"zoomType":"default"},
      "styles":{"align":{"desktop":"flex-start"},"corner":{"bblr":"4px","bbrr":"4px","btlr":"4px","btrr":"4px","radiusType":"custom"},"ftCorner":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"ftLayout":{"desktop":"cover"},"ftShape":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"itemSpacing":{"desktop":"var(--g-s-s)","mobile":"var(--g-s-s)"},"layout":{"desktop":"cover"},"position":{"desktop":"left","mobile":"left"},"ratioLayout":{"desktop":[1,11],"mobile":[2,10]},"ratioLayoutRight":{"desktop":[10,2]},"shape":{"desktop":{"gap":"","height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"shapeFor1Col":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"shapeFor2Col":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"50%"}},"shapeForBottom":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"}},"shapeForFtOnly":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"shapeForInside":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"}},"shapeForInsideBottom":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"}},"spacing":{"desktop":"24px","mobile":"var(--g-s-s)","tablet":"12px"}},
      "productUrl":{{product.url | json | escape}},
      "product":{{product | json | escape}},
      "collectionUrl": {{ collection.url | json | escape }},
      "collection": {{ collection | json | escape}}
    }
  '
      section-id="{{section.id}}" data-id="g3QvEhuucd"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--op-mobile:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:16px;--pr:0px;--mb-mobile:14px;--pr-mobile:0px"
      class="gp-relative gp-w-full g3QvEhuucd "
    >
      <div
        class="{% if product.media.size > 1 %}gp-grid gp-w-full !gp-m-0 gp-relative{% else %}gp-w-full !gp-m-0 gp-relative{% endif %}"
        style="{% if product.media.size > 1 %}--gtc: minmax(0, 1fr) minmax(0, 11fr);--gtc-tablet: minmax(0, 1fr) minmax(0, 11fr);--gtc-mobile: minmax(0, 2fr) minmax(0, 10fr);--gtr: fit-content(0);--gtr-tablet: fit-content(0);--gtr-mobile: fit-content(0);--gg: 24px;--gg-tablet: 12px;--gg-mobile: var(--g-s-s);{% else %}--gtc: minmax(0, 12fr);--gg: 24px;--gg-tablet: 12px;--gg-mobile: var(--g-s-s);{% endif %}"
      >
        
    {% capture featureImageOnlyOne %}
      
        {% assign featureMedia = variant.featured_media %}
        {% unless featureMedia %}
        {% assign featureMedia = product.featured_media %}
        {% endunless %}
        {% if product.media.size > 1 %}
          {% assign featureMedia = variant.featured_media %}
          {% unless featureMedia %}
          {% assign featureMedia = product.featured_media %}
          {% endunless %}
        {% endif %}
        {% assign largestRatio = 0 %}
        {% assign height = featureMedia.height | times: 1.0 %}
        {% assign width = featureMedia.width | times: 1.0 %}
        {% assign ratio = height | divided_by: width %}
        {% if ratio > largestRatio %}{% assign largestRatio = ratio %}{% endif %}
        {% assign productImageWidth = 0 %}
        {% case featureMedia.media_type %}
          {% when 'image' %}
            {% assign productImageWidth = featureMedia.width %}
          {% else %}
            {% assign productImageWidth = featureMedia.preview_image.width %}
        {% endcase %}
        {% if featureMedia == null %}{% assign productImageWidth = 1600 %}{% endif %}
        <div
          class='gp-feature-image-carousel gp-feature-image-only'
          style="--jc: flex-start;--o: 1;--o-tablet: 1;--o-mobile: 1;--d: flex;--d-mobile: flex;--d-tablet: flex;"
        >
          <div
            class="gp-relative"
            style="--w: 100%;--w-tablet: 100%;--w-mobile: 100%;--bblr: 6px;--bbrr: 6px;--btlr: 6px;--btrr: 6px;--hvr-bblr: 6px;--hvr-bbrr: 6px;--hvr-btlr: 6px;--hvr-btrr: 6px;--focus-bblr: 6px;--focus-bbrr: 6px;--focus-btlr: 6px;--focus-btrr: 6px;-undefined-bblr: 6px;-undefined-bbrr: 6px;-undefined-btlr: 6px;-undefined-btrr: 6px;--h: auto;--h-tablet: auto;--h-mobile: auto;"
          >
            
      
    
            <div
              type="gp-feature-image-only"
              product-id="{{product.id}}"
              product-media="{{product.media.size}}"
              class="gp-group gp-z-0 gp-flex !gp-min-w-full !gp-max-w-full gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-outline-1 -gp-outline-offset-1 gp-image-item gp-ft-image-item data-[outline=active]:gp-outline gp-featured-image-wrapper"
              style="max-width: 100%;outline-color: var(--g-c-brand, brand);--bblr: 6px;--bbrr: 6px;--btlr: 6px;--btrr: 6px;--hvr-bblr: 6px;--hvr-bbrr: 6px;--hvr-btlr: 6px;--hvr-btrr: 6px;--focus-bblr: 6px;--focus-bbrr: 6px;--focus-btlr: 6px;--focus-btrr: 6px;-undefined-bblr: 6px;-undefined-bbrr: 6px;-undefined-btlr: 6px;-undefined-btrr: 6px;--h: auto;--h-tablet: auto;--h-mobile: auto;width: {{productImageWidth}}px;"
            >
              <div
                class="gp-w-full gp-relative {% if featureMedia == null or featureMedia.media_type == 'image' %}{{ 'gp-h-0' }}{% else %}{{ 'gp-h-full !gp-pb-0' }}{% endif %}"
                style="--pb: calc((1/1)*100%);--pb-tablet: calc((1/1)*100%);--pb-mobile: calc((1/1)*100%); {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %}{{ 'display: flex; align-items: center; justify-content: center' }}{% endif %}"
              >
                
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
    <img
      alt="{{featureMedia.alt | escape}}" width="{{featureMedia.width}}" height="{{featureMedia.height}}" draggable="false" quality-type="{&quot;desktop&quot;:&quot;finest&quot;}" quality-percent="{&quot;desktop&quot;:100}" data-sizes="auto" sizes="100vw" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9Imcte3tmZWF0dXJlTWVkaWEud2lkdGh9fS17e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0idXJsKCNnLXt7ZmVhdHVyZU1lZGlhLndpZHRofX0te3tmZWF0dXJlTWVkaWEuaGVpZ2h0fX0pIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii17e2ZlYXR1cmVNZWRpYS53aWR0aH19IiB0bz0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" base-src="{{src | image_url}}" data-src="{{src | image_url}}" data-srcset="{% if src != null %}{{src | image_url: width: 480}} 480w, {{src | image_url: width: 768}} 768w, {{src | image_url: width: 1024}} 1024w, {{src | image_url: width: 1440}} 1440w{% else %}{{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}{% endif %}"
      id="{% if featureMedia != null %}{{featureMedia.id}}{% endif %}"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity {{shouldHidden}} gp_lazyload"
    />
       
       
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      src="{{mediaSourceVideo}}" alt="{{featureMedia.alt | escape}}" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" frameborder="0" width="100%" height="100%" controls="true" allowfullscreen="true"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1"
      class="feature-video gp-w-full gp-h-full gp-relative"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed is-check-loaded="true">
    
    <video
    id="gp-video-undefined" title="{{featureMedia.alt | escape}}" preload="none" data-src="{{mediaSourceVideo}}" poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}" data-poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      style="width:100%;max-height:100%"
      class="gp-w-full gp-invisible gp-lazyload-video"
      controls
      
      
      
      playsinline
      src=""
    >
      
    </video>
    
     <div
        role="presentation"
        style="width:100%;max-height:100%"
        class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-h-full"
        >
           
    <img
      width="2237" height="1678" draggable="false" quality-type quality-percent data-sizes="auto" sizes="100vw" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" base-src="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}" data-src="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}" data-srcset="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      id="video-thumbnail"
      
      class="gp-w-full gp-h-full gp-object-cover gp_lazyload"
    />
          <button type="button" class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]" aria-label="Play">
            <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
              />
            </svg>
          </button>
        </div>
  
  </gp-lite-html5-embed>
        <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-lite-html5-embed-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      {% when 'model' %}
        
    <model-viewer
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}" alt="{{featureMedia.preview_image.alt}}" data-shopify-feature="1.12" width="100%" poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}" ar-status="not-presenting" camera-controls="true"
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
    <img
      alt="No Image" width="2237" height="1678" draggable="false" quality-type="{&quot;desktop&quot;:&quot;finest&quot;}" quality-percent="{&quot;desktop&quot;:100}" data-sizes="auto" sizes="100vw" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif"
      id=""
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp_lazyload"
    />
    {% endcase %}
    
              </div>
            </div>
          </div>
        </div>
      
    {% endcapture %}
    {% if product.media.size > 1 %}
        
      
    <gp-carousel gp-data='{"id":"ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}}","isHiddenArrowWhenDisabled":true,"setting":{"loop":{"desktop":false},"itemNumber":{"desktop":1},"dot":{"desktop":true,"tablet":true,"mobile":true},"dotStyle":{"desktop":"none","tablet":"none","mobile":"none"},"dotSize":{"desktop":12},"dotGapToCarousel":{"desktop":16},"dotColor":{"desktop":"bg-1"},"dotActiveColor":{"desktop":"line-3"},"controlOverContent":{"desktop":false,"tablet":false,"mobile":false},"enableDrag":{"desktop":true,"tablet":true,"mobile":true},"arrowCustom":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","arrowIconSize":{"desktop":24},"arrowCustomColor":"#000000","arrowBorder":{"desktop":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"}},"roundedArrow":{"desktop":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"}},"sneakPeakType":{"desktop":"center"},"arrowGapToEachSide":"16","navigationStyle":{"desktop":"none"},"navigationEnable":{"desktop":false},"arrowButtonSize":{"desktop":{}},"enableAction":{"desktop":true}},"styles":{"sizeSetting":{"desktop":{"width":"100%","height":"auto"},"tablet":{"width":"100%","height":"auto"},"mobile":{"width":"100%","height":"auto"}},"align":{"desktop":"flex-start","tablet":"flex-start","mobile":"flex-start"}}}' data-id="gp-carousel-g3QvEhuucd" id="gp-root-carousel-ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}}" type="gp-feature-image-carousel" product-id="{{product.id}}" product-media="{{product.media.size}}" style="--jc:flex-start;--jc-tablet:flex-start;--jc-mobile:flex-start;--o:1;--o-tablet:1;--o-mobile:1;--d:flex;--d-mobile:flex;--d-tablet:flex" class="gp-group/carousel gp-w-full gp-flex `gp-flex-1 gp-w-full gp-feature-image-carousel gp-product-images-gallery-undefined">
      <div
        
        class="gp-carousel-g3QvEhuucd gp-featured-image-wrapper gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block"
        style="playSpeed:600;--h:auto;--h-tablet:auto;--h-mobile:auto;--w:100%;--w-tablet:100%;--w-mobile:100%"
      >
        <div class="gp-relative gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row" style="--h:100%;--h-tablet:100%;--h-mobile:100%;gap:16px">
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0 gp-carousel-arrow-gp-carousel-g3QvEhuucd ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}} gp-carousel-action-back gem-slider-previous gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--d:none;--d-tablet:none;--d-mobile:none;--left:3px;--right:initial;--top:initial;--bottom:;--left-tablet:3px;--right-tablet:initial;--top-tablet:initial;--bottom-tablet:;--left-mobile:3px;--right-mobile:initial;--top-mobile:initial;--bottom-mobile:"
    >
      <style type="text/css">
      .ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g3QvEhuucd {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g3QvEhuucd::before {
        content: '';
        height: 100%;
        width: 100%;
        position: absolute;
        pointer-events: none;
        z-index: 10;
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      @media only screen and (max-width: 1024px) and (min-width: 768px) {
        .ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g3QvEhuucd {
          
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
        }
        .ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g3QvEhuucd::before {
          border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
          
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
        }
      }
      @media only screen and (max-width: 768px) {
        .ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g3QvEhuucd {
          
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
        }
        .ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g3QvEhuucd::before {
          border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
          
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
        }
      }
    </style>
      
    <div
      class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
      style="--c:#000000;--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
    >
        <svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z" fill="currentColor"/>
    </svg>
    </div>
  
    </button>
  
          <div id="gp-carousel-ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none gp-carousel-slider-gp-carousel-g3QvEhuucd mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full" style="--cg-mobile:0px;--cg-tablet:0px;--cg:0px">
            
    {% if product.media.size > 0 %}
      
     {% assign largestRatio = 0 %}
      {% for featureMedia in product.media %}
          {% assign height = featureMedia.height | times: 1.0 %}
          {% assign width = featureMedia.width | times: 1.0 %}
          {% assign ratio = height | divided_by: width %}
          {% if ratio > largestRatio %}{% assign largestRatio = ratio %}{% endif %}
        {% endfor %}
      {% for featureMedia in product.media %}{% if featureMedia.media_type == 'image' %}{% for image in product.images %}{% if image.src == featureMedia.src %}{% assign imageID = image.id %}{% break %}{% endif %}{% endfor %}{% else %}{% assign imageID = '' %}{% endif %}
        
    {% assign productImageWidth = 0 %}
    {% case featureMedia.media_type %}
      {% when 'image' %}
        {% assign productImageWidth = featureMedia.width %}
      {% else %}
        {% assign productImageWidth = featureMedia.preview_image.width %}
    {% endcase %}
    
    <div
      
      id="{{section.id}}-{{imageID}}"
      class="gem-slider-item gp-w-full gem-slider-item-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}} gp-image-item gp-flex gp-items-center gp-justify-center gp-group gp-relative gp-overflow-hidden gp-cursor-pointer data-[outline=deactive]:after:!gp-border-transparent gp-ft-image-item !gp-min-w-full !gp-max-w-full' undefined"
      style="width:{{productImageWidth}}px;--minw:calc(100% / 4 - 33px);--minw-tablet:calc(100% / 4 - 33px);--minw-mobile:calc(100% / 4 - 33px);--maxw:calc(100% / 4 - 33px);--maxw-tablet:calc(100% / 4 - 33px);--maxw-mobile:calc(100% / 4 - 33px);max-width:100%;outline-color:var(--g-c-brand, brand);--bblr:6px;--bbrr:6px;--btlr:6px;--btrr:6px;--hvr-bblr:6px;--hvr-bbrr:6px;--hvr-btlr:6px;--hvr-btrr:6px;--focus-bblr:6px;--focus-bbrr:6px;--focus-btlr:6px;--focus-btrr:6px;-undefined-bblr:6px;-undefined-bbrr:6px;-undefined-btlr:6px;-undefined-btrr:6px;--h:auto;--h-tablet:auto;--h-mobile:auto"
      data-index=""
      grid-index="{{forloop.index}}"
      external-id="{{media.external_id}}"
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
    >
      <div
        class="gp-w-full gp-h-full"
        
      >
      
      <div
        class="gp-w-full gp-relative {% if featureMedia == null or featureMedia.media_type == 'image' %}{{ 'gp-h-0' }}{% else %}{{ 'gp-h-full !gp-pb-0' }}{% endif %}"
        style="--pb: calc((1/1)*100%);--pb-tablet: calc((1/1)*100%);--pb-mobile: calc((1/1)*100%); {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %}{{ 'display: flex; align-items: center; justify-content: center' }}{% endif %}"
      >
        
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
    <img
      alt="{{featureMedia.alt | escape}}" width="{{featureMedia.width}}" height="{{featureMedia.height}}" draggable="false" quality-type="{&quot;desktop&quot;:&quot;finest&quot;}" quality-percent="{&quot;desktop&quot;:100}" data-sizes="auto" sizes="100vw" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9Imcte3tmZWF0dXJlTWVkaWEud2lkdGh9fS17e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0idXJsKCNnLXt7ZmVhdHVyZU1lZGlhLndpZHRofX0te3tmZWF0dXJlTWVkaWEuaGVpZ2h0fX0pIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii17e2ZlYXR1cmVNZWRpYS53aWR0aH19IiB0bz0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" base-src="{{src | image_url}}" data-src="{{src | image_url}}" data-srcset="{% if src != null %}{{src | image_url: width: 480}} 480w, {{src | image_url: width: 768}} 768w, {{src | image_url: width: 1024}} 1024w, {{src | image_url: width: 1440}} 1440w{% else %}{{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}{% endif %}"
      id="{% if featureMedia != null %}{{featureMedia.id}}{% endif %}"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity {{shouldHidden}} gp_lazyload"
    />
       
       
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      src="{{mediaSourceVideo}}" alt="{{featureMedia.alt | escape}}" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" frameborder="0" width="100%" height="100%" controls="true" allowfullscreen="true"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1"
      class="feature-video gp-w-full gp-h-full gp-relative"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed is-check-loaded="true">
    
    <video
    id="gp-video-undefined" title="{{featureMedia.alt | escape}}" preload="none" data-src="{{mediaSourceVideo}}" poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}" data-poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      style="width:100%;max-height:100%"
      class="gp-w-full gp-invisible gp-lazyload-video"
      controls
      
      
      
      playsinline
      src=""
    >
      
    </video>
    
     <div
        role="presentation"
        style="width:100%;max-height:100%"
        class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-h-full"
        >
           
    <img
      width="2237" height="1678" draggable="false" quality-type quality-percent data-sizes="auto" sizes="100vw" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" base-src="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}" data-src="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}" data-srcset="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      id="video-thumbnail"
      
      class="gp-w-full gp-h-full gp-object-cover gp_lazyload"
    />
          <button type="button" class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]" aria-label="Play">
            <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
              />
            </svg>
          </button>
        </div>
  
  </gp-lite-html5-embed>
        <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-lite-html5-embed-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      {% when 'model' %}
        
    <model-viewer
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}" alt="{{featureMedia.preview_image.alt}}" data-shopify-feature="1.12" width="100%" poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}" ar-status="not-presenting" camera-controls="true"
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
    <img
      alt="No Image" width="2237" height="1678" draggable="false" quality-type="{&quot;desktop&quot;:&quot;finest&quot;}" quality-percent="{&quot;desktop&quot;:100}" data-sizes="auto" sizes="100vw" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif"
      id=""
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp_lazyload"
    />
    {% endcase %}
    
      </div>
      
      </div>
    </div>
  {% endfor %}
    {% else %}
    <img
      alt="no image" width="480" height="480" draggable="false" quality-type="{&quot;desktop&quot;:&quot;finest&quot;}" quality-percent="{&quot;desktop&quot;:100}" data-sizes="auto" sizes="100vw" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgwIiBoZWlnaHQ9IjQ4MCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImctNDgwLTQ4MCI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0iNDgwIiBoZWlnaHQ9IjQ4MCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iNDgwIiBoZWlnaHQ9IjQ4MCIgZmlsbD0idXJsKCNnLTQ4MC00ODApIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii00ODAiIHRvPSI0ODAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" base-src="{{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}" data-src="{{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}" data-srcset="{{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}"
      id="noImageError"
      style="height:100%;--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
      class="gp-w-full featured-image-only !gp-rounded-none"
    />{% endif %}
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0 gp-carousel-arrow-gp-carousel-g3QvEhuucd ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}} gp-carousel-action-next gem-slider-next gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--d:none;--d-tablet:none;--d-mobile:none;--right:3px;--left:initial;--top:;--bottom:initial;--right-tablet:3px;--left-tablet:initial;--top-tablet:;--bottom-tablet:initial;--right-mobile:3px;--left-mobile:initial;--top-mobile:;--bottom-mobile:initial"
    >
      <style type="text/css">
      .ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g3QvEhuucd {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g3QvEhuucd::before {
        content: '';
        height: 100%;
        width: 100%;
        position: absolute;
        pointer-events: none;
        z-index: 10;
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      @media only screen and (max-width: 1024px) and (min-width: 768px) {
        .ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g3QvEhuucd {
          
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
        }
        .ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g3QvEhuucd::before {
          border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
          
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
        }
      }
      @media only screen and (max-width: 768px) {
        .ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g3QvEhuucd {
          
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
        }
        .ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g3QvEhuucd::before {
          border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
          
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
        }
      }
    </style>
      
    <div
      class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
      style="--c:#000000;--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
    >
        <svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z" fill="currentColor"/>
    </svg>
    </div>
  
    </button>
  
        </div>
        
    
        <div class="gp-carousel-dot-container gp-carousel-dot-container-ft-gp-carousel-g3QvEhuucd-{{section.id}}-{{product.id}} gp-z-2 gp-flex gp-flex-1 gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-static gp-flex-row gp-left-0 gp-right-0 tablet:gp-static tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-static mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0" style="--bottom:16px;--bottom-tablet:16px;--bottom-mobile:16px;--d:none;--d-tablet:none;--d-mobile:none">
          <!-- item render by js -->
        </div>
      
  
      </div>
    </gp-carousel>
    {% if product.media.size > 1 %} <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-carousel-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script> {% endif %}

  
    
         
      {% else %}{{ featureImageOnlyOne }}{% endif %}
  
        
      <div
        class="gallery-wrapper gp-product-images-gallery gp-flex gp-overflow-hidden gp-max-w-full gp-max-h-full data-[only-image=true]:gp-hidden"
        style="--h: 0px;--h-tablet: 0px;--h-mobile: 0px;--jc: flex-start;--o: 0;--o-tablet: 0;--o-mobile: 0;--pos: static;--pos-tablet: static;--pos-mobile: static;--w: 100%;--w-tablet: 100%;--w-mobile: 100%;--bottom: auto;--bottom-tablet: auto;--bottom-mobile: auto;--top: auto;--top-tablet: auto;--top-mobile: auto;--left: auto;--left-tablet: auto;--left-mobile: auto;--right: auto;--right-tablet: auto;--right-mobile: auto;"
        data-only-image="{% if product.media.size > 1 %}false{% else %}true{% endif %}"
      >
        {% if product.media.size > 1 %}
    
    <gp-carousel gp-data='{"id":"gp-gallery-g3QvEhuucd-{{product.id}}","isHiddenArrowWhenDisabled":true,"setting":{"loop":{"desktop":true},"itemNumber":{"desktop":"auto","tablet":"auto","mobile":"auto"},"dot":{"desktop":false,"tablet":false,"mobile":false},"dotStyle":{"desktop":"none","tablet":"none","mobile":"none"},"controlOverContent":{"desktop":false,"tablet":false,"mobile":false},"enableDrag":{"desktop":true,"tablet":true,"mobile":true},"vertical":{"desktop":true,"mobile":true,"tablet":true},"arrowCustom":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","arrowIconSize":{"desktop":24},"arrowCustomColor":"#000000","arrowBorder":{"desktop":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"}},"roundedArrow":{"desktop":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"}},"sneakPeakType":{"desktop":"center"},"arrowGapToEachSide":"16","navigationStyle":{"desktop":"none"},"navigationEnable":{"desktop":false},"arrowButtonSize":{"desktop":{}},"enableAction":{"desktop":true}},"styles":{"spacing":{"desktop":5,"tablet":5,"mobile":5},"sizeSetting":{"desktop":{"width":"100%","height":"100%"},"tablet":{"width":"100%","height":"100%"},"mobile":{"width":"100%","height":"100%"}}}}' data-id="gp-gallery-g3QvEhuucd" id="gp-root-carousel-gp-gallery-g3QvEhuucd-{{product.id}}" type="grid-carousel" product-media="{{product.media.size}}" style="--d:flex;--d-mobile:flex;--d-tablet:flex" class="gp-group/carousel gp-w-full gp-flex !gp-flex-col tablet:!flex-col mobile:!flex-col gp-flex-1 gp-w-full carousel-gallery gp-px-0 tablet:gp-px-0 mobile:gp-px-0">
      <div
        
        class="gp-gallery-g3QvEhuucd  gp-px-0 tablet:gp-px-0 mobile:gp-px-0 gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper !gp-flex tablet:!gp-flex mobile:!gp-flex mobile:gp-flex tablet:gp-flex gp-flex"
        style="--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%"
      >
        <div class="gp-relative gp-flex gp-items-center gp-justify-between mobile:gp-h-full mobile:gp-flex-1 tablet:gp-h-full tablet:gp-flex-1 gp-h-full gp-flex-1 mobile:!gp-flex-col tablet:!gp-flex-col !gp-flex-col" style="--w:100%;--w-tablet:100%;--w-mobile:100%;gap:16px">
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0 gp-carousel-arrow-gp-gallery-g3QvEhuucd gp-gallery-g3QvEhuucd-{{product.id}} gp-carousel-action-back gem-slider-previous gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--d:none;--d-tablet:none;--d-mobile:none;--top:initial;--left:initial;--bottom:initial;--right:;--top-tablet:initial;--left-tablet:initial;--bottom-tablet:initial;--right-tablet:;--top-mobile:initial;--left-mobile:initial;--bottom-mobile:initial;--right-mobile:"
    >
      <style type="text/css">
      .gp-gallery-g3QvEhuucd-{{product.id}}.gp-carousel-arrow-gp-gallery-g3QvEhuucd {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .gp-gallery-g3QvEhuucd-{{product.id}}.gp-carousel-arrow-gp-gallery-g3QvEhuucd::before {
        content: '';
        height: 100%;
        width: 100%;
        position: absolute;
        pointer-events: none;
        z-index: 10;
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      @media only screen and (max-width: 1024px) and (min-width: 768px) {
        .gp-gallery-g3QvEhuucd-{{product.id}}.gp-carousel-arrow-gp-gallery-g3QvEhuucd {
          
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
        }
        .gp-gallery-g3QvEhuucd-{{product.id}}.gp-carousel-arrow-gp-gallery-g3QvEhuucd::before {
          border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
          
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
        }
      }
      @media only screen and (max-width: 768px) {
        .gp-gallery-g3QvEhuucd-{{product.id}}.gp-carousel-arrow-gp-gallery-g3QvEhuucd {
          
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
        }
        .gp-gallery-g3QvEhuucd-{{product.id}}.gp-carousel-arrow-gp-gallery-g3QvEhuucd::before {
          border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
          
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
        }
      }
    </style>
      
    <div
      class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-[-90deg] tablet:gp-rotate-[-90deg] mobile:gp-rotate-[-90deg]"
      style="--c:#000000;--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
    >
        <svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z" fill="currentColor"/>
    </svg>
    </div>
  
    </button>
  
          <div id="gp-carousel-gp-gallery-g3QvEhuucd-{{product.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none gp-carousel-slider-gp-gallery-g3QvEhuucd !gp-min-h-full mobile:!gp-min-h-full tablet:!gp-min-h-full mobile:!gp-flex-wrap tablet:!gp-flex-wrap !gp-flex-wrap mobile:!gp-min-h-0 tablet:!gp-min-h-0 !gp-min-h-0" style="--rg-mobile:5px;--rg-tablet:5px;--rg:5px">
            
    {% if product.media.size > 1 %}{% for media in product.media %}{% if media.media_type == 'image' %}{% for image in product.images %}{% if image.src == media.src %}{% assign imageID = image.id %}{% break %}{% endif %}{% endfor %}{% else %}{% assign imageID = '' %}{% endif %}
          {% if media.id == product.featured_media.id  %}
    {% if media.media_type == 'video' %}{% assign mediaSourceUrl = media.sources.last.url %}{% endif %}
    {% if media.media_type == 'external_video' %}{% assign mediaSourceUrl = media | external_video_url %}{% endif %}
    {% if media.media_type == 'image' %}{% assign mediaSourceUrl = media.src %}{% endif %}
    
    <div
      data-outline="active"
      id="{{imageID}}"
      class="gem-slider-item gp-w-full gem-slider-item-gp-gallery-g3QvEhuucd-{{product.id}} gp-image-item gp-flex gp-items-center gp-justify-center gp-group gp-relative gp-overflow-hidden gp-cursor-pointer data-[outline=deactive]:after:!gp-border-transparent gp-gallery-image-item active"
      style="--minw:100%;--minw-tablet:100%;--minw-mobile:100%;--maxw:100%;--maxw-tablet:100%;--maxw-mobile:100%;outline-color:var(--g-c-brand, brand);--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--bblr:4px;--bbrr:4px;--btlr:4px;--btrr:4px;--hvr-bblr:4px;--hvr-bbrr:4px;--hvr-btlr:4px;--hvr-btrr:4px;--focus-bblr:4px;--focus-bbrr:4px;--focus-btlr:4px;--focus-btrr:4px;-undefined-bblr:4px;-undefined-bbrr:4px;-undefined-btlr:4px;-undefined-btrr:4px"
      data-index=""
      grid-index="{{forloop.index}}"
      external-id="{{media.external_id}}"
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
    >
      <div
        class="gp-w-full gp-h-full"
        
      >
      
      <div class="gp-w-full gp-relative"
        style="--pb: calc((1/1)*100%);--pb-tablet: calc((1/1)*100%);--pb-mobile: calc((1/1)*100%);;"
      >
      
    <img
      alt="{{media.alt | escape}}" width="{{media.width}}" height="{{media.height}}" draggable="false" quality-type="{&quot;desktop&quot;:&quot;finest&quot;}" quality-percent="{&quot;desktop&quot;:100}" data-sizes="auto" sizes="100vw" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3ttZWRpYS53aWR0aH19IiBoZWlnaHQ9Int7bWVkaWEuaGVpZ2h0fX0iIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLXt7bWVkaWEud2lkdGh9fS17e21lZGlhLmhlaWdodH19Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSJ7e21lZGlhLndpZHRofX0iIGhlaWdodD0ie3ttZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3ttZWRpYS53aWR0aH19IiBoZWlnaHQ9Int7bWVkaWEuaGVpZ2h0fX0iIGZpbGw9InVybCgjZy17e21lZGlhLndpZHRofX0te3ttZWRpYS5oZWlnaHR9fSkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLXt7bWVkaWEud2lkdGh9fSIgdG89Int7bWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" base-src="{{media.preview_image | image_url}}" data-src="{{media.preview_image | image_url}}" data-srcset="{{media.preview_image | image_url: width: 480}} 480w, {{media.preview_image | image_url: width: 768}} 768w, {{media.preview_image | image_url: width: 1024}} 1024w, {{media.preview_image | image_url: width: 1440}} 1440w"
      id=""
      style="width:100%;height:100%;cursor:pointer;--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
      class="!gp-rounded-none gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 gp-cursor-pointer"
    />

    {% if media.media_type == 'video' or media.media_type == 'external_video' %}<div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0" >
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
        <path d="M17.6869 12.2646L17.6868 12.2646L6.78379 18.4464C6.78378 18.4464 6.78376 18.4464 6.78374 18.4464C6.52931 18.5903 6.1665 18.4179 6.1665 18.0416V5.95844C6.1665 5.58218 6.52917 5.40981 6.7836 5.55354C6.78366 5.55357 6.78373 5.55361 6.78379 5.55365L17.6868 11.7354L17.6869 11.7354C17.8819 11.846 17.8819 12.154 17.6869 12.2646Z" stroke="#F9F9F9" stroke-miterlimit="10"/>
      </svg>
    </div>{% endif %}
    {% if media.media_type == 'model' %}<div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.7441 4.57034C11.9017 4.47655 12.098 4.47655 12.2555 4.57034L18.5889 8.33957C18.7404 8.42971 18.8332 8.59296 18.8332 8.76923V15.2308C18.8332 15.407 18.7404 15.5703 18.5889 15.6604L12.2555 19.4297C12.098 19.5234 11.9017 19.5234 11.7441 19.4297L5.41079 15.6604C5.25932 15.5703 5.1665 15.407 5.1665 15.2308V8.76923C5.1665 8.59296 5.25932 8.42971 5.41079 8.33957L11.7441 4.57034ZM6.1665 9.64865V14.9465L11.4998 18.1206V12.8227L6.1665 9.64865ZM12.4998 12.8227V18.1206L17.8332 14.9465V9.64865L12.4998 12.8227ZM17.3555 8.76923L11.9998 11.9566L6.64417 8.76923L11.9998 5.58185L17.3555 8.76923Z" fill="#F9F9F9"/>
      </svg>
    </div>{% endif %}

    <div class="gp-absolute gp-inset-0 gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-bg-black/50 gp-opacity-0 gp-transition-opacity gp-duration-100 group-hover:gp-opacity-100"
    style="--d: none;--d-mobile: none;--d-tablet: none;"
    >
      <svg
        height="100%"
        width="100%"
        xmlns="http://www.w3.org/2000/svg"
        class="gp-h-6 gp-w-6"
        viewBox="0 0 512 512"
        color="#fff"
      ><path
          fill="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M62.2467 345.253C43.7072 326.714 29.1474 305.116 18.9714 281.057C8.42839 256.13 3.08301 229.671 3.08301 202.418C3.08301 175.165 8.43012 148.707 18.974 123.78C29.15 99.7213 43.7098 78.123 62.2485 59.5834C80.788 41.0439 102.386 26.4841 126.445 16.3081C151.372 5.76422 177.831 0.417969 205.084 0.417969C232.337 0.417969 258.794 5.76421 283.722 16.3064C307.78 26.4823 329.379 41.0422 347.918 59.5817C366.458 78.1212 381.017 99.7196 391.194 123.778C401.737 148.706 407.083 175.163 407.083 202.417C407.083 229.671 401.737 256.129 391.194 281.056C388.406 287.648 385.277 294.048 381.839 300.257L493.397 411.815C514.091 432.511 514.091 466.187 493.395 486.883L484.272 496.006C474.245 506.032 460.915 511.553 446.738 511.553C432.559 511.553 419.228 506.032 409.202 496.006L296.022 382.824C291.996 384.854 287.898 386.762 283.721 388.528C258.794 399.073 232.336 404.419 205.082 404.419C177.828 404.419 151.371 399.071 126.443 388.528C102.385 378.352 80.7863 363.793 62.2467 345.253ZM301.699 336.166C313.928 327.317 324.896 316.835 334.282 305.034C342.149 295.142 348.9 284.325 354.355 272.775C364.433 251.432 370.076 227.586 370.076 202.419C370.076 111.296 296.206 37.4253 205.083 37.4253C113.96 37.4253 40.0895 111.294 40.0895 202.418C40.0895 293.541 113.96 367.411 205.084 367.411C227.413 367.411 248.701 362.967 268.126 354.928C280.091 349.976 291.347 343.658 301.699 336.166ZM467.229 460.716C473.507 454.439 473.507 444.26 467.229 437.982L360.595 331.348C356.601 336.153 352.378 340.794 347.919 345.253C341.671 351.502 335.068 357.286 328.147 362.615L435.371 469.839C438.511 472.977 442.624 474.547 446.739 474.547C450.853 474.547 454.967 472.978 458.106 469.839L467.229 460.716ZM223.582 183.91H281.071C291.292 183.91 299.574 192.194 299.575 202.414C299.575 206.778 298.062 210.786 295.533 213.951C292.143 218.195 286.926 220.916 281.072 220.916H228.303H223.583V225.63V278.406C223.583 287.081 217.613 294.358 209.559 296.361C208.124 296.717 206.625 296.909 205.08 296.909C194.861 296.909 186.577 288.625 186.577 278.406V220.917H129.087C118.868 220.917 110.584 212.633 110.584 202.414C110.584 192.195 118.868 183.911 129.087 183.911H186.576V126.421C186.576 116.202 194.86 107.918 205.079 107.918C215.298 107.918 223.582 116.202 223.582 126.421V183.91Z"
        />
      </svg>
    </div>
    </div>
    
      </div>
    </div>
  {% else %}
    {% if media.media_type == 'video' %}{% assign mediaSourceUrl = media.sources.last.url %}{% endif %}
    {% if media.media_type == 'external_video' %}{% assign mediaSourceUrl = media | external_video_url %}{% endif %}
    {% if media.media_type == 'image' %}{% assign mediaSourceUrl = media.src %}{% endif %}
    
    <div
      data-outline="deactive"
      id="{{imageID}}"
      class="gem-slider-item gp-w-full gem-slider-item-gp-gallery-g3QvEhuucd-{{product.id}} gp-image-item gp-flex gp-items-center gp-justify-center gp-group gp-relative gp-overflow-hidden gp-cursor-pointer data-[outline=deactive]:after:!gp-border-transparent gp-gallery-image-item undefined"
      style="--minw:100%;--minw-tablet:100%;--minw-mobile:100%;--maxw:100%;--maxw-tablet:100%;--maxw-mobile:100%;outline-color:var(--g-c-brand, brand);--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--bblr:4px;--bbrr:4px;--btlr:4px;--btrr:4px;--hvr-bblr:4px;--hvr-bbrr:4px;--hvr-btlr:4px;--hvr-btrr:4px;--focus-bblr:4px;--focus-bbrr:4px;--focus-btlr:4px;--focus-btrr:4px;-undefined-bblr:4px;-undefined-bbrr:4px;-undefined-btlr:4px;-undefined-btrr:4px"
      data-index=""
      grid-index="{{forloop.index}}"
      external-id="{{media.external_id}}"
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
    >
      <div
        class="gp-w-full gp-h-full"
        
      >
      
      <div class="gp-w-full gp-relative"
        style="--pb: calc((1/1)*100%);--pb-tablet: calc((1/1)*100%);--pb-mobile: calc((1/1)*100%);;"
      >
      
    <img
      alt="{{media.alt | escape}}" width="{{media.width}}" height="{{media.height}}" draggable="false" quality-type="{&quot;desktop&quot;:&quot;finest&quot;}" quality-percent="{&quot;desktop&quot;:100}" data-sizes="auto" sizes="100vw" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3ttZWRpYS53aWR0aH19IiBoZWlnaHQ9Int7bWVkaWEuaGVpZ2h0fX0iIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLXt7bWVkaWEud2lkdGh9fS17e21lZGlhLmhlaWdodH19Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSJ7e21lZGlhLndpZHRofX0iIGhlaWdodD0ie3ttZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3ttZWRpYS53aWR0aH19IiBoZWlnaHQ9Int7bWVkaWEuaGVpZ2h0fX0iIGZpbGw9InVybCgjZy17e21lZGlhLndpZHRofX0te3ttZWRpYS5oZWlnaHR9fSkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLXt7bWVkaWEud2lkdGh9fSIgdG89Int7bWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" base-src="{{media.preview_image | image_url}}" data-src="{{media.preview_image | image_url}}" data-srcset="{{media.preview_image | image_url: width: 480}} 480w, {{media.preview_image | image_url: width: 768}} 768w, {{media.preview_image | image_url: width: 1024}} 1024w, {{media.preview_image | image_url: width: 1440}} 1440w"
      id=""
      style="width:100%;height:100%;cursor:pointer;--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
      class="!gp-rounded-none gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 gp-cursor-pointer"
    />

    {% if media.media_type == 'video' or media.media_type == 'external_video' %}<div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0" >
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
        <path d="M17.6869 12.2646L17.6868 12.2646L6.78379 18.4464C6.78378 18.4464 6.78376 18.4464 6.78374 18.4464C6.52931 18.5903 6.1665 18.4179 6.1665 18.0416V5.95844C6.1665 5.58218 6.52917 5.40981 6.7836 5.55354C6.78366 5.55357 6.78373 5.55361 6.78379 5.55365L17.6868 11.7354L17.6869 11.7354C17.8819 11.846 17.8819 12.154 17.6869 12.2646Z" stroke="#F9F9F9" stroke-miterlimit="10"/>
      </svg>
    </div>{% endif %}
    {% if media.media_type == 'model' %}<div class="gp-absolute gp-pb-1 gp-pr-1 gp-right-0 gp-bottom-0">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect opacity="0.9" width="24" height="24" rx="3" fill="#212121"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M11.7441 4.57034C11.9017 4.47655 12.098 4.47655 12.2555 4.57034L18.5889 8.33957C18.7404 8.42971 18.8332 8.59296 18.8332 8.76923V15.2308C18.8332 15.407 18.7404 15.5703 18.5889 15.6604L12.2555 19.4297C12.098 19.5234 11.9017 19.5234 11.7441 19.4297L5.41079 15.6604C5.25932 15.5703 5.1665 15.407 5.1665 15.2308V8.76923C5.1665 8.59296 5.25932 8.42971 5.41079 8.33957L11.7441 4.57034ZM6.1665 9.64865V14.9465L11.4998 18.1206V12.8227L6.1665 9.64865ZM12.4998 12.8227V18.1206L17.8332 14.9465V9.64865L12.4998 12.8227ZM17.3555 8.76923L11.9998 11.9566L6.64417 8.76923L11.9998 5.58185L17.3555 8.76923Z" fill="#F9F9F9"/>
      </svg>
    </div>{% endif %}

    <div class="gp-absolute gp-inset-0 gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-bg-black/50 gp-opacity-0 gp-transition-opacity gp-duration-100 group-hover:gp-opacity-100"
    style="--d: none;--d-mobile: none;--d-tablet: none;"
    >
      <svg
        height="100%"
        width="100%"
        xmlns="http://www.w3.org/2000/svg"
        class="gp-h-6 gp-w-6"
        viewBox="0 0 512 512"
        color="#fff"
      ><path
          fill="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M62.2467 345.253C43.7072 326.714 29.1474 305.116 18.9714 281.057C8.42839 256.13 3.08301 229.671 3.08301 202.418C3.08301 175.165 8.43012 148.707 18.974 123.78C29.15 99.7213 43.7098 78.123 62.2485 59.5834C80.788 41.0439 102.386 26.4841 126.445 16.3081C151.372 5.76422 177.831 0.417969 205.084 0.417969C232.337 0.417969 258.794 5.76421 283.722 16.3064C307.78 26.4823 329.379 41.0422 347.918 59.5817C366.458 78.1212 381.017 99.7196 391.194 123.778C401.737 148.706 407.083 175.163 407.083 202.417C407.083 229.671 401.737 256.129 391.194 281.056C388.406 287.648 385.277 294.048 381.839 300.257L493.397 411.815C514.091 432.511 514.091 466.187 493.395 486.883L484.272 496.006C474.245 506.032 460.915 511.553 446.738 511.553C432.559 511.553 419.228 506.032 409.202 496.006L296.022 382.824C291.996 384.854 287.898 386.762 283.721 388.528C258.794 399.073 232.336 404.419 205.082 404.419C177.828 404.419 151.371 399.071 126.443 388.528C102.385 378.352 80.7863 363.793 62.2467 345.253ZM301.699 336.166C313.928 327.317 324.896 316.835 334.282 305.034C342.149 295.142 348.9 284.325 354.355 272.775C364.433 251.432 370.076 227.586 370.076 202.419C370.076 111.296 296.206 37.4253 205.083 37.4253C113.96 37.4253 40.0895 111.294 40.0895 202.418C40.0895 293.541 113.96 367.411 205.084 367.411C227.413 367.411 248.701 362.967 268.126 354.928C280.091 349.976 291.347 343.658 301.699 336.166ZM467.229 460.716C473.507 454.439 473.507 444.26 467.229 437.982L360.595 331.348C356.601 336.153 352.378 340.794 347.919 345.253C341.671 351.502 335.068 357.286 328.147 362.615L435.371 469.839C438.511 472.977 442.624 474.547 446.739 474.547C450.853 474.547 454.967 472.978 458.106 469.839L467.229 460.716ZM223.582 183.91H281.071C291.292 183.91 299.574 192.194 299.575 202.414C299.575 206.778 298.062 210.786 295.533 213.951C292.143 218.195 286.926 220.916 281.072 220.916H228.303H223.583V225.63V278.406C223.583 287.081 217.613 294.358 209.559 296.361C208.124 296.717 206.625 296.909 205.08 296.909C194.861 296.909 186.577 288.625 186.577 278.406V220.917H129.087C118.868 220.917 110.584 212.633 110.584 202.414C110.584 192.195 118.868 183.911 129.087 183.911H186.576V126.421C186.576 116.202 194.86 107.918 205.079 107.918C215.298 107.918 223.582 116.202 223.582 126.421V183.91Z"
        />
      </svg>
    </div>
    </div>
    
      </div>
    </div>
  {% endif %}{% endfor %}{% endif %}
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0 gp-carousel-arrow-gp-gallery-g3QvEhuucd gp-gallery-g3QvEhuucd-{{product.id}} gp-carousel-action-next gem-slider-next gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--d:none;--d-tablet:none;--d-mobile:none;--top:initial;--left:;--bottom:initial;--right:initial;--top-tablet:initial;--left-tablet:;--bottom-tablet:initial;--right-tablet:initial;--top-mobile:initial;--left-mobile:;--bottom-mobile:initial;--right-mobile:initial"
    >
      <style type="text/css">
      .gp-gallery-g3QvEhuucd-{{product.id}}.gp-carousel-arrow-gp-gallery-g3QvEhuucd {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .gp-gallery-g3QvEhuucd-{{product.id}}.gp-carousel-arrow-gp-gallery-g3QvEhuucd::before {
        content: '';
        height: 100%;
        width: 100%;
        position: absolute;
        pointer-events: none;
        z-index: 10;
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      @media only screen and (max-width: 1024px) and (min-width: 768px) {
        .gp-gallery-g3QvEhuucd-{{product.id}}.gp-carousel-arrow-gp-gallery-g3QvEhuucd {
          
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
        }
        .gp-gallery-g3QvEhuucd-{{product.id}}.gp-carousel-arrow-gp-gallery-g3QvEhuucd::before {
          border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
          
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
        }
      }
      @media only screen and (max-width: 768px) {
        .gp-gallery-g3QvEhuucd-{{product.id}}.gp-carousel-arrow-gp-gallery-g3QvEhuucd {
          
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
        }
        .gp-gallery-g3QvEhuucd-{{product.id}}.gp-carousel-arrow-gp-gallery-g3QvEhuucd::before {
          border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
          
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
        }
      }
    </style>
      
    <div
      class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-90 tablet:gp-rotate-90 mobile:gp-rotate-90"
      style="--c:#000000;--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
    >
        <svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z" fill="currentColor"/>
    </svg>
    </div>
  
    </button>
  
        </div>
        
    
        <div class="gp-carousel-dot-container gp-carousel-dot-container-gp-gallery-g3QvEhuucd-{{product.id}} gp-z-2 gp-flex gp-flex-1 gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-static gp-flex-col gp-ml-2 gp-w-fit gp-h-full tablet:gp-static tablet:gp-flex-col tablet:gp-ml-2 tablet:gp-w-fit tablet:gp-h-full mobile:gp-static mobile:gp-flex-col mobile:gp-ml-2 mobile:gp-w-fit mobile:gp-h-full" style="--right:0px;--right-tablet:0px;--right-mobile:0px;--d:none;--d-tablet:none;--d-mobile:none">
          <!-- item render by js -->
        </div>
      
  
      </div>
    </gp-carousel>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-carousel-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
  {% endif %}
        
      </div>
    
      </div>
    </gp-product-images>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-product-images-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      data-same-height-display-contents
      style="--jc:start"
      class="gwMUSrwCnP gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"g3l-8DEAeW"}' data-id="g3l-8DEAeW" id="g3l-8DEAeW" data-same-height-subgrid-container class="g3l-8DEAeW gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--op-mobile:100%;--op-tablet:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:16px;--pl:32px;--mb-mobile:var(--g-s-l);--pt-mobile:auto;--pl-mobile:0px;--mb-tablet:var(--g-s-l);--pl-tablet:0px;--cg:8px;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="gXD4-BSNh_ gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--op-mobile:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:4px;--mt-mobile:0px;--mb-mobile:4px;--pt-mobile:0px" class="ga7_Tpvmo_ ">
      {% unless product %}<p>{{ 'gempages.ProductTitle.product_not_found' | t }}</p>{% else %}
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="ga7_Tpvmo_"
        class="ga7_Tpvmo_"
        style="--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425955242"
      >
      <div  >
        <h1
          data-gp-text
          class="gp-text-g-text-2 gp-text-instant"
          style="--w:100%;--tdt:auto;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:var(--g-c-text-2, text-2);--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-heading, heading);--weight:600;--ls:normal;--size:33px;--size-tablet:28px;--size-mobile:24px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;word-break:break-word;overflow:hidden"
        >
          {{ product.title }}
        </h1>
      </div>
    </div>
    {% endunless %}
      </div>
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gpXUji0DsN"}' data-id="gpXUji0DsN" id="gpXUji0DsN" data-same-height-subgrid-container class="gpXUji0DsN gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--op-mobile:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:24px;--mb-mobile:21px;--cg:12px;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:center"
      class="goCMo_LO4j gp-relative gp-flex gp-flex-col"
    >
      
    
    <div data-id="g6k6Vofar4"  style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">
      <div class="gp-flex gp-flex-wrap" style="--cg:2px;--jc:left">
            <div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gkJBBGb1u5 ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gtQVb0g5ls" class="gp-leading-[0] gtQVb0g5ls" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:0px;--ta:left">
      <div data-id="gtQVb0g5ls" class="icon-wrapper gp-inline-flex gp-overflow-hidden gtQVb0g5ls " style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000">
      
        <div >
          <span style="--c:#0171E3;--t:rotate(0deg);--w:20px;--w-tablet:20px;--w-mobile:20px;--h:20px;--h-tablet:20px;--h-mobile:20px;--minw:20px;--minw-tablet:20px;--minw-mobile:20px;--height-desktop:20px;--height-tablet:20px;--height-mobile:20px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor"><path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gnshJ0VfYu ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gSjLscMV_S" class="gp-leading-[0] gSjLscMV_S" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gSjLscMV_S" class="icon-wrapper gp-inline-flex gp-overflow-hidden gSjLscMV_S " style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000">
      
        <div >
          <span style="--c:#0171E3;--t:rotate(0deg);--w:20px;--w-tablet:20px;--w-mobile:20px;--h:20px;--h-tablet:20px;--h-mobile:20px;--minw:20px;--minw-tablet:20px;--minw-mobile:20px;--height-desktop:20px;--height-tablet:20px;--height-mobile:20px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor"><path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gn6mbpjC1T ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gyO9uVZq5M" class="gp-leading-[0] gyO9uVZq5M" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gyO9uVZq5M" class="icon-wrapper gp-inline-flex gp-overflow-hidden gyO9uVZq5M " style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000">
      
        <div >
          <span style="--c:#0171E3;--t:rotate(0deg);--w:20px;--w-tablet:20px;--w-mobile:20px;--h:20px;--h-tablet:20px;--h-mobile:20px;--minw:20px;--minw-tablet:20px;--minw-mobile:20px;--height-desktop:20px;--height-tablet:20px;--height-mobile:20px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor"><path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="graWKLFqar ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="grsNpsQYOO" class="gp-leading-[0] grsNpsQYOO" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:0px;--ta:left">
      <div data-id="grsNpsQYOO" class="icon-wrapper gp-inline-flex gp-overflow-hidden grsNpsQYOO " style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000">
      
        <div >
          <span style="--c:#0171E3;--t:rotate(0deg);--w:20px;--w-tablet:20px;--w-mobile:20px;--h:20px;--h-tablet:20px;--h-mobile:20px;--minw:20px;--minw-tablet:20px;--minw-mobile:20px;--height-desktop:20px;--height-tablet:20px;--height-mobile:20px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor"><path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gb98Rsky-5 ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gk93DbMnpT" class="gp-leading-[0] gk93DbMnpT" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gk93DbMnpT" class="icon-wrapper gp-inline-flex gp-overflow-hidden gk93DbMnpT " style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000">
      
        <div >
          <span style="--c:#0171E3;--t:rotate(0deg);--w:20px;--w-tablet:20px;--w-mobile:20px;--h:20px;--h-tablet:20px;--h-mobile:20px;--minw:20px;--minw-tablet:20px;--minw-mobile:20px;--height-desktop:20px;--height-tablet:20px;--height-mobile:20px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor"><path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div>
          </div>
    </div>
  
    </div><div
      data-same-height-display-contents
      style="--jc:center"
      class="go8UR5T-Om gp-relative gp-flex gp-flex-col"
    >
      
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="g2J3SUlfsO"
        class="g2J3SUlfsO"
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425955270"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--ls:normal;--size:17px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#000000;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden;--tdl:"
        >
          {{ section.settings.gg2J3SUlfsO_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
      <style id="custom-css-g2J3SUlfsO">
        .g2J3SUlfsO {

}
.g2J3SUlfsO p {

}
      </style>
    
    </div>

      
    </gp-row>
  
  
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gC54dy6Zpl"}' data-id="gC54dy6Zpl" id="gC54dy6Zpl" data-same-height-subgrid-container class="gC54dy6Zpl gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--op-mobile:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:4px;--cg:var(--g-s-s);--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:center"
      class="gw31Hpny6F gp-relative gp-flex gp-flex-col"
    >
      
      <gp-product-price
        id="shopify-text-element-gLicagnxaU" data-id="gLicagnxaU"
        class="gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden gLicagnxaU"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px"
        gp-data='{"priceType":"regular","uid":"gLicagnxaU","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
      >
        
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div id="p-price-gLicagnxaU" data-id
        
        style="--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425955274"
      >
      <div  >
        <div
          data-gp-text
          class="gp-price gp-money  gp-text-instant"
          style="--w:100%;--tdc:#242424;--tdt:1px;--ts:none;--ta:left;--c:#242424;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-heading, heading);--weight:600;--ls:normal;--size:28px;--size-tablet:24px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden;--tdl:"
        >
          
     {% if variant.price %}
       {{ variant.price | money}}
     {% endif %}
   
        </div>
      </div>
    </div>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-product-price-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      data-same-height-display-contents
      style="--jc:center"
      class="gVCwNRv1t4 gp-relative gp-flex gp-flex-col"
    >
      
      <gp-product-price
        id="shopify-text-element-gzSZjlDqzy" data-id="gzSZjlDqzy" data-hidden="{% if variant.compare_at_price > variant.price and variant.compare_at_price >= 0 %}false{% else %}true{% endif %}"
        class="gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden gzSZjlDqzy"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px"
        gp-data='{"priceType":"compare","uid":"gzSZjlDqzy","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
      >
        
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div id="p-price-gzSZjlDqzy" data-id
        
        style="--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425955275"
      >
      <div  >
        <div
          data-gp-text
          class="gp-price gp-product-compare-price gp-g-subheading-2  gp-text-instant"
          style="--w:100%;--tdc:#B4B4B4;--tdt:1px;--ta:left;--c:#000000;word-break:break-word;overflow:hidden;--tdl:line-through"
        >
          
      {% if variant.compare_at_price  %}
        {{ variant.compare_at_price | money}}
      {% else %}

      {% endif %}
    
        </div>
      </div>
    </div>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-product-price-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>

      
    </gp-row>
  
  
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="g6uB5P1M9O"
        class="g6uB5P1M9O"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--op-mobile:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:24px;--mb-mobile:21px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425955276"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--ta-mobile:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--ls:normal;--size:14px;--size-tablet:13px;--size-mobile:12px;--lh:180%;--lh-tablet:130%;--lh-mobile:180%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden;--tdl:"
        >
          {{ section.settings.gg6uB5P1M9O_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
      <style id="custom-css-g6uB5P1M9O">
        .g6uB5P1M9O {

}
.g6uB5P1M9O p {

}
      </style>
    <div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--op-mobile:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:32px;--mb-mobile:21px" class="g-jQyH10E2 ">
      
    {%- assign total_combinations = 1 -%}
    {% for option in product.options_with_values %}{%- assign total_combinations = total_combinations | times: option.values.size -%}{% endfor %}
    <gp-product-variants
      data-id="g-jQyH10E2" has-pre-selected="true" class
      gp-data='{
        "setting":{"blankText":"Please select an option","column":{"desktop":1},"combineFullWidth":{"desktop":true},"combineHeight":"45px","combineWidth":{"desktop":"100%"},"hasOnlyDefaultVariant":false,"hasPreSelected":true,"label":true,"layout":{"desktop":"horizontal"},"optionAlign":{"desktop":"left"},"optionType":"singleOption","price":true,"showAsSwatches":true,"soldOutStyle":"disable","variantPresets":[{"isEdited":true,"optionName":"base","optionType":"rectangle_list","presets":{"color":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"hover":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"normal":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"45px","mobile":"45px","tablet":"45px"}},"dropdown":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"width":{"desktop":"100%","mobile":"100%","tablet":"100%"}},"image":{"height":"64px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"}},"image_shopify":{"height":"64px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"}},"rectangle_list":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":""}}}},{"hide":true,"isEdited":true,"optionName":"Size","optionType":"rectangle_list","presets":{"color":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"hover":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"normal":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"45px","mobile":"45px","tablet":"45px"}},"dropdown":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"width":{"desktop":"100%","mobile":"100%","tablet":"100%"}},"image":{"height":"64px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"}},"image_shopify":{"height":"64px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"}},"rectangle_list":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":""}}}},{"isEdited":true,"optionName":"Color","optionType":"color","presets":{"color":{"height":"35px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"hover":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"normal":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"2px","width":{"desktop":"35px","mobile":"35px","tablet":"35px"}},"dropdown":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"width":{"desktop":"100%","mobile":"100%","tablet":"100%"}},"image":{"height":"Auto","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#575757","isCustom":true,"width":"1px 1px 1px 1px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"#575757","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"#00000000","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"hover":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"normal":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"14px","width":{"desktop":"48px","mobile":"42px","tablet":"48px"}},"image_shopify":{"height":"64px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"}},"rectangle_list":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":""}}}}]},
        "styles":{"align":{"desktop":"left"},"dropdownItemWidth":{"desktop":"fill","mobile":"fill","tablet":"fill"},"fixedDropdownWidth":{"desktop":"240px"},"fullWidth":{"desktop":true},"labelColor":"text-2","labelGap":"14px","labelTypo":{"type":"paragraph-2","attrs":{"bold":true,"color":"#242424","transform":"uppercase"},"custom":{"fontSize":{"desktop":"15px","tablet":"14px","mobile":"12px"},"fontWeight":"400","lineHeight":{"desktop":"180%","tablet":"180%","mobile":"180%"},"fontFamily":{"value":"Roboto","type":"google"},"textShadow":{"angle":37,"blur":"2px","color":"rgba(0, 0, 0, 0.6)","distance":"4px","type":"custom","enable":false},"hasShadowText":false}},"marginBottom":{"desktop":"var(--g-s-xl)","mobile":"14px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"none"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"none"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"none"}},"optionSpacing":"30px","optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"optionTypo":{"type":"paragraph-2","attrs":{"color":"text-2","transform":"uppercase"},"custom":{"fontSize":{"desktop":"14px","tablet":"14px","mobile":"12px"},"fontWeight":"400","lineHeight":{"desktop":"180%","tablet":"180%","mobile":"180%"},"fontFamily":{"value":"Roboto","type":"google"},"textShadow":{"angle":37,"blur":"2px","color":"rgba(0, 0, 0, 0.6)","distance":"4px","type":"custom","enable":false},"hasShadowText":false},"latestUpdate":"transform"},"swatchAutoWidth":{"desktop":true},"swatchHeight":{"desktop":"45px"},"swatchItemWidth":{"desktop":"auto"},"swatchSpacing":"var(--g-s-m)","swatchWidth":{"desktop":"80px"},"width":{"desktop":"100%"}},
        "variants":{{product.variants | json | escape}},
        "optionsWithValues": {{product.options_with_values | json | escape}},
        "variantSelected": {{ variant | json | escape }},
        "variantInventoryQuantity": {{product.variants | map: 'inventory_quantity' | json | escape}},
        "variantInventoryPolicy": {{product.variants | map: 'inventory_policy' | json | escape}},
        "moneyFormat": {{shop.money_format | json | escape}},
        "productId": {{product.id | json | escape}},
        "productUrl": {{product.url | json | escape}},
        "productHandle": {{product.handle | json | escape}},
        "displayState": {"desktop":true,"mobile":true,"tablet":true},
        "totalVariantCombinations": {{total_combinations}},
        "firstAvailableVariant": {{product.selected_or_first_available_variant | json | escape}}
      }
    '>
      <div class="gp-hidden" style="--hvr-shadow: none; --hvr-shadow-tablet: none; --hvr-shadow-mobile: none"></div>
      {%- assign options = product.options_with_values -%}
      {%- assign variants = product.variants -%}
      {% if options.size == 0 or options.size == 1 and variants.size == 1 and variants[0].title == 'Default Title' and variants[0].option1 == 'Default Title' %}<div></div>{% else %}<div
            class="gp-flex gp-flex-col !gp-ml-0"
            style="--gtc:repeat(1, minmax(0, 1fr));--ta:left;--w:100%;--w-tablet:100%;--w-mobile:100%"
          >
          
      {%- assign presets = "base($2)rectangle_list($1)Size($2)rectangle_list($1)Color($2)color" | split: '($1)' -%}
      {% assign hiddenPresetOptions = "Size" | split: ',' %}
      
      {% assign all_option_names = options | map: 'name' %}
      {% assign filtered_options_string = '' %}
      {% assign delimiter = '||' %}
      
      {% for item_name in all_option_names %}
        {% assign is_excluded = false %}
        {% for exclude_name in hiddenPresetOptions %}
          {% if item_name == exclude_name %}
            {% assign is_excluded = true %}
          {% endif %}
        {% endfor %}
        
        {% unless is_excluded %}
          {% if filtered_options_string == '' %}
            {% assign filtered_options_string = item_name %}
          {% else %}
            {% assign filtered_options_string = filtered_options_string | append: delimiter | append: item_name %}
          {% endif %}
        {% endunless %}
      {% endfor %}
      
      {% assign filtered_options_array = filtered_options_string | split: delimiter %}
      
      {% assign last_option_name = filtered_options_array | last %}
      
      {% for option in options %}
        <div
        option-name="{{option.name | escape}}"
        class="gp-flex variant-inside gp-flex-row gp-justify-start {% if hiddenPresetOptions contains option.name %}gp-hidden{% endif %}"
        style="--mb: var(--g-s-xl);--mb-mobile: 14px;{% if forloop.last or option.name == last_option_name %}--mb:0;--mb-mobile:0;--mb-tablet:0;{% endif %}"
        >
          {%- assign showVariantClass = 'variant-display' -%}
          {%- assign optionName = option.name  -%}
          {% for preset in presets %}
            {%- assign presetDetail = preset | split: '($2)' -%}
            {% if presetDetail[1] == 'dropdown' and presetDetail[0] == optionName %}{%- assign showVariantClass = '' -%} {%- break -%}{% endif %}
          {% endfor %}
          
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id
        
        style="--mr:14px;--mb:0px;--mr-tablet:14px;--mb-tablet:0px;--mr-mobile:14px;--mb-mobile:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425955278"
      >
      <div  style="--w:120px;--w-tablet:120px;--w-mobile:120px">
        <legend
          data-gp-text
          class="{{showVariantClass}}  gp-text-instant"
          style="--w:100%;--tdt:auto;--ts:none;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--size:15px;--size-tablet:14px;--size-mobile:12px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--weight:bold;--c:#242424;--tt:uppercase;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{option.name}}
        </legend>
      </div>
    </div>
    
          <div
              variant-option-name="{{option.name | escape}}"
              class="variant-option-group gp-flex gp-w-full gp-flex-wrap gp-items-center  gp-justify-start"
              style="--rg: var(--g-s-m);--cg: var(--g-s-m);"
            >
              {%- assign values = option.values -%}
              {%- assign rootForloop = forloop.index0 -%}
              
                {%- if option.position == 1 -%}
                  {%- assign selectedValue = variant.option1 -%}
                {%- elsif option.position == 2 -%}
                  {%- assign selectedValue = variant.option2 -%}
                {%- else -%}
                  {%- assign selectedValue = variant.option3 -%}
                {%- endif -%}
              
              
              
    {%- assign optionRendered = false -%}
    {%- assign swatches = shop.metafields.GEMPAGES.swatches -%}
    {%- assign swatchesItems = swatches | split: '($1)' -%}

    {% for swatchesItem in swatchesItems %}
      {%- assign colorArraysString = "" -%}
      {%- assign labelsString = "" -%}
      {%- assign imageUrlsString = "" -%}
      {%- assign attrItems = swatchesItem | split: '($3)' -%}

      {% for attrItem in attrItems %}{%- assign attrs = attrItem | split: '($2)' -%}
          {%- assign optionKey = attrs[0] -%}
          {%- assign optionValue = attrs[1] -%}
          
            {% if optionKey == 'optionTitle' %}
            {% assign optionTitle = optionValue %}
            {% elsif optionKey == 'optionType' %}
              {% assign optionType = optionValue %}
            {% endif %}
          

          {% if optionKey == 'optionValues' %}{%- assign opValueItems = optionValue | split: '($4)' -%}
            {% for opValueItem in opValueItems %}{%- assign opValueItemAttrs = opValueItem | split: '($6)' -%}
              {% for opValueItemAttr in opValueItemAttrs %}{%- assign attrs = opValueItemAttr | split: '($5)' -%}
                {%- assign opValueItemKey = attrs[0] -%}
                {%- assign opValueItemValue = attrs[1] -%}

                {% if opValueItemKey == 'label' %}{%- assign labelsString = labelsString | append: opValueItemValue -%}{%- assign labelsString = labelsString | append: "($8)" -%}{% endif %}

                {% if opValueItemKey == 'colors' %}{%- assign colorArraysString = colorArraysString | append: opValueItemValue -%}{%- assign colorArraysString = colorArraysString | append: "($8)" -%}{% endif %}

                {% if opValueItemKey == 'imageUrl' %}{%- assign imageUrlsString = imageUrlsString | append: opValueItemValue -%}{%- assign imageUrlsString = imageUrlsString | append: "($8)" -%}{% endif %}{% endfor %}{% endfor %}{% endif %}
        {% endfor %}

      {%- assign labels = labelsString | split: '($8)' -%}
      {%- assign colorStrings = colorArraysString | split: '($8)' -%}
      {%- assign imageUrls = imageUrlsString | split: '($8)' -%}

      {% if optionTitle == option.name %}
        {%- assign variantPresetString = "base($1)rectangle_list($2)Size($1)rectangle_list($2)Color($1)color" -%}
        {%- assign optionName = option.name | replace: "'", "&apos;" | replace: '"', "&quot;" -%}
        {%- assign items = variantPresetString | split:'($2)' -%}
        {%- assign type = 'dropdown' -%}
        {% for item in items %}
          {%- assign itemPreset = item | split:'($1)' -%}
          {% if itemPreset[0] == optionName %}{%- assign type = itemPreset[1] -%}{% endif %}
          {% if itemPreset[0] == "base" %}{%- assign type = itemPreset[1] -%}{% endif %}
          {% endfor %}
        {%- assign optionRendered = true -%}
        {% for value in values %}
    {%- liquid
      assign option_disabled = true
      for variantItem in product.variants
        case option.position
          when 1
            if variantItem.available and variantItem.option1 == value
              assign option_disabled = false
            endif
          when 2
            if variantItem.available and variantItem.option2 == value
              assign option_disabled = false
            endif
          when 3
            if variantItem.available and variantItem.option3 == value
              assign option_disabled = false
            endif
        endcase
      endfor
    -%}
    
    <label
      id="{{option.name | escape}}-{{value | escape}}"
      for="{{product.id}}-g-jQyH10E2-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
      class="option-item gp-group gp-relative gp-child-item-g-jQyH10E2"
      
    >
      <div class="gp-invisible !gp-max-w-[150px] gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-absolute gp-bottom-[calc(100%+20px)] gp-text-[12px] group-hover:gp-visible gp-w-max gp-bg-[#333333] gp-text-[#F9F9F9] gp-px-[8px] gp-py-[4px] gp-rounded-[8px] gp-mb-[-10px] after:gp-content-[''] after:gp-absolute after:gp-p-[7px] after:gp-left-0 after:gp-w-full after:gp-bottom-[-10px]">
        <p class="gp-text-[#F9F9F9]">{{value | escape}}</p>
        <svg class="gp-absolute gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-z-10 gp-bottom-[-4px]" width="8" height="4" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M8 10L0 0L16 1.41326e-06L8 10Z" fill="#333333"/>
        </svg>
      </div>
      <input
        checked="{%- if option.selected_value == value -%}true{% else %}false{%- endif -%}"
        name="{{product.id}}-options-{{option.name | escape}}"
        id="{{product.id}}-g-jQyH10E2-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
        value="{{value | escape}}"
        type="radio"
        class="gp-sr-only gp-absolute gp-bottom-0 gp-right-0"
      />
      <span class="gp-sr-only">{{value | escape}}</span>
      <div
        option-name="{{option.name | escape}}" option-name-value="{{value | escape}}" data-gp-option-value-id="{{value.id}}"
        class="option-item-inner gp-w-auto gp-h-auto {% if option_disabled == true %}gp-opacity-20{% endif %}">
        
    {% case type %}
      {% when "rectangle_list" %}
      
    <div
      id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}" option-data="{{option.name | escape}}" option-value="{{value | escape}}" option-position="{{option.position}}" data-gp-option-available="{{value.available}}" option-type="rectangle_list" data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}" data-hidden="true"
      class="gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed
  data-[hidden='false']:gp-flex {{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
      style="{% if selectedValue == value %}--ff: var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight: 400;--size: 14px;--size-tablet: 14px;--size-mobile: 12px;--lh: 180%;--lh-tablet: 180%;--lh-mobile: 180%;--c: var(--g-c-text-2, text-2);--tt: uppercase;--d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: none;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--focus-bblr: 0px;--focus-bbrr: 0px;--focus-btlr: 0px;--focus-btrr: 0px;-undefined-bblr: 0px;-undefined-bbrr: 0px;-undefined-btlr: 0px;-undefined-btrr: 0px;{% else %}--ff: var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight: 400;--size: 14px;--size-tablet: 14px;--size-mobile: 12px;--lh: 180%;--lh-tablet: 180%;--lh-mobile: 180%;--tt: uppercase;--d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--focus-bblr: 0px;--focus-bbrr: 0px;--focus-btlr: 0px;--focus-btrr: 0px;-undefined-bblr: 0px;-undefined-bbrr: 0px;-undefined-btlr: 0px;-undefined-btrr: 0px;{% endif %}">
      
      <div
        class="option-value gp-overflow-hidden gp-relative"
        style=" --pl: 16px;--pr: 16px;--pl-tablet: 16px;--pr-tablet: 16px;--pl-mobile: 16px;--pr-mobile: 16px;--pt: 0px;--pb: 0px;">
         
        <span class="gp-text-center" style="--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:14px;--size-tablet:14px;--size-mobile:12px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--tt:uppercase">{{value}}</span>
      </div>
    
    </div>
      {% when "color" %}
      
    {% assign colorsString = null %}
    {% assign colors = null %}
    {% for label in labels %}
      {% if label == value %}
        {% assign colorsString = colorStrings[forloop.index0] %}
      {% endif %}
    {% endfor %}
    {% if colorsString != null %}
      {% assign colors = colorsString | split: '($7)' %}
    {% endif %}
    
    <div
      id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}" option-data="{{option.name | escape}}" option-value="{{value | escape}}" option-position="{{option.position}}" data-gp-option-available="{{value.available}}" option-type="color" data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}" data-hidden="true"
      class="gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed
  data-[hidden='false']:gp-flex {{option.name | escape}}-{{option.position}}-{{ forloop.index }} gp-p-[4px]"
      style="{% if selectedValue == value %}--ff: var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight: 400;--size: 14px;--size-tablet: 14px;--size-mobile: 12px;--lh: 180%;--lh-tablet: 180%;--lh-mobile: 180%;--c: var(--g-c-text-2, text-2);--tt: uppercase;--minw: 32px;--d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: none;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;--focus-bblr: 999999px;--focus-bbrr: 999999px;--focus-btlr: 999999px;--focus-btrr: 999999px;-undefined-bblr: 999999px;-undefined-bbrr: 999999px;-undefined-btlr: 999999px;-undefined-btrr: 999999px;{% else %}--ff: var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight: 400;--size: 14px;--size-tablet: 14px;--size-mobile: 12px;--lh: 180%;--lh-tablet: 180%;--lh-mobile: 180%;--tt: uppercase;--minw: 32px;--d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;--focus-bblr: 999999px;--focus-bbrr: 999999px;--focus-btlr: 999999px;--focus-btrr: 999999px;-undefined-bblr: 999999px;-undefined-bbrr: 999999px;-undefined-btlr: 999999px;-undefined-btrr: 999999px;{% endif %}">
      {% if colors != null and colors.size > 0 %}
      {% for color in colors %}
          {% assign backgroundType = "background-color" %}
          {% if color contains "linear-gradient" %}{% assign backgroundType = "background-image" %}{% endif %}
          <div
            class="color gp-relative gp-h-full gp-w-full gp-overflow-hidden gp-min-w-fit gp-flex gp-color-circle"
            data-test="{{ backgroundType }}: {{ color }}"
            style="{{ backgroundType }}: {{ color }}; {% if selectedValue == value %}--d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: none;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;--focus-bblr: 999999px;--focus-bbrr: 999999px;--focus-btlr: 999999px;--focus-btrr: 999999px;-undefined-bblr: 999999px;-undefined-bbrr: 999999px;-undefined-btlr: 999999px;-undefined-btrr: 999999px;{% else %}--d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;--focus-bblr: 999999px;--focus-bbrr: 999999px;--focus-btlr: 999999px;--focus-btrr: 999999px;-undefined-bblr: 999999px;-undefined-bbrr: 999999px;-undefined-btlr: 999999px;-undefined-btrr: 999999px;{% endif %}"></div>
        {% endfor %}
      {% else %}
      <div
        class="option-value gp-overflow-hidden gp-relative color gp-line-clamp-2 gp-flex gp-h-full gp-w-full gp-items-center gp-justify-center"
        style="{% if selectedValue == value %}--d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: none;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;--focus-bblr: 999999px;--focus-bbrr: 999999px;--focus-btlr: 999999px;--focus-btrr: 999999px;-undefined-bblr: 999999px;-undefined-bbrr: 999999px;-undefined-btlr: 999999px;-undefined-btrr: 999999px;{% else %}--d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;--focus-bblr: 999999px;--focus-bbrr: 999999px;--focus-btlr: 999999px;--focus-btrr: 999999px;-undefined-bblr: 999999px;-undefined-bbrr: 999999px;-undefined-btlr: 999999px;-undefined-btrr: 999999px;{% endif %} ">
         
        <span class="gp-text-center  gp-overflow-hidden gp-text-ellipsis gp-px-1" style="--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:14px;--size-tablet:14px;--size-mobile:12px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--tt:uppercase">{{value}}</span>
      </div>
    {% endif %}
    </div>
  
      {% when "image_shopify" %}
      
      {% assign imageUrl = null %}
      {% for variant in variants %}
        {% assign valueIncludesSelectedOption = false %}
        {% for item in variant.options %}
          {% if item == value %}
          {% assign valueIncludesSelectedOption = true %}
          {% endif %}
        {% endfor %}
        {% if valueIncludesSelectedOption and variant.featured_image or variant.featured_media%}
          {% unless imageUrl %}
            {% if variant.featured_media %}
              {% assign imageUrl = variant.featured_media.preview_image.src | product_img_url: '200x'  %}
            {% else %}
              {% assign imageUrl = variant.featured_image.src | product_img_url: '200x'  %}
            {% endif %}
          {% endunless %}
        {% endif %}
      {% endfor %}
      
    <div
      id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}" option-data="{{option.name | escape}}" option-value="{{value | escape}}" option-position="{{option.position}}" data-gp-option-available="{{value.available}}" option-type="image_shopify" data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}" data-hidden="true"
      class="gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed
  data-[hidden='false']:gp-flex {{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
      style="{% if selectedValue == value %}--ff: var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight: 400;--size: 14px;--size-tablet: 14px;--size-mobile: 12px;--lh: 180%;--lh-tablet: 180%;--lh-mobile: 180%;--c: var(--g-c-text-2, text-2);--tt: uppercase;--d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: none;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--focus-bblr: 0px;--focus-bbrr: 0px;--focus-btlr: 0px;--focus-btrr: 0px;-undefined-bblr: 0px;-undefined-bbrr: 0px;-undefined-btlr: 0px;-undefined-btrr: 0px;{% else %}--ff: var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight: 400;--size: 14px;--size-tablet: 14px;--size-mobile: 12px;--lh: 180%;--lh-tablet: 180%;--lh-mobile: 180%;--tt: uppercase;--d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--focus-bblr: 0px;--focus-bbrr: 0px;--focus-btlr: 0px;--focus-btrr: 0px;-undefined-bblr: 0px;-undefined-bbrr: 0px;-undefined-btlr: 0px;-undefined-btrr: 0px;{% endif %}">
      {% if imageUrl != null and imageUrl != "" %}
      <img class="gp-object-cover gp-rounded-none" style="width: 64px; height: 64px" src="{{imageUrl}}" alt="" />
    {% else %}
      <div
        class="option-value gp-overflow-hidden gp-relative gp-flex gp-flex-col gp-justify-center gp-items-center gp-gap-[6px"
        style=" --pl: 16px;--pr: 16px;--pl-tablet: 16px;--pr-tablet: 16px;--pl-mobile: 16px;--pr-mobile: 16px;--pt: 0px;--pb: 0px;">
        <svg width="15" height="12" viewBox="0 0 15 12" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="2" cy="2" r="2" fill="#D6D6D6"></circle><path d="M0.196854 10.6453L2.40968 8.05946C2.4846 7.97175 2.57719 7.9008 2.68141 7.85124C2.78562 7.80168 2.89913 7.77461 3.01452 7.77181C3.12991 7.76902 3.2446 7.79055 3.3511 7.835C3.45761 7.87945 3.55353 7.94583 3.63262 8.02981L4.66733 9.12714L8.71205 4.29464C8.79123 4.19995 8.89077 4.1243 9.00325 4.07333C9.11573 4.02236 9.23827 3.99737 9.36176 4.00022C9.48524 4.00307 9.6065 4.03369 9.71651 4.08979C9.82651 4.1459 9.92245 4.22605 9.99717 4.32429L14.8329 10.6772C14.9254 10.7989 14.982 10.9441 14.9964 11.0962C15.0107 11.2484 14.9823 11.4016 14.9144 11.5385C14.8464 11.6754 14.7415 11.7907 14.6115 11.8713C14.4815 11.952 14.3316 11.9948 14.1786 11.995L0.822048 12C0.664945 11.9999 0.511148 11.9549 0.378853 11.8703C0.246557 11.7857 0.141299 11.6649 0.0755311 11.5224C0.00976323 11.3799 -0.013762 11.2216 0.00773837 11.0661C0.0292388 10.9107 0.0948652 10.7646 0.196854 10.6453Z" fill="#D6D6D6"></path></svg>
        <span class="gp-text-center  gp-overflow-hidden gp-text-ellipsis gp-px-1" style="--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:14px;--size-tablet:14px;--size-mobile:12px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--tt:uppercase">{{value}}</span>
      </div>
    {% endif %}
    </div>
  
      {% when "image" %}
      
    {% assign imageUrl = null %}
    {% for label in labels %}
    {% if label == value %}
      {% assign imageUrl = imageUrls[forloop.index0] %}
    {% endif %}
    {% endfor %}
    
    <div
      id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}" option-data="{{option.name | escape}}" option-value="{{value | escape}}" option-position="{{option.position}}" data-gp-option-available="{{value.available}}" option-type="image" data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}" data-hidden="true"
      class="gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed
  data-[hidden='false']:gp-flex {{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
      style="{% if selectedValue == value %}--ff: var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight: 400;--size: 14px;--size-tablet: 14px;--size-mobile: 12px;--lh: 180%;--lh-tablet: 180%;--lh-mobile: 180%;--c: var(--g-c-text-2, text-2);--tt: uppercase;--d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: none;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--focus-bblr: 0px;--focus-bbrr: 0px;--focus-btlr: 0px;--focus-btrr: 0px;-undefined-bblr: 0px;-undefined-bbrr: 0px;-undefined-btlr: 0px;-undefined-btrr: 0px;{% else %}--ff: var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight: 400;--size: 14px;--size-tablet: 14px;--size-mobile: 12px;--lh: 180%;--lh-tablet: 180%;--lh-mobile: 180%;--tt: uppercase;--d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--focus-bblr: 0px;--focus-bbrr: 0px;--focus-btlr: 0px;--focus-btrr: 0px;-undefined-bblr: 0px;-undefined-bbrr: 0px;-undefined-btlr: 0px;-undefined-btrr: 0px;{% endif %}">
      {% if imageUrl != null and imageUrl != "" %}
      <img class="gp-object-cover gp-rounded-none" style="width: 64px; height: 64px" src="{{imageUrl}}" alt="" />
    {% else %}
      <div
        class="option-value gp-overflow-hidden gp-relative gp-flex gp-flex-col gp-justify-center gp-items-center gp-gap-[6px"
        style=" --pl: 16px;--pr: 16px;--pl-tablet: 16px;--pr-tablet: 16px;--pl-mobile: 16px;--pr-mobile: 16px;--pt: 0px;--pb: 0px;">
        <svg width="15" height="12" viewBox="0 0 15 12" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="2" cy="2" r="2" fill="#D6D6D6"></circle><path d="M0.196854 10.6453L2.40968 8.05946C2.4846 7.97175 2.57719 7.9008 2.68141 7.85124C2.78562 7.80168 2.89913 7.77461 3.01452 7.77181C3.12991 7.76902 3.2446 7.79055 3.3511 7.835C3.45761 7.87945 3.55353 7.94583 3.63262 8.02981L4.66733 9.12714L8.71205 4.29464C8.79123 4.19995 8.89077 4.1243 9.00325 4.07333C9.11573 4.02236 9.23827 3.99737 9.36176 4.00022C9.48524 4.00307 9.6065 4.03369 9.71651 4.08979C9.82651 4.1459 9.92245 4.22605 9.99717 4.32429L14.8329 10.6772C14.9254 10.7989 14.982 10.9441 14.9964 11.0962C15.0107 11.2484 14.9823 11.4016 14.9144 11.5385C14.8464 11.6754 14.7415 11.7907 14.6115 11.8713C14.4815 11.952 14.3316 11.9948 14.1786 11.995L0.822048 12C0.664945 11.9999 0.511148 11.9549 0.378853 11.8703C0.246557 11.7857 0.141299 11.6649 0.0755311 11.5224C0.00976323 11.3799 -0.013762 11.2216 0.00773837 11.0661C0.0292388 10.9107 0.0948652 10.7646 0.196854 10.6453Z" fill="#D6D6D6"></path></svg>
        <span class="gp-text-center  gp-overflow-hidden gp-text-ellipsis gp-px-1" style="--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:14px;--size-tablet:14px;--size-mobile:12px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--tt:uppercase">{{value}}</span>
      </div>
    {% endif %}
    </div>
  
      {% else %}
    {% endcase %}
    
      </div>
    </label>{% endfor %}
        {% if type == 'dropdown' %}
    <select
      aria-label="{{option.name | escape}}" autocomplete="off" id="p-variant-dropdown-{{option.position}}" name="{%- if option -%}{{option.name | escape}}{% else %}Select Option{%- endif -%}" option-data="{{option.name}}" option-type="{{optionType}}" option-renderer="{{optionRendered}}"
      class="dropdown-option-item gp-truncate gp-bg-auto gp-pl-4 gp-pr-6 gp-outline-none gp-shadow-none gp-border-g-line-2 hover:gp-border-g-line-3 active:gp-text-g-text-2 hover:gp-text-g-text-2 gp-text-g-text-2 active:gp-bg-g-bg-3 hover:gp-bg-g-bg-3 gp-bg-g-bg-3"
      style="--w:true;--w-tablet:true;--w-mobile:true;--h:[object Object];--shadow:none;--hvr-shadow:none;--bg:var(--g-c-bg-3, bg-3);--bs:solid;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-2, line-2);--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:14px;--size-tablet:14px;--size-mobile:12px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--c:var(--g-c-text-2, text-2);--tt:uppercase;--hvr-c:var(--g-c-text-2, text-2);--hvr-bg:var(--g-c-bg-3, bg-3);--hvr-bs:solid;--hvr-bw:1px 1px 1px 1px;--hvr-bc:var(--g-c-line-3, line-3);--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--hvr-bblr:0px;--hvr-bbrr:0px;--hvr-btlr:0px;--hvr-btrr:0px;--focus-bblr:0px;--focus-bbrr:0px;--focus-btlr:0px;--focus-btrr:0px;-undefined-bblr:0px;-undefined-bbrr:0px;-undefined-btlr:0px;-undefined-btrr:0px;appearance:none;background-image:url(https://cdn.shopify.com/s/files/1/1827/4239/t/1/assets/ico-select.svg?v=155563818344741998551488860031);background-repeat:no-repeat;background-position:right 16px center"
    >
      
      {% for value in values %}{%- liquid
      assign option_disabled = true
      for variantItem in product.variants
        case option.position
          when 1
            if variantItem.available and variantItem.option1 == value
              assign option_disabled = false
            endif
          when 2
            if variantItem.available and variantItem.option2 == value
              assign option_disabled = false
            endif
          when 3
            if variantItem.available and variantItem.option3 == value
              assign option_disabled = false
            endif
        endcase
      endfor
    -%}
    {% if option_disabled == true %}{% if value == selectedValue %}<option disabled selected option-position="{{option.position}}"
  option-data="{{option.name | escape}}"
  option-value="{{value | escape}}"
  data-gp-option-value-id="{{value.id}}"
  data-gp-option-available="{{value.available}}"
  value="{{value | escape}}"
  class="option-value-wrapper"
  key="{{value | escape}}"
>{{value}}</option>{% else %}<option disabled 
  option-data="{{option.name | escape}}"
  option-value="{{value | escape}}"
  data-gp-option-value-id="{{value.id}}"
  data-gp-option-available="{{value.available}}"
  value="{{value | escape}}"
  class="option-value-wrapper"
  key="{{value | escape}}"
>{{value}}</option>{% endif %}{% else %}{% if value == selectedValue %}<option selected option-position="{{option.position}}" 
  option-data="{{option.name | escape}}"
  option-value="{{value | escape}}"
  data-gp-option-value-id="{{value.id}}"
  data-gp-option-available="{{value.available}}"
  value="{{value | escape}}"
  class="option-value-wrapper"
  key="{{value | escape}}"
>{{value}}</option>{% else %}<option 
  option-data="{{option.name | escape}}"
  option-value="{{value | escape}}"
  data-gp-option-value-id="{{value.id}}"
  data-gp-option-available="{{value.available}}"
  value="{{value | escape}}"
  class="option-value-wrapper"
  key="{{value | escape}}"
 option-position="{{option.position}}">{{value}}</option>{% endif %}{% endif %}{% endfor %}
    </select>{% endif %}
      {% endif %}
    {% endfor %}

    {% if optionRendered == false %}{% for value in values %}
    {%- liquid
      assign option_disabled = true
      for variantItem in product.variants
        case option.position
          when 1
            if variantItem.available and variantItem.option1 == value
              assign option_disabled = false
            endif
          when 2
            if variantItem.available and variantItem.option2 == value
              assign option_disabled = false
            endif
          when 3
            if variantItem.available and variantItem.option3 == value
              assign option_disabled = false
            endif
        endcase
      endfor
    -%}
    
    <label
      id="{{option.name | escape}}-{{value | escape}}"
      for="{{product.id}}-g-jQyH10E2-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
      class="option-item gp-group gp-relative gp-child-item-g-jQyH10E2"
      
    >
      <div class="gp-invisible !gp-max-w-[150px] gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-absolute gp-bottom-[calc(100%+20px)] gp-text-[12px] group-hover:gp-visible gp-w-max gp-bg-[#333333] gp-text-[#F9F9F9] gp-px-[8px] gp-py-[4px] gp-rounded-[8px] gp-mb-[-10px] after:gp-content-[''] after:gp-absolute after:gp-p-[7px] after:gp-left-0 after:gp-w-full after:gp-bottom-[-10px]">
        <p class="gp-text-[#F9F9F9]">{{value | escape}}</p>
        <svg class="gp-absolute gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-z-10 gp-bottom-[-4px]" width="8" height="4" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M8 10L0 0L16 1.41326e-06L8 10Z" fill="#333333"/>
        </svg>
      </div>
      <input
        checked="{%- if option.selected_value == value -%}true{% else %}false{%- endif -%}"
        name="{{product.id}}-options-{{option.name | escape}}"
        id="{{product.id}}-g-jQyH10E2-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
        value="{{value | escape}}"
        type="radio"
        class="gp-sr-only gp-absolute gp-bottom-0 gp-right-0"
      />
      <span class="gp-sr-only">{{value | escape}}</span>
      <div
        option-name="{{option.name | escape}}" option-name-value="{{value | escape}}" data-gp-option-value-id="{{value.id}}"
        class="option-item-inner gp-w-auto gp-h-auto {% if option_disabled == true %}gp-opacity-20{% endif %}">
        
    
    <div
      id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}" option-data="{{option.name | escape}}" option-value="{{value | escape}}" option-position="{{option.position}}" data-gp-option-available="{{value.available}}" option-type="optionType" data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}" data-hidden="true"
      class="gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed
  data-[hidden='false']:gp-flex {{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
      style="{% if selectedValue == value %}--ff: var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight: 400;--size: 14px;--size-tablet: 14px;--size-mobile: 12px;--lh: 180%;--lh-tablet: 180%;--lh-mobile: 180%;--c: var(--g-c-text-2, text-2);--tt: uppercase;--d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: none;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--focus-bblr: 0px;--focus-bbrr: 0px;--focus-btlr: 0px;--focus-btrr: 0px;-undefined-bblr: 0px;-undefined-bbrr: 0px;-undefined-btlr: 0px;-undefined-btrr: 0px;{% else %}--ff: var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight: 400;--size: 14px;--size-tablet: 14px;--size-mobile: 12px;--lh: 180%;--lh-tablet: 180%;--lh-mobile: 180%;--tt: uppercase;--d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--focus-bblr: 0px;--focus-bbrr: 0px;--focus-btlr: 0px;--focus-btrr: 0px;-undefined-bblr: 0px;-undefined-bbrr: 0px;-undefined-btlr: 0px;-undefined-btrr: 0px;{% endif %}">
      
      <div
        class="option-value gp-overflow-hidden gp-relative"
        style=" --pl: 16px;--pr: 16px;--pl-tablet: 16px;--pr-tablet: 16px;--pl-mobile: 16px;--pr-mobile: 16px;--pt: 0px;--pb: 0px;">
         
        <span class="gp-text-center" style="--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:14px;--size-tablet:14px;--size-mobile:12px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--tt:uppercase">{{value}}</span>
      </div>
    
    </div>
    
      </div>
    </label>{% endfor %}{% endif %}
    
          </div>
      </div>{% endfor %}
    
        </div>{% endif %}
    </gp-product-variants>

    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-product-variant-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"geGZZZvehj"}' data-id="geGZZZvehj" id="geGZZZvehj" data-same-height-subgrid-container class="geGZZZvehj gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--op-mobile:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:32px;--mb-mobile:16px;--cg:0px;--cg-mobile:7px;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:center"
      class="ghWaTsFVKC gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gI57JuqnZk"}' data-id="gI57JuqnZk" id="gI57JuqnZk" data-same-height-subgrid-container class="gI57JuqnZk gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:none;--d-mobile:grid;--d-tablet:none;--op:100%;--op-mobile:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--cg:8px;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:120px;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="g5q0pckB7j gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:solid;--bw:1px 1px 1px 1px;--bc:#B4B4B4;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--op-mobile:100%;--bblr:2px;--bbrr:2px;--btlr:2px;--btrr:2px" class="g8IhTLngRO ">
      
    {%- liquid
      assign current_variant = product.selected_or_first_available_variant
      assign available = current_variant.available | default: false
    -%}
      <gp-product-quantity
        data-id="g8IhTLngRO" data-disabled="{%- if available -%} false {%- else -%} true {%- endif -%}"
        data-price="false"
        class="quantityClass gp-relative gp-inline-flex gp-h-12 gp-w-full gp-bg-transparent gp-transition-all gp-duration-150 data-[disabled=true]:gp-opacity-60 "
        style="--h:45px;--h-mobile:42px;--jc:left"
      >
        <div class="gp-inline-flex gp-w-full gp-overflow-hidden" style="--maxw:140px;--maxw-mobile:[object Object];--b:none;--bc:#7D7D7D;--bw:1px 1px 1px 1px;--shadow:none">
          
              <button
              {% if available == false %} disabled {% endif %}
               title="Decrement" aria-label="decrement" type="button"
                class="gp-flex gp-aspect-square gp-h-full disabled:gp-cursor-not-allowed gp-cursor-pointer gp-items-center gp-justify-center gp-outline-none gp-minus  gp-bg-g-bg-3"
                style="--w:45px;--w-mobile:45px;--hvr-bg:#ffffff;--bg:var(--g-c-bg-3, bg-3);--c:#575757"
              >
                <span class="gp-inline-flex gp-items-center gp-justify-center [&>*]:gp-h-full [&>*]:gp-w-full" style="--w:20px;--w-tablet:20px;--w-mobile:12px"
                >
                  <svg
      xmlns="http://www.w3.org/2000/svg"
      className="gp-h-full gp-w-full"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      >
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6" />
      </svg>
                </span>
              </button>
            
          <input
            {% if available == false %} disabled {% endif %}
            type="text" name="quantity" step="1" min="1" aria-label="quantity" inputMode="numeric" quantity="current_variant.quantity" autocomplete="off" value="1"
            class="gp-flex gp-w-full gp-px-1 gp-min-w-[45px] gp-shrink-[99999] gp-appearance-none gp-items-center gp-text-center gp-outline-none focus-visible:gp-shadow-none focus-visible:gp-outline-none gp-transition-all gp-duration-150 hover:gp-text-black disabled:gp-cursor-not-allowed gp-min-h-0 gp-rounded-none gp-bg-g-bg-3 "
            style="--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;--bg:var(--g-c-bg-3, bg-3);--hvr-c:#242424;--brw:1px;--hvr-brw:1px;--blw:1px;--hvr-blw:1px;--brc:transparent;--blc:transparent"
            inputMode="numeric"
          />

          
            <button
              {% if available == false %} disabled {% endif %}
              title="Increment" aria-label="increment" type="button"
              class="gp-flex gp-aspect-square gp-h-full disabled:gp-cursor-not-allowed gp-cursor-pointer gp-items-center gp-justify-center gp-outline-none gp-plus  gp-bg-g-bg-3"
              style="--w:45px;--w-mobile:45px;--hvr-bg:#ffffff;--bg:var(--g-c-bg-3, bg-3);--c:#575757"
            >
              <span class="gp-inline-flex gp-items-center gp-justify-center [&>*]:gp-h-full [&>*]:gp-w-full" style="--w:20px;--w-tablet:20px;--w-mobile:12px">
                <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"
      />
    </svg>
              </span>
            </button>
          
          </div>
        </gp-product-quantity>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-product-quantity-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
      </div>
    </div>

      
    </gp-row>
  
  
    </div><div
      data-same-height-display-contents
      style="--jc:center"
      class="gK3JmpdHW0 gp-relative gp-flex gp-flex-col"
    >
      
    {%- liquid
      assign inventory_quantity = variant.inventory_quantity | default: 0
      assign is_in_stock = variant.available
    -%}
    <gp-product-button
      
      gp-data-wrapper="true"
      gp-href="{{ request.origin }}{{ routes.root_url | split: '/' | join: '/' }}/cart"
      class="gp-product-button"
      gp-label-out-of-stock="{{ section.settings.gg3BeoVNY2T_outOfStockLabel }}"
      gp-label-unavailable="{{ section.settings.gg3BeoVNY2T_unavailableLabel }}"
      gp-data='{"styles":{"errorTypo":{"attrs":{"color":"error"},"type":"paragraph-2"},"successTypo":{"attrs":{"color":"success"},"type":"paragraph-2"}},"setting":{"errorType":"built-in","actionEffect":"open-cart-drawer","enableMessage":true,"label":"Add to cart","errorMessage":"Cannot add product to cart","successMessage":"Add product to cart successfully","outOfStockLabel":"Out of stock","customURL":{"link":"/cart","target":"_self"}},"variantID":"{{variant.id}}","disabled":"{{variant.available}}","totalVariant":"{{total_combinations}}"}'
      data-variant-selection-required-message=""
    >
      
    <gp-button
      gp-data='{}'
      
      class="gp-flex gp-flex-col"
    >
      <div style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:0px;--ta:left" class="{% unless variant.available %} !gp-hidden {% endunless %}">
        <style>[data-id="g3BeoVNY2T"].gp-button-base::before, [data-id="g3BeoVNY2T-interaction"].gp-button-base::before {
      
    content: "";
    height: 100%;
    width: 100%;
    position: absolute;
    pointer-events: none;
    top: 0;
    left: 0;
    border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
    
      border-bottom-left-radius: 999px;
      border-bottom-right-radius: 999px;
      border-top-left-radius: 999px;
      border-top-right-radius: 999px;
      
  
    }
  
      
  [data-id="g3BeoVNY2T"]:hover::before, [data-id="g3BeoVNY2T-interaction"]:hover::before {
    border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
    
  }</style>
        
        <button
          data-id="g3BeoVNY2T" dataId="g3BeoVNY2T" data-state="idle" aria-label="Add to cart"
          name="add" gp-data-hidden="{% unless variant.available %}true{% endunless %}"
          type="submit"
          style="--w:1000px;--w-tablet:1000px;--w-mobile:1000px;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:14px;--pb:14px;--pl-mobile:12px;--pr-mobile:12px;--pt-mobile:12px;--pb-mobile:12px;--bblr:999px;--bbrr:999px;--btlr:999px;--btrr:999px;--hvr-bblr:999px;--hvr-bbrr:999px;--hvr-btlr:999px;--hvr-btrr:999px;--shadow:none;--bgi:;--hvr-bgi:;--hvr-bg:#0074E8;--bg:#0171E3;--c:var(--g-c-text-3, text-3);--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:700;--ls:normal;--size:18px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase"
          class="gp-button-atc tcustomizer-submit-button g3BeoVNY2T gp-button-base gp-group gp-relative gp-inline-flex gp-max-w-full gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-200 disabled:gp-btn-disabled disabled:gp-opacity-30 gp-text-g-text-3  undefined"
        >
        
  <svg
    class="gp-invisible gp-absolute gp-h-5 gp-w-5 group-data-[state=loading]:gp-animate-spin group-data-[state=loading]:gp-visible"
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle
      class="gp-opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      stroke-width="4"
    ></circle>
    <path
      class="gp-opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    ></path>
  </svg>

        <div class="gp-inline-flex">
          
          
    <span
      data-gp-text
      style="--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:700;--ls:normal;--size:18px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;--ts:none;word-break:break-word"
      class="gp-content-product-button group-active/button:!gp-text-inherit gp-relative gp-flex gp-h-full gp-items-center gp-overflow-hidden gp-break-words group-data-[state=loading]:gp-invisible [&_p]:gp-whitespace-pre-line gp-text button-text"
    >
      {{ section.settings.gg3BeoVNY2T_label }}
    </span>
        </div>
        
    
    
  
        </button>
      </div>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-button-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    </gp-button>
  
      
    <gp-button
      gp-data='{}'
      
      class="gp-flex gp-flex-col"
    >
      <div style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:0px;--ta:left" class="{% if variant.available %} !gp-hidden {% endif %}">
        <style>[data-id="g3BeoVNY2T-sold-out"].gp-button-base::before, [data-id="g3BeoVNY2T-sold-out-interaction"].gp-button-base::before {
      
    content: "";
    height: 100%;
    width: 100%;
    position: absolute;
    pointer-events: none;
    top: 0;
    left: 0;
    border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
    
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      
  
    }
  
      
  [data-id="g3BeoVNY2T-sold-out"]:hover::before, [data-id="g3BeoVNY2T-sold-out-interaction"]:hover::before {
    
    
  }</style>
        
        <button
          data-id="g3BeoVNY2T-sold-out" dataId="g3BeoVNY2T-sold-out" data-state="idle" aria-label="{{ section.settings.gg3BeoVNY2T_outOfStockLabel }}"
          gp-data-hidden="{% if variant.available %}true{% endif %}"
          type="button"
          style="--w:1000px;--w-tablet:1000px;--w-mobile:1000px;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:14px;--pb:14px;--pl-mobile:12px;--pr-mobile:12px;--pt-mobile:12px;--pb-mobile:12px;--bblr:4px;--bbrr:4px;--btlr:4px;--btrr:4px;--hvr-bblr:4px;--hvr-bbrr:4px;--hvr-btlr:4px;--hvr-btrr:4px;--shadow:none;--bgi:;--hvr-bgi:;--bg:#242424;--hvr-bg:#242424;--c:var(--g-c-text-3, text-3);--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:700;--ls:normal;--size:18px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase"
          class="gp-cursor-default gp-opacity-30 btn-disabled gp-button-sold-out g3BeoVNY2T-sold-out gp-button-base gp-group gp-relative gp-inline-flex gp-max-w-full gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-200 disabled:gp-btn-disabled disabled:gp-opacity-30 "
        >
        
        <div class="gp-inline-flex">
          
          
    <span
      data-gp-text
      style="--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:700;--ls:normal;--size:18px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;--ts:none;word-break:break-word"
      class="gp-content-product-button group-active/button:!gp-text-inherit gp-relative gp-flex gp-h-full gp-items-center gp-overflow-hidden gp-break-words group-data-[state=loading]:gp-invisible [&_p]:gp-whitespace-pre-line gp-text button-text"
    >
      {{ section.settings.gg3BeoVNY2T_outOfStockLabel }}
    </span>
        </div>
        
    
    
  
        </button>
      </div>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-button-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    </gp-button>
  
    </gp-product-button>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-product-button-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>

      
    </gp-row>
  
  <div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="guhQK-nPW6 ">
      
    <gp-accordion data-id="guhQK-nPW6" class="gp-flex gp-flex-col gp-w-full" style="--gg:0px;border-radius:inherit"
      gp-data='{"uid":"guhQK-nPW6","setting":{"iconSvg":"<svg width=\"17\" height=\"16\" viewBox=\"0 0 17 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M5.64645 2.64645C5.84171 2.45118 6.15829 2.45118 6.35355 2.64645L11.3536 7.64645C11.5488 7.84171 11.5488 8.15829 11.3536 8.35355L6.35355 13.3536C6.15829 13.5488 5.84171 13.5488 5.64645 13.3536C5.45118 13.1583 5.45118 12.8417 5.64645 12.6464L10.2929 8L5.64645 3.35355C5.45118 3.15829 5.45118 2.84171 5.64645 2.64645Z\" fill=\"currentColor\"/>\n                  </svg>","isIconPlus":false,"activeKey":"1","expanded":false,"expandItem":false,"iconPosition":"right","iconGlobalSize":{"desktop":{"gap":"16px"}},"iconCollapse":"<svg height=\"20\" width=\"20\" xmlns=\"http://www.w3.org/2000/svg\"  viewBox=\"0 0 256 256\" fill=\"currentColor\" data-id=\"508817587901825384\">\n              <path fill=\"currentColor\" strokeLinecap=\"round\" strokeLinejoin=\"round\" fill=\"currentColor\" d=\"M228,128a12,12,0,0,1-12,12H140v76a12,12,0,0,1-24,0V140H40a12,12,0,0,1,0-24h76V40a12,12,0,0,1,24,0v76h76A12,12,0,0,1,228,128Z\" /></svg>","iconExpand":"<svg height=\"20\" width=\"20\" xmlns=\"http://www.w3.org/2000/svg\"  viewBox=\"0 0 256 256\" fill=\"currentColor\" data-id=\"508817584339812712\">\n              <path fill=\"currentColor\" strokeLinecap=\"round\" strokeLinejoin=\"round\" fill=\"currentColor\" d=\"M228,128a12,12,0,0,1-12,12H40a12,12,0,0,1,0-24H216A12,12,0,0,1,228,128Z\" /></svg>","iconCollapseSize":12,"layoutHeader":"text-only","expandedMode":"single","configIconSize":16,"parentUid":"guhQK-nPW6","childListNumber":[],"chidlrenUid":["gV7X2rGWs7","glHDB3DrG8"]},"styles":{"bgColor":{"active":"transparent","hover":"transparent","normal":"transparent"},"color":{"active":"#575757","hover":"#575757","normal":"#575757"},"headerBorder":{"active":{"border":"solid","borderWidth":"Mixed","color":"#EEEEEE","isCustom":true,"position":"bottom","width":"0px 0px 1px 0px"},"hover":{"border":"solid","borderWidth":"Mixed","color":"#EEEEEE","isCustom":true,"position":"bottom","width":"0px 0px 1px 0px"},"normal":{"border":"solid","borderWidth":"Mixed","color":"#EEEEEE","isCustom":true,"position":"bottom","width":"0px 0px 1px 0px"}},"textColor":{"active":"#242424","hover":"#242424","normal":"#242424"},"iconColor":{"active":"#121212","hover":"#121212","normal":"#121212"},"contentSizePadding":{"desktop":{"gap":"0px","padding":{"bottom":"12px","left":"0px","right":"0px","top":"12px","type":"custom"}}},"headerContentPadding":{"desktop":{"gap":"","padding":{"bottom":"24px","left":"0px","right":"0px","top":"24px","type":"custom"}},"mobile":{"customShapeValue":"","gap":"","height":"","padding":{"bottom":"12px","left":"0px","right":"0px","top":"12px","type":"custom"},"shape":"","shapeValue":"","width":""},"tablet":{"customShapeValue":"","gap":"","height":"","padding":{"bottom":"16px","left":"0px","right":"0px","top":"16px","type":"custom"},"shape":"","shapeValue":"","width":""}},"widthHeightSize":{"desktop":{"height":"auto","width":"100%"}}}}'
        >
        <div class=" gp-hidden gp-rotate-90 -gp-rotate-90 gp-rotate-180"></div>
      
          <div class="gp-flex" style="--jc:center">
            
  <div class="gp-overflow-clip gp-group" style="--text-hover-color:#242424;--icon-expand-hover-color:#575757;--icon-expand-active-color:#575757;--icon-active-color:#121212;--icon-hover-color:#121212;--w:100%;--w-tablet:100%;--w-mobile:100%;--bs:none;--bw:1px 1px 1px 1px;--bc:#000000;--bg:transparent;--shadow:none">
    <div data-index="0" class="guhQK-nPW6 gp-overflow-hidden gp-child-item-guhQK-nPW6 gp-accordion-item">
      <div aria-hidden uid="gV7X2rGWs7" id="gV7X2rGWs7" data-index="0"
        class="gp-flex gp-accordion-item_header gp-cursor-pointer gp-items-center gp-gap-[16px] guhQK-nPW6 gp-accordion-item-guhQK-nPW6-0" style="--pl:0px;--pr:0px;--pt:24px;--pb:24px;--pl-tablet:0px;--pr-tablet:0px;--pt-tablet:16px;--pb-tablet:16px;--pl-mobile:0px;--pr-mobile:0px;--pt-mobile:12px;--pb-mobile:12px;--h:auto;--h-tablet:auto;--h-mobile:auto;--fd:row-reverse;--jc:space-between;--hvr-bgc:transparent;--bgc:transparent;--bs:solid;--hvr-bs:solid;--bw:0px 0px 1px 0px;--hvr-bw:0px 0px 1px 0px;--bc:#EEEEEE;--hvr-bc:#EEEEEE">
        
          <div class="gp-inline-flex">
            <span style="--c:#575757;width:12px;height:12px"
            data-index="0"
            class="group-hover:[color:var(--icon-expand-hover-color)] gp-collapsible-icon gp-accordion-item_icon gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-full">
              <svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817587901825384">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M228,128a12,12,0,0,1-12,12H140v76a12,12,0,0,1-24,0V140H40a12,12,0,0,1,0-24h76V40a12,12,0,0,1,24,0v76h76A12,12,0,0,1,228,128Z" /></svg>
            </span>
          </div>
          
        <div class="gp-flex gp-w-full gp-items-center gp-break-all" style="--gg:16px;--jc:left">
          
          
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id
        
        style="--c:#242424;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425955370"
      >
      <div  >
        <div
          data-gp-text
          class="gp-overflow-hidden gp-accordion-text-content group-hover:[color:var(--text-hover-color)!important]  gp-text-instant"
          style="--w:100%;--tdt:auto;--ts:none;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:600;--ls:normal;--size:16px;--size-tablet:14px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.gguhQK-nPW6_childItem_0 }}
        </div>
      </div>
    </div>
    
          
        </div>
      </div>
      <div data-index="0" data-show="false" class="gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden gp-grid-rows-[0fr]" style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)">
        <div data-index="0" class="gp-accordion-item_body-inner gp-transition-all gp-duration-500 gp-min-h-0" style="--pl:0px;--pr:0px">
          <div
      data-same-height-display-contents
      
      class="gJzINuLqYp gp-relative gp-flex gp-flex-col"
    >
      
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gtYWeYhAq0"
        class="gtYWeYhAq0"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425955373"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:14px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#000000;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggtYWeYhAq0_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
      <style id="custom-css-gtYWeYhAq0">
        .gtYWeYhAq0 {

}
.gtYWeYhAq0 p {

}
      </style>
    
    </div>
        </div>
      </div>
    </div>
  </div>
  
          </div>
            
          <div class="gp-flex" style="--jc:center">
            
  <div class="gp-overflow-clip gp-group" style="--text-hover-color:#242424;--icon-expand-hover-color:#575757;--icon-expand-active-color:#575757;--icon-active-color:#121212;--icon-hover-color:#121212;--w:100%;--w-tablet:100%;--w-mobile:100%;--bs:none;--bw:1px 1px 1px 1px;--bc:#000000;--bg:transparent;--shadow:none">
    <div data-index="1" class="guhQK-nPW6 gp-overflow-hidden gp-child-item-guhQK-nPW6 gp-accordion-item">
      <div aria-hidden uid="glHDB3DrG8" id="glHDB3DrG8" data-index="1"
        class="gp-flex gp-accordion-item_header gp-cursor-pointer gp-items-center gp-gap-[16px] guhQK-nPW6 gp-accordion-item-guhQK-nPW6-1" style="--pl:0px;--pr:0px;--pt:24px;--pb:24px;--pl-tablet:0px;--pr-tablet:0px;--pt-tablet:16px;--pb-tablet:16px;--pl-mobile:0px;--pr-mobile:0px;--pt-mobile:12px;--pb-mobile:12px;--h:auto;--h-tablet:auto;--h-mobile:auto;--fd:row-reverse;--jc:space-between;--hvr-bgc:transparent;--bgc:transparent;--bs:solid;--hvr-bs:solid;--bw:0px 0px 1px 0px;--hvr-bw:0px 0px 1px 0px;--bc:#EEEEEE;--hvr-bc:#EEEEEE">
        
          <div class="gp-inline-flex">
            <span style="--c:#575757;width:12px;height:12px"
            data-index="1"
            class="group-hover:[color:var(--icon-expand-hover-color)] gp-collapsible-icon gp-accordion-item_icon gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-full">
              <svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817587901825384">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M228,128a12,12,0,0,1-12,12H140v76a12,12,0,0,1-24,0V140H40a12,12,0,0,1,0-24h76V40a12,12,0,0,1,24,0v76h76A12,12,0,0,1,228,128Z" /></svg>
            </span>
          </div>
          
        <div class="gp-flex gp-w-full gp-items-center gp-break-all" style="--gg:16px;--jc:left">
          
          
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id
        
        style="--c:#242424;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425955375"
      >
      <div  >
        <div
          data-gp-text
          class="gp-overflow-hidden gp-accordion-text-content group-hover:[color:var(--text-hover-color)!important]  gp-text-instant"
          style="--w:100%;--tdt:auto;--ts:none;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:600;--ls:normal;--size:16px;--size-tablet:14px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.gguhQK-nPW6_childItem_1 }}
        </div>
      </div>
    </div>
    
          
        </div>
      </div>
      <div data-index="1" data-show="false" class="gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden gp-grid-rows-[0fr]" style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)">
        <div data-index="1" class="gp-accordion-item_body-inner gp-transition-all gp-duration-500 gp-min-h-0" style="--pl:0px;--pr:0px">
          <div
      data-same-height-display-contents
      
      class="gNkOp03_-e gp-relative gp-flex gp-flex-col"
    >
      
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gtAAny52mR"
        class="gtAAny52mR"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425955378"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:70%;--w-tablet:70%;--w-mobile:70%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--ls:normal;--size:18px;--size-tablet:14px;--size-mobile:14px;--lh:150%;--lh-tablet:130%;--lh-mobile:130%;--c:#000000;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggtAAny52mR_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
      <style id="custom-css-gtAAny52mR">
        .gtAAny52mR {

}
.gtAAny52mR p {

}
      </style>
    
    </div>
        </div>
      </div>
    </div>
  </div>
  
          </div>
            
    </gp-accordion>
    
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-accordion-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    
  
      </div>
    </div>

      
    </gp-row>
  
  
    </div>

      
    </gp-row>
  
  
          {%- endform -%}
        </product-form>
      </gp-product>
      {%- assign product = gpBkProduct -%}
    {% else %}
      <div class="gp-text-center">{{ 'gempages.Product.product_not_found' | t }}</div>
    {% endif %}
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-product-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  