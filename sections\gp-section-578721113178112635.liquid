
    
    <style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-578721113178112635.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-578721113178112635.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-578721113178112635.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-578721113178112635.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-578721113178112635.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-578721113178112635.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-578721113178112635.gps.gpsil [style*="--hvr-bgi:"]:hover{background-image:var(--hvr-bgi)}.gps-578721113178112635.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-578721113178112635.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-578721113178112635.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-578721113178112635.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-578721113178112635.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-578721113178112635.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-578721113178112635.gps.gpsil [style*="--hvr-bb:"]:hover{border-bottom:var(--hvr-bb)}.gps-578721113178112635.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-578721113178112635.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-578721113178112635.gps.gpsil [style*="--hvr-bblr:"]:hover{border-bottom-left-radius:var(--hvr-bblr)}.gps-578721113178112635.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-578721113178112635.gps.gpsil [style*="--hvr-bbrr:"]:hover{border-bottom-right-radius:var(--hvr-bbrr)}.gps-578721113178112635.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-578721113178112635.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-578721113178112635.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-578721113178112635.gps.gpsil [style*="--hvr-bt:"]:hover{border-top:var(--hvr-bt)}.gps-578721113178112635.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-578721113178112635.gps.gpsil [style*="--hvr-btlr:"]:hover{border-top-left-radius:var(--hvr-btlr)}.gps-578721113178112635.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-578721113178112635.gps.gpsil [style*="--hvr-btrr:"]:hover{border-top-right-radius:var(--hvr-btrr)}.gps-578721113178112635.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-578721113178112635.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-578721113178112635.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-578721113178112635.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-578721113178112635.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-578721113178112635.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-578721113178112635.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-578721113178112635.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-578721113178112635.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-578721113178112635.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-578721113178112635.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-578721113178112635.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-578721113178112635.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-578721113178112635.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-578721113178112635.gps.gpsil [style*="--tdt:"]{text-decoration-thickness:var(--tdt)}.gps-578721113178112635.gps.gpsil [style*="--tdl:"]{text-decoration-line:var(--tdl)}.gps-578721113178112635.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-578721113178112635.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-578721113178112635.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-578721113178112635.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-578721113178112635.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-578721113178112635.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-578721113178112635.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-578721113178112635.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-578721113178112635.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-578721113178112635.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-578721113178112635.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-578721113178112635.gps.gpsil [style*="--pos:"]{position:var(--pos)}.gps-578721113178112635.gps.gpsil [style*="--rg:"]{row-gap:var(--rg)}.gps-578721113178112635.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-578721113178112635.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-578721113178112635.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-578721113178112635.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-578721113178112635.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-578721113178112635.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-578721113178112635.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-578721113178112635.gps.gpsil [style*="--ai-tablet:"]{align-items:var(--ai-tablet)}.gps-578721113178112635.gps.gpsil [style*="--bgi-tablet:"]{background-image:var(--bgi-tablet)}.gps-578721113178112635.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-578721113178112635.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-578721113178112635.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-578721113178112635.gps.gpsil [style*="--op-tablet:"]{opacity:var(--op-tablet)}.gps-578721113178112635.gps.gpsil [style*="--pc-tablet:"]{place-content:var(--pc-tablet)}.gps-578721113178112635.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-578721113178112635.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-578721113178112635.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-578721113178112635.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-578721113178112635.gps.gpsil [style*="--pos-tablet:"]{position:var(--pos-tablet)}.gps-578721113178112635.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-578721113178112635.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-578721113178112635.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-578721113178112635.gps.gpsil [style*="--ai-mobile:"]{align-items:var(--ai-mobile)}.gps-578721113178112635.gps.gpsil [style*="--bgi-mobile:"]{background-image:var(--bgi-mobile)}.gps-578721113178112635.gps.gpsil [style*="--bgp-mobile:"]{background-position:var(--bgp-mobile)}.gps-578721113178112635.gps.gpsil [style*="--bgr-mobile:"]{background-repeat:var(--bgr-mobile)}.gps-578721113178112635.gps.gpsil [style*="--bgs-mobile:"]{background-size:var(--bgs-mobile)}.gps-578721113178112635.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-578721113178112635.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-578721113178112635.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-578721113178112635.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-578721113178112635.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-578721113178112635.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-578721113178112635.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-578721113178112635.gps.gpsil [style*="--op-mobile:"]{opacity:var(--op-mobile)}.gps-578721113178112635.gps.gpsil [style*="--pc-mobile:"]{place-content:var(--pc-mobile)}.gps-578721113178112635.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-578721113178112635.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-578721113178112635.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-578721113178112635.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-578721113178112635.gps.gpsil [style*="--pos-mobile:"]{position:var(--pos-mobile)}.gps-578721113178112635.gps.gpsil [style*="--ta-mobile:"]{text-align:var(--ta-mobile)}.gps-578721113178112635.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-578721113178112635.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-578721113178112635.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-578721113178112635 .gp-invisible{visibility:hidden}.gps-578721113178112635 .gp-absolute{position:absolute}.gps-578721113178112635 .gp-relative{position:relative}.gps-578721113178112635 .gp-inset-0{inset:0}.gps-578721113178112635 .gp-left-\[var\(--left\)\]{left:var(--left)}.gps-578721113178112635 .gp-top-0{top:0}.gps-578721113178112635 .gp-top-\[var\(--top\)\]{top:var(--top)}.gps-578721113178112635 .-gp-z-1{z-index:-1}.gps-578721113178112635 .gp-z-1{z-index:1}.gps-578721113178112635 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-578721113178112635 .gp-mb-0{margin-bottom:0}.gps-578721113178112635 .gp-flex{display:flex}.gps-578721113178112635 .gp-inline-flex{display:inline-flex}.gps-578721113178112635 .gp-grid{display:grid}.gps-578721113178112635 .\!gp-hidden{display:none!important}.gps-578721113178112635 .gp-hidden{display:none}.gps-578721113178112635 .gp-h-full{height:100%}.gps-578721113178112635 .gp-w-full{width:100%}.gps-578721113178112635 .gp-max-w-full{max-width:100%}.gps-578721113178112635 .gp-shrink-0{flex-shrink:0}.gps-578721113178112635 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-578721113178112635 .gp-flex-col{flex-direction:column}.gps-578721113178112635 .gp-flex-wrap{flex-wrap:wrap}.gps-578721113178112635 .gp-items-center{align-items:center}.gps-578721113178112635 .gp-justify-end{justify-content:flex-end}.gps-578721113178112635 .gp-justify-center{justify-content:center}.gps-578721113178112635 .\!gp-self-center{align-self:center!important}.gps-578721113178112635 .gp-self-center{align-self:center}.gps-578721113178112635 .gp-overflow-hidden{overflow:hidden}.gps-578721113178112635 .gp-break-words{overflow-wrap:break-word}.gps-578721113178112635 .gp-border{border-width:1px}.gps-578721113178112635 .gp-border-transparent{border-color:transparent}.gps-578721113178112635 .gp-text-left{text-align:left}.gps-578721113178112635 .gp-text-center{text-align:center}.gps-578721113178112635 .gp-leading-\[0\]{line-height:0}.gps-578721113178112635 .gp-no-underline{text-decoration-line:none}.gps-578721113178112635 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-578721113178112635 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-578721113178112635 .gp-duration-200{transition-duration:.2s}.gps-578721113178112635 .gp-duration-300{transition-duration:.3s}.gps-578721113178112635 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-578721113178112635 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-578721113178112635 .disabled\:gp-opacity-30:disabled{opacity:.3}.gps-578721113178112635 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-578721113178112635 .gp-group[data-state=loading] .group-data-\[state\=loading\]\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-578721113178112635 .tablet\:\!gp-relative{position:relative!important}.gps-578721113178112635 .tablet\:\!gp-hidden{display:none!important}.gps-578721113178112635 .tablet\:gp-hidden{display:none}.gps-578721113178112635 .tablet\:gp-justify-end{justify-content:flex-end}.gps-578721113178112635 .tablet\:\!gp-self-center{align-self:center!important}}@media (max-width:767px){.gps-578721113178112635 .mobile\:\!gp-relative{position:relative!important}.gps-578721113178112635 .mobile\:\!gp-hidden{display:none!important}.gps-578721113178112635 .mobile\:gp-hidden{display:none}.gps-578721113178112635 .mobile\:\!gp-h-auto{height:auto!important}.gps-578721113178112635 .mobile\:\!gp-content-stretch{align-content:stretch!important}.gps-578721113178112635 .mobile\:gp-justify-center{justify-content:center}.gps-578721113178112635 .mobile\:\!gp-self-center{align-self:center!important}.gps-578721113178112635 .mobile\:\[\&\>\*\]\:\!gp-justify-center>*{justify-content:center!important}}.gps-578721113178112635 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-578721113178112635 .\[\&\>svg\]\:\!gp-h-\[var\(--size-desktop\)\]>svg{height:var(--size-desktop)!important}.gps-578721113178112635 .\[\&\>svg\]\:\!gp-w-\[var\(--size-desktop\)\]>svg{width:var(--size-desktop)!important}.gps-578721113178112635 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}@media (max-width:1024px){.gps-578721113178112635 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}.gps-578721113178112635 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--size-tablet\)\]>svg{height:var(--size-tablet)!important}.gps-578721113178112635 .tablet\:\[\&\>svg\]\:\!gp-w-\[var\(--size-tablet\)\]>svg{width:var(--size-tablet)!important}}@media (max-width:767px){.gps-578721113178112635 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}.gps-578721113178112635 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--size-mobile\)\]>svg{height:var(--size-mobile)!important}.gps-578721113178112635 .mobile\:\[\&\>svg\]\:\!gp-w-\[var\(--size-mobile\)\]>svg{width:var(--size-mobile)!important}}.gps-578721113178112635 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-578721113178112635 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}</style>
    
    
    

    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}

    
        <section
          class="gp-mx-auto gp-max-w-full [&_*]:gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:none;--pl-tablet:none;--pl-mobile:none;--pr:none;--pr-tablet:none;--pr-mobile:none"
        >
          
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gqHW_dYLFc"}' data-id="gqHW_dYLFc" id="gqHW_dYLFc" data-same-height-subgrid-container class="gqHW_dYLFc gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--blockPadding:base;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--cg:32px;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="gjs1O219En gp-relative gp-flex gp-flex-col"
    >
      
      <gp-hero-banner
        
        data-id="gpajQ70Z-l"
        class="gpajQ70Z-l gp-group/hero gp-flex gp-w-full gp-flex-col gp-self-center gp-transition-colors gp-duration-200 gp-ease-in-out !gp-self-center tablet:!gp-self-center mobile:!gp-self-center"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:flex;--d-mobile:flex;--d-tablet:flex;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:0px;--pl:0px;--pb:0px;--pr:0px;--pt-tablet:0px;--pl-tablet:0px;--pb-tablet:0px;--pr-tablet:0px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:fit-content;--h-tablet:auto;--h-mobile:500px"
        gp-data='{"background":{"desktop":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/gempages_553400155311702965-3ae8e41f-54b7-4e8a-b348-ab8409cff6cd.png","width":1558,"height":2052,"storage":"FILE_CONTENT","backupFileKey":"gempages_553400155311702965-3ae8e41f-54b7-4e8a-b348-ab8409cff6cd.png"},"lazyLoad":false,"loop":true,"position":{"x":100,"y":50},"preload":false,"repeat":"no-repeat","size":"cover","type":"image","video":"https://www.youtube.com/watch?v=cyzh48XRS4M","videoType":"html5"},"tablet":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/gempages_553400155311702965-3ae8e41f-54b7-4e8a-b348-ab8409cff6cd.png","width":1558,"height":2052,"storage":"FILE_CONTENT","backupFileKey":"gempages_553400155311702965-3ae8e41f-54b7-4e8a-b348-ab8409cff6cd.png"},"lazyLoad":false,"loop":true,"position":{"x":100,"y":50},"preload":false,"repeat":"no-repeat","size":"cover","type":"image","video":"https://www.youtube.com/watch?v=cyzh48XRS4M","videoType":"html5"},"mobile":{"attachment":"scroll","image":{"backupFileKey":"gempages_553400155311702965-dce85f57-86c5-48d5-971a-5dbf60d81c76.jpg","height":3056,"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/gempages_553400155311702965-dce85f57-86c5-48d5-971a-5dbf60d81c76.jpg","storage":"FILE_CONTENT","width":2445},"lazyLoad":false,"loop":true,"position":{"x":50,"y":50},"preload":false,"repeat":"no-repeat","size":"cover","type":"image","video":"https://youtu.be/KOxfzBp72uk","videoType":"html5"}},"uid":"gpajQ70Z-l","enableParallax":true,"speedParallax":0.6,"hoverEffect":false,"hoverEffectScale":"100%","layout":{"desktop":{"cols":[6,6],"display":"fill","type":"left-bottom"},"mobile":{"cols":[12],"display":"fill","type":"center"}},"contentPosition1Col":{"desktop":"space-between","mobile":"center"},"contentPosition2Col":{"desktop":"bottom"},"aspectRatio":{"mobile":"auto"},"target":"_self","href":"","linkType":""}'
      >
        
  <div class="gp-relative gp-flex gp-w-full gp-flex-col gp-items-center gp-overflow-hidden hero-banner-container gp-justify-end tablet:gp-justify-end mobile:gp-justify-center" style="--h:fit-content;--h-tablet:auto;--h-mobile:500px;--bs:solid;--bw:0px 0px 0px 0px;--bc:#000000;--bgc:var(--g-c-bg-2);--shadow:none">
      <div
        aria-label="Background Image"
        role="banner"
        class="gp-hero-banner-bg gp-absolute gp-overflow-hidden gp-w-full gp-h-full top-0 left-0"
        style="clip-path:inset(0 0 0 round 0px 0px 0px 0px)"
      >
        <div class="hero-banner-bg gp-hero-banner-image-background hero-banner-bg-parallax gp_lazybg" style="--bgi:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTU1OCIgaGVpZ2h0PSIyMDUyIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xNTU4LTIwNTIiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjE1NTgiIGhlaWdodD0iMjA1MiIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTU1OCIgaGVpZ2h0PSIyMDUyIiBmaWxsPSJ1cmwoI2ctMTU1OC0yMDUyKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTU1OCIgdG89IjE1NTgiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=);--bgi-tablet:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTU1OCIgaGVpZ2h0PSIxNTU4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xNTU4LTE1NTgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjE1NTgiIGhlaWdodD0iMTU1OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTU1OCIgaGVpZ2h0PSIxNTU4IiBmaWxsPSJ1cmwoI2ctMTU1OC0xNTU4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTU1OCIgdG89IjE1NTgiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=);--bgi-mobile:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQ0NSIgaGVpZ2h0PSIzMDU2IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yNDQ1LTMwNTYiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjI0NDUiIGhlaWdodD0iMzA1NiIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjQ0NSIgaGVpZ2h0PSIzMDU2IiBmaWxsPSJ1cmwoI2ctMjQ0NS0zMDU2KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjQ0NSIgdG89IjI0NDUiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=);--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--top:;--top-tablet:;--top-mobile:;--bgc:var(--g-c-bg-2);--bgp:100% 50%;--bgp-mobile:50% 50%;--bgs:cover;--bgs-mobile:cover;--bgr:no-repeat;--bgr-mobile:no-repeat;--duration:0s;--scale:100%;--pos:absolute;--pos-tablet:absolute;--pos-mobile:absolute;transform:;transition:transform 300ms cubic-bezier(0,0,0,1)">
          
    <img
      width="2237" height="1678" draggable="false" quality-type quality-percent data-sizes="auto" sizes="100vw" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" base-src="{{ "gempages_553400155311702965-3ae8e41f-54b7-4e8a-b348-ab8409cff6cd.png" | file_url }}" data-src="{{ "gempages_553400155311702965-3ae8e41f-54b7-4e8a-b348-ab8409cff6cd.png" | file_url }}" data-srcset="{{ "gempages_553400155311702965-3ae8e41f-54b7-4e8a-b348-ab8409cff6cd.png" | file_url }}"
      id=""
      
      class="gp-absolute gp-top-0 gp-invisible gp-w-full gp_lazyload gp-h-full gp_lazyforbg gp_lazyload"
    />
    <img
      width="2237" height="1678" draggable="false" quality-type quality-percent data-sizes="auto" sizes="100vw" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" base-src="{{ "gempages_553400155311702965-3ae8e41f-54b7-4e8a-b348-ab8409cff6cd.png" | file_url }}" data-src="{{ "gempages_553400155311702965-3ae8e41f-54b7-4e8a-b348-ab8409cff6cd.png" | file_url }}" data-srcset="{{ "gempages_553400155311702965-3ae8e41f-54b7-4e8a-b348-ab8409cff6cd.png" | file_url }}"
      id=""
      
      class="gp-absolute gp-top-0 gp-invisible gp-w-full gp_lazyload gp-h-full gp_lazyforbg gp_lazybg_tl gp_lazyload"
    />
    <img
      width="2237" height="1678" draggable="false" quality-type quality-percent data-sizes="auto" sizes="100vw" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" base-src="{{ "gempages_553400155311702965-dce85f57-86c5-48d5-971a-5dbf60d81c76.jpg" | file_url }}" data-src="{{ "gempages_553400155311702965-dce85f57-86c5-48d5-971a-5dbf60d81c76.jpg" | file_url }}" data-srcset="{{ "gempages_553400155311702965-dce85f57-86c5-48d5-971a-5dbf60d81c76.jpg" | file_url }}"
      id=""
      
      class="gp-absolute gp-top-0 gp-invisible gp-w-full gp_lazyload gp-h-full gp_lazyforbg gp_lazybg_mb gp_lazyload"
    />
          
        </div>
        <div aria-label="Overlay" role="banner" class="gp-absolute gp-inset-0 gp-left-[var(--left)] gp-top-[var(--top)] gp-z-1 gp-transition-all gp-duration-300" style="--bgc:#121212;--op:40%;--top:0;--left:0"> </div>
      </div>
      
    

    

    
    <gp-row gp-data='{}' data-id id data-same-height-subgrid-container class="gp-hero-banner-row tablet:!gp-relative mobile:!gp-relative  tablet:[&>*>*]:!gp-justify-undefined mobile:[&>*>*]:!gp-justify-undefined mobile:!gp-content-stretch mobile:[&>*]:!gp-justify-center mobile:!gp-h-auto gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--pl:50px;--pr:50px;--pt:50px;--pb:50px;--pl-tablet:15px;--pr-tablet:15px;--pt-tablet:32px;--pb-tablet:32px;--pl-mobile:15px;--pr-mobile:15px;--pt-mobile:28px;--pb-mobile:28px;--cg:32px;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:1200px;--w-tablet:var(--g-ct-w, 1200px);--w-mobile:var(--g-ct-w, 1200px);--pc:end;--pc-tablet:end;--pc-mobile:center">
      

      <div
      data-same-height-display-contents
      style="--jc:center;--o:0"
      class="g0k5-GQF-i gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gdfW3dWg_A"}' data-id="gdfW3dWg_A" id="gdfW3dWg_A" data-same-height-subgrid-container class="gdfW3dWg_A gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--cg:8px;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="gH3s5OQEzj gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gmkadEnCNL"}' data-id="gmkadEnCNL" id="gmkadEnCNL" data-same-height-subgrid-container class="gmkadEnCNL gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--op-mobile:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:8px;--mb-mobile:7px;--cg:12px;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start;--pc-mobile:center">
      

      <div
      data-same-height-display-contents
      style="--jc:center"
      class="gcxB4FKCU4 gp-relative gp-flex gp-flex-col"
    >
      
    
    <div data-id="g7K4hik6pB"  style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">
      <div class="gp-flex gp-flex-wrap" style="--cg:4px;--jc:left">
            <div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gZcnz3zIoZ ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gfBcnkBGPI" class="gp-leading-[0] gfBcnkBGPI" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gfBcnkBGPI" class="icon-wrapper gp-inline-flex gp-overflow-hidden gfBcnkBGPI " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:1px 1px 1px 1px;--bc:#000000">
      
        <div >
          <span style="--c:#FFFFFF;--t:rotate(0deg);--w:14px;--w-mobile:12px;--h:14px;--h-mobile:12px;--minw:14px;--minw-mobile:12px;--height-desktop:14px;--height-tablet:14px;--height-mobile:12px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor"><path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gEmW2q_BeG ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gHUPPNl8U7" class="gp-leading-[0] gHUPPNl8U7" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gHUPPNl8U7" class="icon-wrapper gp-inline-flex gp-overflow-hidden gHUPPNl8U7 " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:1px 1px 1px 1px;--bc:#000000">
      
        <div >
          <span style="--c:#FFFFFF;--t:rotate(0deg);--w:14px;--w-mobile:12px;--h:14px;--h-mobile:12px;--minw:14px;--minw-mobile:12px;--height-desktop:14px;--height-tablet:14px;--height-mobile:12px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor"><path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gliDexm0tt ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gay4cR_4oi" class="gp-leading-[0] gay4cR_4oi" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gay4cR_4oi" class="icon-wrapper gp-inline-flex gp-overflow-hidden gay4cR_4oi " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:1px 1px 1px 1px;--bc:#000000">
      
        <div >
          <span style="--c:#FFFFFF;--t:rotate(0deg);--w:14px;--w-mobile:12px;--h:14px;--h-mobile:12px;--minw:14px;--minw-mobile:12px;--height-desktop:14px;--height-tablet:14px;--height-mobile:12px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor"><path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gDnFUgMIPO ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gOeEEt2GCQ" class="gp-leading-[0] gOeEEt2GCQ" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gOeEEt2GCQ" class="icon-wrapper gp-inline-flex gp-overflow-hidden gOeEEt2GCQ " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:1px 1px 1px 1px;--bc:#000000">
      
        <div >
          <span style="--c:#FFFFFF;--t:rotate(0deg);--w:14px;--w-mobile:12px;--h:14px;--h-mobile:12px;--minw:14px;--minw-mobile:12px;--height-desktop:14px;--height-tablet:14px;--height-mobile:12px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor"><path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="g0p3VGlTBu ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gjscnqGshd" class="gp-leading-[0] gjscnqGshd" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gjscnqGshd" class="icon-wrapper gp-inline-flex gp-overflow-hidden gjscnqGshd " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:1px 1px 1px 1px;--bc:#000000">
      
        <div >
          <span style="--c:#FFFFFF;--t:rotate(0deg);--w:14px;--w-mobile:12px;--h:14px;--h-mobile:12px;--minw:14px;--minw-mobile:12px;--height-desktop:14px;--height-tablet:14px;--height-mobile:12px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor"><path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div>
          </div>
    </div>
  
    </div><div
      data-same-height-display-contents
      style="--jc:center"
      class="gMLjNJxpoh gp-relative gp-flex gp-flex-col"
    >
      
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gYOpqsMXrL"
        class="gYOpqsMXrL"
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425944141"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#E0E0E0;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggYOpqsMXrL_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
      <style id="custom-css-gYOpqsMXrL">
        .gYOpqsMXrL {

}
.gYOpqsMXrL p {

}
      </style>
    
    </div>

      
    </gp-row>
  
  
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="g8BHXE622M"
        class="g8BHXE622M"
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb-mobile:6px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425944142"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--ta-mobile:center;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-heading, heading);--weight:500;--ls:normal;--size:40px;--size-tablet:40px;--size-mobile:30px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#FFFFFF;--tt:uppercase;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden;--tdl:"
        >
          {{ section.settings.gg8BHXE622M_text | replace: '$locationOrigin', locationOrigin }}
        </h2>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="g__EqMMSss"
        class="g__EqMMSss"
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--op-mobile:100%;--op-tablet:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:24px;--mb-mobile:14px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425944143"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--ta-mobile:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--ls:normal;--size:20px;--size-tablet:20px;--size-mobile:18px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#E0E0E0;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden;--tdl:"
        >
          {{ section.settings.gg__EqMMSss_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    <gp-button
      gp-data='{"btnLink":{"link":"#g071lCCNnn","type":"scroll-to","title":"Section 9"}}'
      
      class="gp-flex gp-flex-col"
    >
      <div style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--op-mobile:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:8px;--mb-mobile:7px;--ta:left;--ta-mobile:center" >
        <style>[data-id="gPB9LANs0i"].gp-button-base::before, [data-id="gPB9LANs0i-interaction"].gp-button-base::before {
      
    content: "";
    height: 100%;
    width: 100%;
    position: absolute;
    pointer-events: none;
    top: 0;
    left: 0;
    border-style: none;
  
  
  
    
      border-bottom-left-radius: 999px;
      border-bottom-right-radius: 999px;
      border-top-left-radius: 999px;
      border-top-right-radius: 999px;
      
  
    }
  
      
  [data-id="gPB9LANs0i"]:hover::before, [data-id="gPB9LANs0i-interaction"]:hover::before {
    
    
  }</style>
        <a
            class="gp-trigger-button-link gp-hidden"
            href=#g071lCCNnn
            target=undefined
          ></a>
        <a
          data-id="gPB9LANs0i" dataId="gPB9LANs0i" data-state="idle" aria-label="<p>GEAR UP WITH ULTRA 2+ TODAY</p>"
          
          href="#g071lCCNnn" target
          style="--w:100%;--w-tablet:100%;--w-mobile:370px;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:12px;--pb:12px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:12px;--pb-mobile:12px;--bblr:999px;--bbrr:999px;--btlr:999px;--btrr:999px;--hvr-bblr:999px;--hvr-bbrr:999px;--hvr-btlr:999px;--hvr-btrr:999px;--shadow:none;--bgi:;--hvr-bgi:;--hvr-bg:#007AF4;--bg:#0171E3;--c:#FFFFFF;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:700;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:16px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase"
          class="gPB9LANs0i gp-text-center gp-button-base gp-group gp-relative gp-inline-flex gp-max-w-full gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-200 disabled:gp-btn-disabled disabled:gp-opacity-30 "
        >
        
        <div class="gp-inline-flex">
          
          
    <span
      data-gp-text
      style="--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:700;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:16px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;--ts:none;word-break:break-word"
      class="gp-content-product-button group-active/button:!gp-text-inherit gp-relative gp-flex gp-h-full gp-items-center gp-overflow-hidden gp-break-words group-data-[state=loading]:gp-invisible [&_p]:gp-whitespace-pre-line gp-text button-text"
    >
      {{ section.settings.ggPB9LANs0i_label }}
    </span>
        </div>
        
        </a>
      </div>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-button-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    </gp-button>
  <div gp-el-wrapper  class="gerYJWWAZA ">
      
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <gp-icon-list
      
      uid="gerYJWWAZA"
      data-id="gerYJWWAZA"
      style="--shadow:none;--d:flex;--d-mobile:flex;--d-tablet:flex;--op:100%;--pageType:GP_STATIC;--jc:left;--jc-mobile:center"
      className="gerYJWWAZA"
      gp-data='{"position":{"desktop":"center","mobile":"center","tablet":"center"},"iconWidth":{"desktop":16,"mobile":16,"tablet":16}}'
    >
      
          <div class="gp-flex" style="--b:none;--bc:#121212;--bw:1px 1px 1px 1px;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--w:100%;--jc:left;--jc-mobile:center">
            <div class="gp-inline-flex gp-flex-col" style="--rg:16px">
              
                    <div
                      key="0"
                      data-index="0"
                      class="gp-icon-list-wrapper gp-flex gp-child-item-gerYJWWAZA"
                      style="--cg:8px;--ai:center;--ai-tablet:center;--ai-mobile:center"
                    >
                      <div class="gp-icon-list-icon gp-inline-flex gp-shrink-0 gp-overflow-hidden" style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:1px 1px 1px 1px;--bc:#000000;--bg:transparent;--top:unset;--top-tablet:unset;--top-mobile:unset;--pos:relative;--pos-tablet:relative;--pos-mobile:relative">
                        <span
                          class="gp-icon gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--size-desktop)] [&>svg]:!gp-w-[var(--size-desktop)] tablet:[&>svg]:!gp-h-[var(--size-tablet)] tablet:[&>svg]:!gp-w-[var(--size-tablet)] mobile:[&>svg]:!gp-h-[var(--size-mobile)] mobile:[&>svg]:!gp-w-[var(--size-mobile)]"
                          style="--c:#00C853;--w:100%;--h:100%;--size-desktop:16px;--size-tablet:16px;--size-mobile:16px"
                        >
                          <svg data-id="515658750703436950" xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
<g clip-path="url(#clip0_442_3948)">
<path d="M5.33801 2.09C4.38559 2.35248 3.43965 2.6379 2.50101 2.946C2.41529 2.97376 2.3391 3.02504 2.28111 3.09399C2.22312 3.16295 2.18567 3.2468 2.17301 3.336C1.61901 7.493 2.89901 10.526 4.42601 12.524C5.07252 13.3784 5.84341 14.1311 6.71301 14.757C7.05901 15.001 7.36501 15.177 7.60601 15.29C7.72601 15.347 7.82401 15.385 7.89901 15.408C7.93181 15.4195 7.96562 15.4279 8.00001 15.433C8.03398 15.4275 8.06743 15.4191 8.10001 15.408C8.17601 15.385 8.27401 15.347 8.39401 15.29C8.63401 15.177 8.94101 15 9.28701 14.757C10.1566 14.1311 10.9275 13.3784 11.574 12.524C13.101 10.527 14.381 7.493 13.827 3.336C13.8145 3.24676 13.777 3.16285 13.719 3.09388C13.661 3.02491 13.5848 2.97366 13.499 2.946C12.848 2.733 11.749 2.386 10.662 2.091C9.55201 1.79 8.53101 1.567 8.00001 1.567C7.47001 1.567 6.44801 1.79 5.33801 2.091V2.09ZM5.07201 1.06C6.15701 0.765 7.31001 0.5 8.00001 0.5C8.69001 0.5 9.84301 0.765 10.928 1.06C12.038 1.36 13.157 1.715 13.815 1.93C14.0901 2.02085 14.334 2.18747 14.5187 2.4107C14.7034 2.63394 14.8213 2.90474 14.859 3.192C15.455 7.669 14.072 10.987 12.394 13.182C11.6824 14.121 10.834 14.9479 9.87701 15.635C9.5461 15.8728 9.19549 16.0819 8.82901 16.26C8.54901 16.392 8.24801 16.5 8.00001 16.5C7.75201 16.5 7.45201 16.392 7.17101 16.26C6.80452 16.0819 6.45391 15.8728 6.12301 15.635C5.16603 14.9478 4.31759 14.121 3.60601 13.182C1.92801 10.987 0.545005 7.669 1.14101 3.192C1.17869 2.90474 1.29665 2.63394 1.48132 2.4107C1.666 2.18747 1.9099 2.02085 2.18501 1.93C3.1402 1.61681 4.10281 1.32672 5.07201 1.06Z" fill="#E0E0E0"/>
<path d="M10.8546 5.64687C10.9012 5.69331 10.9381 5.74849 10.9633 5.80923C10.9885 5.86998 11.0015 5.9351 11.0015 6.00087C11.0015 6.06663 10.9885 6.13175 10.9633 6.1925C10.9381 6.25324 10.9012 6.30842 10.8546 6.35487L7.85461 9.35487C7.80817 9.40143 7.75299 9.43837 7.69225 9.46358C7.6315 9.48878 7.56638 9.50176 7.50061 9.50176C7.43484 9.50176 7.36972 9.48878 7.30898 9.46358C7.24823 9.43837 7.19306 9.40143 7.14661 9.35487L5.64661 7.85487C5.60012 7.80838 5.56325 7.75319 5.53809 7.69245C5.51293 7.63171 5.49998 7.56661 5.49998 7.50087C5.49998 7.43512 5.51293 7.37002 5.53809 7.30928C5.56325 7.24854 5.60012 7.19335 5.64661 7.14687C5.6931 7.10038 5.74829 7.0635 5.80903 7.03834C5.86977 7.01318 5.93487 7.00023 6.00061 7.00023C6.06636 7.00023 6.13146 7.01318 6.1922 7.03834C6.25294 7.0635 6.30812 7.10038 6.35461 7.14687L7.50061 8.29387L10.1466 5.64687C10.1931 5.6003 10.2482 5.56336 10.309 5.53815C10.3697 5.51295 10.4348 5.49997 10.5006 5.49997C10.5664 5.49997 10.6315 5.51295 10.6922 5.53815C10.753 5.56336 10.8082 5.6003 10.8546 5.64687Z" fill="#E0E0E0"/>
</g>
<defs>
<clipPath id="clip0_442_3948">
<rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>
                        </span>
                      </div>
                      
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id
        class="gp-w-full"
        style="--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425944172"
      >
      <div class="gp-flex" >
        <div
          data-gp-text
          class=" gp-w-full gp-border gp-border-transparent gp-text-left gp-icon-list-text  gp-text-instant"
          style="--w:100%;--tdt:auto;--ts:none;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--ls:normal;--size:14px;--size-tablet:13px;--size-mobile:12px;--c:#E0E0E0;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggerYJWWAZA_childItem_0 | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
                    </div>
            </div>
          </div>
    </gp-icon-list>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-icon-list-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div>

      
    </gp-row>
  
  
    </div><div
      data-same-height-display-contents
      style="--jc:center;--o:1;--d-mobile:none"
      class="gV94zk_C9s gp-relative gp-flex gp-flex-col"
    >
      
    </div>

      
    </gp-row>
  
  
        
    <picture style="border-radius:inherit" class="gp-w-full gp-invisible -gp-z-1">
      
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_553400155311702965-dce85f57-86c5-48d5-971a-5dbf60d81c76.jpg" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQ0NSIgaGVpZ2h0PSIzMDU2IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yNDQ1LTMwNTYiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjI0NDUiIGhlaWdodD0iMzA1NiIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjQ0NSIgaGVpZ2h0PSIzMDU2IiBmaWxsPSJ1cmwoI2ctMjQ0NS0zMDU2KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjQ0NSIgdG89IjI0NDUiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_553400155311702965-3ae8e41f-54b7-4e8a-b348-ab8409cff6cd.png" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTU1OCIgaGVpZ2h0PSIyMDUyIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xNTU4LTIwNTIiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjE1NTgiIGhlaWdodD0iMjA1MiIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTU1OCIgaGVpZ2h0PSIyMDUyIiBmaWxsPSJ1cmwoI2ctMTU1OC0yMDUyKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTU1OCIgdG89IjE1NTgiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
    
      
      <img
        title fallbackImg="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE5MjAtOTAwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOTIwIiBoZWlnaHQ9IjkwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIGZpbGw9InVybCgjZy0xOTIwLTkwMCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE5MjAiIHRvPSIxOTIwIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTU1OCIgaGVpZ2h0PSIyMDUyIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xNTU4LTIwNTIiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjE1NTgiIGhlaWdodD0iMjA1MiIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTU1OCIgaGVpZ2h0PSIyMDUyIiBmaWxsPSJ1cmwoI2ctMTU1OC0yMDUyKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTU1OCIgdG89IjE1NTgiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" data-src="{{ "gempages_553400155311702965-3ae8e41f-54b7-4e8a-b348-ab8409cff6cd.png" | file_url }}"
        alt=""
        width="100%"
        style="--d:none;--d-tablet:none;--d-mobile:none;--h:fit-content;--h-tablet:auto;--h-mobile:500px;--w:100%;--w-tablet:100%;--w-mobile:100%"
        class="adaptive-hero-banner gp_lazyload"
      />
    
    </picture>
  
  </div>

        
      </gp-hero-banner>
       <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-hero-banner-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    </div>

      
    </gp-row>
  
        </section>
      
  
    <style {% if section.settings.section_preload == "false" %} class="gps-link" type="text/disabled-css" {% endif %}>@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWubEbVmUiAw.woff) format('woff');
}
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWub2bVmUiAw.woff) format('woff');
}
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmUiAw.woff) format('woff');
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBHMdazQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBHMdazQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBHMdazQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style>
    
{% schema %}
  {
    
    "name": "Section 1",
    "tag": "section",
    "class": "gps-578721113178112635 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=578718746651132539&sectionId=578721113178112635)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggYOpqsMXrL_text","label":"ggYOpqsMXrL_text","default":"<p>Rated 5/5 Based on 2.6K+ Reviews</p>"},{"type":"html","id":"gg8BHXE622M_text","label":"gg8BHXE622M_text","default":"<span style=\"color:#FFFFFF;\">The Rugged Smartwatch Built to</span> <i>Survive Anything.</i>"},{"type":"html","id":"gg__EqMMSss_text","label":"gg__EqMMSss_text","default":"<p>Meet the best rugged smartwatch - GardPro Ultra 2+: Rugged, reliable, and ready for every challenge.</p>"},{"type":"html","id":"ggPB9LANs0i_label","label":"ggPB9LANs0i_label","default":"<p>GEAR UP WITH ULTRA 2+ TODAY</p>"},{"type":"html","id":"ggerYJWWAZA_childItem_0","label":"ggerYJWWAZA_childItem_0","default":"<p>30-day money-back guarantee included&nbsp;</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
  