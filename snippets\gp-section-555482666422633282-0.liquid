<div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gDWmgJsMTc gp-relative gp-flex gp-flex-col"
    >
      
    {%- assign gpBkProduct = product -%}
    
        {%- liquid
          if request.page_type == 'product'
            if 'static' == 'static'
              if '14924340658558' == 'latest'
                paginate collections.all.products by 100000
                  assign product = collections.all.products | sort: 'created_at' | reverse | first
                endpaginate
              else
                assign product = all_products['gard-pro-health-smartwatch-3-1']
                assign productId = '14924340658558' | times: 1
                if product == empty or product == null
                  paginate collections.all.products by 100000
                    for item in collections.all.products
                      if item.id == productId
                        assign product = item
                      endif
                    endfor
                  endpaginate
                endif
              endif
            endif
          else
            if '14924340658558' == 'latest'
              paginate collections.all.products by 100000
                assign product = collections.all.products | sort: 'created_at'| reverse | first
              endpaginate
            else
              assign product = all_products['gard-pro-health-smartwatch-3-1']
              assign productId = '14924340658558' | times: 1
              if product == empty or product == null
                paginate collections.all.products by 100000
                  for item in collections.all.products
                    if item.id == productId
                      assign product = item
                    endif
                  endfor
                endpaginate
              endif
            endif
          endif
        -%}
      

    {%-if product != empty and product != null -%}
      {%- assign initVariantId = 55006243651966 -%}
      {%- assign product_form_id = 'product-form-' | append: "g6pEIRnPeY" -%}
      {%- assign variant = product.variants | where: 'id', initVariantId | first -%}
      {%- assign productSelectedVariant = product.variants | where: 'id', initVariantId | first -%}
      {%-if productSelectedVariant == empty or productSelectedVariant == null -%}
        {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      {%-if variant == empty or variant == null -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      <gp-product data-uid="g6pEIRnPeY" data-id="g6pEIRnPeY"   gp-context='{"productId": {{ product.id }}, "preSelectedOptionIds": [9247452299646,9557196833150], "isSyncProduct": "true", "variantSelected": {{ variant | json | escape }}, "inventoryQuantity": {{ variant.inventory_quantity }}, "quantity": 1, "formId": "{{ product_form_id }}" }'
        gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }}}'>
        <product-form class="product-form">
          {%- form 'product', product, id: product_form_id, class: 'form contents', data-type: 'add-to-cart-form', autocomplete: 'off'  -%}
            <input type="hidden" name="id" value="{{ variant.id }}" />
            <input type="hidden" name="quantity" value="{{ quantity }}" />
            <button type="submit" onclick="return false;" style="display:none;"></button>
            
       
      
    <div
      id="g6pEIRnPeY" data-id="g6pEIRnPeY-row"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb-tablet:30px;--cg:30px;--pc:start;--gtc:minmax(0, 12fr);--w:600px;--w-tablet:100%;--w-mobile:100%"
        class="g6pEIRnPeY gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gnNV5vNvDD gp-relative gp-flex gp-flex-col"
    >
      
  {% assign featured_image = product.featured_image %}
  {%- if variant != null and variant.featured_image != null -%}
  {% assign featured_image = variant.featured_image %}
  {%- endif -%}
    <gp-product-images-v2
      gp-data='
    {
      "id":"gfy9Q8_V8D",
      "pageContext": {"pageType":"GP_STATIC","sectionName":"","isPreviewing":false,"isOptimizePlan":false,"isTranslateWithLocale":false,"isLazyLoadSection":true,"isLazyLoadImage":false,"hasCollectionHandle":true,"numberOfProductOfProductList":0,"pageBackground":"","fontType":"google","primaryLocale":"","enableLazyLoadImage":false},
      "setting":{"arrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","arrowIconColor":"#000000","arrowNavBorder":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"},"arrowNavRadius":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"borderActive":{"border":"solid","borderType":"none","borderWidth":"1px","color":"#000000","isCustom":false,"width":"1px 1px 1px 1px"},"clickOpenLightBox":{"desktop":false},"dragToScroll":true,"ftAnimationMode":"ease-out","ftAnimationSpeed":500,"ftArrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","ftArrowIconColor":"#000000","ftArrowNavBorder":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"},"ftArrowNavRadius":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"ftClickOpenLightBox":{"desktop":"product-link"},"ftClickOpenNewTab":false,"ftDotActiveColor":{"desktop":"line-3"},"ftDotColor":{"desktop":"bg-1"},"ftDotGapToCarousel":{"desktop":16},"ftDotSize":{"desktop":12},"ftDotStyle":{"desktop":"none"},"ftDragToScroll":false,"ftLoop":{"desktop":false},"ftNavigationPosition":{"desktop":"none"},"ftPauseOnHover":true,"galleryHoverEffect":"none","galleryZoom":150,"galleryZoomType":"default","hoverEffect":"other","loop":{"desktop":true},"navigationPosition":{"desktop":"inside"},"otherImage":1,"pauseOnHover":true,"preDisplay":"1st-available-variant","preload":true,"qualityPercent":{"desktop":100},"qualityType":{"desktop":"high"},"speed":1,"type":{"desktop":"slider"},"typeDisplay":"all-images","zoom":150,"zoomType":"default"},
      "styles":{"align":{"desktop":"flex-start"},"corner":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"ftCorner":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"ftLayout":{"desktop":"cover"},"ftShape":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"itemSpacing":{"desktop":"var(--g-s-s)","mobile":"var(--g-s-s)"},"layout":{"desktop":"cover"},"position":{"desktop":"only-feature","mobile":"only-feature"},"ratioLayout":{"desktop":[2,10]},"ratioLayoutRight":{"desktop":[10,2]},"shape":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"shapeFor1Col":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"shapeFor2Col":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"50%"}},"shapeForBottom":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"}},"shapeForFtOnly":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"shapeForInside":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"}},"shapeForInsideBottom":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"}},"spacing":{"desktop":"var(--g-s-s)","mobile":"var(--g-s-s)"}}, "productUrl":{{product.url | json | escape}}, "product":{{product | json | escape}}, "collectionUrl": {{ collection.url | json | escape }}, "collection": {{ collection | json | escape}}
    }
  '
      section-id="{{section.id}}"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:16px"
      data-id="gfy9Q8_V8D"
      class="gfy9Q8_V8D gp-overflow-hidden"
    >
      <div
        class="{%- if product.media.size > 1 -%} gp-grid gp-w-full !gp-m-0 gp-relative {%- else -%} gp-w-full !gp-m-0 gp-relative {%- endif -%}"
        {%- if product.media.size > 1 -%}
        style="--gtc:minmax(0, 12fr);--gtc-tablet:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--gg:var(--g-s-s);--gg-mobile:var(--g-s-s)"
        {%- else -%}
        style="--gtc:minmax(0, 12fr);--gg:var(--g-s-s);--gg-mobile:var(--g-s-s)"
        {%- endif -%}
      >
        
    {% capture featureImageOnlyOne %}
      
        {% assign featureMedia = variant.featured_media %}
        {% unless featureMedia %}
        {% assign featureMedia = product.featured_media %}
        {% endunless %}
        {% if product.media.size > 1 %}
          
          {% assign featureMedia = variant.featured_media %}
          {% unless featureMedia %}
          {% assign featureMedia = product.featured_media %}
          {% endunless %}
        
        {% endif %}
        {% assign largestRatio = 0 %}
        {% assign height = featureMedia.height | times: 1.0 %}
        {% assign width = featureMedia.width | times: 1.0 %}
        {% assign ratio = height | divided_by: width %}
        {% if ratio > largestRatio %}
          {% assign largestRatio = ratio %}
        {% endif %}
        {% assign productImageWidth = 0 %}
        {% case featureMedia.media_type %}
          {% when 'image' %}
            {% assign productImageWidth = featureMedia.width %}
          {% else %}
            {% assign productImageWidth = featureMedia.preview_image.width %}
        {% endcase %}
        {% if featureMedia == null %}
        {% assign productImageWidth = 1600 %}
        {% endif %}
        <div 
          class='gp-feature-image-carousel gp-feature-image-only' 
          style="--o:0;--o-tablet:0;--o-mobile:0;--d:flex;--d-mobile:flex;--d-tablet:flex;--jc:flex-start"
        >
          <div 
            class="gp-relative"
            style="--w:100%;--w-tablet:100%;--w-mobile:100%"
          >
            
      
    
            <div 
              type="gp-feature-image-only"
              product-id="{{product.id}}"
              product-media="{{product.media.size}}"
              class="gp-image-item gp-ft-image-item gp-group gp-z-0 gp-flex !gp-max-w-full !gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-featured-image-wrapper"
              style="--h:auto;--h-tablet:auto;--h-mobile:auto;width:{{productImageWidth}}px"
            >
              <div 
                class="gp-w-full  {% if featureMedia == null or featureMedia.media_type == 'image' %} {{ 'gp-h-0' }} {% else %} {{ 'gp-h-full !gp-pb-0' }} {% endif %} gp-relative" 
                style="--pb: calc((1/1)*100%);--pb-tablet: calc((1/1)*100%);--pb-mobile: calc((1/1)*100%); {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %} {{ 'display: flex; align-items: center; justify-content: center' }} {% endif %}"
              >
                
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity group-hover:gp-opacity-0 {{shouldHidden}}"
      src="{{src | image_url}}" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}"
      fetchpriority="high"
      quality-type={"desktop":"high"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset="{{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w"
      sizes="(max-width: 768px) 50vw, 100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
      <div class="zoom-element !gp-max-w-none gp-absolute gp-left-0 gp-top-0 gp-opacity-0 gp-transition-opacity gp-bg-white gp-pointer-events-none">
            
    {% assign src = featureMedia.src %}
    
      {% assign media_length = product.media | size %}
      {% assign other_image_index = 1 | plus: 0 %}
      {% if other_image_index >= media_length %}
        {% assign other_image_index = media_length | minus: 1 %}
      {% endif %}
      {% assign otherImage = product.media[other_image_index] %}
      {% assign src = otherImage.src %}
      {% if otherImage.media_type != 'image' %}
        {% assign src = otherImage.preview_image.src %}
      {%- endif -%}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none image-zoom"
      src="{{src | image_url}}" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}"
      fetchpriority="high"
      quality-type={"desktop":"high"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset="{{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w"
      sizes="(max-width: 768px) 50vw, 100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
          </div>
      
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{featureMedia.alt}}"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{featureMedia.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
      {% when 'model' %}
        
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%"
      
      
      
      
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}"
      alt="{{featureMedia.preview_image.alt}}"
      camera-controls="true"
      poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
  <img
      
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none"
      src width="2237" height="1678" alt="No Image"
      fetchpriority="high"
      quality-type={"desktop":"high"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset=""
      sizes="(max-width: 768px) 50vw, 100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
    {% endcase %}
    
              </div>
            </div>
          </div>
        </div>
      
    {% endcapture %}
    {% if product.media.size > 1 %}
      
      {{ featureImageOnlyOne }}
    {% else %}
      {{ featureImageOnlyOne }}
    {% endif %}
  
         
      </div>
    </gp-product-images-v2>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-images-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pos:absolute;--right:8px;--radius:var(--g-radius-small);--top:8px;--z:1" class="gryrRaVzpy ">
      
  {% liquid
    assign price = variant.price | times: 1.0
    assign salePrice = variant.compare_at_price | times: 1.0
    assign priceSave = salePrice | minus: price
    assign prefixVal = 
    assign suffixVal = section.settings.ggryrRaVzpy_customContent_suffix
    if salePrice == null or price == null
      assign pricePercentage = prefixVal | append: ' 0% ' | append: suffixVal
    else
         assign salePercent = priceSave | divided_by: salePrice | times: 100  | round
                assign pricePercentage = prefixVal | append: ' ' | append: salePercent | append: '% ' | append: suffixVal
              
    endif
  %}

  <gp-product-tag
  data-id="gryrRaVzpy"
    data-disabled="{%- if priceSave > 0 -%} false {%- else -%} true {%- endif -%}"
    gp-data='{"setting":{"customContent":{"prefix":"","suffix":"off","unit":"percentage"},"translate":"customContent"}, "id": "gryrRaVzpy", "locale": "{{shop.locale}}", "currency": "{{shop.currency}}", "moneyFormat": "{{ shop.money_format | replace: '"', '\"' | escape }}"}'
    class="gp-block data-[disabled=true]:gp-hidden "
    style="--ta:left"
    price-save="{{priceSave}}"
    data-prefix="{{}}"
    data-suffix="{{section.settings.ggryrRaVzpy_customContent_suffix}}"
  >
     <div class="gp-inline-flex gp-flex-wrap gp-items-end gp-gap-3" >
       <div
         class="gp-flex gp-items-center gp-w-full gp-h-full gp-bg-g-bg-3"
         style="--pl:12px;--pr:12px;--pt:4px;--pb:4px;--bs:none;--bw:0px;--bc:transparent;--c:#FB4606;--radius:var(--g-radius-none)"
       >
         
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-tag-gryrRaVzpy"
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A']"
          style="--w:100%;--ta:left;--c:#FB4606;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >{{pricePercentage}}</div>
      </div>
    </div>
    </gp-text>
    
       </div>
     </div>

 </gp-product-tag>
 <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-tag.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
   
      </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gnT0Zj7ctg gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-xs);--pr:0px" class="gLayS268kO ">
      
    {%- unless product -%}
      <p>{{ "gempages.ProductTitle.product_not_found" | t }}</p>
    {%- else -%}
      
        <a href="{{ product.url }}" title="{{ product.title }}" class="gp-product-title-link-wrapper">
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gLayS268kO">
    <div
      
        class="gLayS268kO "
        style="--tt:default"
      >
      <div  >
        <h1
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A']"
          style="--w:100%;--ta:left;--line-clamp:2;--line-clamp-tablet:2;--line-clamp-mobile:0;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:none;word-break:break-word;overflow:hidden"
        >{{ product.title }}</h1>
      </div>
    </div>
    </gp-text>
    
        </a>
      
    {%- endunless -%}
  
      </div>
       
      
    <div
      parentTag="Col" id="gUfbV_VVpN" data-id="gUfbV_VVpN"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-l);--cg:8px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gUfbV_VVpN gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gCwPlAL3Un gp-relative gp-flex gp-flex-col"
    >
      
      <gp-product-price
        data-id="ghgOpvUg8z"
        class="ghgOpvUg8z gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden"
        gp-data='{"priceType":"regular","uid":"ghgOpvUg8z","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
        
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
        data-hidden="false"
      >
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-price-ghgOpvUg8z"
        class=" "
        
      >
      <div  >
        <div
          type="regular"
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-price gp-money gp-decoration-g-highlight"
          style="--w:100%;--tdc:highlight;--tdt:1;--ta:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >
     {% if variant.price %} 
       {{ variant.price | money}}
     {% endif %}
   </div>
      </div>
    </div>
    </gp-text>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-price.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gsBZsp3q4v gp-relative gp-flex gp-flex-col"
    >
      
      <gp-product-price
        data-id="gTCpQYzahI"
        class="gTCpQYzahI gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden"
        gp-data='{"priceType":"compare","uid":"gTCpQYzahI","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
        
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
        data-hidden="{% if variant.compare_at_price > variant.price and variant.compare_at_price >= 0 %}false{% else %}true{% endif %}"
      >
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-price-gTCpQYzahI"
        class=" "
        
      >
      <div  >
        <div
          type="compare"
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-price gp-product-compare-price gp-line-through"
          style="--w:100%;--tdc:#B4B4B4;--tdt:1;--ta:left;--c:#B4B4B4;--fs:normal;--ff:var(--g-font-heading, heading);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >
      {% if variant.compare_at_price  %} 
        {{ variant.compare_at_price | money}}
      {% else %}
        
      {% endif %}
    </div>
      </div>
    </div>
    </gp-text>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-price.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
   
    <div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:var(--g-s-l)" class="g_fMEtQw8Y ">
      
  {%- assign total_combinations = 1 -%}
  {%- for option in product.options_with_values -%}
    {%- assign total_combinations = total_combinations | times: option.values.size -%}
  {%- endfor -%}
  <gp-product-variants
    data-id="g_fMEtQw8Y"
    
    has-pre-selected="true"
    gp-data='{
      "setting":{"blankText":"Please select an option","column":{"desktop":1},"combineFullWidth":{"desktop":true},"combineHeight":"45px","combineWidth":{"desktop":"100%"},"hasOnlyDefaultVariant":false,"hasPreSelected":true,"label":false,"layout":{"desktop":"vertical"},"optionAlign":{"desktop":"left"},"optionType":"singleOption","price":true,"showAsSwatches":true,"soldOutStyle":"line","variantPresets":[{"isEdited":true,"optionName":"base","optionType":"rectangle_list","presets":{"color":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"hover":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"normal":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"45px","mobile":"45px","tablet":"45px"}},"dropdown":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"width":{"desktop":"100%","mobile":"100%","tablet":"100%"}},"image":{"height":"64px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"}},"image_shopify":{"height":"64px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"}},"rectangle_list":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":""}}}},{"isEdited":true,"optionName":"Color","optionType":"color","presets":{"color":{"height":"30px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"hover":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"normal":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"2px","width":{"desktop":"30px","mobile":"30px","tablet":"30px"}},"dropdown":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"width":{"desktop":"100%","mobile":"100%","tablet":"100%"}},"image":{"height":"64px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"}},"image_shopify":{"height":"64px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"}},"rectangle_list":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":""}}}},{"hide":true,"isEdited":true,"optionName":"Case Size","optionType":"rectangle_list","presets":{"color":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"hover":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"},"normal":{"bblr":"999999px","bbrr":"999999px","btlr":"999999px","btrr":"999999px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"45px","mobile":"45px","tablet":"45px"}},"dropdown":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"width":{"desktop":"100%","mobile":"100%","tablet":"100%"}},"image":{"height":"64px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"}},"image_shopify":{"height":"64px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":"64px","mobile":"64px","tablet":"64px"}},"rectangle_list":{"height":"45px","optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"spacing":"16px","width":{"desktop":""}}}}]},
      "styles":{"align":{"desktop":"left"},"dropdownItemWidth":{"desktop":"fill","mobile":"fill","tablet":"fill"},"fixedDropdownWidth":{"desktop":"240px"},"fullWidth":{"desktop":true},"labelColor":"text-2","labelGap":"8px","labelTypo":{"attrs":{"bold":true,"color":"text-1"},"type":"paragraph-2"},"marginBottom":{"desktop":"8px","mobile":"var(--g-s-xl)"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"active":false,"hover":false,"normal":false},"optionRounded":{"active":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"hover":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"},"normal":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"custom"}},"optionShadow":{"active":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionSpacing":"30px","optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"optionTypo":{"type":"paragraph-2"},"swatchAutoWidth":{"desktop":true},"swatchHeight":{"desktop":"45px"},"swatchItemWidth":{"desktop":"auto"},"swatchSpacing":"var(--g-s-m)","swatchWidth":{"desktop":"80px"},"width":{"desktop":"100%"}},
      "variants":{{product.variants | json | escape}},
      "optionsWithValues": {{product.options_with_values | json | escape}},
      "variantSelected": {{ variant | json | escape }},
      "variantInventoryQuantity": {{product.variants | map: 'inventory_quantity' | json | escape}},
      "variantInventoryPolicy": {{product.variants | map: 'inventory_policy' | json | escape}},
      "moneyFormat": {{shop.money_format | json | escape}},
      "productId": {{product.id | json | escape}},
      "productUrl": {{product.url | json | escape}},
      "productHandle": {{product.handle | json | escape}},
      "displayState": {"desktop":true,"mobile":true,"tablet":true},
      "totalVariantCombinations": {{total_combinations}},
      "firstAvailableVariant": {{product.selected_or_first_available_variant | json | escape}}
    }
  '>
    {%- assign options = product.options_with_values -%}
    {%- assign variants = product.variants -%}
    {%- if options.size == 0 or options.size == 1 and variants.size == 1 and variants[0].title == 'Default Title' and variants[0].option1 == 'Default Title' -%}
      <div></div>
    {% else %}
      <div
      class="gp-grid !gp-ml-0"
      style="--gtc:repeat(1, minmax(0, 1fr));--ta:left;--w:100%;--w-tablet:100%;--w-mobile:100%;--rg:8px;--rg-mobile:var(--g-s-xl)"
    >
      
      {% assign presets = "base($2)rectangle_list($1)Color($2)color($1)Case Size($2)rectangle_list" | split: '($1)' %}
      {% assign hiddenPresetOptions = "Case Size" | split: ',' %}
      {%- for option in options -%}
        <div
        option-name="{{option.name | escape}}"
        class="gp-flex variant-inside gp-flex-col gp-items-start"

        >
          {% assign showVariantClass = 'variant-display' %}
          {% assign optionName = option.name %}
          {% for preset in presets %}
            {% assign presetDetail = preset | split: '($2)' %}
            {% if presetDetail[1] == 'dropdown' and presetDetail[0] == optionName %}
              {% assign showVariantClass = '' %}
              {% break %}
            {% endif %}
          {% endfor %}

          

          <div
              variant-option-name="{{option.name | escape}}"
              class="gp-justify-start gp-flex gp-w-full gp-flex-wrap gp-items-center variant-option-group"
              style="--rg:var(--g-s-m);--cg:var(--g-s-m)"
            >
              {%- assign values = option.values -%}
              {%- assign rootForloop = forloop.index0 -%}
              {%- if option.position == 1 -%}
                {%- assign selectedValue = variant.option1 -%}
              {%- elsif option.position == 2 -%}
                {%- assign selectedValue = variant.option2 -%}
              {%- else -%}
                {%- assign selectedValue = variant.option3 -%}
              {%- endif -%}
              
              
    {% assign optionRendered = false %}
    {%  assign swatches = shop.metafields.GEMPAGES.swatches %}
    {%  assign swatchesItems = swatches | split: '($1)'  %}
    {% for swatchesItem in swatchesItems %}
      {% assign colorArraysString = "" %}
      {% assign labelsString = "" %}
      {% assign imageUrlsString = "" %}

      {%  assign attrItems = swatchesItem | split: '($3)'  %}
      {% for attrItem in attrItems %}
        {%  assign attrs = attrItem | split: '($2)'  %}


          {% assign optionKey = attrs[0] %}
          {% assign optionValue = attrs[1] %}
          {% if optionKey == 'optionTitle' %}
                {% assign optionTitle = optionValue %}
              {% elsif optionKey == 'optionType' %}
                {% assign optionType = optionValue %}
            {% endif %}


            {% if optionKey == 'optionValues' %}

              {% assign opValueItems = optionValue | split: '($4)'  %}
              {% for opValueItem in opValueItems %}
                {% assign opValueItemAttrs = opValueItem | split: '($6)'  %}
                {% for opValueItemAttr in opValueItemAttrs %}
                  {% assign attrs = opValueItemAttr | split: '($5)'  %}
                  {% assign opValueItemKey = attrs[0] %}
                  {% assign opValueItemValue = attrs[1] %}

                  {% if opValueItemKey == 'label' %}
                    {% assign labelsString = labelsString | append: opValueItemValue %}
                    {% assign labelsString = labelsString | append: "($8)" %}
                  {% endif %}

                  {% if opValueItemKey == 'colors' %}
                    {% assign colorArraysString = colorArraysString | append: opValueItemValue %}
                    {% assign colorArraysString = colorArraysString | append: "($8)" %}
                  {% endif %}

                  {% if opValueItemKey == 'imageUrl' %}
                    {% assign imageUrlsString = imageUrlsString | append: opValueItemValue %}
                    {% assign imageUrlsString = imageUrlsString | append: "($8)" %}

                  {% endif %}
                {% endfor %}
              {% endfor %}
            {% endif %}

      {% endfor %}
      {% assign labels = labelsString | split: '($8)' %}
      {% assign colorStrings = colorArraysString | split: '($8)' %}
      {% assign imageUrls = imageUrlsString | split: '($8)' %}

      {% if optionTitle == option.name %}
      {% assign variantPresetString = "base($1)rectangle_list($2)Color($1)color($2)Case Size($1)rectangle_list" %}
      {% assign optionName = option.name | replace: "'", "&apos;" | replace: '"', "&quot;" %}
        {% assign items = variantPresetString | split:'($2)' %}
        {% assign type = 'dropdown' %}
        {%- for item in items -%}
          {% assign itemPreset = item | split:'($1)' %}
          {% if itemPreset[0] == optionName %}
            {% assign type = itemPreset[1] %}
          {% endif %}
          {% if itemPreset[0] == "base" %}
            {% assign type = itemPreset[1] %}
          {% endif %}
        {%- endfor -%}
        {% assign optionRendered = true %}
        {%- for value in values -%}
          
    {%- liquid
      assign option_disabled = true
      for variantItem in product.variants
        case option.position
          when 1
            if variantItem.available and variantItem.option1 == value
              assign option_disabled = false
            endif
          when 2
            if variantItem.available and variantItem.option2 == value
              assign option_disabled = false
            endif
          when 3
            if variantItem.available and variantItem.option3 == value
              assign option_disabled = false
            endif
        endcase
      endfor
    -%}

  {% assign option_disabled = false %}
  <label
  id="{{option.name | escape}}-{{value | escape}}"
  for="{{product.id}}-g_fMEtQw8Y-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
  class="gp-group gp-relative option-item gp-child-item-g_fMEtQw8Y" 
>
  <div class="gp-invisible mobile:gp-overflow-hidden mobile:!gp-max-w-[0px] !gp-max-w-[150px] gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-absolute gp-bottom-[calc(100%+20px)] gp-text-[12px] group-hover:gp-visible gp-w-max gp-bg-[#333333] gp-text-[#F9F9F9] mobile:gp-px-[0px] gp-px-[8px] gp-py-[4px] gp-rounded-[8px] gp-mb-[-10px] after:gp-content-[''] after:gp-absolute mobile:after:gp-p-[0px] after:gp-p-[7px] after:gp-left-0 after:gp-w-full after:gp-bottom-[-10px]">
    <p class="gp-text-[#F9F9F9]">{{value | escape}}</p>
    <svg class="gp-absolute gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-z-10 gp-bottom-[-4px]" width="8" height="4" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8 10L0 0L16 1.41326e-06L8 10Z" fill="#333333"/>
    </svg>
  </div>
  <input
    checked="{%- if option.selected_value == value -%}true{% else %}false{%- endif -%}"
    name="{{product.id}}-options-{{option.name | escape}}"
    id="{{product.id}}-g_fMEtQw8Y-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
    value="{{value | escape}}"
    type="radio"
    class="gp-sr-only gp-absolute gp-bottom-0 gp-right-0"
  />
  <span class="gp-sr-only">{{value | escape}}</span>
  <div   
  option-name="{{option.name | escape}}"
  option-name-value="{{value | escape}}"
  data-gp-option-value-id="{{value.id}}"
  class="option-item-inner gp-w-auto gp-h-auto {%- if option_disabled == true -%}{%- endif -%}">
  
    {% case type %}
      {% when "rectangle_list" %}
      <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bg: var(--g-c-bg-3, bg-3);--bs: solid;--bw: 2px 2px 2px 2px;--bc: var(--g-c-line-3, line-3);--c: var(--g-c-text-2, text-2);--shadow: none;--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--h: 45px;
      {%- else -%}
        --bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--shadow: none;--hvr-shadow: none;--hvr-bg: var(--g-c-bg-3, bg-3);--bg: var(--g-c-bg-3, bg-3);--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--hvr-c: var(--g-c-text-2, text-2);--c: var(--g-c-text-2, text-2);--h: 45px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="rectangle_list"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    
      <div class="option-value gp-overflow-hidden gp-relative 
      "
      style="--pl: 16px;--pr: 16px;--pt: 8px;--pb: 8px;">
      
      <span class="gp-text-center gp-g-paragraph-2" >{{value}}</span>
      </div>
    
  </div>
      {% when "color" %}
      
    {% assign colorsString = null %}
    {% assign colors = null %}
    {% for label in labels %}
      {% if label == value %}
        {% assign colorsString = colorStrings[forloop.index0] %}
      {% endif %}
    {% endfor %}
    {% if colorsString != null %}
      {% assign colors = colorsString | split: '($7)' %}
    {% endif %}
    <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2 gp-p-[4px]
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2 gp-p-[4px]
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bs: solid;--bw: 2px 2px 2px 2px;--bc: var(--g-c-line-3, line-3);--shadow: none;--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--h: 45px;--w: 45px;
      {%- else -%}
        --bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;--shadow: none;--hvr-shadow: none;--h: 45px;--w: 45px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="color"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    {%- if colors != null and colors.size > 0 -%} 
      {%- for color in colors -%}
      {% assign backgroundType = "background-color" %}
        {% if color contains "linear-gradient" %}
          {% assign backgroundType = "background-image" %}
        {% endif %}
        <div
          class="gp-relative gp-h-full gp-w-full gp-min-w-fit gp-flex gp-color-circle before:gp-hidden before:gp-absolute before:gp-top-[50%] before:-gp-rotate-45 before:gp-border-t before:gp-z-1 before:gp-content-[''] before:gp-w-full"
          data-test="{{ backgroundType }}: {{ color }}"
          style="{{ backgroundType }}: {{ color }}; 
    {%- if selectedValue == value -%}
      --d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: none;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;
    {%- else -%}
      --d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;
    {%- endif -%}
    "></div>
      {%- endfor -%}
       {% else %} 
      <div class="option-value gp-overflow-hidden gp-relative 
      "
      style="
    {%- if selectedValue == value -%}
      --d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: none;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;
    {%- else -%}
      --d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;
    {%- endif -%}
    ">
      
      <span class="gp-text-center gp-overflow-hidden gp-text-ellipsis gp-px-1 gp-g-paragraph-2" >{{value}}</span>
      </div>
     {%- endif -%}
  </div>
  
      {% when "image_shopify" %}
      
      {% assign imageUrl = null %}
      {% for variant in variants %}
        {% assign valueIncludesSelectedOption = false %}
        {% for item in variant.options %}
          {% if item == value %}
          {% assign valueIncludesSelectedOption = true %}
          {% endif %}
        {% endfor %}
        {% if valueIncludesSelectedOption and variant.featured_image or variant.featured_media%}
          {% unless imageUrl %}
            {% if variant.featured_media %}
              {% assign imageUrl = variant.featured_media.preview_image.src | product_img_url: '200x'  %}
            {% else %}
              {% assign imageUrl = variant.featured_image.src | product_img_url: '200x'  %}
            {% endif %}
          {% endunless %}
        {% endif %}
      {% endfor %}
      <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bg: var(--g-c-bg-3, bg-3);--bs: solid;--bw: 2px 2px 2px 2px;--bc: var(--g-c-line-3, line-3);--shadow: none;--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--h: 64px;--w: 64px;
      {%- else -%}
        --bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--shadow: none;--hvr-shadow: none;--hvr-bg: var(--g-c-bg-3, bg-3);--bg: var(--g-c-bg-3, bg-3);--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--h: 64px;--w: 64px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="image_shopify"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    {%- if imageUrl != null and imageUrl != "" -%} 
      <img style="width: 64px; height: 64px" class="gp-object-cover gp-rounded-none" src="{{imageUrl}}" alt="" />
     {% else %} 
      <div class="option-value gp-overflow-hidden gp-relative gp-flex gp-flex-col gp-justify-center gp-items-center gp-gap-[6px]
      "
      >
      <svg width="15" height="12" viewBox="0 0 15 12" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="2" cy="2" r="2" fill="#D6D6D6"></circle><path d="M0.196854 10.6453L2.40968 8.05946C2.4846 7.97175 2.57719 7.9008 2.68141 7.85124C2.78562 7.80168 2.89913 7.77461 3.01452 7.77181C3.12991 7.76902 3.2446 7.79055 3.3511 7.835C3.45761 7.87945 3.55353 7.94583 3.63262 8.02981L4.66733 9.12714L8.71205 4.29464C8.79123 4.19995 8.89077 4.1243 9.00325 4.07333C9.11573 4.02236 9.23827 3.99737 9.36176 4.00022C9.48524 4.00307 9.6065 4.03369 9.71651 4.08979C9.82651 4.1459 9.92245 4.22605 9.99717 4.32429L14.8329 10.6772C14.9254 10.7989 14.982 10.9441 14.9964 11.0962C15.0107 11.2484 14.9823 11.4016 14.9144 11.5385C14.8464 11.6754 14.7415 11.7907 14.6115 11.8713C14.4815 11.952 14.3316 11.9948 14.1786 11.995L0.822048 12C0.664945 11.9999 0.511148 11.9549 0.378853 11.8703C0.246557 11.7857 0.141299 11.6649 0.0755311 11.5224C0.00976323 11.3799 -0.013762 11.2216 0.00773837 11.0661C0.0292388 10.9107 0.0948652 10.7646 0.196854 10.6453Z" fill="#D6D6D6"></path></svg>
      <span class="gp-text-center gp-overflow-hidden gp-text-ellipsis gp-px-1 gp-g-paragraph-2" >{{value}}</span>
      </div>
     {%- endif -%}
  </div>
  
      {% when "image" %}
      
    {% assign imageUrl = null %}
    {% for label in labels %}
    {% if label == value %}
      {% assign imageUrl = imageUrls[forloop.index0] %}
    {% endif %}
    {% endfor %}
    <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bg: var(--g-c-bg-3, bg-3);--bs: solid;--bw: 2px 2px 2px 2px;--bc: var(--g-c-line-3, line-3);--shadow: none;--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--h: 64px;--w: 64px;
      {%- else -%}
        --bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--shadow: none;--hvr-shadow: none;--hvr-bg: var(--g-c-bg-3, bg-3);--bg: var(--g-c-bg-3, bg-3);--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--h: 64px;--w: 64px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="image"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    {%- if imageUrl != null and imageUrl != "" -%} 
      <img style="width: 64px; height: 64px" class="gp-object-cover gp-rounded-none" src="{{imageUrl}}" alt="" />
     {% else %} 
      <div class="option-value gp-overflow-hidden gp-relative gp-flex gp-flex-col gp-justify-center gp-items-center gp-gap-[6px]
      "
      >
      <svg width="15" height="12" viewBox="0 0 15 12" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="2" cy="2" r="2" fill="#D6D6D6"></circle><path d="M0.196854 10.6453L2.40968 8.05946C2.4846 7.97175 2.57719 7.9008 2.68141 7.85124C2.78562 7.80168 2.89913 7.77461 3.01452 7.77181C3.12991 7.76902 3.2446 7.79055 3.3511 7.835C3.45761 7.87945 3.55353 7.94583 3.63262 8.02981L4.66733 9.12714L8.71205 4.29464C8.79123 4.19995 8.89077 4.1243 9.00325 4.07333C9.11573 4.02236 9.23827 3.99737 9.36176 4.00022C9.48524 4.00307 9.6065 4.03369 9.71651 4.08979C9.82651 4.1459 9.92245 4.22605 9.99717 4.32429L14.8329 10.6772C14.9254 10.7989 14.982 10.9441 14.9964 11.0962C15.0107 11.2484 14.9823 11.4016 14.9144 11.5385C14.8464 11.6754 14.7415 11.7907 14.6115 11.8713C14.4815 11.952 14.3316 11.9948 14.1786 11.995L0.822048 12C0.664945 11.9999 0.511148 11.9549 0.378853 11.8703C0.246557 11.7857 0.141299 11.6649 0.0755311 11.5224C0.00976323 11.3799 -0.013762 11.2216 0.00773837 11.0661C0.0292388 10.9107 0.0948652 10.7646 0.196854 10.6453Z" fill="#D6D6D6"></path></svg>
      <span class="gp-text-center gp-overflow-hidden gp-text-ellipsis gp-px-1 gp-g-paragraph-2" >{{value}}</span>
      </div>
     {%- endif -%}
  </div>
  
      {% else %}
    {% endcase %}
      

     
    
    
  </div>
</label>
        {%- endfor -%}
        {% if type == 'dropdown' %}
        
    <select
    aria-label={{option.name | escape}}
    autocomplete="off"
    id="p-variant-dropdown-{{option.position}}"
    name="{%- if option -%}{{option.name | escape}}{% else %}Select Option{%- endif -%}"
    option-data="{{option.name}}"
    option-type="{{optionType}}"
    option-renderer="{{optionRendered}}"
    class="gp-truncate gp-bg-auto gp-pl-4 gp-pr-6 gp-outline-none dropdown-option-item gp-border-g-line-2 hover:gp-border-g-line-3 gp-g-paragraph-2 active:gp-text-g-text-2 hover:gp-text-g-text-2 gp-text-g-text-2 active:gp-bg-g-bg-3 hover:gp-bg-g-bg-3 gp-bg-g-bg-3 gp-outline-none gp-shadow-none"
 
    style="--shadow:none;--hvr-shadow:none;--bg:var(--g-c-bg-3, bg-3);--bs:solid;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-2, line-2);--hvr-c:var(--g-c-text-2, text-2);--c:var(--g-c-text-2, text-2);--hvr-bg:var(--g-c-bg-3, bg-3);--h:45px;--w:100%;--w-tablet:100%;--w-mobile:100%;--hvr-bs:solid;--hvr-bw:1px 1px 1px 1px;--hvr-bc:var(--g-c-line-3, line-3);--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--hvr-bblr:0px;--hvr-bbrr:0px;--hvr-btlr:0px;--hvr-btrr:0px;appearance:none;background-image:url(https://cdn.shopify.com/s/files/1/1827/4239/t/1/assets/ico-select.svg?v=155563818344741998551488860031);background-repeat:no-repeat;background-position:right 16px center"
  >
  
  {%- for value in values -%}
          {%- liquid
            assign option_disabled = true
            for variantItem in product.variants
              case option.position
                when 1
                  if variantItem.available and variantItem.option1 == value
                    assign option_disabled = false
                  endif
                when 2
                  if variantItem.available and variantItem.option2 == value
                    assign option_disabled = false
                  endif
                when 3
                  if variantItem.available and variantItem.option3 == value
                    assign option_disabled = false
                  endif
              endcase
            endfor
          -%}
          {%- if value == selectedValue -%}
              <option selected 
                  
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}" 
    data-gp-option-value-id="{{value.id}}" 
    data-gp-option-available="{{value.available}}"
    value="{{value | escape}}" 
    class="option-value-wrapper" 
    key="{{value | escape}}"
  
                  option-position="{{option.position}}">
                  {{value}} 
              </option>
              {% else %}
              <option
                  
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}" 
    data-gp-option-value-id="{{value.id}}" 
    data-gp-option-available="{{value.available}}"
    value="{{value | escape}}" 
    class="option-value-wrapper" 
    key="{{value | escape}}"
  
                  option-position="{{option.position}}">
                  {{value}} 
              </option>
              {%- endif -%}
         
        {%- endfor -%}
  </select>
    
        {% endif %}
      {% endif %}
    {% endfor %}

    {% if optionRendered == false %}
      
    {%- for value in values -%}
      
    {%- liquid
      assign option_disabled = true
      for variantItem in product.variants
        case option.position
          when 1
            if variantItem.available and variantItem.option1 == value
              assign option_disabled = false
            endif
          when 2
            if variantItem.available and variantItem.option2 == value
              assign option_disabled = false
            endif
          when 3
            if variantItem.available and variantItem.option3 == value
              assign option_disabled = false
            endif
        endcase
      endfor
    -%}

  {% assign option_disabled = false %}
  <label
  id="{{option.name | escape}}-{{value | escape}}"
  for="{{product.id}}-g_fMEtQw8Y-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
  class="gp-group gp-relative option-item gp-child-item-g_fMEtQw8Y" 
>
  <div class="gp-invisible mobile:gp-overflow-hidden mobile:!gp-max-w-[0px] !gp-max-w-[150px] gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-absolute gp-bottom-[calc(100%+20px)] gp-text-[12px] group-hover:gp-visible gp-w-max gp-bg-[#333333] gp-text-[#F9F9F9] mobile:gp-px-[0px] gp-px-[8px] gp-py-[4px] gp-rounded-[8px] gp-mb-[-10px] after:gp-content-[''] after:gp-absolute mobile:after:gp-p-[0px] after:gp-p-[7px] after:gp-left-0 after:gp-w-full after:gp-bottom-[-10px]">
    <p class="gp-text-[#F9F9F9]">{{value | escape}}</p>
    <svg class="gp-absolute gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-z-10 gp-bottom-[-4px]" width="8" height="4" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8 10L0 0L16 1.41326e-06L8 10Z" fill="#333333"/>
    </svg>
  </div>
  <input
    checked="{%- if option.selected_value == value -%}true{% else %}false{%- endif -%}"
    name="{{product.id}}-options-{{option.name | escape}}"
    id="{{product.id}}-g_fMEtQw8Y-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
    value="{{value | escape}}"
    type="radio"
    class="gp-sr-only gp-absolute gp-bottom-0 gp-right-0"
  />
  <span class="gp-sr-only">{{value | escape}}</span>
  <div   
  option-name="{{option.name | escape}}"
  option-name-value="{{value | escape}}"
  data-gp-option-value-id="{{value.id}}"
  class="option-item-inner gp-w-auto gp-h-auto {%- if option_disabled == true -%}{%- endif -%}">
  
    <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bg: var(--g-c-bg-3, bg-3);--bs: solid;--bw: 2px 2px 2px 2px;--bc: var(--g-c-line-3, line-3);--c: var(--g-c-text-2, text-2);--shadow: none;--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--h: 45px;--w: 80px;
      {%- else -%}
        --bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--shadow: none;--hvr-shadow: none;--hvr-bg: var(--g-c-bg-3, bg-3);--bg: var(--g-c-bg-3, bg-3);--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--hvr-c: var(--g-c-text-2, text-2);--c: var(--g-c-text-2, text-2);--h: 45px;--w: 80px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="rectangle_list"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    
      <div class="option-value gp-overflow-hidden gp-relative 
      "
      style="--pl: 16px;--pr: 16px;--pt: 8px;--pb: 8px;">
      
      <span class="gp-text-center gp-g-paragraph-2" >{{value}}</span>
      </div>
    
  </div>
    
  </div>
</label>
    {%- endfor -%}
    
    {% endif %}
    
          </div>
      </div>
      {%- endfor -%}
    
    </div>
    {%- endif -%}

  </gp-product-variants>
  <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-variant-v3.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
   
      </div>
   {%- liquid
      assign inventory_quantity = variant.inventory_quantity | default: 0
    -%}
    <gp-product-button
      class="gp-product-button"
      gp-data-wrapper="true"
      gp-label-out-of-stock="{{section.settings.gg84gdL8s9__outOfStockLabel}}"
      gp-label-unavailable="{{section.settings.gg84gdL8s9__unavailableLabel}}"
      gp-data='{"setting":{"actionEffect":"open-cart-drawer","errorMessage":"Cannot add product to cart","successMessage":"Add product to cart successfully","enableSuccessMessage":true,"enableErrorMessage":true,"label":"Add to cart","outOfStockLabel":"Out Of Stock","customURL":{"link":"/cart","target":"_self"}},"styles":{"errorTypo":{"attrs":{"color":"error"},"type":"paragraph-2"},"successTypo":{"attrs":{"color":"success"},"type":"paragraph-2"}},"disabled":"{{variant.available}}","variantID":"{{variant.id}}","totalVariant":"{{product.variants.size}}"}' 
       gp-href="{{ request.origin }}{{ routes.root_url | split: '/' | join: '/' }}/cart"
       data-variant-selection-required-message="{{}}"
    >
        
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    class="{% unless variant.available %} !gp-hidden {% endunless %}"
  >
    <style>
    .g84gdL8s9_.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px 0px 0px 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      
    }

    .g84gdL8s9_:hover::before {
      border-style: none;
  border-width: 0px 0px 0px 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .g84gdL8s9_:hover .gp-button-icon {
      color: #f6f6f6;
    }

     .g84gdL8s9_ .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .g84gdL8s9_:hover .gp-button-price {
      color: #f6f6f6;
    }

    .g84gdL8s9_ .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .g84gdL8s9_ .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .g84gdL8s9_:hover .gp-product-dot-price {
      color: #f6f6f6;
    }
  </style>
    <button
      type="submit" data-id="g84gdL8s9_" aria-label="Add to cart"
      name="add" gp-data-hidden="{% unless variant.available %}true{% endunless %}"
      data-state="idle"
      class="g84gdL8s9_ gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-atc tcustomizer-submit-button"
      style="--hvr-bg:#0274F7;--bg:#096de3;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:32px;--pr:32px;--pt:12px;--pb:12px;--pl-tablet:32px;--pr-tablet:32px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:32px;--pr-mobile:32px;--pt-mobile:12px;--pb-mobile:12px;--hvr-c:#f6f6f6;--c:var(--g-c-text-3, text-3);--size:14px;--size-tablet:13px;--size-mobile:12px;--fs:normal;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:600;--ls:normal;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:uppercase"
    >
        <svg
          class="gp-invisible gp-absolute gp-h-5 gp-w-5 group-data-[state=loading]/button:gp-animate-spin group-data-[state=loading]/button:gp-visible"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="gp-opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="gp-opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:14px;--size-tablet:13px;--size-mobile:12px;--hvr-c:#f6f6f6;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.gg84gdL8s9__label }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

        
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    class="{% if variant.available %} !gp-hidden {% endif %}"
  >
    <style>
    .g84gdL8s9_-sold-out.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px 0px 0px 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      
    }

    .g84gdL8s9_-sold-out:hover::before {
      border-style: none;
  border-width: 0px 0px 0px 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .g84gdL8s9_-sold-out:hover .gp-button-icon {
      color: #f6f6f6;
    }

     .g84gdL8s9_-sold-out .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .g84gdL8s9_-sold-out:hover .gp-button-price {
      color: #f6f6f6;
    }

    .g84gdL8s9_-sold-out .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .g84gdL8s9_-sold-out .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .g84gdL8s9_-sold-out:hover .gp-product-dot-price {
      color: #f6f6f6;
    }
  </style>
    <button
      type="button" data-id="g84gdL8s9_" aria-label="{{section.settings.gg84gdL8s9__outOfStockLabel}}"
      gp-data-hidden="{% if variant.available %}true{% endif %}"
      data-state="idle"
      class="g84gdL8s9_-sold-out gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-sold-out btn-disabled gp-opacity-30 gp-cursor-default"
      style="--hvr-bg:#0274F7;--bg:#096de3;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:32px;--pr:32px;--pt:12px;--pb:12px;--pl-tablet:32px;--pr-tablet:32px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:32px;--pr-mobile:32px;--pt-mobile:12px;--pb-mobile:12px;--hvr-c:#f6f6f6;--c:var(--g-c-text-3, text-3);--size:14px;--size-tablet:13px;--size-mobile:12px;--fs:normal;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:600;--ls:normal;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:uppercase"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:14px;--size-tablet:13px;--size-mobile:12px;--hvr-c:#f6f6f6;--c:var(--g-c-text-3, text-3)"
      >
        {{section.settings.gg84gdL8s9__outOfStockLabel}}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

    </gp-product-button>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-button.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
  
    <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--ta:left"
      data-keep-class="true"
      class="gBkzk1f16W "
    ><div></div></div>
  
      <script id="custom-js-gBkzk1f16W">
        try {
          const product = document.getElementById('g6pEIRnPeY');
const hoverImage = product.querySelector('gp-product-images-v2 .zoom-element img');
 hoverImage.setAttribute('src', 'https://www.gardpro.com/cdn/shop/files/HS3-Q4-US-0001.1-SIDE_1080x.png?v=1741761310');
 hoverImage.setAttribute('srcset', '');
        } catch(err){}
      </script>
    
    </div>
    </div>
   
    
          {%- endform -%}
        </product-form>
      </gp-product>
      {%- assign product = gpBkProduct -%}
    {% else %}
      <div class="gp-text-center">{{ "gempages.Product.product_not_found" | t }}</div>
    {%- endif -%}
     <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      <style id="custom-css-g6pEIRnPeY">
        .g6pEIRnPeY {
    
}
      </style>
    
    </div>