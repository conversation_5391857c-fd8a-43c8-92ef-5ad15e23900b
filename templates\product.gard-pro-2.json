/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-product",
      "blocks": {
        "b1fe5681-145e-4e46-9f66-31e684dd0590": {
          "type": "shopify://apps/loox-reviews/blocks/loox-rating/5c3b337f-fd14-4df5-b1d6-80ec13e6e28e",
          "settings": {
            "contentSize": 16,
            "pattern": "",
            "hideText": false,
            "alignment": "",
            "layout": "",
            "textColor": "",
            "starColor": "",
            "showAllReviews": false,
            "looxOpenFloatingWidget": false,
            "emptyRatingWidget": false
          }
        },
        "price": {
          "type": "price",
          "settings": {}
        },
        "f63957b1-12b9-4ddb-b95a-797a3fde5a7e": {
          "type": "text",
          "settings": {
            "text": "The most advanced smartwatch for your health just got a whole lot better."
          }
        },
        "separator": {
          "type": "separator",
          "settings": {}
        },
        "variant_picker": {
          "type": "variant_picker",
          "settings": {
            "variant_labels": true,
            "picker_type": "button",
            "product_dynamic_variants_enable": true,
            "color_swatches": true
          }
        },
        "c02c5e00-a022-4462-934b-dfc75c7fddf2": {
          "type": "sales_point",
          "settings": {
            "icon": "shield",
            "text": "2-year warranty"
          }
        },
        "sales_point": {
          "type": "sales_point",
          "settings": {
            "icon": "globe",
            "text": "Express delivery"
          }
        },
        "b17e64d2-2edb-493d-8bf1-c5ac93790366": {
          "type": "sales_point",
          "settings": {
            "icon": "checkmark",
            "text": "IOS & Android"
          }
        },
        "inventory_status": {
          "type": "inventory_status",
          "settings": {
            "inventory_threshold": 10,
            "inventory_transfers_enable": true
          }
        },
        "buy_buttons": {
          "type": "buy_buttons",
          "settings": {
            "show_dynamic_checkout": false,
            "surface_pickup_enable": false
          }
        },
        "956403fc-73da-4fcf-b9cc-57083fdb7343": {
          "type": "tab",
          "settings": {
            "title": "Specifications",
            "content": "",
            "page": "gard-pro-health-smartwatch-2-specifications"
          }
        },
        "tab": {
          "type": "tab",
          "settings": {
            "title": "Shipping & Returns",
            "content": "<p><strong>UNITED STATES:</strong> 3 - 6 Business days delivery<br/><br/><strong>THE REST OF THE WORLD:</strong> 3 - 6 Business days delivery<br/></p><p>Orders placed before 9 p.m. will be shipped the same day.</p><p><strong>SHIPPING</strong><br/>Gard Pro orders are shipped with DHL, FedEx, and UPS. When your order is shipped, you will receive a Track & Trace number by e-mail.<br/></p><p><strong>RETURNS</strong><br/>We offer a 30-day return period on every order to return or exchange if necessary; for more information, please see our <a href=\"/pages/shipping-returns\" title=\"Shipping & Returns\">full policy</a>.</p>",
            "page": ""
          }
        }
      },
      "block_order": [
        "b1fe5681-145e-4e46-9f66-31e684dd0590",
        "price",
        "f63957b1-12b9-4ddb-b95a-797a3fde5a7e",
        "separator",
        "variant_picker",
        "c02c5e00-a022-4462-934b-dfc75c7fddf2",
        "sales_point",
        "b17e64d2-2edb-493d-8bf1-c5ac93790366",
        "inventory_status",
        "buy_buttons",
        "956403fc-73da-4fcf-b9cc-57083fdb7343",
        "tab"
      ],
      "settings": {
        "badge_label": "",
        "gallery_text": "",
        "sku_enable": false,
        "image_position": "left",
        "image_size": "medium",
        "product_zoom_enable": true,
        "thumbnail_position": "beside",
        "thumbnail_height": "flexible",
        "thumbnail_arrows": false,
        "mobile_layout": "full",
        "enable_video_looping": false,
        "product_video_style": "muted"
      }
    },
    "sub": {
      "type": "product-full-width",
      "disabled": true,
      "settings": {
        "max_width": true
      }
    },
    "e5412bd3-d2c5-495d-bb98-a21ddbe364bc": {
      "type": "slideshow",
      "blocks": {
        "template--16654695989496__e5412bd3-d2c5-495d-bb98-a21ddbe364bc-1672313792ead70c11-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "",
            "title_size": 80,
            "subheading": "",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "image": "shopify://shop_images/1de_f0ef9673-71e6-43a1-9d50-9337b7925152.jpg",
            "image_mobile": "shopify://shop_images/1de_f0ef9673-71e6-43a1-9d50-9337b7925152.jpg",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "template--16654695989496__e5412bd3-d2c5-495d-bb98-a21ddbe364bc-1672313792ead70c11-0"
      ],
      "settings": {
        "section_height": "natural",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": true,
        "autoplay_speed": 5
      }
    },
    "c235d874-1184-47c1-9400-43fc55fe1a25": {
      "type": "slideshow",
      "blocks": {
        "template--16654695989496__c235d874-1184-47c1-9400-43fc55fe1a25-1672313792ead70c11-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "",
            "title_size": 80,
            "subheading": "",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "image": "shopify://shop_images/8de.png",
            "image_mobile": "shopify://shop_images/8de.png",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "template--16654695989496__c235d874-1184-47c1-9400-43fc55fe1a25-1672313792ead70c11-0"
      ],
      "settings": {
        "section_height": "natural",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": true,
        "autoplay_speed": 5
      }
    },
    "c37f9fd9-deaa-400c-bdf0-c7541ac3c4d9": {
      "type": "slideshow",
      "blocks": {
        "template--16654695989496__c37f9fd9-deaa-400c-bdf0-c7541ac3c4d9-1672313792ead70c11-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "",
            "title_size": 80,
            "subheading": "",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "image": "shopify://shop_images/6nl_4d3220eb-722a-491a-969b-83c2d5f40170.jpg",
            "image_mobile": "shopify://shop_images/6nl_4d3220eb-722a-491a-969b-83c2d5f40170.jpg",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "template--16654695989496__c37f9fd9-deaa-400c-bdf0-c7541ac3c4d9-1672313792ead70c11-0"
      ],
      "settings": {
        "section_height": "natural",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": true,
        "autoplay_speed": 5
      }
    },
    "10a825f0-f564-435f-8177-33a7a7f3e384": {
      "type": "slideshow",
      "blocks": {
        "template--16654695989496__10a825f0-f564-435f-8177-33a7a7f3e384-167233279995767a39-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "",
            "title_size": 80,
            "subheading": "",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "image": "shopify://shop_images/8nl_22_a0b1d856-8432-4369-9907-ef02e05d8433.png",
            "image_mobile": "shopify://shop_images/8nl_22.png",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "template--16654695989496__10a825f0-f564-435f-8177-33a7a7f3e384-167233279995767a39-0"
      ],
      "settings": {
        "section_height": "natural",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": true,
        "autoplay_speed": 5
      }
    },
    "915185a0-c03b-4a24-b6d6-f5fd514a192e": {
      "type": "slideshow",
      "blocks": {
        "template--16654695989496__915185a0-c03b-4a24-b6d6-f5fd514a192e-1672313792ead70c11-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "",
            "title_size": 80,
            "subheading": "",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "image": "shopify://shop_images/7nl_19.png",
            "image_mobile": "shopify://shop_images/7nl_19.png",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "template--16654695989496__915185a0-c03b-4a24-b6d6-f5fd514a192e-1672313792ead70c11-0"
      ],
      "settings": {
        "section_height": "natural",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": true,
        "autoplay_speed": 5
      }
    },
    "b9329daf-d9a9-43b3-ac8a-277b9258a8bf": {
      "type": "slideshow",
      "blocks": {
        "template--16654695989496__b9329daf-d9a9-43b3-ac8a-277b9258a8bf-1672313792ead70c11-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "",
            "title_size": 80,
            "subheading": "",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "image": "shopify://shop_images/9nl.jpg",
            "image_mobile": "shopify://shop_images/9nl.jpg",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "template--16654695989496__b9329daf-d9a9-43b3-ac8a-277b9258a8bf-1672313792ead70c11-0"
      ],
      "settings": {
        "section_height": "natural",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": true,
        "autoplay_speed": 5
      }
    },
    "a94d2aaf-7903-4d48-8235-084bf57b085b": {
      "type": "slideshow",
      "blocks": {
        "template--16654695989496__a94d2aaf-7903-4d48-8235-084bf57b085b-167822831544c504d8-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "",
            "title_size": 80,
            "subheading": "",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "image": "shopify://shop_images/7nl.jpg",
            "image_mobile": "shopify://shop_images/7nl.jpg",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "template--16654695989496__a94d2aaf-7903-4d48-8235-084bf57b085b-167822831544c504d8-0"
      ],
      "settings": {
        "section_height": "natural",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": false,
        "autoplay_speed": 5
      }
    },
    "5f75b590-160e-4e64-8ec1-cadd90f2786d": {
      "type": "slideshow",
      "blocks": {
        "template--16654695989496__5f75b590-160e-4e64-8ec1-cadd90f2786d-167233279995767a39-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "",
            "title_size": 80,
            "subheading": "",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "image": "shopify://shop_images/10nl_6.png",
            "image_mobile": "shopify://shop_images/10nl_6.png",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "template--16654695989496__5f75b590-160e-4e64-8ec1-cadd90f2786d-167233279995767a39-0"
      ],
      "settings": {
        "section_height": "natural",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": true,
        "autoplay_speed": 5
      }
    },
    "recently-viewed": {
      "type": "recently-viewed",
      "disabled": true,
      "settings": {
        "recent_count": 5
      }
    },
    "9148d2a1-ea51-4510-974f-078c0016ee1d": {
      "type": "slideshow",
      "blocks": {
        "template--16654695989496__9148d2a1-ea51-4510-974f-078c0016ee1d-16727974657cad4551-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "",
            "title_size": 80,
            "subheading": "",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "image": "shopify://shop_images/11nl.jpg",
            "image_mobile": "shopify://shop_images/11nl.jpg",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "template--16654695989496__9148d2a1-ea51-4510-974f-078c0016ee1d-16727974657cad4551-0"
      ],
      "settings": {
        "section_height": "natural",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": false,
        "autoplay_speed": 5
      }
    },
    "26b12c81-9ba5-4f0b-a27f-86190d435c46": {
      "type": "slideshow",
      "blocks": {
        "template--16654695989496__26b12c81-9ba5-4f0b-a27f-86190d435c46-16727974657cad4551-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "",
            "title_size": 80,
            "subheading": "",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "image": "shopify://shop_images/12nl_8.png",
            "image_mobile": "shopify://shop_images/12nl_8.png",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "template--16654695989496__26b12c81-9ba5-4f0b-a27f-86190d435c46-16727974657cad4551-0"
      ],
      "settings": {
        "section_height": "natural",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": true,
        "autoplay_speed": 5
      }
    },
    "39aa2214-b1b7-464a-b4d6-f090cc4bc3fe": {
      "type": "slideshow",
      "blocks": {
        "template--16654695989496__39aa2214-b1b7-464a-b4d6-f090cc4bc3fe-1672334735a73f8cc8-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "",
            "title_size": 80,
            "subheading": "",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "image": "shopify://shop_images/12eng_8.png",
            "image_mobile": "shopify://shop_images/12eng_8.png",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "template--16654695989496__39aa2214-b1b7-464a-b4d6-f090cc4bc3fe-1672334735a73f8cc8-0"
      ],
      "settings": {
        "section_height": "natural",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": true,
        "autoplay_speed": 5
      }
    },
    "851f8093-032d-4cde-bcc8-fe2333bf7483": {
      "type": "slideshow",
      "blocks": {
        "template--16654695989496__851f8093-032d-4cde-bcc8-fe2333bf7483-1672334735a73f8cc8-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "",
            "title_size": 80,
            "subheading": "",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "image": "shopify://shop_images/12nl_9.png",
            "image_mobile": "shopify://shop_images/12nl_9.png",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "template--16654695989496__851f8093-032d-4cde-bcc8-fe2333bf7483-1672334735a73f8cc8-0"
      ],
      "settings": {
        "section_height": "natural",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": true,
        "autoplay_speed": 5
      }
    },
    "1ac352f2-b4b5-40f0-8deb-cbfb04fbca86": {
      "type": "slideshow",
      "blocks": {
        "template--16713451143416__1ac352f2-b4b5-40f0-8deb-cbfb04fbca86-16788280849cc9c863-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "",
            "title_size": 80,
            "subheading": "",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "image": "shopify://shop_images/14nl_9.png",
            "image_mobile": "shopify://shop_images/14nl_9.png",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "template--16713451143416__1ac352f2-b4b5-40f0-8deb-cbfb04fbca86-16788280849cc9c863-0"
      ],
      "settings": {
        "section_height": "natural",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": false,
        "autoplay_speed": 5
      }
    },
    "8b41982a-0e1e-44b0-9c4b-860cd987386d": {
      "type": "slideshow",
      "blocks": {
        "template--16654695989496__8b41982a-0e1e-44b0-9c4b-860cd987386d-1672334735a73f8cc8-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "",
            "title_size": 80,
            "subheading": "",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "image": "shopify://shop_images/14nl_11.png",
            "image_mobile": "shopify://shop_images/14nl_11.png",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "template--16654695989496__8b41982a-0e1e-44b0-9c4b-860cd987386d-1672334735a73f8cc8-0"
      ],
      "settings": {
        "section_height": "natural",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": true,
        "autoplay_speed": 5
      }
    },
    "17ae2fa4-2c2d-4c77-98d1-89f35e432c37": {
      "type": "slideshow",
      "blocks": {
        "template--16654695989496__17ae2fa4-2c2d-4c77-98d1-89f35e432c37-16763343657276b137-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "",
            "title_size": 80,
            "subheading": "",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "image": "shopify://shop_images/15eng_24.png",
            "image_mobile": "shopify://shop_images/15eng_24.png",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "template--16654695989496__17ae2fa4-2c2d-4c77-98d1-89f35e432c37-16763343657276b137-0"
      ],
      "settings": {
        "section_height": "natural",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": true,
        "autoplay_speed": 5
      }
    },
    "a92e2bb5-af33-47b0-91f1-6681bec12899": {
      "type": "slideshow",
      "blocks": {
        "template--16654695989496__a92e2bb5-af33-47b0-91f1-6681bec12899-167233279995767a39-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "",
            "title_size": 80,
            "subheading": "",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "image": "shopify://shop_images/16nl_7.png",
            "image_mobile": "shopify://shop_images/16nl_7.png",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "template--16654695989496__a92e2bb5-af33-47b0-91f1-6681bec12899-167233279995767a39-0"
      ],
      "settings": {
        "section_height": "natural",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": true,
        "autoplay_speed": 5
      }
    },
    "2344d7dc-39a6-4f99-ba09-954b4d114c72": {
      "type": "slideshow",
      "blocks": {
        "template--16713451143416__2344d7dc-39a6-4f99-ba09-954b4d114c72-16788280849cc9c863-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "",
            "title_size": 80,
            "subheading": "",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "image": "shopify://shop_images/18nl_14.png",
            "image_mobile": "shopify://shop_images/18nl_14.png",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "template--16713451143416__2344d7dc-39a6-4f99-ba09-954b4d114c72-16788280849cc9c863-0"
      ],
      "settings": {
        "section_height": "natural",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": false,
        "autoplay_speed": 5
      }
    },
    "ec118aa0-4947-48b8-b8c5-8077743a2bee": {
      "type": "text-columns",
      "blocks": {
        "template--16654695989496__ec118aa0-4947-48b8-b8c5-8077743a2bee-1672313798afac472d-0": {
          "type": "text_block",
          "settings": {
            "enable_image": true,
            "image": "shopify://shop_images/12345.gif",
            "image_width": 60,
            "title": "Express delivery",
            "text": "<p>We deliver worldwide with, DHL, FEDEX, UPS for the fastest and most reliable service. Free shipping for orders in the US above $50. Order before 9:00 PM.</p>",
            "button_label": "",
            "button_link": ""
          }
        },
        "template--16654695989496__ec118aa0-4947-48b8-b8c5-8077743a2bee-1672313798afac472d-1": {
          "type": "text_block",
          "settings": {
            "enable_image": true,
            "image": "shopify://shop_images/1234.gif",
            "image_width": 60,
            "title": "2 Year Warranty",
            "text": "<p>We believe in the quality of our products, so much so that we offer a 2-year warranty to ensure your continued satisfaction with our Health Smartwatch 2+</p>",
            "button_label": "",
            "button_link": ""
          }
        },
        "template--16654695989496__ec118aa0-4947-48b8-b8c5-8077743a2bee-1672313798afac472d-2": {
          "type": "text_block",
          "settings": {
            "enable_image": true,
            "image": "shopify://shop_images/123.gif",
            "image_width": 60,
            "title": "30 Day Risk-Free Trial",
            "text": "<p>Not sure it’s right for you? Try it for 30 days, completely risk-free. If it’s not exactly what you wanted, return or exchange it—no hassle, no pressure. We’re confident you’ll love it.</p>",
            "button_label": "",
            "button_link": ""
          }
        }
      },
      "block_order": [
        "template--16654695989496__ec118aa0-4947-48b8-b8c5-8077743a2bee-1672313798afac472d-0",
        "template--16654695989496__ec118aa0-4947-48b8-b8c5-8077743a2bee-1672313798afac472d-1",
        "template--16654695989496__ec118aa0-4947-48b8-b8c5-8077743a2bee-1672313798afac472d-2"
      ],
      "settings": {
        "custom_class": "",
        "title": "",
        "align_text": "center",
        "full_width": false,
        "divider": false,
        "enable_slider": true
      }
    },
    "1672353341dc19961e": {
      "type": "apps",
      "blocks": {
        "3c41ac7d-ade6-4e2f-92ac-199bee8fd118": {
          "type": "shopify://apps/loox-reviews/blocks/loox-dynamic-section/5c3b337f-fd14-4df5-b1d6-80ec13e6e28e",
          "settings": {
            "only_photos": false,
            "aggregated": false,
            "limit": 20,
            "hide_thumbnails": false,
            "maxwidth": 1080,
            "is_sample": true
          }
        }
      },
      "block_order": [
        "3c41ac7d-ade6-4e2f-92ac-199bee8fd118"
      ],
      "settings": {
        "full_width": false,
        "space_around": true
      }
    },
    "b824c859-f130-4f02-bd1c-b09acfd66678": {
      "type": "faq",
      "blocks": {
        "template--16654695989496__b824c859-f130-4f02-bd1c-b09acfd66678-1673390250f472f9d3-0": {
          "type": "question",
          "settings": {
            "title": "What's in the box?",
            "text": "<p>• Health Smartwatch 2+ Case<br/><br/>• Health Smartwatch 2+ Loop<br/><br/>• Health Smartwatch 2+ magnetic fast charger to USB cable<br/><br/>• Manual</p>"
          }
        },
        "template--16654695989496__b824c859-f130-4f02-bd1c-b09acfd66678-1673390250f472f9d3-1": {
          "type": "question",
          "settings": {
            "title": "Is it compatible with my phone?",
            "text": "<p>Gard Pro Health Smartwatch 2+ is compatible with any Android device (Android 6 and above) and iPhone (iOS 9 and above).</p>"
          }
        },
        "0d8dab95-e93a-4c84-abbe-3bfcd82ddc31": {
          "type": "question",
          "settings": {
            "title": "What are the dimensions of the Health Smartwatch 2+?",
            "text": "<p>The case is 38 mm wide, 45 mm high and less than 9.9 mm thick.</p>"
          }
        },
        "ebdef205-d44f-4c99-8639-a77e1d18b2f5": {
          "type": "question",
          "settings": {
            "title": "This is NOT a subscription!",
            "text": "<p>This is a one-time purchase. The app that connects to the smartwatch is 100% free and has no subscription. We believe you should be able to track your vitals for free and forever.</p>"
          }
        }
      },
      "block_order": [
        "template--16654695989496__b824c859-f130-4f02-bd1c-b09acfd66678-1673390250f472f9d3-0",
        "template--16654695989496__b824c859-f130-4f02-bd1c-b09acfd66678-1673390250f472f9d3-1",
        "0d8dab95-e93a-4c84-abbe-3bfcd82ddc31",
        "ebdef205-d44f-4c99-8639-a77e1d18b2f5"
      ],
      "settings": {
        "title": "Frequently Asked Questions"
      }
    },
    "product-recommendations": {
      "type": "product-recommendations",
      "settings": {
        "show_product_recommendations": true,
        "product_recommendations_heading": "You might also like this",
        "related_count": 5
      }
    },
    "collection-return": {
      "type": "collection-return",
      "settings": {}
    }
  },
  "order": [
    "main",
    "sub",
    "e5412bd3-d2c5-495d-bb98-a21ddbe364bc",
    "c235d874-1184-47c1-9400-43fc55fe1a25",
    "c37f9fd9-deaa-400c-bdf0-c7541ac3c4d9",
    "10a825f0-f564-435f-8177-33a7a7f3e384",
    "915185a0-c03b-4a24-b6d6-f5fd514a192e",
    "b9329daf-d9a9-43b3-ac8a-277b9258a8bf",
    "a94d2aaf-7903-4d48-8235-084bf57b085b",
    "5f75b590-160e-4e64-8ec1-cadd90f2786d",
    "recently-viewed",
    "9148d2a1-ea51-4510-974f-078c0016ee1d",
    "26b12c81-9ba5-4f0b-a27f-86190d435c46",
    "39aa2214-b1b7-464a-b4d6-f090cc4bc3fe",
    "851f8093-032d-4cde-bcc8-fe2333bf7483",
    "1ac352f2-b4b5-40f0-8deb-cbfb04fbca86",
    "8b41982a-0e1e-44b0-9c4b-860cd987386d",
    "17ae2fa4-2c2d-4c77-98d1-89f35e432c37",
    "a92e2bb5-af33-47b0-91f1-6681bec12899",
    "2344d7dc-39a6-4f99-ba09-954b4d114c72",
    "ec118aa0-4947-48b8-b8c5-8077743a2bee",
    "1672353341dc19961e",
    "b824c859-f130-4f02-bd1c-b09acfd66678",
    "product-recommendations",
    "collection-return"
  ]
}
