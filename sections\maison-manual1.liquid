{% schema %}
  {
    "name": "Manual 1",
    "blocks": [
      {
        "type": "depth1",
        "name": "Main tab",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "default": "Getting Started"
          },
          {
            "type": "textarea",
            "id": "search_keys",
            "label": "Search keys",
            "default": "How to pair\nBattery life\nReset watch",
          }
        ]
      },
      {
        "type": "content1",
        "name": "Content 1",
        "settings": [
          {
            "type": "text",
            "id": "match",
            "label": "match",
            "default": "Getting Started"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "default": "Getting Started"
          },
          {
            "type": "text",
            "id": "subtitle",
            "label": "Subtitle",
            "default": "Welcome to the GARD PRO community! Your GARD PRO Health Smartwatch 2 is designed to help you live healthier, stay active, and stay connected. This manual provides detailed instructions on how to use each feature effectively."
          }
        ]
      },
      {
        "type": "content2",
        "name": "Content 2",
        "settings": [
          {
            "type": "text",
            "id": "match",
            "label": "match",
            "default": "Getting Started"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "default": "Getting Started"
          },
          {
            "type": "text",
            "id": "subtitle",
            "label": "Subtitle",
            "default": "Welcome to the GARD PRO community! Your GARD PRO Health Smartwatch 2 is designed to help you live healthier, stay active, and stay connected. This manual provides detailed instructions on how to use each feature effectively."
          },
          {
            "type": "textarea",
            "id": "blocks",
            "label": "Blocks",
            "default": "Charge:30 minutes minimum",
            "info": "Each block should be on a new line, separated by a colon."
          }
        ]
      },
      {
        "type": "content3",
        "name": "Intro",
        "settings": [
          {
            "type": "text",
            "id": "match",
            "label": "match",
            "default": "Getting Started"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "default": "Getting Started"
          },
          {
            "type": "textarea",
            "id": "blocks",
            "label": "Blocks",
            "default": "1x GARD PRO Health Smartwatch 2",
            "info": "Each block should be on a new line."
          }
        ]
      },
      {
        "type": "content4",
        "name": "Two Cards",
        "settings": [
          {
            "type": "text",
            "id": "match",
            "label": "match",
            "default": "Getting Started"
          },
          {
            "type": "text",
            "id": "icon1",
            "label": "Icon 1",
            "default": "🔌"
          },
          {
            "type": "text",
            "id": "title1",
            "label": "Title 1",
            "default": "Charging"
          },
          {
            "type": "textarea",
            "id": "items1",
            "label": "Items 1",
            "default": "Connect the magnetic charger to the back of the watch."
          },
          {
            "type": "text",
            "id": "icon2",
            "label": "Icon 2",
            "default": "⏻"
          },
          {
            "type": "text",
            "id": "title2",
            "label": "Title 2",
            "default": "POWER ON/OFF"
          },
          {
            "type": "textarea",
            "id": "items2",
            "label": "Items 2",
            "default": "Press and hold the side button for 3 seconds to turn on."
          }
        ]
      },
      {
        "type": "content5",
        "name": "Rich Text",
        "settings": [
          {
            "type": "text",
            "id": "match",
            "label": "match",
            "default": "Getting Started"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "default": "📲 Pairing with GloryFit Pro App"
          },
          {
            "type": "richtext",
            "id": "richtext",
            "label": "Richtext",
            "default": "<p>Richtext</p>"
          }
        ]
      },
      {
        "type": "content6",
        "name": "Three Cards",
        "settings": [
          {
            "type": "text",
            "id": "match",
            "label": "match",
            "default": "Getting Started"
          },
          {
            "type": "text",
            "id": "icon1",
            "label": "Icon 1",
            "default": "❤️"
          },
          {
            "type": "text",
            "id": "title1",
            "label": "Title 1",
            "default": "Heart Rate Measurement"
          },
          {
            "type": "richtext",
            "id": "richtext1",
            "label": "Richtext 1",
            "default": "<p>Richtext</p>"
          },
  
          {
            "type": "text",
            "id": "icon2",
            "label": "Icon 2",
            "default": "🫀"
          },
          {
            "type": "text",
            "id": "title2",
            "label": "Title 2",
            "default": "Blood Pressure & SpO2"
          },
          {
            "type": "richtext",
            "id": "richtext2",
            "label": "Richtext 2",
            "default": "<p>Richtext</p>"
          },
  
          {
            "type": "text",
            "id": "icon3",
            "label": "Icon 3",
            "default": "🌡️"
          },
          {
            "type": "text",
            "id": "title3",
            "label": "Title 3",
            "default": "Body Temperature"
          },
          {
            "type": "richtext",
            "id": "richtext3",
            "label": "Richtext 3",
            "default": "<p>Richtext</p>"
          }
        ]
      },
      {
        "type": "content7",
        "name": "Troubleshoot",
        "settings": [
          {
            "type": "text",
            "id": "match",
            "label": "match",
            "default": "Getting Started"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "default": "Watch won't connect to phone"
          },
          {
            "type": "richtext",
            "id": "richtext",
            "label": "Richtext",
            "default": "<p>Richtext</p>"
          }
        ]
      },
      {
        "type": "content8",
        "name": "Video",
        "settings": [
          {
            "type": "text",
            "id": "match",
            "label": "match",
            "default": "Getting Started"
          },
          {
            "type": "video",
            "id": "video",
            "label": "Video",
            "info": "Upload a video file"
          },
          {
            "type": "checkbox",
            "id": "controls",
            "label": "Controls",
            "default": true
          },
          {
            "type": "checkbox",
            "id": "autoplay",
            "label": "Autoplay",
            "default": false
          },
          {
            "type": "checkbox",
            "id": "loop",
            "label": "Loop",
            "default": false
          },
          {
            "type": "checkbox",
            "id": "muted",
            "label": "Muted",
            "default": false
          },
          {
            "type": "checkbox",
            "id": "playsinline",
            "label": "Playsinline",
            "default": false
          },
          {
            "type": "image_picker",
            "id": "image",
            "label": "Image",
            "info": "Upload an image"
          },
          {
            "type": "range",
            "id": "desktop_width",
            "label": "Desktop image/video width in percent",
            "default": 100,
            "min": 1,
            "max": 100,
            "step": 1
          }
        ]
      }
    ],
    "settings": [
      {
        "type": "checkbox",
        "id": "hide_search",
        "label": "Hide search",
        "default": true
      },
      {
        "type": "text",
        "id": "selector_title",
        "label": "Selector title",
        "default": "First, Select your watch"  
      },
      {
        "type": "header",
        "content": "Tab 1"
      },
      {
        "type": "text",
        "id": "tab_1_title",
        "label": "Tab 1 title",
        "default": "Health Series"
      },
      {
        "type": "text",
        "id": "model_1_name",
        "label": "Model 1 Name",
        "default": "Health Smartwatch 2"
      },
      {
        "type": "url",
        "id": "link_1",
        "label": "Model 1 Link"
      },
      {
        "type": "text",
        "id": "model_2_name",
        "label": "Model 2 Name",
        "default": "Health Smartwatch 2+"
      },
      {
        "type": "url",
        "id": "link_2",
        "label": "Model 2 Link"
      },
      {
        "type": "text",
        "id": "model_3_name",
        "label": "Model 3 Name",
        "default": "Health Smartwatch 3"
      },
      {
        "type": "url",
        "id": "link_3",
        "label": "Model 3 Link"
      },
      {
        "type": "header",
        "content": "Tab 2"
      },
      {
        "type": "text",
        "id": "tab_2_title",
        "label": "Tab 2 title",
        "default": "Ultra series"
      },
      {
        "type": "text",
        "id": "model_4_name",
        "label": "Model 4 Name",
        "default": "GARD PRO Ultra"
      },
      {
        "type": "url",
        "id": "link_4",
        "label": "Model 4 Link"
      },
      {
        "type": "text",
        "id": "model_5_name",
        "label": "Model 5 Name",
        "default": "GARD PRO Ultra 2+"
      },
      {
        "type": "url",
        "id": "link_5",
        "label": "Model 5 Link"
      },
      {
        "type": "text",
        "id": "model_6_name",
        "label": "Model 6 Name",
        "default": "GARD PRO Ultra 3"
      },
      {
        "type": "url",
        "id": "link_6",
        "label": "Model 6 Link"
      },
    ]
  }
  {% endschema %}
  
  <style>
    .video-container video,
    .image-container img {
      width: 100%;
      border-radius: 8px;
    }
  
    {% if section.settings.hide_search %}
      #shopify-section-{{ section.id }} button[aria-label="Open search"] {
        display: none
      }
    {% endif %}
  </style>
  
  <script>
    {% assign depth1 = section.blocks | where: "type", "depth1" %}
    const tabs = [
      {% for block in depth1 %}
        {
          id: "{{ block.settings.title | handleize }}",
          name: "{{ block.settings.title | strip }}",
          searchKeys: [
            {% assign search_keys = block.settings.search_keys | downcase | newline_to_br | split: '<br />' | compact %}
            {% for search_key in search_keys %}
              "{{ search_key | strip }}",
            {% endfor %}
          ],
        },
      {% endfor %}
    ]
  </script>
  
  <div>
    <!-- Header -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <div class="logo">
            <img src="{{ 'logo-gardpro.png' | asset_url }}" alt="GARD PRO Manual Logo">
          </div>
          <div class="header-actions">
            <button
              class="theme-toggle"
              @click="toggleTheme()"
              :aria-label="darkMode ? 'Switch to light mode' : 'Switch to dark mode'"
            >
              <svg
                x-show="!darkMode"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <circle cx="12" cy="12" r="5"></circle>
                <line x1="12" y1="1" x2="12" y2="3"></line>
                <line x1="12" y1="21" x2="12" y2="23"></line>
                <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                <line x1="1" y1="12" x2="3" y2="12"></line>
                <line x1="21" y1="12" x2="23" y2="12"></line>
                <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
              </svg>
              <svg
                x-show="darkMode"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
              </svg>
            </button>
            <button
              class="theme-toggle"
              style="font-size:15px;gap:4px;"
              @click="showSearchPopup = true"
              aria-label="Open search"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.35-4.35"></path>
              </svg>
              Search
            </button>
          </div>
        </div>
      </div>
    </header>
  
    {% render 'maison-manual-search' %}
  
    <!-- Product Selector -->
    <section class="product-selector" x-data="{ 
      manualTab: null,
      isHealthActive() {
        return this.manualTab ? this.manualTab === 'health' : {% unless request.path == section.settings.link_4 or request.path == section.settings.link_5 or request.path == section.settings.link_6 %}true{% else %}false{% endunless %};
      },
      isUltraActive() {
        return this.manualTab ? this.manualTab === 'ultra' : {% if request.path == section.settings.link_4 or request.path == section.settings.link_5 or request.path == section.settings.link_6 %}true{% else %}false{% endif %};
      },
      showHealthModels() {
        return this.manualTab ? this.manualTab === 'health' : {% if request.path == section.settings.link_1 or request.path == section.settings.link_2 or request.path == section.settings.link_3 or request.path != section.settings.link_4 and request.path != section.settings.link_5 and request.path != section.settings.link_6 %}true{% else %}false{% endif %};
      },
      showUltraModels() {
        return this.manualTab ? this.manualTab === 'ultra' : {% if request.path == section.settings.link_4 or request.path == section.settings.link_5 or request.path == section.settings.link_6 %}true{% else %}false{% endif %};
      }
    }">
      <div class="container">
        <div class="product-selector-content">
          <p style="font-size:14px;color:var(--text-secondary);margin-bottom:10px;">{{ section.settings.selector_title }}</p>
          <div class="series-tabs">
            <button
              class="series-tab"
              :class="{ active: isHealthActive() }"
              @click="manualTab = 'health'"
            >
              {{ section.settings.tab_1_title }}
            </button>
            <button
              class="series-tab"
              :class="{ active: isUltraActive() }"
              @click="manualTab = 'ultra'"
            >
              {{ section.settings.tab_2_title }}
            </button>
          </div>
          <div class="model-grid">
            <!-- Health Series Models (Links 1-3) -->
            <div x-show="showHealthModels()" class="model-grid">
              {% if section.settings.link_1 != blank %}
                <a href="{{ section.settings.link_1 }}" class="model-card{% if request.path == section.settings.link_1 %} selected{% endif %}">
                  <div class="model-name">{{ section.settings.model_1_name }}</div>
                </a>
              {% endif %}
              {% if section.settings.link_2 != blank %}
                <a href="{{ section.settings.link_2 }}" class="model-card{% if request.path == section.settings.link_2 %} selected{% endif %}">
                  <div class="model-name">{{ section.settings.model_2_name }}</div>
                </a>
              {% endif %}
              {% if section.settings.link_3 != blank %}
                <a href="{{ section.settings.link_3 }}" class="model-card{% if request.path == section.settings.link_3 %} selected{% endif %}">
                  <div class="model-name">{{ section.settings.model_3_name }}</div>
                </a>
              {% endif %}
            </div>
            
            <!-- Ultra Series Models (Links 4-6) -->
            <div x-show="showUltraModels()" class="model-grid">
              {% if section.settings.link_4 != blank %}
                <a href="{{ section.settings.link_4 }}" class="model-card{% if request.path == section.settings.link_4 %} selected{% endif %}">
                  <div class="model-name">{{ section.settings.model_4_name }}</div>
                </a>
              {% endif %}
              {% if section.settings.link_5 != blank %}
                <a href="{{ section.settings.link_5 }}" class="model-card{% if request.path == section.settings.link_5 %} selected{% endif %}">
                  <div class="model-name">{{ section.settings.model_5_name }}</div>
                </a>
              {% endif %}
              {% if section.settings.link_6 != blank %}
                <a href="{{ section.settings.link_6 }}" class="model-card{% if request.path == section.settings.link_6 %} selected{% endif %}">
                  <div class="model-name">{{ section.settings.model_6_name }}</div>
                </a>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </section>
  
    <!-- Navigation -->
    <nav class="nav-section">
      <div class="container">
        <div class="nav-tabs">
          <template x-for="tab in tabs" :key="tab.id">
            <button
              class="nav-tab"
              :class="{ active: activeTab === tab.id }"
              @click="setActiveTab(tab.id)"
              x-text="tab.name"
            ></button>
          </template>
        </div>
        <!-- Mobile Dropdown -->
        <select
          class="nav-dropdown"
          x-model="activeTab"
          @change="setActiveTab($event.target.value)"
        >
          <template x-for="tab in tabs" :key="tab.id">
            <option :value="tab.id" x-text="tab.name"></option>
          </template>
        </select>
      </div>
    </nav>
  
    <!-- Main Content -->
    <main class="container">
      <!-- Getting Started Section -->
  
      <section class="content-section active">
        {% for block in section.blocks %}
          {% if block.settings.match == blank %}
            {% continue %}
          {% endif %}
  
          <div x-show="activeTab === '{{ block.settings.match | handleize }}'">
            {% case block.type %}
              {% when 'content1' %}
                <div class="section-header" {{block.shopify_attributes}}>
                  <h1>{{ block.settings.title }}</h1>
                  <p class="section-subtitle">
                    {{ block.settings.subtitle }}
                  </p>
                </div>
              {% when 'content2' %}
                <div class="quick-ref-card" {{block.shopify_attributes}}>
                  <div class="quick-ref-content">
                    <h2>{{ block.settings.title }}</h2>
                    <p>{{ block.settings.subtitle }}</p>
                    <div class="quick-ref-grid">
                      {% assign items = block.settings.blocks | newline_to_br | split: '<br />' | compact %}
                      {% for item in items %}
                        <div class="quick-ref-item">
                          <strong>{{ item | split: ':' | first }}</strong>
                          <p>{{ item | split: ':' | last }}</p>
                        </div>
                      {% endfor %}
                    </div>
                  </div>
                </div>
              {% when 'content3' %}
                <div class="quick-ref-card" {{block.shopify_attributes}}>
                  <div class="quick-ref-content">
                    <h2>{{ block.settings.title }}</h2>
                    <div class="quick-ref-grid">
                      {% assign items = block.settings.blocks | newline_to_br | split: '<br />' | compact %}
                      {% for item in items %}
                        <div class="quick-ref-item">
                          <p>{{ item }}</p>
                        </div>
                      {% endfor %}
                    </div>
                  </div>
                </div>
              {% when 'content4' %}
                <div class="feature-grid" {{block.shopify_attributes}}>
                  <div class="feature-card">
                    <div class="feature-icon">{{ block.settings.icon1 }}</div>
                    <h3 class="feature-title">{{ block.settings.title1 }}</h3>
                    <p class="feature-description"></p>
                    <ol>
                      {% assign items = block.settings.items1 | newline_to_br | split: '<br />' | compact %}
                      {% for item in items %}
                        <li>{{ item }}</li>
                      {% endfor %}
                    </ol>
                    <p></p>
                  </div>  
  
                  <div class="feature-card">
                    <div class="feature-icon">{{ block.settings.icon2 }}</div>
                    <h3 class="feature-title">{{ block.settings.title2 }}</h3>
                    <p class="feature-description"></p>
                    <ol>
                      {% assign items = block.settings.items2 | newline_to_br | split: '<br />' | compact %}
                      {% for item in items %}
                        <li>{{ item }}</li>
                      {% endfor %}
                    </ol>
                    <p></p>
                  </div>
                </div>
  
              {% when 'content5' %}
                <div class="card" {{block.shopify_attributes}}>
                  <h3>{{ block.settings.title }}</h3>
                  <div class="richtext-container">
                    {{ block.settings.richtext }}
                  </div>
                </div>
  
              {% when 'content6' %}
                <div class="feature-grid" {{block.shopify_attributes}}>
                  <div class="feature-card">
                    <div class="feature-icon">{{ block.settings.icon1 }}</div>
                    <h3 class="feature-title">{{ block.settings.title1 }}</h3>
                    <div class="richtext-container">
                      {{ block.settings.richtext1 }}
                    </div>
                  </div>
  
                  <div class="feature-card">
                    <div class="feature-icon">{{ block.settings.icon2 }}</div>
                    <h3 class="feature-title">{{ block.settings.title2 }}</h3>
                    <div class="richtext-container">
                      {{ block.settings.richtext2 }}
                    </div>
                  </div>
  
                  <div class="feature-card">
                    <div class="feature-icon">{{ block.settings.icon3 }}</div>
                    <h3 class="feature-title">{{ block.settings.title3 }}</h3>
                    <div class="richtext-container">
                      {{ block.settings.richtext3 }}
                    </div>
                  </div>
                </div>
  
              {% when 'content7' %}
                <div class="troubleshoot-card" x-data="{ show: false }" {{block.shopify_attributes}}>
                  <div class="troubleshoot-header" @click="show = !show">
                    <span class="troubleshoot-title">{{ block.settings.title }}</span>
                    <svg
                      class="troubleshoot-icon rotate"
                      :class="{ rotate: show }"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                  </div>
                  <div class="troubleshoot-content richtext-container" :class="{ expanded: show }">
                    {{ block.settings.richtext }}
                  </div>
                </div>
              {% when 'content8' %}
                {% if block.settings.video != blank %}
                  <div class="video-container" {{block.shopify_attributes}}>
                    {{
                      block.settings.video
                      | video_tag:
                        controls: block.settings.controls,
                        autoplay: block.settings.autoplay,
                        loop: block.settings.loop,
                        muted: block.settings.muted,
                        playsinline: block.settings.playsinline,
                        image_size: '1000x'
                    }}
                  </div>
                {% endif %}
                {% if block.settings.image != blank %}
                  <div class="image-container" {{block.shopify_attributes}}>
                    <img src="{{ block.settings.image | image_url: width: 1000 }}" alt="{{ block.settings.image.alt }}">
                  </div>
                {% endif %}
  
                <style>
                  @media (min-width: 900px) {
                    .video-container video,
                    .image-container img {
                      width: {{ block.settings.desktop_width }}%;
                    }
                  }
                </style>
            {% endcase %}
          </div>
        {% endfor %}
      </section>
    </main>
  </div>
  
  <script src="{{ 'maison-manual1.js' | asset_url }}"></script>
  {{ 'maison-manual1.css' | asset_url | stylesheet_tag }}
  
  <style>
    .richtext-container {
      color: var(--text-secondary);
      max-width: 600px;
    }
  
    .richtext-container ul {
      margin-left: 32px;
    }
  
    .richtext-container li {
      margin-bottom: 8px;
    }
  
    .richtext-container > * {
      padding-top: 8px;
    }

    .model-card {
      text-decoration: none
    }
  </style>
  