

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-555417746817418106.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-555417746817418106.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-555417746817418106.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-555417746817418106.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-555417746817418106.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-555417746817418106.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-555417746817418106.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-555417746817418106.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-555417746817418106.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-555417746817418106.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-555417746817418106.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-555417746817418106.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-555417746817418106.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-555417746817418106.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-555417746817418106.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-555417746817418106.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-555417746817418106.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-555417746817418106.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-555417746817418106.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-555417746817418106.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-555417746817418106.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-555417746817418106.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-555417746817418106.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-555417746817418106.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-555417746817418106.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-555417746817418106.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-555417746817418106.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-555417746817418106.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-555417746817418106.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-555417746817418106.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-555417746817418106.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-555417746817418106.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-555417746817418106.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-555417746817418106.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-555417746817418106.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-555417746817418106.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-555417746817418106.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-555417746817418106.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-555417746817418106.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-555417746817418106.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-555417746817418106.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-555417746817418106.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-555417746817418106 .gp-relative{position:relative}.gps-555417746817418106 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-555417746817418106 .gp-mb-0{margin-bottom:0}.gps-555417746817418106 .gp-flex{display:flex}.gps-555417746817418106 .gp-grid{display:grid}.gps-555417746817418106 .gp-contents{display:contents}.gps-555417746817418106 .\!gp-hidden{display:none!important}.gps-555417746817418106 .gp-hidden{display:none}.gps-555417746817418106 .gp-h-auto{height:auto}.gps-555417746817418106 .gp-h-full{height:100%}.gps-555417746817418106 .gp-w-full{width:100%}.gps-555417746817418106 .gp-max-w-full{max-width:100%}.gps-555417746817418106 .gp-flex-none{flex:none}.gps-555417746817418106 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-555417746817418106 .gp-flex-col{flex-direction:column}.gps-555417746817418106 .gp-gap-y-0{row-gap:0}.gps-555417746817418106 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555417746817418106 .gp-duration-200{transition-duration:.2s}.gps-555417746817418106 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-555417746817418106 .tablet\:\!gp-hidden{display:none!important}.gps-555417746817418106 .tablet\:gp-hidden{display:none}.gps-555417746817418106 .tablet\:gp-h-auto{height:auto}.gps-555417746817418106 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-555417746817418106 .mobile\:\!gp-hidden{display:none!important}.gps-555417746817418106 .mobile\:gp-hidden{display:none}.gps-555417746817418106 .mobile\:gp-h-auto{height:auto}.gps-555417746817418106 .mobile\:gp-flex-none{flex:none}}.gps-555417746817418106 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gAYUiGF7jd" data-id="gAYUiGF7jd"
        style="--blockPadding:base;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-2xl);--pb:var(--g-s-2xl);--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gAYUiGF7jd gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="ghR3OSE_PG gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gU9sTt78Re"
    role="presentation"
    class="gp-group/image gU9sTt78Re gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/SMARTWATCHES_01_2_768x.png?v=1690648026" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/SMARTWATCHES_01_2_768x.png?v=1690648026" />
      <source media="(max-width: 1024px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/SMARTWATCHES_01_2_1024x.png?v=1690648026" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/SMARTWATCHES_01_2_1024x.png?v=1690648026" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/SMARTWATCHES_01_2.png?v=1690648026"
        data-src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/SMARTWATCHES_01_2.png?v=1690648026"
        width="100%"
        alt=""
        style="--aspect:auto;--aspect-tablet:auto;--aspect-mobile:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--radiusType:none;--shadow:none"
      />
    
  </picture>
      </div>
  </div>
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 9",
    "tag": "section",
    "class": "gps-555417746817418106 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555417746733532026&sectionId=555417746817418106)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
