{%- if settings.cart_type == 'drawer' -%}
  <div id="CartDrawer" class="drawer drawer--right">
    <form id="CartDrawerForm" action="{{ routes.cart_url }}" method="post" novalidate class="drawer__contents">
      <div class="drawer__fixed-header">
        <div class="drawer__header appear-animation appear-delay-1">
          <div class="h2 drawer__title">{{ 'cart.general.title' | t }}</div>
          <div class="drawer__close">
            <button type="button" class="drawer__close-button js-drawer-close">
              <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-close" viewBox="0 0 64 64"><path d="M19 17.61l27.12 27.13m0-27.12L19 44.74"/></svg>
              <span class="icon__fallback-text">{{ 'cart.general.close_cart' | t }}</span>
            </button>
          </div>
        </div>
      </div>

      <div class="drawer__inner">
        <div class="drawer__scrollable">
          <div data-products class="appear-animation appear-delay-2"></div>

          {% if settings.cart_notes_enable %}
            <div class="appear-animation appear-delay-3">
              <label for="CartNoteDrawer">{{ 'cart.general.note' | t }}</label>
              <textarea name="note" class="input-full cart-notes" id="CartNoteDrawer">{{ cart.note }}</textarea>
            </div>
          {% endif %}
        </div>

        <div class="drawer__footer appear-animation appear-delay-4">
          <div data-discounts>
            {% if cart.cart_level_discount_applications != blank %}
              <div class="cart__discounts cart__item-sub cart__item-row">
                <div>{{ 'cart.general.discounts' | t }}</div>
                <div class="text-right">
                  {% for cart_discount in cart.cart_level_discount_applications %}
                    <div class="cart__discount">
                      {{ cart_discount.title }} (-{{ cart_discount.total_allocated_amount | money }})
                    </div>
                  {% endfor %}
                </div>
              </div>
            {% endif %}
          </div>

          <div class="cart__item-sub cart__item-row">
            <div class="ajaxcart__subtotal">{{ 'cart.general.subtotal' | t }}</div>
            <div data-subtotal>{{ cart.total_price | money }}</div>
          </div>

          <div class="cart__item-row text-center">
            <small>
              {{ 'cart.general.shipping_at_checkout' | t }}<br />
            </small>
          </div>

          {% if settings.cart_terms_conditions_enable %}
            <div class="cart__item-row cart__terms">
              <input type="checkbox" id="CartTermsDrawer" class="cart__terms-checkbox">
              <label for="CartTermsDrawer">
                {% if settings.cart_terms_conditions_page != blank %}
                  {{ 'cart.general.terms_html' | t: url: settings.cart_terms_conditions_page.url }}
                {% else %}
                  {{ 'cart.general.terms' | t }}
                {% endif %}
              </label>
            </div>
          {% endif %}

          <div class="cart__checkout-wrapper">
            <button type="submit" name="checkout" data-terms-required="{{ settings.cart_terms_conditions_enable }}" class="btn cart__checkout">
              {{ 'cart.general.checkout' | t }}
            </button>

            {% if additional_checkout_buttons and settings.cart_additional_buttons %}
              <div class="additional-checkout-buttons additional-checkout-buttons--vertical">{{ content_for_additional_checkout_buttons }}</div>
            {% endif %}
          </div>
        </div>
      </div>

      <div class="drawer__cart-empty appear-animation appear-delay-2">
        <div class="drawer__scrollable">
          {{ 'cart.general.empty' | t }}
        </div>
      </div>
    </form>
  </div>
{%- endif -%}
