/* variant label radius  */

.variant-input-wrap .variant-mt {
  border-radius: 7px;
}

.custom-table-section{
  padding-bottom: 50px;
}
.custom-table-section .section-stack__main{
 width: 100%; 
}
.custom-table-section .section-stack__intro .h2{
  text-transform: capitalize;
  text-align: left;
  font-size: 2rem;
  margin: 0;
}
.custom-table-section .section-stack__intro{
  width: 100%;
}
.custom-table-section .section {
    padding-block-start: 3.5rem;
    padding-block-end: 3.5rem;
}
  .custom-table-section .feature-chart__product {
    width: 80px;
    gap: 1.25rem;
    place-content: start;
    display: grid;
    position: relative;
} 

.custom-table-section .feature-chart__product :is(img,svg) {
    width: 150px;
}

.custom-table-section .section-stack {
    gap: 3rem 4rem;
    grid-auto-columns: minmax(0, 1fr);
    display: grid;
}
/* .custom-table-section .bg-custom {
    background: rgb(243, 243, 243);
} */
/* .custom-table-section .text-custom {
    color: rgb(17,17,17);
} */
.custom-table-section .feature-chart {
    padding: 0 2rem;
    /* scroll-padding-top: var(--sticky-area-height); */
    display: block;
}
.custom-table-section .scroll-area {
    scrollbar-width: none;
    scroll-snap-type: x mandatory;
    overscroll-behavior-x: contain;
    overflow: auto hidden;
}
.custom-table-section .feature-chart__table-row {
    --feature-chart-heading-width: 140px;
    grid-template-columns: 180px repeat(4, minmax(140px, 1fr));
    gap: 0.25rem 1.25rem;
    min-width: min-content;
    padding-block-start: 1rem;
    padding-block-end: 1rem;
    display: grid;
}
.custom-table-section img {
    color: #0000;
    max-width: min(var(--image-mobile-max-width, var(--image-max-width, 100%)), 100%);
    object-fit: inherit;
    object-position: inherit;
    height: auto;
}

.custom-table-section .v-stack {
    display: grid;
      gap: 1.5rem;
}
.custom-table-section .line-clamp {
    -webkit-line-clamp: var(--line-clamp-count, 2);
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  min-height: 43px;
}
.custom-table-section .bold {
    font-weight: 700;
}
.custom-table-section .price-list {
    align-items: baseline;
    gap: 0.125rem 0.5rem;
    flex-wrap: wrap;
    display: flex;
}
.custom-table-section .text-on-sale {
    color: #000000;
}
.custom-table-section .text-on-sale .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
}
.custom-table-section .text-subdued .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
}
.custom-table-section .line-through {
    text-decoration: line-through;
}
/* .custom-table-section .text-subdued {
    /* color: rgb(17 17 17 / .7); */
  opacity: .7;
} */
.custom-table-section .gap-2 {
    gap: 0.5rem;
}
.custom-table-section .h-stack {
    align-items: center;
    display: flex;
}
.custom-table-section .feature-chart__product .color-swatch {
    margin: 0;
}
.custom-table-section .rounded-full {
    border-radius: 9999px;
}
.custom-table-section .color-swatch--sm {
    --swatch-size: 0.875;
    --swatch-offset: 2px;
}
.custom-table-section a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
}
.custom-table-section .color-swatch {
    --swatch-margin: calc(2px * 2);
    width:0.875rem;
    height:0.875rem;
    background: linear-gradient(to right,,) center / cover;
    flex-shrink: 0;
    place-items: center;
    display: grid;
    position: relative;
}
.custom-table-section .color-swatch:before {
    content: "";
    inset: calc(-1* 2px);
    opacity: 0;
    border-radius: inherit;
    will-change: transform;
    transition: opacity .2s ease-in-out, transform .2s ease-in-out;
    position: absolute;
    transform: scale(.75);
    box-shadow: 0 0 0 2px;
}
.custom-table-section .justify-self-start {
    justify-self: start;
}
.custom-table-section .custom-button{
    display: flex;
    padding: 16px 45px 16px 16px;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
    position: relative;
    border-radius: 56px;
}
/* .custom-table-section .custom-button {
    background: #1111111a !important; 
    color: #111111 !important;
    --button-outline-color: #111111 !important;
} */
.custom-table-section .custom-button:after{
    content: "";
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background-image: url(https://cdn.shopify.com/s/files/1/0664/9810/1416/files/button_bg_arrow.svg?v=1721050588);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}
.custom-table-section .divide-y>:not([hidden])~:not([hidden]) {
    border-top: 1px solid #1111111f;
}
@media screen and (min-width: 1150px) {
   .custom-table-section .section-stack--horizontal {
        justify-content: center;
        display: flex;
    }
}
@media screen and (min-width: 1000px) {
    .custom-table-section .md\:block {
        display: block;
    }
     .custom-table-section .feature-chart__product {
        width: 100%;
        max-width: 260px;
        
    }

  
   .custom-table-section .feature-chart {
        padding: 1rem 3rem;
    }
   
    .custom-table-section .feature-chart__table-row {
        --feature-chart-heading-width: 180px;
        column-gap: 1.5rem;
        padding-block-start: 1.5rem;
        padding-block-end: 1.5rem;
    }
    .custom-table-section .feature-chart__table-row--product {
        padding-block-end: 2.5rem;
    }
}

@media screen and (max-width: 1000px){
  .custom-table-section .custom-button{
    padding: 10px 40px 10px 10px;
}
  .custom-table-section .custom-button:after{
    width: 20px;
    height: 20px;
}
  .custom-table-section .feature-chart__product :is(img,svg) {
    width: 80px;
}
}
@media screen and (min-width: 700px) {
  .custom-table-section .feature-chart__product {
        width: 100%;
        max-width: 260px;
        
    }
 

    .custom-table-section .custom-button{
        padding-block-start: 0.75rem;
        padding-block-end: 0.75rem;
        padding-inline-start: 1rem;
        padding-inline-end: 2.6rem;
      font-weight: 600;
    }
     .custom-table-section .sm\:gap-0\.5 {
        gap: 0.125rem;
    }
     #shopify-section-template--22498217492809__comparison_table_XcUfqe .custom-table-section .section-stack {
        flex-wrap: wrap;
        justify-content: start;
    }
       #shopify-section-template--22498217492809__comparison_table_XcUfqe .custom-table-section .section-stack__intro {
        /* flex: 1 0 350px;
        max-width: 750px; */
        width: 100%;
    }
    .custom-table-section .section-stack__intro .h2 {
       text-transform: capitalize;
    }
      #shopify-section-template--22498217492809__comparison_table_XcUfqe .custom-table-section .section-stack__main {
        /* flex: 1 1 auto;
        min-width: 450px; */
        width: 100%;
    }  
     .custom-table-section .feature-chart  {
        border-radius: 0.75rem;
    }
     .custom-table-section .feature-chart__table-row--product>:first-child {
        grid-column-start: 2;
    }
 .custom-table-section img {
        max-width: min(var(--image-max-width, 100%), 100%);
    }
} 




@media screen and (max-width: 699px) {
    /* .custom-table-section .feature-chart, .feature-chart__table {
        margin-inline-start: calc(-1* 1.25rem);
        margin-inline-end: calc(-1* 1.25rem);
        padding-inline-start: 1.25rem;
        padding-inline-end: 1.25rem;
        display: grid;
    } */
  .custom-table-section .section-stack__main  {
        padding: 0;
    }
     .custom-table-section .feature-chart__heading {
        position: sticky;
    }
     .custom-table-section .feature-chart__heading:not(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi)) {
        left: 0;
    }
     .custom-table-section .feature-chart__table--multi-columns .feature-chart__table-row {
        grid-template-columns: repeat(4, 150px);
    }
     .custom-table-section .feature-chart__value {
        grid-row: 2;
    }
   .custom-table-section .feature-chart__product {
        width: 100%;
        max-width: 260px;
        
    }
}


/* collection page  */
 .custom-collection-grid__wrapper .collection-filter .btn{
  border-radius: 0px;
  font-family: var(--typeBasePrimary), var(--typeBaseFallback);
}
.custom-collection-grid__wrapper .collection-filter select{
  border-radius: 0px;
} 


/* mega menu */
.megamenu .grid__item .h5{
  letter-spacing: 0.2em;
    font-size: 1.24em;
  line-height: 1.8rem;
}
.custom-header .megamenu .site-nav__dropdown-link:not(.site-nav__dropdown-link--top-level) {
    font-size: calc(var(--typeBaseSize) - -1px);
    line-height: 1.5;
}

/* product page  */
.custom-product-section .product-block--header {
    margin-bottom: 14px;
}
.custom-product-section .product-single__meta .shopify-block.shopify-app-block {
    margin-bottom: 7px;
}
