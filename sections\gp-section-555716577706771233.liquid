

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-555716577706771233.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-555716577706771233.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-555716577706771233.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-555716577706771233.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-555716577706771233.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-555716577706771233.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-555716577706771233.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-555716577706771233.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-555716577706771233.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-555716577706771233.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-555716577706771233.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-555716577706771233.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-555716577706771233.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-555716577706771233.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-555716577706771233.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-555716577706771233.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-555716577706771233.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-555716577706771233.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-555716577706771233.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-555716577706771233.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-555716577706771233.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-555716577706771233.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-555716577706771233.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-555716577706771233.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-555716577706771233.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-555716577706771233.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-555716577706771233.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-555716577706771233.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-555716577706771233.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-555716577706771233.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-555716577706771233.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-555716577706771233.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-555716577706771233.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-555716577706771233.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-555716577706771233.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-555716577706771233.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-555716577706771233.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-555716577706771233.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-555716577706771233.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-555716577706771233.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-555716577706771233.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-555716577706771233.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-555716577706771233.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-555716577706771233.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-555716577706771233.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-555716577706771233.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-555716577706771233.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-555716577706771233.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-555716577706771233.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-555716577706771233.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-555716577706771233.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-555716577706771233.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-555716577706771233.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-555716577706771233.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-555716577706771233.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-555716577706771233.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-555716577706771233.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-555716577706771233.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-555716577706771233.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-555716577706771233.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-555716577706771233.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-555716577706771233.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-555716577706771233.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-555716577706771233 .gp-relative{position:relative}.gps-555716577706771233 .gp-z-1{z-index:1}.gps-555716577706771233 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-555716577706771233 .gp-mb-0{margin-bottom:0}.gps-555716577706771233 .gp-flex{display:flex}.gps-555716577706771233 .gp-inline-flex{display:inline-flex}.gps-555716577706771233 .gp-grid{display:grid}.gps-555716577706771233 .\!gp-hidden{display:none!important}.gps-555716577706771233 .gp-hidden{display:none}.gps-555716577706771233 .gp-h-full{height:100%}.gps-555716577706771233 .gp-max-w-full{max-width:100%}.gps-555716577706771233 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-555716577706771233 .gp-flex-col{flex-direction:column}.gps-555716577706771233 .gp-items-center{align-items:center}.gps-555716577706771233 .gp-justify-center{justify-content:center}.gps-555716577706771233 .gp-gap-y-0{row-gap:0}.gps-555716577706771233 .gp-overflow-hidden{overflow:hidden}.gps-555716577706771233 .gp-break-words{overflow-wrap:break-word}.gps-555716577706771233 .gp-rounded-none{border-radius:0}.gps-555716577706771233 .gp-text-center{text-align:center}.gps-555716577706771233 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-555716577706771233 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-555716577706771233 .gp-no-underline{text-decoration-line:none}.gps-555716577706771233 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555716577706771233 .gp-duration-200{transition-duration:.2s}.gps-555716577706771233 .gp-duration-300{transition-duration:.3s}.gps-555716577706771233 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555716577706771233 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-555716577706771233 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-555716577706771233 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-555716577706771233 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-555716577706771233 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-555716577706771233 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-555716577706771233 .tablet\:\!gp-hidden{display:none!important}.gps-555716577706771233 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-555716577706771233 .mobile\:\!gp-hidden{display:none!important}.gps-555716577706771233 .mobile\:gp-hidden{display:none}}.gps-555716577706771233 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-555716577706771233 .\[\&_p\]\:gp-inline p{display:inline}.gps-555716577706771233 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-555716577706771233 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-555716577706771233 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="geYJnNLMXp" data-id="geYJnNLMXp"
        style="--blockPadding:base;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-2xl);--pb:var(--g-s-2xl);--pt-mobile:var(--g-s-2xl);--pl-mobile:24px;--pb-mobile:var(--g-s-2xl);--pr-mobile:24px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="geYJnNLMXp gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gO1qALudgr gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="g1YyCogSgW" data-id="g1YyCogSgW"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-l);--cg:8px;--pc:start;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g1YyCogSgW gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gj8do2WuA5 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="ghej18nrtP">
    <div
      parentTag="Col"
        class="ghej18nrtP "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:29px"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:600px;--w-tablet:600px;--w-mobile:600px;--ts:none;--ta:center;--c:#242424;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-heading, heading);--weight:700;--ls:normal;--size:33px;--size-tablet:33px;--size-mobile:26px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.gghej18nrtP_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  <gp-button >
  <div
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--mb:17px;--ta:center"
    
  >
    <style>
    .gwDxPzCRj9.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }

    .gwDxPzCRj9:hover::before {
      
      
    }

    .gwDxPzCRj9:hover .gp-button-icon {
      color: undefined;
    }

     .gwDxPzCRj9 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gwDxPzCRj9:hover .gp-button-price {
      color: undefined;
    }

    .gwDxPzCRj9 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gwDxPzCRj9 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gwDxPzCRj9:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#guP0xNZBba" target="_self" data-id="gwDxPzCRj9" aria-label="<p>Shop the Men&#039;s Collection</p>"
      
      data-state="idle"
      class="gwDxPzCRj9 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center"
      style="--hvr-bg:#1180FF;--bg:#096de3;--radius:var(--g-radius-small);--shadow:none;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:32px;--pr:32px;--pt:12px;--pb:12px;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:600;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--tt:uppercase"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggwDxPzCRj9_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    
<style {% if section.settings.section_preload == "false" %} class="gps-link" type="text/disabled-css" {% endif %}>@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmUiAw.woff) format('woff');
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmZiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmQiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmYiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmXiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalnoiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjaln6iAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmbiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmaiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmUiAz0klQmz24.woff) format('woff');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style>

{% schema %}
  {
    
    "name": "Section 9",
    "tag": "section",
    "class": "gps-555716577706771233 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555716577622754081&sectionId=555716577706771233)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"gghej18nrtP_text","label":"gghej18nrtP_text","default":"Your Performance, Your Watch—Find Your GardPro Today."},{"type":"html","id":"ggwDxPzCRj9_label","label":"ggwDxPzCRj9_label","default":"<p>Shop the Men's Collection</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
