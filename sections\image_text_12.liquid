<section class="image_text_12"  style="background:{{section.settings.bg_color}}; {% if section.settings.bg_image != blank %}background-image: url({{ section.settings.bg_image | img_url: 'master' }}); background-size: cover; background-repeat: no-repeat; background-position: center;{% endif %};">
  <div class="page_wrapper">
    <div class="wrapper">
      <div class="text_block">

        {% if section.settings.heading != blank %}
        <h2 class="heading">{{ section.settings.heading }}</h2>
        {% endif %}
  
      {% if section.settings.image !=blank %}
        <div class="image"><img src="{{ section.settings.image |img_url :'master' }}"></div>
      {% endif %}
      </div>
   
      
    </div>
  </div>
</section>

<style>


section.image_text_12 .wrapper .text_block {
    width: 100%;
    display: flex;
      gap: 60px;
    flex-direction: column;

}
section.image_text_12 .wrapper .text_block h2.heading {
    font-size: 74px;
    font-family: Helvetica-Bold;
    margin: 0;
  color:#2a2b2b;

      text-transform: none;
          margin: 0 auto;
        text-align: center;
      width: 100%;
    max-width: 1000px;
}
  section.image_text_12 .wrapper .text_block h2.heading p{
      margin: 0;
  }



    section.image_text_12 {
    padding: 50px 150px;
}
  .image_text_12 .image {
    background: #f7f7f7;
    padding: 40px 0;
    width: 100%;
}
.image_text_12 .image img {
    width: 100%;
    max-width: 60%;
    margin: 0 auto;
    display: flex
;
    justify-content: center;
    align-items: center;
}
  @media only screen and (min-width: 2600px) {
section.image_text_12 .wrapper .text_block h2.heading {
        font-size: 135px;
        width: 100%;
        max-width: 1810px;
}


  }
   @media only screen and (max-width: 1600px) {
  section.image_text_12 {
    padding: 25px 60px;
}
   }
  @media only screen and (max-width: 1280px) {
section.image_text_12 .wrapper .text_block h2.heading {
    font-size: 48px;
 max-width: 730px;
}
    section.image_text_12 .wrapper .text_block {
    gap: 40px;
    }
    .image_text_12 .image img {
    width: 100%;
    max-width: 70%;
    }
  }
  @media only screen and (max-width: 1024px) {
 
  }
  @media only screen and (max-width: 840px) {
        section.image_text_12 {
        padding: 30px 20px;
    }
  
  }
  @media only screen and (max-width: 480px) {
    .image_text_12 .image img {
        width: 100%;
        max-width: 90%;
    }
  section.image_text_12 .wrapper .text_block h2.heading {
    font-size: 38px;
}
  

  }


  
</style>


  

{% schema %}
{
  "name": "Image Text 12",
  "settings": [
    {
      "type": "color",
      "id": "bg_color",
      "default": "transparent",
      "label": "Backgroud Color"
    },
       {
      "type": "image_picker",
      "id": "bg_image",
      "label": "Background Image"
    },
    

      {
          "type": "richtext",
          "id": "heading",
          "label": "Heading"
        },
  
     {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
               
      
  ],


  
  "presets": [
    {
      "name": "Image Text 12",
      "blocks": []
    }
  ]
}
{% endschema %}

