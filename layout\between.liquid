<!doctype html>
<html lang="{{ request.locale.iso_code }}">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    {{ content_for_header }}
  </head>


    <style>
        :root {
            --primary-blue: #0071E6;
            --primary-blue-hover: #2784e8;
            --text-primary: #282521;
            --text-secondary: rgba(17, 17, 17, 0.56);
            --text-tertiary: rgba(17, 17, 17, 0.32);
            --bg-primary: #F2F1F0;
            --bg-white: #FFFFFF;
            --bg-secondary: #F5F5F5;
            --bg-tertiary: #FAFAFA;
            --border-color: rgba(17, 17, 17, 0.12);
            --border-light: rgba(17, 17, 17, 0.08);
            --shadow-sm: 0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.1);
            --shadow-md: 0px 4px 12px rgba(0, 0, 0, 0.1);
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-full: 999px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.5;
            font-size: 16px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }

        .logo-container {
            margin:24px auto;
            animation: fadeInDown 0.6s ease-out;
            text-align:center;
        }

        .logo {
            height: 50px;
            width: auto;
        }

        .main-container {
            max-width: 600px;
            width: 100%;
            text-align: center;
            animation: fadeIn 0.8s ease-out;
        }

        h1 {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .subtitle {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .regions-container {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-bottom: 40px;
        }

        .region-group {
            background: var(--bg-white);
            border-radius: var(--radius-lg);
            padding: 20px;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            animation: slideUp 0.6s ease-out forwards;
            opacity: 0;
            transform: translateY(20px);
        }

        .region-group:nth-child(1) { animation-delay: 0.1s; }
        .region-group:nth-child(2) { animation-delay: 0.2s; }
        .region-group:nth-child(3) { animation-delay: 0.3s; }
        .region-group:nth-child(4) { animation-delay: 0.4s; }
        .region-group:nth-child(5) { animation-delay: 0.5s; }

        .region-group:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .region-link {
            display: flex;
            align-items: center;
            justify-content: space-between;
            text-decoration: none;
            color: var(--text-primary);
            padding: 8px;
            border-radius: var(--radius-md);
            transition: background-color 0.2s ease;
        }

        .region-link:hover {
            background-color: var(--bg-tertiary);
        }

        .store-info {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
            flex: 1;
        }

        .store-name {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            line-height: 1.2;
            text-align:left;
        }

        .store-regions {
            font-size: 13px;
            color: var(--text-secondary);
            line-height: 1.4;
          text-align:left;        }

        .manual-text {
            font-size: 14px;
            font-weight: 500;
            color: var(--primary-blue);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .arrow {
            font-size: 18px;
            transition: transform 0.2s ease;
        }

        .region-link:hover .arrow {
            transform: translateX(4px);
        }

        .footer {
            margin-top: auto;
            padding-top: 40px;
            text-align: center;
            color: var(--text-tertiary);
            font-size: 14px;
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            body {
                padding: 16px;
            }

            .logo-container {
                margin:24px auto;
            }

            .logo {
                height: 40px;
            }

            h1 {
                font-size: 24px;
            }

            .subtitle {
                font-size: 15px;
                margin-bottom: 30px;
            }

            .regions-container {
                gap: 12px;
            }

            .region-group {
                padding: 16px;
            }

            .store-name {
                font-size: 16px;
            }

            .store-regions {
                font-size: 12px;
            }

            .manual-text {
                font-size: 13px;
            }
        }

        /* Animations */
        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Loading state for slow connections */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-light);
            border-top-color: var(--primary-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 40px auto;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        /* Accessibility improvements */
        .region-link:focus {
            outline: 2px solid var(--primary-blue);
            outline-offset: 2px;
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
    </style>

  <body>
    {{ content_for_layout }}
  </body>
</html>

