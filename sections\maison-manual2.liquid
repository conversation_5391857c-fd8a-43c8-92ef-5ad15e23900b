{% schema %}
{
  "name": "Manual 2",
  "blocks": [
    {
      "type": "depth1",
      "name": "Main tab",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Getting Started"
        },
        {
          "type": "textarea",
          "id": "sublevels",
          "label": "Sublevels",
          "default": "Sublevel 1\nSublevel 2\nSublevel 3"
        },
        {
          "type": "textarea",
          "id": "search_keys",
          "label": "Search keys",
          "default": "How to pair\nBattery life\nReset watch",
        }
      ]
    },
    {
      "type": "content1",
      "name": "Content 1",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Getting Started:Sublevel 1",
          "info": "The match should be the main tab and the sublevel, separated by a colon."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Getting Started"
        },
        {
          "type": "textarea",
          "id": "subtitle",
          "label": "Subtitle",
          "default": "Subtitle"
        }
      ]
    },
    {
      "type": "content2",
      "name": "Content 2",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Getting Started:Sublevel 1",
          "info": "The match should be the main tab and the sublevel, separated by a colon."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Getting Started"
        },
        {
          "type": "textarea",
          "id": "content",
          "label": "Content",
          "default": "Content"
        }
      ]
    },
    {
      "type": "content3",
      "name": "Content 3",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Getting Started:Sublevel 1",
          "info": "The match should be the main tab and the sublevel, separated by a colon."
        },
        {
          "type": "text",
          "id": "icon1",
          "label": "Icon 1",
          "default": "🔌"
        },
        {
          "type": "text",
          "id": "title1",
          "label": "Title 1",
          "default": "Feature 1"
        },
        {
          "type": "richtext",
          "id": "richtext1",
          "label": "Richtext 1",
          "default": "<p>Description for feature 1</p>"
        },
        {
          "type": "text",
          "id": "icon2",
          "label": "Icon 2",
          "default": "⚡"
        },
        {
          "type": "text",
          "id": "title2",
          "label": "Title 2",
          "default": "Feature 2"
        },
        {
          "type": "richtext",
          "id": "richtext2",
          "label": "Richtext 2",
          "default": "<p>Description for feature 2</p>"
        }
      ]
    },
    {
      "type": "content4",
      "name": "Content 4",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Getting Started:Sublevel 1",
          "info": "The match should be the main tab and the sublevel, separated by a colon."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Quick Reference"
        },
        {
          "type": "textarea",
          "id": "content",
          "label": "Content",
          "default": "Quick reference content goes here"
        }
      ]
    },
    {
      "type": "content5",
      "name": "Content 5",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Getting Started:Sublevel 1",
          "info": "The match should be the main tab and the sublevel, separated by a colon."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Title"
        },
        {
          "type": "text",
          "id": "subtitle",
          "label": "Subtitle",
          "default": "Subtitle"
        }
      ]
    },
    {
      "type": "content6",
      "name": "Content 6",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Getting Started:Sublevel 1",
          "info": "The match should be the main tab and the sublevel, separated by a colon."
        },
        {
          "type": "textarea",
          "id": "content",
          "label": "Content",
          "default": "Content goes here"
        }
      ]
    },
    {
      "type": "content7",
      "name": "Content 7",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Getting Started:Sublevel 1",
          "info": "The match should be the main tab and the sublevel, separated by a colon."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Title"
        },
        {
          "type": "textarea",
          "id": "blocks",
          "label": "Blocks",
          "default": "Block 1\nBlock 2\nBlock 3"
        },
        {
          "type": "text",
          "id": "subtitle",
          "label": "Subtitle",
          "default": "Subtitle"
        }
      ]
    },
    {
      "type": "content8",
      "name": "Content 8",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Getting Started:Sublevel 1",
          "info": "The match should be the main tab and the sublevel, separated by a colon."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Title"
        },
        {
          "type": "text",
          "id": "column1",
          "label": "Column 1",
          "default": "Column 1"
        },
        {
          "type": "text",
          "id": "column2",
          "label": "Column 2",
          "default": "Column 2"
        },
        {
          "type": "textarea",
          "id": "blocks",
          "label": "Blocks",
          "default": "Block 1:Column 1\nBlock 2:Column 2\nBlock 3:Column 1",
          "info": "The blocks should be separated by a newline. They are split into columns based with a colon."
        },
        {
          "type": "text",
          "id": "subtitle",
          "label": "Subtitle",
          "default": "Subtitle"
        }
      ]
    },
    {
      "type": "content9",
      "name": "Content 9",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Getting Started:Sublevel 1",
          "info": "The match should be the main tab and the sublevel, separated by a colon."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Title"
        },
        {
          "type": "richtext",
          "id": "richtext",
          "label": "Content",
          "default": "<p>Content goes here</p>"
        }
      ]
    },
    {
      "type": "content10",
      "name": "Content 10",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Getting Started:Sublevel 1",
          "info": "The match should be the main tab and the sublevel, separated by a colon."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Title"
        },
        {
          "type": "textarea",
          "id": "blocks",
          "label": "Blocks",
          "default": "Block 1\nBlock 2\nBlock 3",
          "info": "The blocks should be separated by a newline."
        },
        {
          "type": "richtext",
          "id": "richtext",
          "label": "Content",
          "default": "<p>Content goes here</p>"
        }
      ]
    },
    {
      "type": "content11",
      "name": "Content 11",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Getting Started:Sublevel 1",
          "info": "The match should be the main tab and the sublevel, separated by a colon."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Title"
        },
        {
          "type": "text",
          "id": "subtitle",
          "label": "Subtitle",
          "default": "Subtitle"
        },
        {
          "type": "textarea",
          "id": "blocks",
          "label": "Blocks",
          "default": "Block 1\nBlock 2\nBlock 3",
          "info": "The blocks should be separated by a newline."
        }
      ]
    },
    {
      "type": "content12",
      "name": "Content 12",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Getting Started:Sublevel 1",
          "info": "The match should be the main tab and the sublevel, separated by a colon."
        },
        {
          "type": "text",
          "id": "number",
          "label": "Number",
          "default": "1"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Title"
        },
        {
          "type": "textarea",
          "id": "blocks",
          "label": "Blocks",
          "default": "Block 1\nBlock 2\nBlock 3",
          "info": "The blocks should be separated by a newline."
        },
        {
          "type": "text",
          "id": "subtitle",
          "label": "Subtitle",
          "default": "Subtitle"
        }
      ]
    },
    {
      "type": "content13",
      "name": "Content 13",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Getting Started:Sublevel 1",
          "info": "The match should be the main tab and the sublevel, separated by a colon."
        },
        {
          "type": "textarea",
          "id": "content",
          "label": "Content",
          "default": "Content"
        }
      ]
    },
    {
      "type": "content14",
      "name": "Content 14",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Getting Started:Sublevel 1",
          "info": "The match should be the main tab and the sublevel, separated by a colon."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Title"
        },
        {
          "type": "textarea",
          "id": "blocks",
          "label": "Blocks",
          "default": "Block 1\nBlock 2\nBlock 3",
          "info": "The blocks should be separated by a newline."
        },
        {
          "type": "text",
          "id": "subtitle",
          "label": "Subtitle",
          "default": "Subtitle"
        }
      ]
    },
    {
      "type": "content15",
      "name": "Content 15",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Getting Started:Sublevel 1",
          "info": "The match should be the main tab and the sublevel, separated by a colon."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Title"
        },
        {
          "type": "richtext",
          "id": "richtext",
          "label": "Content",
          "default": "<p>Content</p>"
        }
      ]
    },
    {
      "type": "content16",
      "name": "Content 16",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Getting Started:Sublevel 1",
          "info": "The match should be the main tab and the sublevel, separated by a colon."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Title"
        },
        {
          "type": "text",
          "id": "icon1",
          "label": "Icon 1",
          "default": "📱"
        },
        {
          "type": "text",
          "id": "title1",
          "label": "Title 1",
          "default": "Title 1"
        },
        {
          "type": "textarea",
          "id": "content1",
          "label": "Content 1",
          "default": "Content 1"
        },
        {
          "type": "url",
          "id": "link1",
          "label": "Link 1"
        },
        {
          "type": "text",
          "id": "linktext1",
          "label": "Link Text 1",
          "default": "Learn More"
        },
        {
          "type": "text",
          "id": "icon2",
          "label": "Icon 2",
          "default": "⚙️"
        },
        {
          "type": "text",
          "id": "title2",
          "label": "Title 2",
          "default": "Title 2"
        },
        {
          "type": "textarea",
          "id": "content2",
          "label": "Content 2",
          "default": "Content 2"
        },
        {
          "type": "url",
          "id": "link2",
          "label": "Link 2"
        },
        {
          "type": "text",
          "id": "linktext2",
          "label": "Link Text 2",
          "default": "Learn More"
        }
      ]
    },
    {
      "type": "content17",
      "name": "Content 17",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "match",
          "default": "Getting Started"
        },
        {
          "type": "video",
          "id": "video",
          "label": "Video",
          "info": "Upload a video file"
        },
        {
          "type": "checkbox",
          "id": "controls",
          "label": "Controls",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "autoplay",
          "label": "Autoplay",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "loop",
          "label": "Loop",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "muted",
          "label": "Muted",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "playsinline",
          "label": "Playsinline",
          "default": false
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "Upload an image"
        },
        {
          "type": "range",
          "id": "desktop_width",
          "label": "Desktop image/video width in percent",
          "default": 100,
          "min": 1,
          "max": 100,
          "step": 1
        }
      ]
    }
  ]
}
{% endschema %}

<style>
  .video-container video,
  .image-container img {
    width: 100%;
    border-radius: 8px;
  }
</style>

<script>
  {% assign depth1 = section.blocks | where: "type", "depth1" %}
  const tabs = [
    {% for block in depth1 %}
      {
        id: "{{ block.settings.title | handleize }}",
        name: "{{ block.settings.title | strip }}",
        searchKeys: [
          {% assign search_keys = block.settings.search_keys | downcase | newline_to_br | split: '<br />' | compact %}
          {% for search_key in search_keys %}
            "{{ search_key | strip }}",
          {% endfor %}
        ],
        subtopics: [
          {% assign sublevels = block.settings.sublevels | newline_to_br | split: '<br />' | compact %}
          {% for sublevel in sublevels %}
            {
              id: "{{ sublevel | handleize }}",
              name: "{{ sublevel | strip }}"
            },
          {% endfor %}
        ]
      },
    {% endfor %}
  ]
</script>

<div>
  <!-- Header -->
  <header class="header">
    <div class="container">
      <div class="header-content">
        <div class="logo">
          <img src="{{ 'logo-gardpro.png' | asset_url }}" alt="GARD PRO Manual Logo">
        </div>
        <div class="header-actions">
          <button
            class="theme-toggle"
            @click="toggleTheme()"
            :aria-label="darkMode ? 'Switch to light mode' : 'Switch to dark mode'"
          >
            <svg
              x-show="!darkMode"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <circle cx="12" cy="12" r="5"></circle>
              <line x1="12" y1="1" x2="12" y2="3"></line>
              <line x1="12" y1="21" x2="12" y2="23"></line>
              <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
              <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
              <line x1="1" y1="12" x2="3" y2="12"></line>
              <line x1="21" y1="12" x2="23" y2="12"></line>
              <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
              <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
            </svg>
            <svg
              x-show="darkMode"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
            </svg>
          </button>
          <button
            class="theme-toggle"
            style="font-size:15px;gap:4px;"
            @click="showSearchPopup = true"
            aria-label="Open search"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.35-4.35"></path>
            </svg>
            Search
          </button>
        </div>
      </div>
    </div>
  </header>

  {% render 'maison-manual-search' %}

  <!-- Product Selector -->
  <section class="product-selector">
    <div class="container">
      <div class="product-selector-content">
        <p style="font-size:14px;color:var(--text-secondary);margin-bottom:10px;">First, Select your watch</p>
        <div class="series-tabs">
          <button
            class="series-tab"
            :class="{ active: selectedSeries === 'health' }"
            @click="selectSeries('health')"
          >
            Health Series
          </button>
          <button
            class="series-tab"
            :class="{ active: selectedSeries === 'ultra' }"
            @click="selectSeries('ultra')"
          >
            Ultra Series
          </button>
        </div>
        <div class="model-grid">
          <template x-for="model in currentModels" :key="model.id">
            <div
              class="model-card"
              :class="{ selected: selectedModel === model.id }"
              @click="selectModel(model.id)"
            >
              <div class="model-name" x-text="model.name"></div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </section>

  <!-- Navigation -->
  <nav class="nav-section">
    <div class="container">
      <div class="nav-tabs">
        <template x-for="tab in tabs" :key="tab.id">
          <button
            class="nav-tab"
            :class="{ active: activeTab === tab.id }"
            @click="setActiveTab(tab.id)"
            x-text="tab.name"
          ></button>
        </template>
      </div>
      <!-- Mobile Dropdown -->
      <select
        class="nav-dropdown"
        x-model="activeTab"
        @change="setActiveTab($event.target.value)"
      >
        <option disabled>Select a section</option>
        <template x-for="tab in tabs" :key="tab.id">
          <option :value="tab.id" x-text="tab.name"></option>
        </template>
      </select>

      <select
        class="mobile-subtopic-dropdown"
        x-model="activeSubtopic"
        @change="setActiveSubtopic($event.target.value)"
        x-show="currentSubtopics.length > 0"
      >
        <option disabled>Select a subtopic</option>
        <template x-for="subtopic in tabs.find(t => t.id === activeTab)?.subtopics" :key="subtopic.id">
          <option :value="subtopic.id" x-text="'↳ ' + subtopic.name"></option>
        </template>
      </select>
    </div>
  </nav>

  <!-- Main Content -->
  <main class="container">
    <div class="content-wrapper">
      <!-- Mobile Subtopic Dropdown -->

      <!-- Sidebar -->
      <aside class="sidebar" x-show="currentSubtopics.length > 0">
        <nav class="sidebar-nav">
          <h3 class="sidebar-title" x-text="tabs.find(t => t.id === activeTab)?.name || ''"></h3>
          <ul class="sidebar-list">
            <template x-for="subtopic in tabs.find(t => t.id === activeTab)?.subtopics" :key="subtopic.id">
              <li
                class="sidebar-item"
                :class="{ active: activeSubtopic === subtopic.id }"
                @click="setActiveSubtopic(subtopic.id)"
                x-text="subtopic.name"
              ></li>
            </template>
          </ul>
        </nav>
      </aside>

      <!-- Main Content Area -->
      <div class="main-content">
        <!-- GET STARTED Section -->
        <section class="content-section active">
          {% for block in section.blocks %}
            {% assign depth = block.settings.match | split: ':' | first | handleize %}
            {% assign subtopic = block.settings.match | split: ':' | last | handleize %}
            <div x-show="activeTab === '{{ depth }}' && activeSubtopic === '{{ subtopic }}'">
              {% case block.type %}
                {% when 'content1' %}
                  <div class="section-header">
                    <h1>{{ block.settings.title }}</h1>
                    <p class="section-subtitle">
                      {{ block.settings.subtitle }}
                    </p>
                  </div>
                {% when 'content2' %}
                  <div class="card">
                    <h3>{{ block.settings.title }}</h3>
                    <p style="color: var(--text-secondary); margin-top: 8px;">
                      {{
                        block.settings.content
                        | newline_to_br
                        | replace: '[', '<strong>'
                        | replace: ']', '</strong>'
                      }}
                    </p>
                  </div>
                {% when 'content3' %}
                  <div class="feature-grid">
                    <div class="feature-card">
                      <div class="feature-icon">{{ block.settings.icon1 }}</div>
                      <h3 class="feature-title">{{ block.settings.title1 }}</h3>
                      <p class="feature-description richtext-container">
                        {{ block.settings.richtext1 }}
                      </p>
                    </div>

                    <div class="feature-card">
                      <div class="feature-icon">{{ block.settings.icon2 }}</div>
                      <h3 class="feature-title">{{ block.settings.title2 }}</h3>
                      <p class="feature-description richtext-container">
                        {{ block.settings.richtext2 }}
                      </p>
                    </div>
                  </div>
                {% when 'content4' %}
                  <div class="quick-ref-card">
                    <div class="quick-ref-content">
                      <h2>{{ block.settings.title }}</h2>
                      <p>
                        {{
                          block.settings.content
                          | newline_to_br
                          | replace: '[', '<strong>'
                          | replace: ']', '</strong>'
                        }}
                      </p>
                    </div>
                  </div>
                {% when 'content5' %}
                  <div class="section-header">
                    <h1>{{ block.settings.title }}</h1>
                    <h3 style="margin-bottom: 16px;">{{ block.settings.subtitle }}</h3>
                  </div>
                {% when 'content6' %}
                  <p style="color: var(--text-secondary); margin-bottom: 24px;">
                    {{ block.settings.content | newline_to_br | replace: '[', '<strong>' | replace: ']', '</strong>' }}
                  </p>
                {% when 'content7' %}
                  <div class="card">
                    <h3>{{ block.settings.title }}</h3>
                    <div class="steps-container" style="margin-top: 16px;">
                      {% assign steps = block.settings.blocks | newline_to_br | split: '<br />' | compact %}
                      {% for step in steps %}
                        <div class="step">
                          <div class="step-number">{{ forloop.index }}</div>
                          <div class="step-content">
                            <p class="step-description">{{ step }}</p>
                          </div>
                        </div>
                      {% endfor %}
                    </div>
                    <p style="color: var(--text-secondary); margin-top: 16px;">{{ block.settings.subtitle }}</p>
                  </div>
                {% when 'content8' %}
                  <div class="card">
                    <h3>{{ block.settings.title }}</h3>
                    <table class="settings-table">
                      <thead>
                        <tr>
                          <th>{{ block.settings.column1 }}</th>
                          <th>{{ block.settings.column2 }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        {% assign settings = block.settings.blocks | newline_to_br | split: '<br />' | compact %}
                        {% for setting in settings %}
                          <tr>
                            <td>
                              <strong>{{ setting | split: ':' | first }}</strong>
                            </td>
                            <td>{{ setting | split: ':' | last }}</td>
                          </tr>
                        {% endfor %}
                      </tbody>
                    </table>
                    <p style="color: var(--text-secondary); margin-top: 16px;">
                      {{ block.settings.subtitle }}
                    </p>
                  </div>
                {% when 'content9' %}
                  <div
                    class="card"
                    style="background: linear-gradient(180deg, rgba(0, 113, 227, 0.05) 0%, rgba(0, 113, 227, 0.02) 100%); border-color: var(--primary-blue);"
                  >
                    <h3>{{ block.settings.title }}</h3>
                    <div class="richtext-container">
                      {{ block.settings.richtext }}
                    </div>
                  </div>
                {% when 'content10' %}
                  <div class="card">
                    <h3>{{ block.settings.title }}</h3>
                    <div class="feature-grid" style="margin-top: 16px;">
                      {% assign features = block.settings.blocks | newline_to_br | split: '<br />' | compact %}
                      {% for feature in features %}
                        <div style="padding: 8px;">• {{ feature }}</div>
                      {% endfor %}
                    </div>
                    <div class="richtext-container">
                      {{ block.settings.richtext }}
                    </div>
                  </div>
                {% when 'content11' %}
                  <div
                    class="card"
                    style="background: linear-gradient(180deg, rgba(245, 158, 11, 0.05) 0%, rgba(245, 158, 11, 0.02) 100%); border-color: #F59E0B;"
                  >
                    <h3>{{ block.settings.title }}</h3>
                    <p style="font-weight: 600;">{{ block.settings.subtitle }}</p>
                    <ol style="margin-top: 16px; margin-left: 24px; line-height: 1.8;">
                      {% assign steps = block.settings.blocks | newline_to_br | split: '<br />' | compact %}
                      {% for step in steps %}
                        <li>{{ step }}</li>
                      {% endfor %}
                    </ol>
                  </div>
                {% when 'content12' %}
                  <div class="step">
                    <div class="step-number">{{ block.settings.number }}</div>
                    <div class="step-content">
                      <h3 class="step-title">{{ block.settings.title }}</h3>
                      <div class="step-description">
                        <ol>
                          {% assign steps = block.settings.blocks | newline_to_br | split: '<br />' | compact %}
                          {% for step in steps %}
                            <li>{{ step }}</li>
                          {% endfor %}
                        </ol>
                        <p style="margin-top: 16px;">
                          {{ block.settings.subtitle }}
                        </p>
                      </div>
                    </div>
                  </div>
                {% when 'content13' %}
                  <p style="color: var(--text-secondary); margin-top: 24px; font-weight: 600; text-align: center;">
                    {{ block.settings.content | newline_to_br | replace: '[', '<strong>' | replace: ']', '</strong>' }}
                  </p>
                {% when 'content14' %}
                  <div class="card">
                    <h3>{{ block.settings.title }}</h3>
                    <ul style="margin-top: 16px; margin-left: 24px; color: var(--text-secondary);">
                      {% assign features = block.settings.blocks | newline_to_br | split: '<br />' | compact %}
                      {% for feature in features %}
                        <li>{{ feature }}</li>
                      {% endfor %}
                    </ul>
                    <p style="color: var(--text-secondary); margin-top: 16px; font-weight: 600; text-align: center;">
                      {{ block.settings.subtitle }}
                    </p>
                  </div>
                {% when 'content15' %}
                  <div class="troubleshoot-card" x-data="{ show: false }">
                    <div class="troubleshoot-header" @click="show = !show">
                      <span class="troubleshoot-title">{{ block.settings.title }}</span>
                      <svg
                        class="troubleshoot-icon rotate"
                        :class="{ rotate: show }"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                      >
                        <polyline points="6 9 12 15 18 9"></polyline>
                      </svg>
                    </div>
                    <div class="troubleshoot-content expanded richtext-container" :class="{ expanded: show }">
                      {{ block.settings.richtext }}
                    </div>
                  </div>
                {% when 'content16' %}
                  <div class="feature-grid">
                    <div class="feature-card">
                      <div class="feature-icon">{{ block.settings.icon1 }}</div>
                      <h3 class="feature-title">{{ block.settings.title1 }}</h3>
                      <p class="feature-description">
                        {{
                          block.settings.content1
                          | newline_to_br
                          | replace: '[', '<strong>'
                          | replace: ']', '</strong>'
                        }}
                        {% if block.settings.link1 != blank %}
                          <a href="{{ block.settings.link1 }}" style="color: var(--primary-blue); font-weight: 500;">
                            {{- block.settings.linktext1 -}}
                          </a>
                        {% endif %}
                      </p>
                    </div>

                    <div class="feature-card">
                      <div class="feature-icon">{{ block.settings.icon2 }}</div>
                      <h3 class="feature-title">{{ block.settings.title2 }}</h3>
                      <p class="feature-description">
                        {{
                          block.settings.content2
                          | newline_to_br
                          | replace: '[', '<strong>'
                          | replace: ']', '</strong>'
                        }}
                        {% if block.settings.link2 != blank %}
                          <a href="{{ block.settings.link2 }}" style="color: var(--primary-blue); font-weight: 500;">
                            {{- block.settings.linktext2 -}}
                          </a>
                        {% endif %}
                      </p>
                    </div>
                  </div>
                {% when 'content17' %}
                  {% if block.settings.video != blank %}
                    <div class="video-container">
                      {{
                        block.settings.video
                        | video_tag:
                          controls: block.settings.controls,
                          autoplay: block.settings.autoplay,
                          loop: block.settings.loop,
                          muted: block.settings.muted,
                          playsinline: block.settings.playsinline,
                          image_size: '1000x'
                      }}
                    </div>
                  {% endif %}
                  {% if block.settings.image != blank %}
                    <div class="image-container">
                      <img
                        src="{{ block.settings.image | image_url: width: 1000 }}"
                        alt="{{ block.settings.image.alt }}"
                      >
                    </div>
                  {% endif %}

                  <style>
                    @media (min-width: 900px) {
                      .video-container video,
                      .image-container img {
                        width: {{ section.settings.desktop_width }}%;
                      }
                    }
                  </style>
              {% endcase %}
            </div>
          {% endfor %}
        </section>
      </div>
    </div>
  </main>
</div>

<script src="{{ 'maison-manual2.js' | asset_url }}"></script>
{{ 'maison-manual2.css' | asset_url | stylesheet_tag }}

<style>
  .richtext-container {
    color: var(--text-secondary);
  }

  .richtext-container ul {
    margin-left: 16px;
  }

  .richtext-container > * {
    padding-top: 8px;
  }
</style>
