{%- liquid
  assign isModal = false
  if template == 'product.modal'
    assign isModal = true
  endif
-%}

{%- render 'product-template',
  product: product,
  section_id: section.id,
  blocks: section.blocks,
  image_position: section.settings.image_position,
  image_container_width: section.settings.image_size,
  product_zoom_enable: section.settings.product_zoom_enable,
  sku_enable: section.settings.sku_enable,
  isModal: isModal,
  thumbnail_position: section.settings.thumbnail_position,
  thumbnail_height: section.settings.thumbnail_height,
  thumbnail_arrows: section.settings.thumbnail_arrows,
  mobile_layout: section.settings.mobile_layout,
  video_looping: section.settings.enable_video_looping,
  video_style: section.settings.product_video_style,
  section: section
-%}

{% schema %}
{
  "name": "t:sections.main-product.name",
  "settings": [
    {
      "type": "text",
      "id": "badge_label",
      "label": "Badge Label"
    },
    {
      "type": "image_picker",
      "id": "gallery_icon",
      "label": "Gallery Icon"
    },
    {
      "type": "textarea",
      "id": "gallery_text",
      "label": "Gallery Text"
    },
    {
      "type": "checkbox",
      "id": "sku_enable",
      "label": "t:sections.main-product.settings.sku_enable.label"
    },
    {
      "type": "header",
      "content": "t:sections.main-product.settings.header_media"
    },
    {
      "type": "paragraph",
      "content": "t:sections.main-product.settings.content"
    },
    {
      "type": "select",
      "id": "image_position",
      "label": "t:sections.main-product.settings.image_position.label",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:sections.main-product.settings.image_position.options.left.label"
        },
        {
          "value": "right",
          "label": "t:sections.main-product.settings.image_position.options.right.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "image_size",
      "label": "t:sections.main-product.settings.image_size.label",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "t:sections.main-product.settings.image_size.options.small.label"
        },
        {
          "value": "medium",
          "label": "t:sections.main-product.settings.image_size.options.medium.label"
        },
        {
          "value": "large",
          "label": "t:sections.main-product.settings.image_size.options.large.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "product_zoom_enable",
      "label": "t:sections.main-product.settings.product_zoom_enable.label",
      "default": true
    },
    {
      "type": "select",
      "id": "thumbnail_position",
      "label": "t:sections.main-product.settings.thumbnail_position.label",
      "default": "beside",
      "options": [
        {
          "value": "beside",
          "label": "t:sections.main-product.settings.thumbnail_position.options.beside.label"
        },
        {
          "value": "below",
          "label": "t:sections.main-product.settings.thumbnail_position.options.below.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "thumbnail_height",
      "label": "t:sections.main-product.settings.thumbnail_height.label",
      "info": "t:sections.main-product.settings.thumbnail_height.info",
      "default": "flexible",
      "options": [
        {
          "value": "fixed",
          "label": "t:sections.main-product.settings.thumbnail_height.options.fixed.label"
        },
        {
          "value": "flexible",
          "label": "t:sections.main-product.settings.thumbnail_height.options.flexible.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "thumbnail_arrows",
      "label": "t:sections.main-product.settings.thumbnail_arrows.label"
    },
    {
      "type": "select",
      "id": "mobile_layout",
      "label": "t:sections.main-product.settings.mobile_layout.label",
      "default": "partial",
      "options": [
        {
          "value": "partial",
          "label": "t:sections.main-product.settings.mobile_layout.options.partial.label"
        },
        {
          "value": "full",
          "label": "t:sections.main-product.settings.mobile_layout.options.full.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "label": "t:sections.main-product.settings.enable_video_looping.label",
      "default": true
    },
    {
      "type": "select",
      "id": "product_video_style",
      "label": "t:sections.main-product.settings.product_video_style.label",
      "default": "muted",
      "options": [
        {
          "value": "muted",
          "label": "t:sections.main-product.settings.product_video_style.options.muted.label"
        },
        {
          "value": "unmuted",
          "label": "t:sections.main-product.settings.product_video_style.options.unmuted.label"
        }
      ],
      "info": "t:sections.main-product.settings.product_video_style.info"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "richtext",
      "name": "Richtext",
      "settings": [
        {
          "type": "richtext",
          "id": "richtext",
          "label": "Richtext"
        }
      ]
    },
    {
      "type": "estimated-delivery",
      "name": "Estimated Delivery",
      "settings": [
        {
          "type": "text",
          "id": "delivery_text",
          "label": "Delivery Text",
          "default": "Estimate delivery by"
        },
        {
          "type": "range",
          "id": "delivery_days",
          "min": 0,
          "max": 15,
          "step": 1,
          "label": "Delivery Days",
          "default": 4
        }
      ]
    },
    {
      "type": "main-image-card",
      "name": "Main Image Card",
      "limit": 1
    },
    {
      "type": "image-card",
      "name": "Image Card",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "video",
          "id": "video",
          "label": "video"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        }
      ]
    },
    {
      "type": "pair-it-with",
      "name": "Pair it with",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "Heading",
          "default": "Pair it with"
        },
        {
          "type": "range",
          "id": "products_to_show",
          "min": 2,
          "max": 10,
          "step": 1,
          "default": 4,
          "label": "Number of products to show"
        }
      ]
    },
    {
      "type": "price",
      "name": "t:product_block.price.name",
      "limit": 1
    },
    {
      "type": "klarna",
      "name": "Klarna",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "text_info",
          "label": "Klarna Text",
          "default": "Bestel nu betaal later"
        }
      ]
    },
    {
      "type": "quantity_selector",
      "name": "t:product_block.quantity_selector.name",
      "limit": 1
    },
    {
      "type": "size_chart",
      "name": "t:product_block.size_chart.name",
      "limit": 1,
      "settings": [
        {
          "type": "page",
          "id": "size_chart",
          "label": "t:product_block.size_chart.settings.page.label"
        }
      ]
    },
    {
      "type": "variant_picker",
      "name": "Variant picker",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "variant_labels",
          "label": "t:product_block.variant_picker.settings.variant_labels.label",
          "default": true
        },
        {
          "type": "select",
          "id": "picker_type",
          "label": "t:product_block.variant_picker.settings.picker_type.label",
          "options": [
            {
              "value": "button",
              "label": "t:product_block.variant_picker.settings.picker_type.options.button.label"
            },
            {
              "value": "dropdown",
              "label": "t:product_block.variant_picker.settings.picker_type.options.dropdown.label"
            }
          ],
          "default": "button"
        },
        {
          "type": "checkbox",
          "id": "product_dynamic_variants_enable",
          "label": "t:product_block.variant_picker.settings.product_dynamic_variants_enable.label",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "color_swatches",
          "label": "Enable color swatches",
          "info": "Requires type to be set to 'Buttons'. [Learn how to set up swatches](https://archetypethemes.co/blogs/impulse/how-do-i-set-up-color-swatches)"
        }
      ]
    },
    {
      "type": "description",
      "name": "t:product_block.description.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "is_tab",
          "label": "t:product_block.description.settings.is_tab.label"
        }
      ]
    },
    {
      "type": "buy_buttons",
      "name": "t:product_block.buy_buttons.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_dynamic_checkout",
          "label": "t:product_block.buy_buttons.settings.show_dynamic_checkout.label",
          "info": "t:product_block.buy_buttons.settings.show_dynamic_checkout.info",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "surface_pickup_enable",
          "label": "t:product_block.buy_buttons.settings.surface_pickup_enable.label",
          "info": "t:product_block.buy_buttons.settings.surface_pickup_enable.info",
          "default": true
        }
      ]
    },
    {
      "type": "inventory_status",
      "name": "t:product_block.inventory_status.name",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "inventory_threshold",
          "label": "t:product_block.inventory_status.settings.inventory_threshold.label",
          "default": 10,
          "min": 0,
          "max": 20,
          "step": 2
        },
        {
          "type": "checkbox",
          "id": "inventory_transfers_enable",
          "label": "t:product_block.inventory_status.settings.inventory_transfers_enable.label",
          "info": "t:product_block.inventory_status.settings.inventory_transfers_enable.info",
          "default": true
        }
      ]
    },
    {
      "type": "sales_point",
      "name": "t:product_block.sales_point.name",
      "settings": [
        {
          "type": "select",
          "id": "icon",
          "label": "t:product_block.sales_point.settings.icon.label",
          "default": "globe",
          "options": [
            {
              "value": "checkmark",
              "label": "t:product_block.sales_point.settings.icon.options.checkmark.label"
            },
            {
              "value": "gift",
              "label": "t:product_block.sales_point.settings.icon.options.gift.label"
            },
            {
              "value": "globe",
              "label": "t:product_block.sales_point.settings.icon.options.globe.label"
            },
            {
              "value": "heart",
              "label": "t:product_block.sales_point.settings.icon.options.heart.label"
            },
            {
              "value": "leaf",
              "label": "t:product_block.sales_point.settings.icon.options.leaf.label"
            },
            {
              "value": "lock",
              "label": "t:product_block.sales_point.settings.icon.options.lock.label"
            },
            {
              "value": "package",
              "label": "t:product_block.sales_point.settings.icon.options.package.label"
            },
            {
              "value": "phone",
              "label": "t:product_block.sales_point.settings.icon.options.phone.label"
            },
            {
              "value": "ribbon",
              "label": "t:product_block.sales_point.settings.icon.options.ribbon.label"
            },
            {
              "value": "shield",
              "label": "t:product_block.sales_point.settings.icon.options.shield.label"
            },
            {
              "value": "tag",
              "label": "t:product_block.sales_point.settings.icon.options.tag.label"
            },
            {
              "value": "truck",
              "label": "t:product_block.sales_point.settings.icon.options.truck.label"
            }
          ]
        },
        {
          "type": "text",
          "id": "text",
          "label": "t:product_block.sales_point.settings.text.label",
          "default": "Free worldwide shipping"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:product_block.text.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "default": "Text block",
          "label": "t:product_block.text.settings.text.label"
        }
      ]
    },
    {
      "type": "trust_badge",
      "name": "t:product_block.trust_badge.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "trust_image",
          "label": "t:product_block.trust_badge.settings.trust_image.label"
        }
      ]
    },
    {
      "type": "tab",
      "name": "t:product_block.tab.name",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "t:product_block.tab.settings.title.label",
          "default": "Shipping information"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:product_block.tab.settings.content.label",
          "default": "<p>Use collapsible tabs for more detailed information that will help customers make a purchasing decision.</p><p>Ex: Shipping and return policies, size guides, and other common questions.</p>"
        },
        {
          "type": "page",
          "id": "page",
          "label": "t:product_block.tab.settings.page.label"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        }
      ]
    },
    {
      "type": "share",
      "name": "t:product_block.share_on_social.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:product_block.share_on_social.settings.content"
        }
      ]
    },
    {
      "type": "separator",
      "name": "t:product_block.separator.name"
    },
    {
      "type": "contact",
      "name": "t:product_block.contact_form.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:product_block.contact_form.settings.content"
        },
        {
          "type": "text",
          "id": "title",
          "label": "t:product_block.contact_form.settings.title.label",
          "default": "Ask a question"
        },
        {
          "type": "checkbox",
          "id": "phone",
          "label": "t:product_block.contact_form.settings.phone.label"
        }
      ]
    },
    {
      "type": "custom",
      "name": "t:product_block.html.name",
      "settings": [
        {
          "type": "liquid",
          "id": "code",
          "label": "t:product_block.html.settings.code.label",
          "default": "<h4>Custom code block</h4><p>Use this advanced section to add custom HTML, app scripts, or liquid.</p>",
          "info": "t:product_block.html.settings.code.info"
        }
      ]
    }
  ]
}
{% endschema %}
