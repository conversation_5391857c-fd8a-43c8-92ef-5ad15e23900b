
    
    <style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-580612614443238318.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-580612614443238318.gps.gpsil [style*="--as:"]{align-self:var(--as)}.gps-580612614443238318.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-580612614443238318.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-580612614443238318.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-580612614443238318.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-580612614443238318.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-580612614443238318.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-580612614443238318.gps.gpsil [style*="--hvr-bgi:"]:hover{background-image:var(--hvr-bgi)}.gps-580612614443238318.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-580612614443238318.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-580612614443238318.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-580612614443238318.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-580612614443238318.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-580612614443238318.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-580612614443238318.gps.gpsil [style*="--hvr-bb:"]:hover{border-bottom:var(--hvr-bb)}.gps-580612614443238318.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-580612614443238318.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-580612614443238318.gps.gpsil [style*="--hvr-bblr:"]:hover{border-bottom-left-radius:var(--hvr-bblr)}.gps-580612614443238318.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-580612614443238318.gps.gpsil [style*="--hvr-bbrr:"]:hover{border-bottom-right-radius:var(--hvr-bbrr)}.gps-580612614443238318.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-580612614443238318.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-580612614443238318.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-580612614443238318.gps.gpsil [style*="--hvr-bt:"]:hover{border-top:var(--hvr-bt)}.gps-580612614443238318.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-580612614443238318.gps.gpsil [style*="--hvr-btlr:"]:hover{border-top-left-radius:var(--hvr-btlr)}.gps-580612614443238318.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-580612614443238318.gps.gpsil [style*="--hvr-btrr:"]:hover{border-top-right-radius:var(--hvr-btrr)}.gps-580612614443238318.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-580612614443238318.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-580612614443238318.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-580612614443238318.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-580612614443238318.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-580612614443238318.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-580612614443238318.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-580612614443238318.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-580612614443238318.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-580612614443238318.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-580612614443238318.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-580612614443238318.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-580612614443238318.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-580612614443238318.gps.gpsil [style*="--tdt:"]{text-decoration-thickness:var(--tdt)}.gps-580612614443238318.gps.gpsil [style*="--tdl:"]{text-decoration-line:var(--tdl)}.gps-580612614443238318.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-580612614443238318.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-580612614443238318.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-580612614443238318.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-580612614443238318.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-580612614443238318.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-580612614443238318.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-580612614443238318.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-580612614443238318.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-580612614443238318.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-580612614443238318.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-580612614443238318.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-580612614443238318.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-580612614443238318.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-580612614443238318.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-580612614443238318.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-580612614443238318.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-580612614443238318.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-580612614443238318.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-580612614443238318.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-580612614443238318.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-580612614443238318.gps.gpsil [style*="--mb-tablet:"]{margin-bottom:var(--mb-tablet)}.gps-580612614443238318.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-580612614443238318.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-580612614443238318.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-580612614443238318.gps.gpsil [style*="--ta-tablet:"]{text-align:var(--ta-tablet)}.gps-580612614443238318.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-580612614443238318.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-580612614443238318.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-580612614443238318.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-580612614443238318.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-580612614443238318.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-580612614443238318.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-580612614443238318.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-580612614443238318.gps.gpsil [style*="--objf-mobile:"]{-o-object-fit:var(--objf-mobile);object-fit:var(--objf-mobile)}.gps-580612614443238318.gps.gpsil [style*="--op-mobile:"]{opacity:var(--op-mobile)}.gps-580612614443238318.gps.gpsil [style*="--o-mobile:"]{order:var(--o-mobile)}.gps-580612614443238318.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-580612614443238318.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-580612614443238318.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-580612614443238318.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-580612614443238318.gps.gpsil [style*="--ta-mobile:"]{text-align:var(--ta-mobile)}.gps-580612614443238318.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-580612614443238318.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-580612614443238318 .gp-relative{position:relative}.gps-580612614443238318 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-580612614443238318 .gp-mb-0{margin-bottom:0}.gps-580612614443238318 .gp-inline-block{display:inline-block}.gps-580612614443238318 .gp-flex{display:flex}.gps-580612614443238318 .gp-inline-flex{display:inline-flex}.gps-580612614443238318 .gp-grid{display:grid}.gps-580612614443238318 .gp-contents{display:contents}.gps-580612614443238318 .\!gp-hidden{display:none!important}.gps-580612614443238318 .gp-hidden{display:none}.gps-580612614443238318 .gp-h-auto{height:auto}.gps-580612614443238318 .gp-h-full{height:100%}.gps-580612614443238318 .gp-w-full{width:100%}.gps-580612614443238318 .gp-max-w-full{max-width:100%}.gps-580612614443238318 .gp-flex-none{flex:none}.gps-580612614443238318 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-580612614443238318 .gp-flex-col{flex-direction:column}.gps-580612614443238318 .gp-items-center{align-items:center}.gps-580612614443238318 .gp-justify-center{justify-content:center}.gps-580612614443238318 .gp-overflow-hidden{overflow:hidden}.gps-580612614443238318 .gp-break-words{overflow-wrap:break-word}.gps-580612614443238318 .gp-text-center{text-align:center}.gps-580612614443238318 .gp-leading-\[0\]{line-height:0}.gps-580612614443238318 .gp-no-underline{text-decoration-line:none}.gps-580612614443238318 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-580612614443238318 .gp-duration-200{transition-duration:.2s}.gps-580612614443238318 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-580612614443238318 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-580612614443238318 .disabled\:gp-opacity-30:disabled{opacity:.3}.gps-580612614443238318 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-580612614443238318 .gp-group[data-state=loading] .group-data-\[state\=loading\]\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-580612614443238318 .tablet\:\!gp-hidden{display:none!important}.gps-580612614443238318 .tablet\:gp-hidden{display:none}.gps-580612614443238318 .tablet\:gp-h-auto{height:auto}.gps-580612614443238318 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-580612614443238318 .mobile\:\!gp-hidden{display:none!important}.gps-580612614443238318 .mobile\:gp-hidden{display:none}.gps-580612614443238318 .mobile\:gp-h-auto{height:auto}.gps-580612614443238318 .mobile\:gp-flex-none{flex:none}}.gps-580612614443238318 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-580612614443238318 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}@media (max-width:1024px){.gps-580612614443238318 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-580612614443238318 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-580612614443238318 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-580612614443238318 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}</style>
    
    
    

    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}

    
        <section
          class="gp-mx-auto gp-max-w-full [&_*]:gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:none;--pl-tablet:none;--pl-mobile:none;--pr:none;--pr-tablet:none;--pr-mobile:none"
        >
          
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"#FFFFFF","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gIajO8Ih_L"}' data-id="gIajO8Ih_L" id="gIajO8Ih_L" data-same-height-subgrid-container class="gIajO8Ih_L gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--pt:var(--g-s-4xl);--pl:24px;--pb:var(--g-s-4xl);--pr:24px;--pt-mobile:24px;--pl-mobile:24px;--pb-mobile:24px;--pr-mobile:24px;--cg:30px;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--ai:normal;--jc:start;--o:0"
      class="gZVKQhNq29 gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gkDkWv4ZRs"}' data-id="gkDkWv4ZRs" id="gkDkWv4ZRs" data-same-height-subgrid-container class="gkDkWv4ZRs gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-2xl);--mb-mobile:20px;--cg:var(--g-s-2xl);--gtc:minmax(0, 12fr);--w:780px;--w-tablet:780px;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="gQswyy07aq gp-relative gp-flex gp-flex-col"
    >
      
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gQQDzMYc3k"
        class="gQQDzMYc3k"
        style="--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-l);--pl:var(--g-s-xl);--pr:var(--g-s-xl);--pl-mobile:var(--g-s-l);--pr-mobile:var(--g-s-l);--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425947747"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:80%;--w-tablet:80%;--w-mobile:100%;--tdt:auto;--ts:none;--ta:center;--ta-tablet:center;--ta-mobile:center;--c:#242424;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-heading, heading);--weight:700;--ls:normal;--size:36px;--size-tablet:30px;--size-mobile:24px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggQQDzMYc3k_text | replace: '$locationOrigin', locationOrigin }}
        </h2>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gpqltFhXV3"
        class="gpqltFhXV3"
        style="--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-2xl);--pt:0px;--pl:var(--g-s-l);--pb:0px;--pr:var(--g-s-l);--mb-mobile:20px;--pl-mobile:var(--g-s-l);--pr-mobile:var(--g-s-l);--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425947747"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:80%;--w-tablet:80%;--w-mobile:100%;--tdt:auto;--ts:none;--ta:center;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#000000;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--ls:normal;--size:18px;--size-tablet:19px;--size-mobile:17px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggpqltFhXV3_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    <gp-button
      gp-data='{"btnLink":{"link":"#g071lCCNnn","type":"scroll-to","title":"Section 9"}}'
      
      class="gp-flex gp-flex-col"
    >
      <div style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--op-mobile:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:var(--g-s-2xl);--mb-mobile:0px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--mb-tablet:0px;--ta:center;--ta-mobile:center" >
        <style>[data-id="gKbwJr2E8v"].gp-button-base::before, [data-id="gKbwJr2E8v-interaction"].gp-button-base::before {
      
    content: "";
    height: 100%;
    width: 100%;
    position: absolute;
    pointer-events: none;
    top: 0;
    left: 0;
    border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
    
      border-bottom-left-radius: 999px;
      border-bottom-right-radius: 999px;
      border-top-left-radius: 999px;
      border-top-right-radius: 999px;
      
  
    }
  
      
  [data-id="gKbwJr2E8v"]:hover::before, [data-id="gKbwJr2E8v-interaction"]:hover::before {
    
    
  }</style>
        <a
            class="gp-trigger-button-link gp-hidden"
            href=#g071lCCNnn
            target=undefined
          ></a>
        <a
          data-id="gKbwJr2E8v" dataId="gKbwJr2E8v" data-state="idle" aria-label="<p>GEAR UP WITH ULTRA 2+ TODAY</p>"
          
          href="#g071lCCNnn" target
          style="--w:50%;--w-tablet:50%;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:12px;--pb:12px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:12px;--pb-mobile:12px;--bblr:999px;--bbrr:999px;--btlr:999px;--btrr:999px;--hvr-bblr:999px;--hvr-bbrr:999px;--hvr-btlr:999px;--hvr-btrr:999px;--shadow:none;--bgi:;--hvr-bgi:;--hvr-bg:#007AF4;--bg:#0171E3;--c:#FFFFFF;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:700;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:16px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase"
          class="gKbwJr2E8v gp-text-center gp-button-base gp-group gp-relative gp-inline-flex gp-max-w-full gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-200 disabled:gp-btn-disabled disabled:gp-opacity-30 "
        >
        
        <div class="gp-inline-flex">
          
          
    <span
      data-gp-text
      style="--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:700;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:16px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;--ts:none;word-break:break-word"
      class="gp-content-product-button group-active/button:!gp-text-inherit gp-relative gp-flex gp-h-full gp-items-center gp-overflow-hidden gp-break-words group-data-[state=loading]:gp-invisible [&_p]:gp-whitespace-pre-line gp-text button-text"
    >
      {{ section.settings.ggKbwJr2E8v_label }}
    </span>
        </div>
        
        </a>
      </div>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-button-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    </gp-button>
  
    </div>

      
    </gp-row>
  
  
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"ggcjpfmAJY"}' data-id="ggcjpfmAJY" id="ggcjpfmAJY" data-same-height-subgrid-container class="ggcjpfmAJY gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--cg:30px;--gtc:minmax(0, 4fr) minmax(0, 4fr) minmax(0, 4fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--ai:normal;--jc:center;--o:0;--o-mobile:1"
      class="gEq6QjmNP4 gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gaR5QxTOlK"}' data-id="gaR5QxTOlK" id="gaR5QxTOlK" data-same-height-subgrid-container class="gaR5QxTOlK gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--pb:var(--g-s-3xl);--cg:30px;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--ai:normal;--jc:start;--o:0"
      class="gcX3FfEx58 gp-relative gp-flex gp-flex-col"
    >
      
    <div id="gWq_XzZ2eR" class="gp-leading-[0] gWq_XzZ2eR" style="--mb:var(--g-s-l);--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-3, line-3);--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--op:100%;--ta:center">
      <div data-id="gWq_XzZ2eR" class="icon-wrapper gp-inline-flex gp-overflow-hidden gWq_XzZ2eR " style="--shadow:none">
      
        <div >
          <span style="--c:#0171E3;--t:rotate(0deg);--w:60px;--w-tablet:60px;--w-mobile:60px;--h:60px;--h-tablet:60px;--h-mobile:60px;--minw:60px;--minw-tablet:60px;--minw-mobile:60px;--height-desktop:60px;--height-tablet:60px;--height-mobile:60px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="20" width="20" data-name="mountains-filled" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817746260001128">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M254.88,195.92l-54.56-92.08A15.87,15.87,0,0,0,186.55,96h0a15.85,15.85,0,0,0-13.76,7.84l-15.64,26.39a4,4,0,0,0,0,4.07l26.8,45.47a8.13,8.13,0,0,1-1.89,10.55,8,8,0,0,1-11.8-2.26L101.79,71.88a16,16,0,0,0-27.58,0L1.11,195.94a8,8,0,0,0,1,9.52A8.23,8.23,0,0,0,8.23,208H247.77a8.29,8.29,0,0,0,6.09-2.55A8,8,0,0,0,254.88,195.92ZM64.43,120,88,80l23.57,40ZM140,52a24,24,0,1,1,24,24A24,24,0,0,1,140,52Z" /></svg></span>
        </div>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gD_5597ija"
        class="gD_5597ija"
        style="--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:12px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425947776"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--ts:none;--ta:center;--ta-mobile:center;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#0370E3;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:700;--ls:normal;--size:18px;--size-tablet:16px;--size-mobile:18px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:uppercase;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggD_5597ija_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="g78lYeFwah"
        class="g78lYeFwah"
        style="--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--pt:0px;--pl:0px;--pb:0px;--pr:0px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425947777"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--ts:none;--ta:center;--ta-mobile:center;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#000000;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:18px;--size-tablet:16px;--size-mobile:16px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.gg78lYeFwah_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    </div>

      
    </gp-row>
  
  
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"guXsqM-SP5"}' data-id="guXsqM-SP5" id="guXsqM-SP5" data-same-height-subgrid-container class="guXsqM-SP5 gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--pb-mobile:var(--g-s-3xl);--cg:30px;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--ai:normal;--jc:start;--o:0"
      class="gia-tRpgOf gp-relative gp-flex gp-flex-col"
    >
      
    <div id="gZfhjN0lF7" class="gp-leading-[0] gZfhjN0lF7" style="--mb:var(--g-s-l);--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-3, line-3);--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--op:100%;--ta:center">
      <div data-id="gZfhjN0lF7" class="icon-wrapper gp-inline-flex gp-overflow-hidden gZfhjN0lF7 " style="--shadow:none">
      
        <div >
          <span style="--c:#0171E3;--t:rotate(0deg);--w:60px;--w-tablet:60px;--w-mobile:60px;--h:60px;--h-tablet:60px;--h-mobile:60px;--minw:60px;--minw-tablet:60px;--minw-mobile:60px;--height-desktop:60px;--height-tablet:60px;--height-mobile:60px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="20" width="20" data-name="battery-full-bold" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817554949800296">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M196,52H28A28,28,0,0,0,0,80v96a28,28,0,0,0,28,28H196a28,28,0,0,0,28-28V80A28,28,0,0,0,196,52Zm4,124a4,4,0,0,1-4,4H28a4,4,0,0,1-4-4V80a4,4,0,0,1,4-4H196a4,4,0,0,1,4,4Zm-16-72v48a12,12,0,0,1-24,0V104a12,12,0,0,1,24,0Zm-40,0v48a12,12,0,0,1-24,0V104a12,12,0,0,1,24,0Zm-40,0v48a12,12,0,0,1-24,0V104a12,12,0,0,1,24,0Zm-40,0v48a12,12,0,0,1-24,0V104a12,12,0,0,1,24,0Zm192,0v48a12,12,0,0,1-24,0V104a12,12,0,0,1,24,0Z" /></svg></span>
        </div>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="g5SMvvz8BY"
        class="g5SMvvz8BY"
        style="--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-l);--mb-mobile:12px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425947780"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--ts:none;--ta:center;--ta-mobile:center;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#0370E3;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:700;--ls:normal;--size:18px;--size-tablet:16px;--size-mobile:18px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:uppercase;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.gg5SMvvz8BY_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gAkFzfjLqG"
        class="gAkFzfjLqG"
        style="--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--pt:0px;--pl:0px;--pb:0px;--pr:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425947781"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--ts:none;--ta:center;--ta-mobile:center;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#000000;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:18px;--size-tablet:16px;--size-mobile:16px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggAkFzfjLqG_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    </div>

      
    </gp-row>
  
  
    </div><div
      data-same-height-display-contents
      style="--jc:center;--o-mobile:0"
      class="gL0K5q1JOE gp-relative gp-flex gp-flex-col"
    >
      
    <div
      role="presentation"
      data-id="gsNNS54ki4"
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb-mobile:var(--g-s-2xl);--ta:center"
      class="gp-group/image gp-relative force-publish-1757425947782 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto gsNNS54ki4"
    >
      <div
        
        style="border-radius:inherit;--jc:center"
        class="gp-h-full gp-w-full gp-flex pointer-events-auto"
      >
        
    <picture style="border-radius:inherit" class="gp-contents">
      
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_553400155311702965-15e5b6cd-4788-478a-9323-6b9accacb682.png" | file_url }}" srcset="{{ "gempages_553400155311702965-15e5b6cd-4788-478a-9323-6b9accacb682.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_553400155311702965-15e5b6cd-4788-478a-9323-6b9accacb682.png" | file_url }}" srcset="{{ "gempages_553400155311702965-15e5b6cd-4788-478a-9323-6b9accacb682.png" | file_url }}" />
    
      
      <img
        loading="eager" fetchpriority="high"
        src="{{ "gempages_553400155311702965-15e5b6cd-4788-478a-9323-6b9accacb682.png" | file_url }}" data-src="{{ "gempages_553400155311702965-15e5b6cd-4788-478a-9323-6b9accacb682.png" | file_url }}"
        alt=""
        width="100%"
        style="--aspect:auto;--objf:fill;--objf-mobile:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--btrr:12px;--bblr:12px;--bbrr:12px;--btlr:12px;--radiusType:rounded"
        class="gp-inline-block gp-w-full gp-max-w-full gp_force_load"
      />
    
    </picture>
  
      </div>
    </div>
  
    </div><div
      data-same-height-display-contents
      style="--jc:center;--o-mobile:2"
      class="g0EnyZasWE gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"g89FBaaG34"}' data-id="g89FBaaG34" id="g89FBaaG34" data-same-height-subgrid-container class="g89FBaaG34 gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--pb:var(--g-s-3xl);--cg:30px;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--ai:normal;--jc:start;--o:0"
      class="gn9fMFbwG7 gp-relative gp-flex gp-flex-col"
    >
      
    <div id="g-bfff6AfE" class="gp-leading-[0] g-bfff6AfE" style="--mb:var(--g-s-l);--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-3, line-3);--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--op:100%;--ta:center">
      <div data-id="g-bfff6AfE" class="icon-wrapper gp-inline-flex gp-overflow-hidden g-bfff6AfE " style="--shadow:none">
      
        <div >
          <span style="--c:#0171E3;--t:rotate(0deg);--w:60px;--w-tablet:60px;--w-mobile:60px;--h:60px;--h-tablet:60px;--h-mobile:60px;--minw:60px;--minw-tablet:60px;--minw-mobile:60px;--height-desktop:60px;--height-tablet:60px;--height-mobile:60px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="20" width="20" data-name="cloud-rain-bold" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817562048135528">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M156,12A80.22,80.22,0,0,0,82.39,60.36,56.76,56.76,0,0,0,76,60a56,56,0,0,0,0,112h29.58L86,201.34a12,12,0,1,0,20,13.32L134.42,172H156a80,80,0,0,0,0-160Zm0,136H76a32,32,0,0,1,0-64h.28c-.11,1.1-.2,2.2-.26,3.3a12,12,0,1,0,24,1.39A56.06,56.06,0,1,1,156,148Zm.65,58.66-26.67,40a12,12,0,1,1-20-13.32l26.66-40a12,12,0,1,1,20,13.32Z" /></svg></span>
        </div>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="ghzGhWd0ug"
        class="ghzGhWd0ug"
        style="--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:12px;--pt:0px;--pl:0px;--pb:0px;--pr:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425947785"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:80%;--w-tablet:80%;--w-mobile:80%;--tdt:auto;--ts:none;--ta:center;--ta-mobile:center;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#0370E3;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:700;--ls:normal;--size:18px;--size-tablet:16px;--size-mobile:19px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:uppercase;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.gghzGhWd0ug_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gtNXxIRp_n"
        class="gtNXxIRp_n"
        style="--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425947786"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--ts:none;--ta:center;--ta-mobile:center;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#000000;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:18px;--size-tablet:16px;--size-mobile:16px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggtNXxIRp_n_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    </div>

      
    </gp-row>
  
  
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gi3hlJi1Zd"}' data-id="gi3hlJi1Zd" id="gi3hlJi1Zd" data-same-height-subgrid-container class="gi3hlJi1Zd gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--cg:30px;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--ai:normal;--jc:start;--o:0"
      class="g-lPNBRANb gp-relative gp-flex gp-flex-col"
    >
      
    <div id="gysSIzZLd0" class="gp-leading-[0] gysSIzZLd0" style="--mb:var(--g-s-l);--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-3, line-3);--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--op:100%;--ta:center">
      <div data-id="gysSIzZLd0" class="icon-wrapper gp-inline-flex gp-overflow-hidden gysSIzZLd0 " style="--shadow:none">
      
        <div >
          <span style="--c:#0171E3;--t:rotate(0deg);--w:60px;--w-tablet:60px;--w-mobile:60px;--h:60px;--h-tablet:60px;--h-mobile:60px;--minw:60px;--minw-tablet:60px;--minw-mobile:60px;--height-desktop:60px;--height-tablet:60px;--height-mobile:60px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="20" width="20" data-name="film-script-filled" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817735018086760">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M200,24H56A16,16,0,0,0,40,40V216a16,16,0,0,0,16,16H200a16,16,0,0,0,16-16V40A16,16,0,0,0,200,24ZM76,188a12,12,0,1,1,12-12A12,12,0,0,1,76,188Zm0-48a12,12,0,1,1,12-12A12,12,0,0,1,76,140Zm0-48A12,12,0,1,1,88,80,12,12,0,0,1,76,92Z" /></svg></span>
        </div>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gSpH--gLxl"
        class="gSpH--gLxl"
        style="--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-l);--pt:0px;--pl:0px;--pb:0px;--pr:0px;--mb-mobile:12px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425947789"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:80%;--w-tablet:80%;--w-mobile:80%;--tdt:auto;--ts:none;--ta:center;--ta-mobile:center;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#0370E3;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:700;--ls:normal;--size:18px;--size-tablet:16px;--size-mobile:18px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:uppercase;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggSpH--gLxl_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="g0NrCE5Kyr"
        class="g0NrCE5Kyr"
        style="--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425947790"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--ts:none;--ta:center;--ta-mobile:center;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#000000;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:18px;--size-tablet:16px;--size-mobile:16px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.gg0NrCE5Kyr_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    </div>

      
    </gp-row>
  
  
    </div>

      
    </gp-row>
  
  
    </div>

      
    </gp-row>
  
        </section>
      
  
    <style {% if section.settings.section_preload == "false" %} class="gps-link" type="text/disabled-css" {% endif %}>@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWubEbVmUiAw.woff) format('woff');
}
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmUiAw.woff) format('woff');
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBHMdazQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBHMdazQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style>
    
{% schema %}
  {
    
    "name": "Section 4",
    "tag": "section",
    "class": "gps-580612614443238318 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=578718746651132539&sectionId=580612614443238318)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggQQDzMYc3k_text","label":"ggQQDzMYc3k_text","default":"Technology That Thrives Where Others Fail."},{"type":"html","id":"ggpqltFhXV3_text","label":"ggpqltFhXV3_text","default":"<p>Every detail of the Ultra 2+ is crafted to outlast the elements — so you can keep pushing forward, no matter where you are.</p>"},{"type":"html","id":"ggKbwJr2E8v_label","label":"ggKbwJr2E8v_label","default":"<p>GEAR UP WITH ULTRA 2+ TODAY</p>"},{"type":"html","id":"ggD_5597ija_text","label":"ggD_5597ija_text","default":"<p>Military-Grade Durability</p>"},{"type":"html","id":"gg78lYeFwah_text","label":"gg78lYeFwah_text","default":"<p>Engineered with impact-resistant materials and reinforced casing, the Ultra 2+ is built to handle serious wear and tear. <strong>Because a cracked watch face shouldn’t be the reason your day gets cut short.</strong></p>"},{"type":"html","id":"gg5SMvvz8BY_text","label":"gg5SMvvz8BY_text","default":"<p>Extended Battery Life</p>"},{"type":"html","id":"ggAkFzfjLqG_text","label":"ggAkFzfjLqG_text","default":"<p>With days of battery life on a single charge, it's one of the longest battery life smart watches on the market - even with active GPS and app use. <strong>No more stressing about a dead battery in the middle of your adventure.</strong></p>"},{"type":"html","id":"gghzGhWd0ug_text","label":"gghzGhWd0ug_text","default":"<p>Waterproof &amp; Dustproof</p>"},{"type":"html","id":"ggtNXxIRp_n_text","label":"ggtNXxIRp_n_text","default":"<p>The trimmer's steel blades delicately rub&nbsp;<br>Tested to military-grade standards, the Ultra 2+ stands strong against the elements — whether submerged in water or coated in dirt.<br><strong>Keep moving, no matter the weather or terrain.</strong></p>"},{"type":"html","id":"ggSpH--gLxl_text","label":"ggSpH--gLxl_text","default":"<p>Scratch-Resistant Display</p>"},{"type":"html","id":"gg0NrCE5Kyr_text","label":"gg0NrCE5Kyr_text","default":"<p>The hardened, ultra-clear glass resists scratches and scuffs from tools, rocks, and daily wear. <strong>So your screen stays sharp, readable, and ready for anything.</strong></p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
  