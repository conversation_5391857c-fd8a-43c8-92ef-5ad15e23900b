

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-554688430635287792.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-554688430635287792.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-554688430635287792.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-554688430635287792.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-554688430635287792.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-554688430635287792.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-554688430635287792.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-554688430635287792.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-554688430635287792.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-554688430635287792.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-554688430635287792.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-554688430635287792.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-554688430635287792.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-554688430635287792.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-554688430635287792.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-554688430635287792.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-554688430635287792.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-554688430635287792.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-554688430635287792.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-554688430635287792.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-554688430635287792.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-554688430635287792.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-554688430635287792.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-554688430635287792.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-554688430635287792.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-554688430635287792.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-554688430635287792.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-554688430635287792.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-554688430635287792.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-554688430635287792.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-554688430635287792.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-554688430635287792.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-554688430635287792.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-554688430635287792.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-554688430635287792.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-554688430635287792.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-554688430635287792.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-554688430635287792.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-554688430635287792.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-554688430635287792.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-554688430635287792.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-554688430635287792.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-554688430635287792.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-554688430635287792.gps.gpsil [style*="--right:"]{right:var(--right)}.gps-554688430635287792.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-554688430635287792.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-554688430635287792.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-554688430635287792.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-554688430635287792.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-554688430635287792.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-554688430635287792.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-554688430635287792.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-554688430635287792.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-554688430635287792.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-554688430635287792.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-554688430635287792.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-554688430635287792.gps.gpsil [style*="--ml-tablet:"]{margin-left:var(--ml-tablet)}.gps-554688430635287792.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-554688430635287792.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-554688430635287792.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-554688430635287792.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-554688430635287792.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-554688430635287792.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-554688430635287792.gps.gpsil [style*="--right-tablet:"]{right:var(--right-tablet)}.gps-554688430635287792.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-554688430635287792.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-554688430635287792.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-554688430635287792.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-554688430635287792.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-554688430635287792.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-554688430635287792.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-554688430635287792.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-554688430635287792.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-554688430635287792.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-554688430635287792.gps.gpsil [style*="--ml-mobile:"]{margin-left:var(--ml-mobile)}.gps-554688430635287792.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-554688430635287792.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-554688430635287792.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-554688430635287792.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-554688430635287792.gps.gpsil [style*="--right-mobile:"]{right:var(--right-mobile)}.gps-554688430635287792.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-554688430635287792.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-554688430635287792.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-554688430635287792 .gp-rotate-0,.gps-554688430635287792 .gp-rotate-180,.gps-554688430635287792 .mobile\:gp-rotate-0,.gps-554688430635287792 .mobile\:gp-rotate-180,.gps-554688430635287792 .tablet\:gp-rotate-0,.gps-554688430635287792 .tablet\:gp-rotate-180{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-554688430635287792 .gp-shadow-md{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-554688430635287792 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-554688430635287792 .gp-static{position:static}.gps-554688430635287792 .gp-relative{position:relative}.gps-554688430635287792 .gp-left-0{left:0}.gps-554688430635287792 .gp-right-0{right:0}.gps-554688430635287792 .gp-z-1{z-index:1}.gps-554688430635287792 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-554688430635287792 .gp-my-0{margin-bottom:0;margin-top:0}.gps-554688430635287792 .gp-mb-0{margin-bottom:0}.gps-554688430635287792 .gp-block{display:block}.gps-554688430635287792 .gp-flex{display:flex}.gps-554688430635287792 .gp-grid{display:grid}.gps-554688430635287792 .gp-contents{display:contents}.gps-554688430635287792 .\!gp-hidden{display:none!important}.gps-554688430635287792 .gp-hidden{display:none}.gps-554688430635287792 .gp-aspect-square{aspect-ratio:1/1}.gps-554688430635287792 .gp-h-auto{height:auto}.gps-554688430635287792 .gp-h-full{height:100%}.gps-554688430635287792 .\!gp-min-h-full{min-height:100%!important}.gps-554688430635287792 .gp-w-\[12px\]{width:12px}.gps-554688430635287792 .gp-w-full{width:100%}.gps-554688430635287792 .gp-max-w-full{max-width:100%}.gps-554688430635287792 .gp-flex-none{flex:none}.gps-554688430635287792 .gp-shrink-0{flex-shrink:0}.gps-554688430635287792 .gp-rotate-0{--tw-rotate:0deg}.gps-554688430635287792 .gp-rotate-0,.gps-554688430635287792 .gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-554688430635287792 .gp-rotate-180{--tw-rotate:180deg}.gps-554688430635287792 .gp-cursor-pointer{cursor:pointer}.gps-554688430635287792 .gp-select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.gps-554688430635287792 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-554688430635287792 .\!gp-flex-row{flex-direction:row!important}.gps-554688430635287792 .gp-flex-row{flex-direction:row}.gps-554688430635287792 .gp-flex-col{flex-direction:column}.gps-554688430635287792 .\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-554688430635287792 .gp-items-center{align-items:center}.gps-554688430635287792 .gp-justify-center{justify-content:center}.gps-554688430635287792 .gp-justify-between{justify-content:space-between}.gps-554688430635287792 .gp-gap-2{gap:8px}.gps-554688430635287792 .gp-gap-y-0{row-gap:0}.gps-554688430635287792 .gp-overflow-hidden{overflow:hidden}.gps-554688430635287792 .gp-rounded-full{border-radius:9999px}.gps-554688430635287792 .gp-text-center{text-align:center}.gps-554688430635287792 .gp-text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity))}.gps-554688430635287792 .gp-shadow-md{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-554688430635287792 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-554688430635287792 .gp-duration-200{transition-duration:.2s}.gps-554688430635287792 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-554688430635287792 .tablet\:gp-static{position:static}.gps-554688430635287792 .tablet\:gp-left-0{left:0}.gps-554688430635287792 .tablet\:gp-right-0{right:0}.gps-554688430635287792 .tablet\:gp-block{display:block}.gps-554688430635287792 .tablet\:\!gp-hidden{display:none!important}.gps-554688430635287792 .tablet\:gp-hidden{display:none}.gps-554688430635287792 .tablet\:gp-h-auto{height:auto}.gps-554688430635287792 .tablet\:\!gp-min-h-full{min-height:100%!important}.gps-554688430635287792 .tablet\:gp-flex-none{flex:none}.gps-554688430635287792 .tablet\:gp-rotate-0{--tw-rotate:0deg}.gps-554688430635287792 .tablet\:gp-rotate-0,.gps-554688430635287792 .tablet\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-554688430635287792 .tablet\:gp-rotate-180{--tw-rotate:180deg}.gps-554688430635287792 .tablet\:\!gp-flex-row{flex-direction:row!important}.gps-554688430635287792 .tablet\:gp-flex-row{flex-direction:row}.gps-554688430635287792 .tablet\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-554688430635287792 .tablet\:gp-px-0{padding-left:0;padding-right:0}}@media (max-width:767px){.gps-554688430635287792 .mobile\:gp-static{position:static}.gps-554688430635287792 .mobile\:gp-left-0{left:0}.gps-554688430635287792 .mobile\:gp-right-0{right:0}.gps-554688430635287792 .mobile\:gp-block{display:block}.gps-554688430635287792 .mobile\:\!gp-hidden{display:none!important}.gps-554688430635287792 .mobile\:gp-hidden{display:none}.gps-554688430635287792 .mobile\:gp-h-auto{height:auto}.gps-554688430635287792 .mobile\:\!gp-min-h-full{min-height:100%!important}.gps-554688430635287792 .mobile\:gp-flex-none{flex:none}.gps-554688430635287792 .mobile\:gp-rotate-0{--tw-rotate:0deg}.gps-554688430635287792 .mobile\:gp-rotate-0,.gps-554688430635287792 .mobile\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-554688430635287792 .mobile\:gp-rotate-180{--tw-rotate:180deg}.gps-554688430635287792 .mobile\:\!gp-flex-row{flex-direction:row!important}.gps-554688430635287792 .mobile\:gp-flex-row{flex-direction:row}.gps-554688430635287792 .mobile\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-554688430635287792 .mobile\:gp-px-0{padding-left:0;padding-right:0}}.gps-554688430635287792 .\[\&\>svg\]\:gp-h-full>svg{height:100%}.gps-554688430635287792 .\[\&\>svg\]\:gp-w-full>svg{width:100%}.gps-554688430635287792 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-554688430635287792 .\[\&_p\]\:gp-inline p{display:inline}.gps-554688430635287792 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-554688430635287792 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gdHZzJu_tG" data-id="gdHZzJu_tG"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-4xl);--pl:15px;--pb:var(--g-s-4xl);--pr:15px;--pt-tablet:var(--g-s-4xl);--pl-tablet:15px;--pb-tablet:var(--g-s-4xl);--pr-tablet:15px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gdHZzJu_tG gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gb4Fd42zxO gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gerlvm8Bxw" data-id="gerlvm8Bxw"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gerlvm8Bxw gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gADLa6H4Wk gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="grru5yfXXI" data-id="grru5yfXXI"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:63px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="grru5yfXXI gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gwTzjoZAqj gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="g8MyVynCpW" data-id="g8MyVynCpW"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-s);--cg:16px;--pc:center;--gtc:minmax(0, auto);--gtc-mobile:minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g8MyVynCpW gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:center;--o:0"
      class="g86v5MK6Nf gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gwjswY0Y8j">
    <div
      parentTag="Col"
        class="gwjswY0Y8j "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:40px;--size-tablet:28px;--size-mobile:24px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggwjswY0Y8j_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmC-d-m4fy">
    <div
      parentTag="Col"
        class="gmC-d-m4fy "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#575757;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggmC-d-m4fy_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    <gp-carousel data-id="g9fbb2WBjK"  id="gp-root-carousel-g9fbb2WBjK-{{section.id}}" class="  gp-group/carousel gp-flex" gp-data='{"id":"g9fbb2WBjK-{{section.id}}","setting":{"animationMode":"ease-in","arrowBackgroundColor":"transparent","arrowBorder":{"desktop":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"}},"arrowButtonSize":{"desktop":{"height":"32px","padding":{"linked":true},"shapeLinked":true,"shapeValue":"1/1","width":"32px"}},"arrowCustom":"<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"currentColor\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z\"/>\n              </svg>\n              ","arrowCustomColor":"#000000","arrowGapToEachSide":"16","arrowIconSize":{"desktop":24},"autoplay":false,"autoplayTimeout":2,"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"childItem":["Slide 1","Slide 2","Slide 3"],"controlOverContent":{"desktop":true},"dot":{"desktop":true,"mobile":true,"tablet":true},"dotActiveColor":{"desktop":"line-3"},"dotColor":{"desktop":"bg-1"},"dotGapToCarousel":{"desktop":16},"dotSize":{"desktop":12},"dotStyle":{"desktop":"none"},"enableDrag":{"desktop":true},"itemNumber":{"desktop":3,"mobile":2,"tablet":3},"label":true,"loop":{"desktop":false},"navigationStyle":{"desktop":"none"},"pauseOnHover":true,"roundedArrow":{"desktop":{"radiusType":"small"}},"rtl":false,"runPreview":false,"showWhenHover":false,"sneakPeak":{"desktop":false,"mobile":true},"sneakPeakOffsetCenter":{"desktop":50},"sneakPeakOffsetForward":{"desktop":50},"sneakPeakType":{"desktop":"forward"},"vertical":{"desktop":false}},"styles":{"align":{"desktop":"center"},"playSpeed":500,"sizeSetting":{"desktop":{"height":"auto","width":"100%"},"mobile":{"width":"100%"},"tablet":{"width":"100%"}},"spacing":{"desktop":"20"}},"isHiddenArrowWhenDisabled":true}' style="--jc:center">
      <div class="gp-hidden gp-aspect-square gp-w-[12px] gp-rounded-full gp-shadow-md"></div>
      <div
        
        class="gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block g9fbb2WBjK"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      >
        <div
          class="gp-h-full gp-relative gp-mx-auto gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row"
          style="--h:100%;--h-tablet:100%;--h-mobile:100%;gap:16px"
        >
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 gp-carousel-action-back gem-slider-previous g9fbb2WBjK-{{section.id}} gp-carousel-arrow-g9fbb2WBjK gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--left:initial;--top:initial;--right:initial;--bottom:;--left-tablet:initial;--top-tablet:initial;--right-tablet:initial;--bottom-tablet:;--left-mobile:initial;--top-mobile:initial;--right-mobile:initial;--bottom-mobile:;--d:none;--d-tablet:none;--d-mobile:none;background-color:transparent;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
  style="--c:#000000;--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
  >
    <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z"/>
              </svg>
              
    </div>
      <style>
    .gp-carousel-arrow-g9fbb2WBjK {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-g9fbb2WBjK::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-g9fbb2WBjK {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-g9fbb2WBjK::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-g9fbb2WBjK {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-g9fbb2WBjK::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
          <div id="gp-carousel-g9fbb2WBjK-{{section.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full"
          style="--cg-mobile:20px;--cg-tablet:20px;--cg:20px">
          
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      label="Carousel Item" tag="CarouselItem" type="component"
      style="--bgc:transparent;--minw:calc(100% / 3 - 13.333333333333334px);--minw-tablet:calc(100% / 3 - 13.333333333333334px);--minw-mobile:calc(100% / 1.5 - 6.666666666666667px);--maxw:calc(100% / 3 - 13.333333333333334px);--maxw-tablet:calc(100% / 3 - 13.333333333333334px);--maxw-mobile:calc(100% / 1.5 - 6.666666666666667px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-g9fbb2WBjK gyDE1n4R6M"
      data-index="0"
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      <div
    
     data-id="g-tl1OUE8J"
    role="presentation"
    class="gp-group/image g-tl1OUE8J gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:20px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_2023_08_21T04_29_54_024Z_768x.png?v=1692879045" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_2023_08_21T04_29_54_024Z_768x.png?v=1692879045" />
      <source media="(max-width: 1024px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_2023_08_21T04_29_54_024Z_1024x.png?v=1692879045" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_2023_08_21T04_29_54_024Z_1024x.png?v=1692879045" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_2023_08_21T04_29_54_024Z.png?v=1692879045"
        data-src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_2023_08_21T04_29_54_024Z.png?v=1692879045"
        width="100%"
        alt=""
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div><div
    
     data-id="geLtHKWslh"
    role="presentation"
    class="gp-group/image geLtHKWslh gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/health_series_bg_768x.svg?v=**********" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/health_series_bg_768x.svg?v=**********" />
      <source media="(max-width: 1024px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/health_series_bg_1024x.svg?v=**********" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/health_series_bg_1024x.svg?v=**********" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/health_series_bg.svg?v=**********"
        data-src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/health_series_bg.svg?v=**********"
        width="100%"
        alt=""
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      label="Carousel Item" tag="CarouselItem" type="component"
      style="--bgc:transparent;--minw:calc(100% / 3 - 13.333333333333334px);--minw-tablet:calc(100% / 3 - 13.333333333333334px);--minw-mobile:calc(100% / 1.5 - 6.666666666666667px);--maxw:calc(100% / 3 - 13.333333333333334px);--maxw-tablet:calc(100% / 3 - 13.333333333333334px);--maxw-mobile:calc(100% / 1.5 - 6.666666666666667px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-g9fbb2WBjK gbqkOpeZJF"
      data-index="1"
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      <div
    
     data-id="gCRZ6lBUMB"
    role="presentation"
    class="gp-group/image gCRZ6lBUMB gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mt:52px;--mb:20px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_22_768x.jpg?v=1703431825" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_22_768x.jpg?v=1703431825" />
      <source media="(max-width: 1024px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_22_1024x.jpg?v=1703431825" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_22_1024x.jpg?v=1703431825" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_22.jpg?v=1703431825"
        data-src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_22.jpg?v=1703431825"
        width="100%"
        alt=""
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div><div
    
     data-id="gDmyKLN42E"
    role="presentation"
    class="gp-group/image gDmyKLN42E gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/smart_watches_bg_768x.svg?v=1722523515" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/smart_watches_bg_768x.svg?v=1722523515" />
      <source media="(max-width: 1024px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/smart_watches_bg_1024x.svg?v=1722523515" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/smart_watches_bg_1024x.svg?v=1722523515" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/smart_watches_bg.svg?v=1722523515"
        data-src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/smart_watches_bg.svg?v=1722523515"
        width="100%"
        alt=""
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      label="Carousel Item" tag="CarouselItem" type="component"
      style="--bgc:transparent;--minw:calc(100% / 3 - 13.333333333333334px);--minw-tablet:calc(100% / 3 - 13.333333333333334px);--minw-mobile:calc(100% / 1.5 - 6.666666666666667px);--maxw:calc(100% / 3 - 13.333333333333334px);--maxw-tablet:calc(100% / 3 - 13.333333333333334px);--maxw-mobile:calc(100% / 1.5 - 6.666666666666667px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-g9fbb2WBjK g0ihU4DKwh"
      data-index="2"
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      <div
    
     data-id="gQngj7zjG7"
    role="presentation"
    class="gp-group/image gQngj7zjG7 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:20px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_50_768x.jpg?v=1727426429" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_50_768x.jpg?v=1727426429" />
      <source media="(max-width: 1024px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_50_1024x.jpg?v=1727426429" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_50_1024x.jpg?v=1727426429" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_50.jpg?v=1727426429"
        data-src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_50.jpg?v=1727426429"
        width="100%"
        alt=""
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div><div
    
     data-id="g5A7zfxGZ2"
    role="presentation"
    class="gp-group/image g5A7zfxGZ2 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/pdp_col_2_768x.svg?v=1722524196" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/pdp_col_2_768x.svg?v=1722524196" />
      <source media="(max-width: 1024px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/pdp_col_2_1024x.svg?v=1722524196" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/pdp_col_2_1024x.svg?v=1722524196" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/pdp_col_2.svg?v=1722524196"
        data-src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/pdp_col_2.svg?v=1722524196"
        width="100%"
        alt=""
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
      </div>
    </div>
  
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 gp-carousel-action-next gem-slider-next g9fbb2WBjK-{{section.id}} gp-carousel-arrow-g9fbb2WBjK gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--left:initial;--top:;--right:initial;--bottom:initial;--left-tablet:initial;--top-tablet:;--right-tablet:initial;--bottom-tablet:initial;--left-mobile:initial;--top-mobile:;--right-mobile:initial;--bottom-mobile:initial;--d:none;--d-tablet:none;--d-mobile:none;background-color:transparent;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
  style="--c:#000000;--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
  >
    <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z"/>
              </svg>
              
    </div>
      <style>
    .gp-carousel-arrow-g9fbb2WBjK {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-g9fbb2WBjK::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-g9fbb2WBjK {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-g9fbb2WBjK::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-g9fbb2WBjK {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-g9fbb2WBjK::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
        </div>
        <div
    class="gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-carousel-dot-container gp-carousel-dot-container-g9fbb2WBjK-{{section.id}} gp-static gp-flex-row gp-left-0 gp-right-0 tablet:gp-static tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-static mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0"
    style="--bottom:16px;--bottom-tablet:16px;--bottom-mobile:16px;--d:none;--d-tablet:none;--d-mobile:none"
 ></div>
      </div>
    </gp-carousel>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-carousel-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 6",
    "tag": "section",
    "class": "gps-554688430635287792 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=554688430584366320&sectionId=554688430635287792)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggwjswY0Y8j_text","label":"ggwjswY0Y8j_text","default":"ELEVATE EVERY SINGLE MOMENT"},{"type":"html","id":"ggmC-d-m4fy_text","label":"ggmC-d-m4fy_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:18px;\">From workouts to adventures, our men's smartwatches are built to keep you moving every step of the way.</span></p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
