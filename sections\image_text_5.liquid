<section class="image_text_5"  style="background:{{section.settings.bg_color}}; {% if section.settings.bg_image != blank %}background-image: url({{ section.settings.bg_image | img_url: 'master' }}); background-size: cover; background-repeat: no-repeat; background-position: center;{% endif %};">
  <div class="page_wrapper">
    <div class="wrapper">
      <div class="text_block">
        {% if section.settings.heading != blank %}
        <h2 class="heading">{{ section.settings.heading }}</h2>
        {% endif %}
        {% if section.settings.content != blank %}
        <div class="content">{{ section.settings.content }}</div>
        {% endif %}
      </div>
     
      
    </div>
  </div>
   <div class="image_block">
        {% if section.settings.image != blank %}
        <img src="{{ section.settings.image |img_url :'master' }}">
        {% endif %}
      </div>
</section>

<style>



section.image_text_5 .wrapper .text_block {
    width: 100%;
    display: flex
;
    flex-direction: column;
    gap: 30px;
    max-width: 890px;
}
section.image_text_5 .wrapper .text_block h2.heading {
    font-size: 74px;
    font-family: Helvetica-Bold;
    margin: 0;
  color:#2a2b2b;
      text-transform: none;
}
section.image_text_5 .wrapper .text_block .content p {
    margin: 0;
    font-size: 25px;
    font-family: Helvetica-Bold;
    color: #676666;
  font-weight:700;
}
  section.image_text_5 .image_block {
    width: 100%;
    font-size: 0;
        padding-top: 50px;
}
  section.image_text_5 .image_block img{
    width: 100%;
    }
  .image_text_5 .wrapper {
    display: grid;
        align-items: center;
}
  section.image_text_5 .wrapper .text_block .content {
    width: 100%;
    /* max-width: 530px; */
}

    section.image_text_5 {
    padding: 50px 0px;
}
  .image_text_5 .page_wrapper {
      padding: 0px 150px;
}
  @media only screen and (min-width: 2600px) {
    section.image_text_5 .wrapper .text_block{
      max-width:1760px;
             
    }
  section.image_text_5 .wrapper .text_block h2.heading {
    font-size: 135px;
     padding-bottom: 50px;
  }
    section.image_text_5 .wrapper .text_block .content p {

    font-size: 50px;
    }
  }
    @media only screen and (max-width: 1600px) {
  section.image_text_5 {
    padding: 25px 0px;
}
        .image_text_5 .page_wrapper {
              padding: 0px 60px;
        }
   }
  @media only screen and (max-width: 1280px) {
section.image_text_5 .wrapper .text_block h2.heading {
    font-size: 48px;
}
    section.image_text_5 .wrapper .text_block {
    max-width: 700px;
}
      section.image_text_5 .wrapper .text_block .content p {

    font-size: 20px;
    }
  }
  @media only screen and (max-width: 1024px) {
    section.image_text_5 .wrapper .text_block {
        max-width: 100%;
    }
  }
  @media only screen and (max-width: 840px) {

        section.image_text_5 {
        padding: 30px 0px;
    }
       .image_text_5 .page_wrapper {
          padding: 0px 20px; 
       }
section.image_text_5 .image_block {
           padding-top: 20px;
}
  }
    @media only screen and (max-width: 480px) {
      section.image_text_5 .wrapper .text_block h2.heading {
        font-size: 38px;
    }
        section.image_text_5 .wrapper .text_block .content p {

    font-size: 18px;
    }
  }
</style>




  

{% schema %}
{
  "name": "Image Text 5",
  "settings": [
    {
      "type": "color",
      "id": "bg_color",
      "default": "transparent",
      "label": "Backgroud Color"
    },
       {
      "type": "image_picker",
      "id": "bg_image",
      "label": "Background Image"
    },
      {
          "type": "text",
          "id": "heading",
          "label": "Heading"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "content"
        },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    }
    
    
    
    
  ],

  "presets": [
    {
      "name": "Image Text 5",
      "blocks": []
    }
  ]
}
{% endschema %}

