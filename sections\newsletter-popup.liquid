{% unless section.settings.mode == blank or section.settings.disable_for_account_holders and customer %}

{% for block in section.blocks %}
  {% if block.type == 'header' %}
    {% assign has_reminder = true %}
  {% endif %}
{% endfor %}

<div
  id="NewsletterPopup-{{ section.id }}"
  class="modal modal--square modal--mobile-friendly"
  data-section-id="{{ section.id }}"
  data-section-type="newsletter-popup"
  data-delay-days="{{ section.settings.popup_days }}"
  data-has-reminder="{{ has_reminder }}"
  data-delay-seconds="{{ section.settings.popup_seconds }}"
  {% if section.settings.popup_image != blank %}data-has-image{% endif %}
  data-test-mode="{{ request.design_mode }}"
  data-enabled="{{ section.settings.mode }}">
  <div class="modal__inner">
    <div class="modal__centered medium-up--text-center">
      <div class="modal__centered-content {% if section.settings.popup_image != blank %}newsletter--has-image{% endif %}">

        <div class="newsletter newsletter-popup {% if section.settings.image_position == 'right' %}newsletter-popup--image-reversed{% endif %}">
          {% if section.settings.popup_image != blank %}
            <div class="newsletter-popup__image"></div>
            {% style %}
              .newsletter-popup__image {
                background-image: url({{ section.settings.popup_image | img_url: '1000x' }});
              }
            {% endstyle %}
          {% endif %}

          <div class="newsletter-popup__content">
            {%- if section.settings.popup_title != blank -%}
              <h3>{{ section.settings.popup_title }}</h3>
            {%- endif -%}

            {%- if section.settings.popup_text != blank -%}
              <div class="rte">
                {{ section.settings.popup_text }}
              </div>
            {%- endif -%}

            {%- if section.settings.enable_newsletter -%}
              <div class="popup-cta">
                {%- render 'newsletter-form', section_id: section.id, snippet_context: 'popup' -%}
              </div>
            {%- endif -%}

            {% if section.settings.button_label != blank %}
              <a href="{{ section.settings.button_link }}" class="btn newsletter-button">
                <div class="button--text">
                  {{ section.settings.button_label }}
                </div>
              </a>
            {% endif %}

            {% if section.settings.show_social_icons %}
              {% render 'social-icons', additional_classes: 'inline-list' %}
            {% endif %}
          </div>
        </div>
      </div>

      <button type="button" class="modal__close js-modal-close text-link">
        <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-close" viewBox="0 0 64 64"><path d="M19 17.61l27.12 27.13m0-27.12L19 44.74"/></svg>
        <span class="icon__fallback-text">{{ 'general.accessibility.close_modal' | t | json }}</span>
      </button>
    </div>
  </div>
</div>

{% for block in section.blocks %}
  {% if block.type == 'header' %}
    {% render 'newsletter-reminder',
      section: section,
      block: block
    %}
  {% endif %}
{% endfor %}

{% endunless %}

{% schema %}
  {
    "name": "t:sections.newsletter-popup.name",
    "max_blocks": 1,
    "class": "index-section--hidden",
    "settings": [
      {
        "type": "checkbox",
        "id": "mode",
        "label": "t:sections.newsletter-popup.settings.mode.label",
        "default": false,
        "info": "t:sections.newsletter-popup.settings.mode.info"
      },
      {
        "type": "checkbox",
        "id": "disable_for_account_holders",
        "label": "t:sections.newsletter-popup.settings.disable_for_account_holders.label",
        "default": true,
        "info": "t:sections.newsletter-popup.settings.disable_for_account_holders.info"
      },
      {
        "type": "range",
        "id": "popup_seconds",
        "label": "t:sections.newsletter-popup.settings.popup_seconds.label",
        "info": "t:sections.newsletter-popup.settings.popup_seconds.info",
        "default": 5,
        "min": 2,
        "max": 60,
        "step": 1,
        "unit": "sec"
      },
      {
        "type": "range",
        "id": "popup_days",
        "label": "t:sections.newsletter-popup.settings.popup_days.label",
        "default": 30,
        "info": "t:sections.newsletter-popup.settings.popup_days.info",
        "min": 2,
        "max": 30,
        "step": 1,
        "unit": "day"
      },
      {
        "type": "header",
        "content": "t:sections.newsletter-popup.settings.header_content"
      },
      {
        "type": "text",
        "id": "popup_title",
        "label": "t:sections.newsletter-popup.settings.popup_title.label",
        "default": "Sign up and save"
      },
      {
        "type": "richtext",
        "id": "popup_text",
        "label": "t:sections.newsletter-popup.settings.popup_text.label",
        "default": "<p>Entice customers to sign up for your mailing list with discounts or exclusive offers. Include an image for extra impact.</p>"
      },
      {
        "type": "image_picker",
        "id": "popup_image",
        "label": "t:sections.newsletter-popup.settings.popup_image.label",
        "info": "t:sections.newsletter-popup.settings.popup_image.info"
      },
      {
        "type": "select",
        "id": "image_position",
        "label": "t:sections.newsletter-popup.settings.image_position.label",
        "default": "left",
        "options": [
          {
            "value": "left",
            "label": "t:sections.newsletter-popup.settings.image_position.options.left.label"
          },
          {
            "value": "right",
            "label": "t:sections.newsletter-popup.settings.image_position.options.right.label"
          }
        ]
      },
      {
        "type": "checkbox",
        "id": "show_social_icons",
        "label": "Show social icons",
        "default": false
      },
      {
        "type": "header",
        "content": "t:sections.newsletter-popup.settings.header_newsletter"
      },
      {
        "type": "paragraph",
        "content": "t:sections.newsletter-popup.settings.content"
      },
      {
        "type": "checkbox",
        "id": "enable_newsletter",
        "label": "t:sections.newsletter-popup.settings.enable_newsletter.label",
        "default": true
      },
      {
        "type": "header",
        "content": "t:sections.newsletter-popup.settings.header_button"
      },
      {
        "type": "text",
        "id": "button_label",
        "label": "t:sections.newsletter-popup.settings.button_label.label",
        "default": "Optional button"
      },
      {
        "type": "url",
        "id": "button_link",
        "label": "t:sections.newsletter-popup.settings.button_link.label"
      }
    ],
    "blocks": [
      {
        "type": "header",
        "name": "t:sections.newsletter-popup.blocks.header.name",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "reminder_label",
            "label": "t:sections.newsletter-popup.blocks.header.settings.text.label",
            "default": "Get 10% off",
            "info": "t:sections.newsletter-popup.blocks.header.settings.text.info"
          }
        ]
      }
    ],
    "default": {
      "settings": {},
      "blocks": [
        {
          "type": "header",
          "settings": {}
        }
      ]
    }
  }
{% endschema %}
