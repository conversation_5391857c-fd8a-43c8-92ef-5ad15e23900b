

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-555524770926560178.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-555524770926560178.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-555524770926560178.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-555524770926560178.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-555524770926560178.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-555524770926560178.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-555524770926560178.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-555524770926560178.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-555524770926560178.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-555524770926560178.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-555524770926560178.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-555524770926560178.gps.gpsil [style*="--hvr-bc:"]:hover{border-color:var(--hvr-bc)}.gps-555524770926560178.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-555524770926560178.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-555524770926560178.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-555524770926560178.gps.gpsil [style*="--hvr-bs:"]:hover{border-style:var(--hvr-bs)}.gps-555524770926560178.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-555524770926560178.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-555524770926560178.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-555524770926560178.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-555524770926560178.gps.gpsil [style*="--hvr-bw:"]:hover{border-width:var(--hvr-bw)}.gps-555524770926560178.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-555524770926560178.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-555524770926560178.gps.gpsil [style*="--hvr-c:"]:hover{color:var(--hvr-c)}.gps-555524770926560178.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-555524770926560178.gps.gpsil [style*="--fd:"]{flex-direction:var(--fd)}.gps-555524770926560178.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-555524770926560178.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-555524770926560178.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-555524770926560178.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-555524770926560178.gps.gpsil [style*="--gg:"]{grid-gap:var(--gg)}.gps-555524770926560178.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-555524770926560178.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-555524770926560178.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-555524770926560178.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-555524770926560178.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-555524770926560178.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-555524770926560178.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-555524770926560178.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-555524770926560178.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-555524770926560178.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-555524770926560178.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-555524770926560178.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-555524770926560178.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-555524770926560178.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-555524770926560178.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-555524770926560178.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-555524770926560178.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-555524770926560178.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-555524770926560178.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-555524770926560178.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-555524770926560178.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-555524770926560178.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-555524770926560178.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-555524770926560178.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-555524770926560178.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-555524770926560178.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-555524770926560178.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-555524770926560178.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-555524770926560178.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-555524770926560178.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-555524770926560178.gps.gpsil [style*="--gg-mobile:"]{grid-gap:var(--gg-mobile)}.gps-555524770926560178.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-555524770926560178.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-555524770926560178.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-555524770926560178.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-555524770926560178.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-555524770926560178.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-555524770926560178.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-555524770926560178.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-555524770926560178.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-555524770926560178 .-gp-rotate-90,.gps-555524770926560178 .gp-rotate-180,.gps-555524770926560178 .gp-rotate-90{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-555524770926560178 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-555524770926560178 .gp-relative{position:relative}.gps-555524770926560178 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-555524770926560178 .gp-mb-0{margin-bottom:0}.gps-555524770926560178 .gp-flex{display:flex}.gps-555524770926560178 .gp-inline-flex{display:inline-flex}.gps-555524770926560178 .gp-grid{display:grid}.gps-555524770926560178 .\!gp-hidden{display:none!important}.gps-555524770926560178 .gp-hidden{display:none}.gps-555524770926560178 .gp-min-h-0{min-height:0}.gps-555524770926560178 .gp-w-full{width:100%}.gps-555524770926560178 .gp-max-w-full{max-width:100%}.gps-555524770926560178 .-gp-rotate-90{--tw-rotate:-90deg}.gps-555524770926560178 .-gp-rotate-90,.gps-555524770926560178 .gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555524770926560178 .gp-rotate-180{--tw-rotate:180deg}.gps-555524770926560178 .gp-rotate-90{--tw-rotate:90deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555524770926560178 .gp-cursor-pointer{cursor:pointer}.gps-555524770926560178 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-555524770926560178 .gp-flex-col{flex-direction:column}.gps-555524770926560178 .gp-items-center{align-items:center}.gps-555524770926560178 .gp-justify-center{justify-content:center}.gps-555524770926560178 .gp-gap-y-0{row-gap:0}.gps-555524770926560178 .gp-overflow-hidden{overflow:hidden}.gps-555524770926560178 .gp-overflow-clip{overflow:clip}.gps-555524770926560178 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-555524770926560178 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555524770926560178 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555524770926560178 .gp-duration-200{transition-duration:.2s}.gps-555524770926560178 .gp-duration-500{transition-duration:.5s}.gps-555524770926560178 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-555524770926560178 .tablet\:\!gp-hidden{display:none!important}.gps-555524770926560178 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-555524770926560178 .mobile\:\!gp-hidden{display:none!important}.gps-555524770926560178 .mobile\:gp-hidden{display:none}}.gps-555524770926560178 .\[\&\>svg\]\:\!gp-h-\[var\(--height-iconCollapseSize\)\]>svg{height:var(--height-iconCollapseSize)!important}.gps-555524770926560178 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}.gps-555524770926560178 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-555524770926560178 .\[\&_p\]\:gp-inline p{display:inline}.gps-555524770926560178 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-555524770926560178 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gWT30OmuMm" data-id="gWT30OmuMm"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mr:15px;--pt:var(--g-s-4xl);--pl:15px;--pb:var(--g-s-4xl);--pt-tablet:var(--g-s-4xl);--pl-tablet:15px;--pb-tablet:var(--g-s-4xl);--pr-tablet:15px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gWT30OmuMm gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g6iqoEZro4 gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="g10L2x7DOC" data-id="g10L2x7DOC"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-l);--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g10L2x7DOC gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g7IefxhRWb gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="ggriLL81KY">
    <div
      parentTag="Col"
        class="ggriLL81KY "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:48px;--mb-mobile:18px"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:33px;--size-tablet:33px;--size-mobile:29px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.gggriLL81KY_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gQp1dXoooZ" data-id="gQp1dXoooZ"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(undefined);--cg:30px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gQp1dXoooZ gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gTeXOsQnmk gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px;--mb-mobile:var(--g-s-xl)" class="g_cF3TjNR1 ">
      
    <gp-accordion
      data-id="g_cF3TjNR1"
     uid="g_cF3TjNR1"
      class="gp-flex gp-w-full gp-flex-col "
      style="--gg:24px;--gg-mobile:21px;border-radius:inherit"
      gp-data='{"setting":{"iconSvg":"<svg width=\"17\" height=\"16\" viewBox=\"0 0 17 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M4 12.1763V3.82365C4 3.15846 4.88548 2.7686 5.52991 3.15006L12.5853 7.32641C13.1382 7.65374 13.1382 8.34626 12.5853 8.67359L5.52991 12.8499C4.88548 13.2314 4 12.8415 4 12.1763Z\" fill=\"currentColor\"/>\n                    </svg>","isIconPlus":false,"activeKey":1,"expanded":false,"expandItem":false,"iconPosition":"right","iconGlobalSize":{"desktop":{"gap":"16px","height":"16px","width":"16px"}},"layoutHeader":"text-only","expandedMode":"single","configIconSize":16,"parentUid":"g_cF3TjNR1","childListNumber":[],"chidlrenUid":["g3DwSHuXPw","gu_zlgbwK-","gUds2YFW0T","gab5aeyBEy"]},"styles":{"bgColor":{"active":"transparent"},"color":{"active":"#575757","hover":"#B4B4B4","normal":"#B4B4B4"},"headerBorder":{"active":{"border":"solid","borderType":"style-1","borderWidth":"0","color":"#121212","isCustom":true,"position":"bottom","width":"0px 0px 0px 0px"},"hover":{"border":"solid","borderType":"style-1","borderWidth":"0","color":"#121212","isCustom":true,"position":"bottom","width":"0px 0px 0px 0px"},"normal":{"border":"solid","borderType":"style-1","borderWidth":"0","color":"#121212","isCustom":true,"position":"bottom","width":"0px 0px 0px 0px"}},"fullWidth":{"desktop":true},"width":{"desktop":"1170px"},"itemHeaderSpacing":{"custom":{"desktop":{"horizontal":"24px","vertical":"16px"}}},"textColor":{"active":"#242424","hover":"#242424","normal":"#242424"},"iconColor":{"active":"#121212","hover":"#121212","normal":"#121212"},"contentSizePadding":{"desktop":{"gap":"24px","padding":{"bottom":"0px","left":"24px","right":"24px","top":"0px","type":"custom"}},"mobile":{"gap":"21px","padding":{"bottom":"0px","left":"24px","right":"24px","top":"0px","type":"custom"}},"tablet":{"padding":{"bottom":"0px","left":"24px","right":"24px","top":"0px","type":"custom"}}},"headerContentPadding":{"desktop":{"padding":{"bottom":"16px","left":"24px","right":"24px","top":"16px","type":"custom"}},"mobile":{"padding":{"bottom":"16px","left":"24px","right":"24px","top":"16px","type":"custom"}},"tablet":{"padding":{"bottom":"16px","left":"24px","right":"24px","top":"16px","type":"custom"}}},"widthHeightSize":{"desktop":{"width":"100%"},"mobile":{"width":"100%"},"tablet":{"width":"100%"}}},"uid":"g_cF3TjNR1"}'
    >
    <div class="gp-hidden gp-rotate-90 -gp-rotate-90 gp-rotate-180"></div>
    <div
                class="gp-flex"
                style="--jc:left"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:1px 1px 1px 1px;--bc:#F3F3F3;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px">
    <div
      data-index="0"
      class="g_cF3TjNR1 gp-accordion-item gp-overflow-hidden gp-child-item-g_cF3TjNR1"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="g3DwSHuXPw"
        id="g3DwSHuXPw"
        data-index="0"
        class="g_cF3TjNR1 gp-accordion-item-g_cF3TjNR1-0 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:24px;--pr:24px;--pt:16px;--pb:16px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:16px;--pb-tablet:16px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:16px;--pb-mobile:16px;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-c:#242424;--c:#242424;--bs:solid;--hvr-bs:solid;--bw:0px 0px 0px 0px;--hvr-bw:0px 0px 0px 0px;--bc:#121212;--hvr-bc:#121212"
      >
       <style class="accordion-style">.gp-accordion-item-g_cF3TjNR1-0:hover 
      {
        .gp-collapsible-icon { 
          color: #B4B4B4 !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#B4B4B4;--c:#B4B4B4;width:16px;height:16px"
            data-index="0"
            class="g_cF3TjNR1 gp-accordion-item_icon gp-collapsible-icon gp-rotate-90 gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          ><svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 12.1763V3.82365C4 3.15846 4.88548 2.7686 5.52991 3.15006L12.5853 7.32641C13.1382 7.65374 13.1382 8.34626 12.5853 8.67359L5.52991 12.8499C4.88548 13.2314 4 12.8415 4 12.1763Z" fill="currentColor"/>
                    </svg></span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg_cF3TjNR1_childItem_0 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="g3DwSHuXPw"
        data-index="0"
        data-show="false"
        class="g_cF3TjNR1 gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="g3DwSHuXPw"
        data-index="0"
        class="g_cF3TjNR1 gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:24px;--pr:24px;--pl-tablet:24px;--pr-tablet:24px;--pl-mobile:24px;--pr-mobile:24px" >
            <div
      label="Block" tag="Col" type="component"
      
      class="gIZQbXp-rH gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gXdvKuaqYZ">
    <div
      parentTag="Col"
        class="gXdvKuaqYZ "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggXdvKuaqYZ_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            <div
                class="gp-flex"
                style="--jc:left"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:1px 1px 1px 1px;--bc:#F3F3F3;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px">
    <div
      data-index="1"
      class="g_cF3TjNR1 gp-accordion-item gp-overflow-hidden gp-child-item-g_cF3TjNR1"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gu_zlgbwK-"
        id="gu_zlgbwK-"
        data-index="1"
        class="g_cF3TjNR1 gp-accordion-item-g_cF3TjNR1-1 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:24px;--pr:24px;--pt:16px;--pb:16px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:16px;--pb-tablet:16px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:16px;--pb-mobile:16px;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-c:#242424;--c:#242424;--bs:solid;--hvr-bs:solid;--bw:0px 0px 0px 0px;--hvr-bw:0px 0px 0px 0px;--bc:#121212;--hvr-bc:#121212"
      >
       <style class="accordion-style">.gp-accordion-item-g_cF3TjNR1-1:hover 
      {
        .gp-collapsible-icon { 
          color: #B4B4B4 !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#B4B4B4;--c:#B4B4B4;width:16px;height:16px"
            data-index="1"
            class="g_cF3TjNR1 gp-accordion-item_icon gp-collapsible-icon gp-rotate-90 gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          ><svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 12.1763V3.82365C4 3.15846 4.88548 2.7686 5.52991 3.15006L12.5853 7.32641C13.1382 7.65374 13.1382 8.34626 12.5853 8.67359L5.52991 12.8499C4.88548 13.2314 4 12.8415 4 12.1763Z" fill="currentColor"/>
                    </svg></span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg_cF3TjNR1_childItem_1 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gu_zlgbwK-"
        data-index="1"
        data-show="false"
        class="g_cF3TjNR1 gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gu_zlgbwK-"
        data-index="1"
        class="g_cF3TjNR1 gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:24px;--pr:24px;--pl-tablet:24px;--pr-tablet:24px;--pl-mobile:24px;--pr-mobile:24px" >
            <div
      label="Block" tag="Col" type="component"
      
      class="gyaEbi07I5 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gZvM8g_z3E">
    <div
      parentTag="Col"
        class="gZvM8g_z3E "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggZvM8g_z3E_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            <div
                class="gp-flex"
                style="--jc:left"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:1px 1px 1px 1px;--bc:#F3F3F3;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px">
    <div
      data-index="2"
      class="g_cF3TjNR1 gp-accordion-item gp-overflow-hidden gp-child-item-g_cF3TjNR1"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gUds2YFW0T"
        id="gUds2YFW0T"
        data-index="2"
        class="g_cF3TjNR1 gp-accordion-item-g_cF3TjNR1-2 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:24px;--pr:24px;--pt:16px;--pb:16px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:16px;--pb-tablet:16px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:16px;--pb-mobile:16px;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-c:#242424;--c:#242424;--bs:solid;--hvr-bs:solid;--bw:0px 0px 0px 0px;--hvr-bw:0px 0px 0px 0px;--bc:#121212;--hvr-bc:#121212"
      >
       <style class="accordion-style">.gp-accordion-item-g_cF3TjNR1-2:hover 
      {
        .gp-collapsible-icon { 
          color: #B4B4B4 !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#B4B4B4;--c:#B4B4B4;width:16px;height:16px"
            data-index="2"
            class="g_cF3TjNR1 gp-accordion-item_icon gp-collapsible-icon gp-rotate-90 gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          ><svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 12.1763V3.82365C4 3.15846 4.88548 2.7686 5.52991 3.15006L12.5853 7.32641C13.1382 7.65374 13.1382 8.34626 12.5853 8.67359L5.52991 12.8499C4.88548 13.2314 4 12.8415 4 12.1763Z" fill="currentColor"/>
                    </svg></span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg_cF3TjNR1_childItem_2 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gUds2YFW0T"
        data-index="2"
        data-show="false"
        class="g_cF3TjNR1 gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gUds2YFW0T"
        data-index="2"
        class="g_cF3TjNR1 gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:24px;--pr:24px;--pl-tablet:24px;--pr-tablet:24px;--pl-mobile:24px;--pr-mobile:24px" >
            <div
      label="Block" tag="Col" type="component"
      
      class="gWdelcKlAg gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gfMJz9v7Bq">
    <div
      parentTag="Col"
        class="gfMJz9v7Bq "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggfMJz9v7Bq_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            <div
                class="gp-flex"
                style="--jc:left"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:1px 1px 1px 1px;--bc:#F3F3F3;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px">
    <div
      data-index="3"
      class="g_cF3TjNR1 gp-accordion-item gp-overflow-hidden gp-child-item-g_cF3TjNR1"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gab5aeyBEy"
        id="gab5aeyBEy"
        data-index="3"
        class="g_cF3TjNR1 gp-accordion-item-g_cF3TjNR1-3 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:24px;--pr:24px;--pt:16px;--pb:16px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:16px;--pb-tablet:16px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:16px;--pb-mobile:16px;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-c:#242424;--c:#242424;--bs:solid;--hvr-bs:solid;--bw:0px 0px 0px 0px;--hvr-bw:0px 0px 0px 0px;--bc:#121212;--hvr-bc:#121212"
      >
       <style class="accordion-style">.gp-accordion-item-g_cF3TjNR1-3:hover 
      {
        .gp-collapsible-icon { 
          color: #B4B4B4 !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#B4B4B4;--c:#B4B4B4;width:16px;height:16px"
            data-index="3"
            class="g_cF3TjNR1 gp-accordion-item_icon gp-collapsible-icon gp-rotate-90 gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          ><svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 12.1763V3.82365C4 3.15846 4.88548 2.7686 5.52991 3.15006L12.5853 7.32641C13.1382 7.65374 13.1382 8.34626 12.5853 8.67359L5.52991 12.8499C4.88548 13.2314 4 12.8415 4 12.1763Z" fill="currentColor"/>
                    </svg></span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg_cF3TjNR1_childItem_3 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gab5aeyBEy"
        data-index="3"
        data-show="false"
        class="g_cF3TjNR1 gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gab5aeyBEy"
        data-index="3"
        class="g_cF3TjNR1 gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:24px;--pr:24px;--pl-tablet:24px;--pr-tablet:24px;--pl-mobile:24px;--pr-mobile:24px" >
            <div
      label="Block" tag="Col" type="component"
      
      class="go-_1WAoMu gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g8FP_rn5yn">
    <div
      parentTag="Col"
        class="g8FP_rn5yn "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg8FP_rn5yn_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            
    </gp-accordion>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-accordion.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gCtKMMaHR3 gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px" class="gGgmiN5pTZ ">
      
    <gp-accordion
      data-id="gGgmiN5pTZ"
     uid="gGgmiN5pTZ"
      class="gp-flex gp-w-full gp-flex-col "
      style="--gg:24px;border-radius:inherit"
      gp-data='{"setting":{"iconSvg":"<svg width=\"17\" height=\"16\" viewBox=\"0 0 17 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M4 12.1763V3.82365C4 3.15846 4.88548 2.7686 5.52991 3.15006L12.5853 7.32641C13.1382 7.65374 13.1382 8.34626 12.5853 8.67359L5.52991 12.8499C4.88548 13.2314 4 12.8415 4 12.1763Z\" fill=\"currentColor\"/>\n                    </svg>","isIconPlus":false,"activeKey":1,"expanded":false,"expandItem":false,"iconPosition":"right","iconGlobalSize":{"desktop":{"gap":"16px","height":"16px","width":"16px"}},"layoutHeader":"text-only","expandedMode":"single","configIconSize":16,"parentUid":"gGgmiN5pTZ","childListNumber":[],"chidlrenUid":["gih22OUeco","gY8DTpfn8L","gcfVLEFcmQ","g2RHBs5gub"]},"styles":{"bgColor":{"active":"transparent"},"color":{"active":"#575757","hover":"#B4B4B4","normal":"#B4B4B4"},"headerBorder":{"active":{"border":"solid","borderType":"style-1","borderWidth":"0","color":"#121212","isCustom":true,"position":"bottom","width":"0px 0px 0px 0px"},"hover":{"border":"solid","borderType":"style-1","borderWidth":"0","color":"#121212","isCustom":true,"position":"bottom","width":"0px 0px 0px 0px"},"normal":{"border":"solid","borderType":"style-1","borderWidth":"0","color":"#121212","isCustom":true,"position":"bottom","width":"0px 0px 0px 0px"}},"fullWidth":{"desktop":true},"width":{"desktop":"1170px"},"itemHeaderSpacing":{"custom":{"desktop":{"horizontal":"24px","vertical":"16px"}}},"textColor":{"active":"#242424","hover":"#242424","normal":"#242424"},"iconColor":{"active":"#121212","hover":"#121212","normal":"#121212"},"contentSizePadding":{"desktop":{"gap":"24px","padding":{"bottom":"0px","left":"24px","right":"24px","top":"0px","type":"custom"}},"mobile":{"padding":{"bottom":"0px","left":"24px","right":"24px","top":"0px","type":"custom"}},"tablet":{"padding":{"bottom":"0px","left":"24px","right":"24px","top":"0px","type":"custom"}}},"headerContentPadding":{"desktop":{"padding":{"bottom":"16px","left":"24px","right":"24px","top":"16px","type":"custom"}},"mobile":{"padding":{"bottom":"16px","left":"24px","right":"24px","top":"16px","type":"custom"}},"tablet":{"padding":{"bottom":"16px","left":"24px","right":"24px","top":"16px","type":"custom"}}},"widthHeightSize":{"desktop":{"width":"100%"},"mobile":{"width":"100%"},"tablet":{"width":"100%"}}},"uid":"gGgmiN5pTZ"}'
    >
    <div class="gp-hidden gp-rotate-90 -gp-rotate-90 gp-rotate-180"></div>
    <div
                class="gp-flex"
                style="--jc:left"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:1px 1px 1px 1px;--bc:#F3F3F3;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px">
    <div
      data-index="0"
      class="gGgmiN5pTZ gp-accordion-item gp-overflow-hidden gp-child-item-gGgmiN5pTZ"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gih22OUeco"
        id="gih22OUeco"
        data-index="0"
        class="gGgmiN5pTZ gp-accordion-item-gGgmiN5pTZ-0 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:24px;--pr:24px;--pt:16px;--pb:16px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:16px;--pb-tablet:16px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:16px;--pb-mobile:16px;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-c:#242424;--c:#242424;--bs:solid;--hvr-bs:solid;--bw:0px 0px 0px 0px;--hvr-bw:0px 0px 0px 0px;--bc:#121212;--hvr-bc:#121212"
      >
       <style class="accordion-style">.gp-accordion-item-gGgmiN5pTZ-0:hover 
      {
        .gp-collapsible-icon { 
          color: #B4B4B4 !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#B4B4B4;--c:#B4B4B4;width:16px;height:16px"
            data-index="0"
            class="gGgmiN5pTZ gp-accordion-item_icon gp-collapsible-icon gp-rotate-90 gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          ><svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 12.1763V3.82365C4 3.15846 4.88548 2.7686 5.52991 3.15006L12.5853 7.32641C13.1382 7.65374 13.1382 8.34626 12.5853 8.67359L5.52991 12.8499C4.88548 13.2314 4 12.8415 4 12.1763Z" fill="currentColor"/>
                    </svg></span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggGgmiN5pTZ_childItem_0 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gih22OUeco"
        data-index="0"
        data-show="false"
        class="gGgmiN5pTZ gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gih22OUeco"
        data-index="0"
        class="gGgmiN5pTZ gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:24px;--pr:24px;--pl-tablet:24px;--pr-tablet:24px;--pl-mobile:24px;--pr-mobile:24px" >
            <div
      label="Block" tag="Col" type="component"
      
      class="gqwONC0LCL gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g_Xx_c5sEf">
    <div
      parentTag="Col"
        class="g_Xx_c5sEf "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg_Xx_c5sEf_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            <div
                class="gp-flex"
                style="--jc:left"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:1px 1px 1px 1px;--bc:#F3F3F3;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px">
    <div
      data-index="1"
      class="gGgmiN5pTZ gp-accordion-item gp-overflow-hidden gp-child-item-gGgmiN5pTZ"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gY8DTpfn8L"
        id="gY8DTpfn8L"
        data-index="1"
        class="gGgmiN5pTZ gp-accordion-item-gGgmiN5pTZ-1 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:24px;--pr:24px;--pt:16px;--pb:16px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:16px;--pb-tablet:16px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:16px;--pb-mobile:16px;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-c:#242424;--c:#242424;--bs:solid;--hvr-bs:solid;--bw:0px 0px 0px 0px;--hvr-bw:0px 0px 0px 0px;--bc:#121212;--hvr-bc:#121212"
      >
       <style class="accordion-style">.gp-accordion-item-gGgmiN5pTZ-1:hover 
      {
        .gp-collapsible-icon { 
          color: #B4B4B4 !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#B4B4B4;--c:#B4B4B4;width:16px;height:16px"
            data-index="1"
            class="gGgmiN5pTZ gp-accordion-item_icon gp-collapsible-icon gp-rotate-90 gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          ><svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 12.1763V3.82365C4 3.15846 4.88548 2.7686 5.52991 3.15006L12.5853 7.32641C13.1382 7.65374 13.1382 8.34626 12.5853 8.67359L5.52991 12.8499C4.88548 13.2314 4 12.8415 4 12.1763Z" fill="currentColor"/>
                    </svg></span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggGgmiN5pTZ_childItem_1 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gY8DTpfn8L"
        data-index="1"
        data-show="false"
        class="gGgmiN5pTZ gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gY8DTpfn8L"
        data-index="1"
        class="gGgmiN5pTZ gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:24px;--pr:24px;--pl-tablet:24px;--pr-tablet:24px;--pl-mobile:24px;--pr-mobile:24px" >
            <div
      label="Block" tag="Col" type="component"
      
      class="gwqTyHUsVW gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gvIzRZ4kqU">
    <div
      parentTag="Col"
        class="gvIzRZ4kqU "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggvIzRZ4kqU_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            <div
                class="gp-flex"
                style="--jc:left"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:1px 1px 1px 1px;--bc:#F3F3F3;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px">
    <div
      data-index="2"
      class="gGgmiN5pTZ gp-accordion-item gp-overflow-hidden gp-child-item-gGgmiN5pTZ"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gcfVLEFcmQ"
        id="gcfVLEFcmQ"
        data-index="2"
        class="gGgmiN5pTZ gp-accordion-item-gGgmiN5pTZ-2 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:24px;--pr:24px;--pt:16px;--pb:16px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:16px;--pb-tablet:16px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:16px;--pb-mobile:16px;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-c:#242424;--c:#242424;--bs:solid;--hvr-bs:solid;--bw:0px 0px 0px 0px;--hvr-bw:0px 0px 0px 0px;--bc:#121212;--hvr-bc:#121212"
      >
       <style class="accordion-style">.gp-accordion-item-gGgmiN5pTZ-2:hover 
      {
        .gp-collapsible-icon { 
          color: #B4B4B4 !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#B4B4B4;--c:#B4B4B4;width:16px;height:16px"
            data-index="2"
            class="gGgmiN5pTZ gp-accordion-item_icon gp-collapsible-icon gp-rotate-90 gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          ><svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 12.1763V3.82365C4 3.15846 4.88548 2.7686 5.52991 3.15006L12.5853 7.32641C13.1382 7.65374 13.1382 8.34626 12.5853 8.67359L5.52991 12.8499C4.88548 13.2314 4 12.8415 4 12.1763Z" fill="currentColor"/>
                    </svg></span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggGgmiN5pTZ_childItem_2 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gcfVLEFcmQ"
        data-index="2"
        data-show="false"
        class="gGgmiN5pTZ gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gcfVLEFcmQ"
        data-index="2"
        class="gGgmiN5pTZ gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:24px;--pr:24px;--pl-tablet:24px;--pr-tablet:24px;--pl-mobile:24px;--pr-mobile:24px" >
            <div
      label="Block" tag="Col" type="component"
      
      class="gf9O-S2ER4 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gdLVMAByBK">
    <div
      parentTag="Col"
        class="gdLVMAByBK "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggdLVMAByBK_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            <div
                class="gp-flex"
                style="--jc:left"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:1px 1px 1px 1px;--bc:#F3F3F3;--bblr:10px;--bbrr:10px;--btlr:10px;--btrr:10px">
    <div
      data-index="3"
      class="gGgmiN5pTZ gp-accordion-item gp-overflow-hidden gp-child-item-gGgmiN5pTZ"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="g2RHBs5gub"
        id="g2RHBs5gub"
        data-index="3"
        class="gGgmiN5pTZ gp-accordion-item-gGgmiN5pTZ-3 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:24px;--pr:24px;--pt:16px;--pb:16px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:16px;--pb-tablet:16px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:16px;--pb-mobile:16px;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-c:#242424;--c:#242424;--bs:solid;--hvr-bs:solid;--bw:0px 0px 0px 0px;--hvr-bw:0px 0px 0px 0px;--bc:#121212;--hvr-bc:#121212"
      >
       <style class="accordion-style">.gp-accordion-item-gGgmiN5pTZ-3:hover 
      {
        .gp-collapsible-icon { 
          color: #B4B4B4 !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#B4B4B4;--c:#B4B4B4;width:16px;height:16px"
            data-index="3"
            class="gGgmiN5pTZ gp-accordion-item_icon gp-collapsible-icon gp-rotate-90 gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          ><svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4 12.1763V3.82365C4 3.15846 4.88548 2.7686 5.52991 3.15006L12.5853 7.32641C13.1382 7.65374 13.1382 8.34626 12.5853 8.67359L5.52991 12.8499C4.88548 13.2314 4 12.8415 4 12.1763Z" fill="currentColor"/>
                    </svg></span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:17px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggGgmiN5pTZ_childItem_3 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="g2RHBs5gub"
        data-index="3"
        data-show="false"
        class="gGgmiN5pTZ gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="g2RHBs5gub"
        data-index="3"
        class="gGgmiN5pTZ gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:24px;--pr:24px;--pl-tablet:24px;--pr-tablet:24px;--pl-mobile:24px;--pr-mobile:24px" >
            <div
      label="Block" tag="Col" type="component"
      
      class="gTvFKEGu7x gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gnghdnotPa">
    <div
      parentTag="Col"
        class="gnghdnotPa "
        style="--tt:default;--ta:left;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggnghdnotPa_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            
    </gp-accordion>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-accordion.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 7",
    "tag": "section",
    "class": "gps-555524770926560178 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555524770859320242&sectionId=555524770926560178)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"gggriLL81KY_text","label":"gggriLL81KY_text","default":"Frequently asked questions"},{"type":"html","id":"gg_cF3TjNR1_childItem_0","label":"gg_cF3TjNR1_childItem_0","default":"<p><span style=\"color:rgb(29,29,31);\">How do I know which Gard Pro model is best for me?</span></p>"},{"type":"html","id":"gg_cF3TjNR1_childItem_1","label":"gg_cF3TjNR1_childItem_1","default":"<p>Are the Gard Pro smartwatches pairable with any phone?</p>"},{"type":"html","id":"gg_cF3TjNR1_childItem_2","label":"gg_cF3TjNR1_childItem_2","default":"<p>How long will it take to receive my order?</p>"},{"type":"html","id":"gg_cF3TjNR1_childItem_3","label":"gg_cF3TjNR1_childItem_3","default":"<p>With which carrier do you ship your products?</p>"},{"type":"html","id":"ggXdvKuaqYZ_text","label":"ggXdvKuaqYZ_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(29,29,31);font-size:17px;\">This is indeed a difficult choice! We are happy to help you choose. Therefore, we have created a list of questions for you on our homepage. In the middle of the page you should click on the blue button ''Find the perfect smartwatch''.</span></p>"},{"type":"html","id":"ggZvM8g_z3E_text","label":"ggZvM8g_z3E_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(29,29,31);font-size:17px;\">Our smartwatches pair with any Android device (Android 6 and above) and iPhone (iOS 9 and above).</span></p>"},{"type":"html","id":"ggfMJz9v7Bq_text","label":"ggfMJz9v7Bq_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(29,29,31);font-size:17px;\">Delivery time for customers worldwide 3-6 business days. Shortly after placing your order, you will receive a shipping confirmation email with a tracking number. If you have any questions, feel free to contact us.</span></p>"},{"type":"html","id":"gg8FP_rn5yn_text","label":"gg8FP_rn5yn_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(29,29,31);font-size:17px;\">We deliver worldwide with, DHL, FEDEX, USPS for the fastest and most reliable service. Free shipping for orders in US and Canada above $50</span></p>"},{"type":"html","id":"ggGgmiN5pTZ_childItem_0","label":"ggGgmiN5pTZ_childItem_0","default":"<p>What are the dimensions of the Gard Pro smartwatches and will they fit on my wrist?</p>"},{"type":"html","id":"ggGgmiN5pTZ_childItem_1","label":"ggGgmiN5pTZ_childItem_1","default":"<p>Do you offer warranty or insurance?</p>"},{"type":"html","id":"ggGgmiN5pTZ_childItem_2","label":"ggGgmiN5pTZ_childItem_2","default":"<p>How long is the battery life and how long does it take to charge?</p>"},{"type":"html","id":"ggGgmiN5pTZ_childItem_3","label":"ggGgmiN5pTZ_childItem_3","default":"<p>Can I replace the straps on my GardPro?</p>"},{"type":"html","id":"gg_Xx_c5sEf_text","label":"gg_Xx_c5sEf_text","default":"<p><strong>Gard Pro Health Smartwatch 2 </strong>The case is 1.50 inches wide, 1.77 inches high and 0.43 inches thick.</p><p><strong>Gard Pro Health Smartwatch 2 </strong>The case is 1.50 inches wide, 1.77 inches high and 0,41 inches thick.</p><p><strong>Gard Pro Ultra </strong>The case is 1.71 wide, 2.24 inches high and 0.54 inches thick.</p><p><strong>Gard Pro Ultra 2+ </strong>The case is 2.02 inches wide and high and 0,51 inches thick.</p>"},{"type":"html","id":"ggvIzRZ4kqU_text","label":"ggvIzRZ4kqU_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(29,29,31);font-size:17px;\">All Gard Pro smartwatches come with a standard 2 year hardware warranty! Within Gard Pro we also offer an insurance GardCare+. This insurance covers you against e.g. falls and/or water damage. If this happens, we will provide you with a replacement product free of charge!</span></p>"},{"type":"html","id":"ggdLVMAByBK_text","label":"ggdLVMAByBK_text","default":"<p>Gard Pro Health SeriesBattery duration up to 7 days with ''normal'' use.</p><p>Gard Pro Ultra SeriesBattery duration up to 15 days with ''normal'' use.</p><p>For both series models, when the smartwatches are on ''standby'', the battery duration is double in days.</p><p>For maximum charging of the smartwatches, keep +- 2 hours.</p>"},{"type":"html","id":"ggnghdnotPa_text","label":"ggnghdnotPa_text","default":"<p><strong>🎨 Yes! </strong><span style=\"background-color:rgb(255,255,255);color:rgb(29,29,31);font-size:16px;\">All models have interchangeable straps that <u>fit&nbsp;</u></span></p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
