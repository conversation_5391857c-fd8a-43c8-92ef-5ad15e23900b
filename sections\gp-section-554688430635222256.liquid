

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-554688430635222256.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-554688430635222256.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-554688430635222256.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-554688430635222256.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-554688430635222256.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-554688430635222256.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-554688430635222256.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-554688430635222256.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-554688430635222256.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-554688430635222256.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-554688430635222256.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-554688430635222256.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-554688430635222256.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-554688430635222256.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-554688430635222256.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-554688430635222256.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-554688430635222256.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-554688430635222256.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-554688430635222256.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-554688430635222256.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-554688430635222256.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-554688430635222256.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-554688430635222256.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-554688430635222256.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-554688430635222256.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-554688430635222256.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-554688430635222256.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-554688430635222256.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-554688430635222256.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-554688430635222256.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-554688430635222256.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-554688430635222256.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-554688430635222256.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-554688430635222256.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-554688430635222256.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-554688430635222256.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-554688430635222256.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-554688430635222256.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-554688430635222256.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-554688430635222256.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-554688430635222256.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-554688430635222256.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-554688430635222256.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-554688430635222256.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-554688430635222256.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-554688430635222256.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-554688430635222256.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-554688430635222256.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-554688430635222256.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-554688430635222256.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-554688430635222256.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-554688430635222256.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-554688430635222256.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-554688430635222256.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-554688430635222256.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-554688430635222256.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-554688430635222256.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-554688430635222256.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-554688430635222256.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-554688430635222256.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-554688430635222256 .gp-relative{position:relative}.gps-554688430635222256 .gp-z-1{z-index:1}.gps-554688430635222256 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-554688430635222256 .gp-mb-0{margin-bottom:0}.gps-554688430635222256 .gp-flex{display:flex}.gps-554688430635222256 .gp-inline-flex{display:inline-flex}.gps-554688430635222256 .gp-grid{display:grid}.gps-554688430635222256 .\!gp-hidden{display:none!important}.gps-554688430635222256 .gp-hidden{display:none}.gps-554688430635222256 .gp-h-full{height:100%}.gps-554688430635222256 .gp-max-w-full{max-width:100%}.gps-554688430635222256 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-554688430635222256 .gp-flex-col{flex-direction:column}.gps-554688430635222256 .gp-items-center{align-items:center}.gps-554688430635222256 .gp-justify-center{justify-content:center}.gps-554688430635222256 .gp-gap-y-0{row-gap:0}.gps-554688430635222256 .gp-overflow-hidden{overflow:hidden}.gps-554688430635222256 .gp-break-words{overflow-wrap:break-word}.gps-554688430635222256 .gp-rounded-none{border-radius:0}.gps-554688430635222256 .gp-text-center{text-align:center}.gps-554688430635222256 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-554688430635222256 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-554688430635222256 .gp-no-underline{text-decoration-line:none}.gps-554688430635222256 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-554688430635222256 .gp-duration-200{transition-duration:.2s}.gps-554688430635222256 .gp-duration-300{transition-duration:.3s}.gps-554688430635222256 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-554688430635222256 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-554688430635222256 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-554688430635222256 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-554688430635222256 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-554688430635222256 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-554688430635222256 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-554688430635222256 .tablet\:\!gp-hidden{display:none!important}.gps-554688430635222256 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-554688430635222256 .mobile\:\!gp-hidden{display:none!important}.gps-554688430635222256 .mobile\:gp-hidden{display:none}}.gps-554688430635222256 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-554688430635222256 .\[\&_p\]\:gp-inline p{display:inline}.gps-554688430635222256 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-554688430635222256 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-554688430635222256 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="geYJnNLMXp" data-id="geYJnNLMXp"
        style="--blockPadding:base;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-2xl);--pb:var(--g-s-2xl);--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="geYJnNLMXp gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gO1qALudgr gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="g1YyCogSgW" data-id="g1YyCogSgW"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-l);--cg:8px;--pc:start;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g1YyCogSgW gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gj8do2WuA5 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="ghej18nrtP">
    <div
      parentTag="Col"
        class="ghej18nrtP "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:29px"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:400px;--w-tablet:400px;--w-mobile:400px;--ta:center;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:33px;--size-tablet:33px;--size-mobile:28px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.gghej18nrtP_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  <gp-button >
  <div
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--mb:17px;--ta:center"
    
  >
    <style>
    .gwDxPzCRj9.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }

    .gwDxPzCRj9:hover::before {
      
      
    }

    .gwDxPzCRj9:hover .gp-button-icon {
      color: undefined;
    }

     .gwDxPzCRj9 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gwDxPzCRj9:hover .gp-button-price {
      color: undefined;
    }

    .gwDxPzCRj9 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gwDxPzCRj9 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gwDxPzCRj9:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#gc4cTS57DT" target="_self" data-id="gwDxPzCRj9" aria-label="<p>SHOP THE RANGE</p>"
      
      data-state="idle"
      class="gwDxPzCRj9 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center"
      style="--hvr-bg:#1180FF;--bg:#096de3;--radius:var(--g-radius-small);--shadow:none;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:8px;--pb:8px;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:600;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--tt:uppercase"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggwDxPzCRj9_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 8",
    "tag": "section",
    "class": "gps-554688430635222256 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=554688430584366320&sectionId=554688430635222256)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"gghej18nrtP_text","label":"gghej18nrtP_text","default":"UNLOCK YOUR HEALTH WITH GARD PRO TODAY"},{"type":"html","id":"ggwDxPzCRj9_label","label":"ggwDxPzCRj9_label","default":"<p>SHOP THE RANGE</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
