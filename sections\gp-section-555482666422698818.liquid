

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-555482666422698818.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-555482666422698818.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-555482666422698818.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-555482666422698818.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-555482666422698818.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-555482666422698818.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-555482666422698818.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-555482666422698818.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-555482666422698818.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-555482666422698818.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-555482666422698818.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-555482666422698818.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-555482666422698818.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-555482666422698818.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-555482666422698818.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-555482666422698818.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-555482666422698818.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-555482666422698818.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-555482666422698818.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-555482666422698818.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-555482666422698818.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-555482666422698818.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-555482666422698818.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-555482666422698818.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-555482666422698818.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-555482666422698818.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-555482666422698818.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-555482666422698818.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-555482666422698818.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-555482666422698818.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-555482666422698818.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-555482666422698818.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-555482666422698818.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-555482666422698818.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-555482666422698818.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-555482666422698818.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-555482666422698818.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-555482666422698818.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-555482666422698818.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-555482666422698818.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-555482666422698818.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-555482666422698818.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-555482666422698818.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-555482666422698818.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-555482666422698818.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-555482666422698818.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-555482666422698818.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-555482666422698818.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-555482666422698818.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-555482666422698818.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-555482666422698818.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-555482666422698818.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-555482666422698818.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-555482666422698818.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-555482666422698818.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-555482666422698818.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-555482666422698818.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-555482666422698818.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-555482666422698818.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-555482666422698818.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-555482666422698818.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-555482666422698818.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-555482666422698818.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-555482666422698818.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-555482666422698818.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-555482666422698818.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-555482666422698818.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-555482666422698818.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-555482666422698818.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-555482666422698818.gps.gpsil [style*="--o-mobile:"]{order:var(--o-mobile)}.gps-555482666422698818.gps.gpsil [style*="--pc-mobile:"]{place-content:var(--pc-mobile)}.gps-555482666422698818.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-555482666422698818.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-555482666422698818.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-555482666422698818.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-555482666422698818.gps.gpsil [style*="--ta-mobile:"]{text-align:var(--ta-mobile)}.gps-555482666422698818.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-555482666422698818.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-555482666422698818 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-555482666422698818 .gp-relative{position:relative}.gps-555482666422698818 .gp-z-1{z-index:1}.gps-555482666422698818 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-555482666422698818 .gp-mb-0{margin-bottom:0}.gps-555482666422698818 .gp-flex{display:flex}.gps-555482666422698818 .gp-inline-flex{display:inline-flex}.gps-555482666422698818 .gp-grid{display:grid}.gps-555482666422698818 .gp-contents{display:contents}.gps-555482666422698818 .\!gp-hidden{display:none!important}.gps-555482666422698818 .gp-hidden{display:none}.gps-555482666422698818 .gp-h-auto{height:auto}.gps-555482666422698818 .gp-h-full{height:100%}.gps-555482666422698818 .gp-w-full{width:100%}.gps-555482666422698818 .gp-max-w-full{max-width:100%}.gps-555482666422698818 .gp-flex-none{flex:none}.gps-555482666422698818 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-555482666422698818 .gp-flex-col{flex-direction:column}.gps-555482666422698818 .gp-items-center{align-items:center}.gps-555482666422698818 .gp-justify-center{justify-content:center}.gps-555482666422698818 .gp-gap-y-0{row-gap:0}.gps-555482666422698818 .gp-overflow-hidden{overflow:hidden}.gps-555482666422698818 .gp-break-words{overflow-wrap:break-word}.gps-555482666422698818 .gp-rounded-none{border-radius:0}.gps-555482666422698818 .gp-text-center{text-align:center}.gps-555482666422698818 .gp-leading-\[0\]{line-height:0}.gps-555482666422698818 .gp-text-g-line-3{color:var(--g-c-line-3)}.gps-555482666422698818 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-555482666422698818 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-555482666422698818 .gp-no-underline{text-decoration-line:none}.gps-555482666422698818 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555482666422698818 .gp-duration-200{transition-duration:.2s}.gps-555482666422698818 .gp-duration-300{transition-duration:.3s}.gps-555482666422698818 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555482666422698818 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-555482666422698818 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-555482666422698818 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-555482666422698818 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-555482666422698818 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-555482666422698818 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-555482666422698818 .tablet\:\!gp-hidden{display:none!important}.gps-555482666422698818 .tablet\:gp-hidden{display:none}.gps-555482666422698818 .tablet\:gp-h-auto{height:auto}.gps-555482666422698818 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-555482666422698818 .mobile\:\!gp-hidden{display:none!important}.gps-555482666422698818 .mobile\:gp-hidden{display:none}.gps-555482666422698818 .mobile\:gp-h-auto{height:auto}.gps-555482666422698818 .mobile\:gp-flex-none{flex:none}}.gps-555482666422698818 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-555482666422698818 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}@media (max-width:1024px){.gps-555482666422698818 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-555482666422698818 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-555482666422698818 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-555482666422698818 .\[\&_p\]\:gp-inline p{display:inline}.gps-555482666422698818 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-555482666422698818 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-555482666422698818 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gQ0_65uiqa" data-id="gQ0_65uiqa"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-4xl);--pl:15px;--pb:var(--g-s-4xl);--pr:15px;--pt-mobile:24px;--pl-mobile:24px;--pb-mobile:24px;--pr-mobile:24px;--pt-tablet:var(--g-s-4xl);--pl-tablet:24px;--pb-tablet:var(--g-s-4xl);--pr-tablet:4px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gQ0_65uiqa gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="goBzjrwq5l gp-relative gp-flex gp-flex-col"
    >
      
    {%- assign gpBkProduct = product -%}
    
        {%- liquid
          if request.page_type == 'product'
            if 'static' == 'static'
              if '14924340658558' == 'latest'
                paginate collections.all.products by 100000
                  assign product = collections.all.products | sort: 'created_at' | reverse | first
                endpaginate
              else
                assign product = all_products['gard-pro-health-smartwatch-3-1']
                assign productId = '14924340658558' | times: 1
                if product == empty or product == null
                  paginate collections.all.products by 100000
                    for item in collections.all.products
                      if item.id == productId
                        assign product = item
                      endif
                    endfor
                  endpaginate
                endif
              endif
            endif
          else
            if '14924340658558' == 'latest'
              paginate collections.all.products by 100000
                assign product = collections.all.products | sort: 'created_at'| reverse | first
              endpaginate
            else
              assign product = all_products['gard-pro-health-smartwatch-3-1']
              assign productId = '14924340658558' | times: 1
              if product == empty or product == null
                paginate collections.all.products by 100000
                  for item in collections.all.products
                    if item.id == productId
                      assign product = item
                    endif
                  endfor
                endpaginate
              endif
            endif
          endif
        -%}
      

    {%-if product != empty and product != null -%}
      {%- assign initVariantId =  -%}
      {%- assign product_form_id = 'product-form-' | append: "g64OMjKjZ-" -%}
      {%- assign variant = product.selected_or_first_available_variant -%}
      {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%-if productSelectedVariant == empty or productSelectedVariant == null -%}
        {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      {%-if variant == empty or variant == null -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      <gp-product data-uid="g64OMjKjZ-" data-id="g64OMjKjZ-"   gp-context='{"productId": {{ product.id }}, "preSelectedOptionIds": [], "isSyncProduct": "", "variantSelected": {{ variant | json | escape }}, "inventoryQuantity": {{ variant.inventory_quantity }}, "quantity": 1, "formId": "{{ product_form_id }}" }'
        gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }}}'>
        <product-form class="product-form">
          {%- form 'product', product, id: product_form_id, class: 'form contents', data-type: 'add-to-cart-form', autocomplete: 'off'  -%}
            <input type="hidden" name="id" value="{{ variant.id }}" />
            <input type="hidden" name="quantity" value="{{ quantity }}" />
            <button type="submit" onclick="return false;" style="display:none;"></button>
            
       
      
    <div
      id="g64OMjKjZ-" data-id="g64OMjKjZ--row"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:30px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%"
        class="g64OMjKjZ- gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g2toJ-rqJq gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gDHlIpU3w0" data-id="gDHlIpU3w0"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-3xl);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gDHlIpU3w0 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="ggr1U7gIJw gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gPbOce5sxh">
    <div
      parentTag="Col"
        class="gPbOce5sxh "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:600px;--w-tablet:600px;--w-mobile:600px;--ta:center;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:40px;--size-tablet:30px;--size-mobile:22px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggPbOce5sxh_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gZY20VIavD">
    <div
      parentTag="Col"
        class="gZY20VIavD "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:20px;--mb-mobile:0px"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:600px;--w-tablet:600px;--w-mobile:600px;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#424242;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:19px;--size-tablet:18px;--size-mobile:16px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggZY20VIavD_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gXt-Gd3A8Z" data-id="gXt-Gd3A8Z"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:62px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gXt-Gd3A8Z gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:space-between;--o-mobile:1"
      class="g4_Q8TtXFC gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gD-sxmhg09"
    role="presentation"
    class="gp-group/image gD-sxmhg09 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_3_768x.jpg?v=1711369212" />
      <source media="(max-width: 1024px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_3_1024x.jpg?v=1711369212" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_3.jpg?v=1711369212"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--aspect-tablet:auto;--aspect-mobile:auto;--objf:cover;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--bblr:12px;--bbrr:12px;--btlr:12px;--btrr:12px;--radiusType:custom;--shadow:none"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:space-between;--o-mobile:0"
      class="gBQXDu8t_h gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gZszntkBBW" data-id="gZszntkBBW"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-3xl);--pt:0px;--pl:0px;--pb:0px;--pr:0px;--cg:var(--g-s-xl);--pc:start;--pc-mobile:center;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:rgba(251, 251, 251, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gZszntkBBW gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g0AhcXZ9UA gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gC1z1gQl_x"
    >
      <div 
      data-id="gC1z1gQl_x"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:40px;--w-tablet:40px;--w-mobile:40px;--h:40px;--h-tablet:40px;--h-mobile:40px;--minw:40px;--minw-tablet:40px;--minw-mobile:40px;--height-desktop:40px;--height-tablet:40px;--height-mobile:40px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817606067028328">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M128,42a94,94,0,1,0,94,94A94.11,94.11,0,0,0,128,42Zm0,176a82,82,0,1,1,82-82A82.1,82.1,0,0,1,128,218ZM60.24,36.24l-32,32a6,6,0,1,1-8.48-8.48l32-32a6,6,0,1,1,8.48,8.48Zm176,32a6,6,0,0,1-8.48,0l-32-32a6,6,0,0,1,8.48-8.48l32,32A6,6,0,0,1,236.24,68.24ZM184,130a6,6,0,0,1,0,12H128a6,6,0,0,1-6-6V80a6,6,0,0,1,12,0v50Z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gjNt6kYO1E gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gSbJC7RxkP">
    <div
      parentTag="Col"
        class="gSbJC7RxkP "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s)"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:16px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggSbJC7RxkP_text | replace: '$locationOrigin', locationOrigin }}</h3>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gYvtTTrO3C">
    <div
      parentTag="Col"
        class="gYvtTTrO3C "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggYvtTTrO3C_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gMcJUiuig-" data-id="gMcJUiuig-"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-3xl);--pt:0px;--pl:0px;--pb:0px;--pr:0px;--cg:var(--g-s-xl);--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:rgba(251, 251, 251, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gMcJUiuig- gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gqrSl2s7Pc gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] g5WJB9-dwc"
    >
      <div 
      data-id="g5WJB9-dwc"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:40px;--w-tablet:40px;--w-mobile:40px;--h:40px;--h-tablet:40px;--h-mobile:40px;--minw:40px;--minw-tablet:40px;--minw-mobile:40px;--height-desktop:40px;--height-tablet:40px;--height-mobile:40px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817690726302056">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M240,96a8,8,0,0,1-8,8H216v16a8,8,0,0,1-16,0V104H184a8,8,0,0,1,0-16h16V72a8,8,0,0,1,16,0V88h16A8,8,0,0,1,240,96ZM144,56h8v8a8,8,0,0,0,16,0V56h8a8,8,0,0,0,0-16h-8V32a8,8,0,0,0-16,0v8h-8a8,8,0,0,0,0,16Zm72.77,97a8,8,0,0,1,1.43,8A96,96,0,1,1,95.07,37.8a8,8,0,0,1,10.6,9.06A88.07,88.07,0,0,0,209.14,150.33,8,8,0,0,1,216.77,153Zm-19.39,14.88c-1.79.09-3.59.14-5.38.14A104.11,104.11,0,0,1,88,64c0-1.79,0-3.59.14-5.38A80,80,0,1,0,197.38,167.86Z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gSj2WT6LdR gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gD7JFgMZw2">
    <div
      parentTag="Col"
        class="gD7JFgMZw2 "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s)"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:16px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggD7JFgMZw2_text | replace: '$locationOrigin', locationOrigin }}</h3>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gWNucRUVqC">
    <div
      parentTag="Col"
        class="gWNucRUVqC "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggWNucRUVqC_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="glP1KgjaIt" data-id="glP1KgjaIt"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-3xl);--pt:0px;--pl:0px;--pb:0px;--pr:0px;--cg:var(--g-s-xl);--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:rgba(251, 251, 251, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="glP1KgjaIt gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gC02kdWDsk gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gDKb_W4sfC"
    >
      <div 
      data-id="gDKb_W4sfC"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:40px;--w-tablet:40px;--w-mobile:40px;--h:40px;--h-tablet:40px;--h-mobile:40px;--minw:40px;--minw-tablet:40px;--minw-mobile:40px;--height-desktop:40px;--height-tablet:40px;--height-mobile:40px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817626371457384">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M241.91,118.1l-16-16a14,14,0,0,0-19.55-.23L154.13,49.64a14,14,0,0,0-.23-19.55l-16-16a14,14,0,0,0-19.8,0l-64,64a14,14,0,0,0,0,19.8l16,16a14,14,0,0,0,19.55.23L99.52,124,32.73,190.79a23,23,0,0,0,32.48,32.49L132,156.49l9.87,9.87a14,14,0,0,0,.23,19.55l16,16a14,14,0,0,0,19.8,0l64-64A14,14,0,0,0,241.91,118.1Zm-91.56,39.76-52.21-52.2,47.52-47.52,52.2,52.2ZM78.59,105.41l-16-16a2,2,0,0,1,0-2.83l64-64a2,2,0,0,1,2.83,0l16,16a2,2,0,0,1,0,2.83l-64,64A2,2,0,0,1,78.59,105.41ZM56.73,214.8a11,11,0,0,1-15.52-15.52L108,132.49,123.52,148Zm176.69-85.38-64,64a2,2,0,0,1-2.83,0l-16-16a2,2,0,0,1,0-2.83l64-64a2,2,0,0,1,2.83,0l16,16A2,2,0,0,1,233.42,129.42Z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g7B6p1ezSt gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gOuXyrpm0I">
    <div
      parentTag="Col"
        class="gOuXyrpm0I "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s)"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:16px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggOuXyrpm0I_text | replace: '$locationOrigin', locationOrigin }}</h3>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g1h17Ganbj">
    <div
      parentTag="Col"
        class="g1h17Ganbj "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg1h17Ganbj_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="g8YHZaZB9M" data-id="g8YHZaZB9M"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-3xl);--pt:0px;--pl:0px;--pb:0px;--pr:0px;--mb-mobile:var(--g-s-2xl);--cg:var(--g-s-xl);--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:rgba(251, 251, 251, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g8YHZaZB9M gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g5-eabb_nN gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gD6V92evzB"
    >
      <div 
      data-id="gD6V92evzB"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:40px;--w-tablet:40px;--w-mobile:40px;--h:40px;--h-tablet:40px;--h-mobile:40px;--minw:40px;--minw-tablet:40px;--minw-mobile:40px;--height-desktop:40px;--height-tablet:40px;--height-mobile:40px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817609074868584">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M216,74H30V48a6,6,0,0,0-12,0V208a6,6,0,0,0,12,0V174H242v34a6,6,0,0,0,12,0V112A38,38,0,0,0,216,74ZM30,86h76v76H30Zm88,76V86h98a26,26,0,0,1,26,26v50Z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gj6DV63as3 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g8WgM9xqkD">
    <div
      parentTag="Col"
        class="g8WgM9xqkD "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s)"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:16px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg8WgM9xqkD_text | replace: '$locationOrigin', locationOrigin }}</h3>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gubdVuEf4y">
    <div
      parentTag="Col"
        class="gubdVuEf4y "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggubdVuEf4y_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:0px;--mb-mobile:11px;--ta:left"
    
  >
    <style>
    .gaWy5asr32.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      
    }

    .gaWy5asr32:hover::before {
      
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .gaWy5asr32:hover .gp-button-icon {
      color: undefined;
    }

     .gaWy5asr32 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gaWy5asr32:hover .gp-button-price {
      color: undefined;
    }

    .gaWy5asr32 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gaWy5asr32 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gaWy5asr32:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#g5UWRTEtPs" target="_self" data-id="gaWy5asr32" aria-label="<p>SHOP THE FEMALE COLLECTION</p>"
      
      data-state="idle"
      class="gaWy5asr32 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center"
      style="--hvr-bg:#1180FF;--bg:#096de3;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--w:100%;--w-tablet:Auto;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:32px;--pr:32px;--pt:12px;--pb:12px;--pl-tablet:32px;--pr-tablet:32px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:32px;--pr-mobile:32px;--pt-mobile:12px;--pb-mobile:12px;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--fs:normal;--weight:600;--ls:1px;--lh-tablet:150%"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggaWy5asr32_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
    </div>
   
    
    </div>
    </div>
   
    
          {%- endform -%}
        </product-form>
      </gp-product>
      {%- assign product = gpBkProduct -%}
    {% else %}
      <div class="gp-text-center">{{ "gempages.Product.product_not_found" | t }}</div>
    {%- endif -%}
     <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 3",
    "tag": "section",
    "class": "gps-555482666422698818 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555482666321838914&sectionId=555482666422698818)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggPbOce5sxh_text","label":"ggPbOce5sxh_text","default":"STAY ON TOP OF EVERY HEALTH METRIC"},{"type":"html","id":"ggZY20VIavD_text","label":"ggZY20VIavD_text","default":"<p>Our women's smartwatches offer real-time insights to keep your wellness in check. Track your steps, monitor your heart rate and blood pressure, and get gentle reminders to stay active.</p>"},{"type":"html","id":"ggSbJC7RxkP_text","label":"ggSbJC7RxkP_text","default":"<span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:20px;\"><strong>24/7 Wellness Monitoring</strong></span>"},{"type":"html","id":"ggYvtTTrO3C_text","label":"ggYvtTTrO3C_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:16px;\">Keep tabs on your heart rate, blood pressure, and more—anytime, anywhere.</span></p>"},{"type":"html","id":"ggD7JFgMZw2_text","label":"ggD7JFgMZw2_text","default":"<span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:20px;\"><strong>Advanced Sleep Tracking</strong></span>"},{"type":"html","id":"ggWNucRUVqC_text","label":"ggWNucRUVqC_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:16px;\">Understand your rest patterns and wake up feeling more energized each day.</span></p>"},{"type":"html","id":"ggOuXyrpm0I_text","label":"ggOuXyrpm0I_text","default":"Designed For Durability"},{"type":"html","id":"gg1h17Ganbj_text","label":"gg1h17Ganbj_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:16px;\">Waterproof, dustproof, and shockproof—this waterproof wristwatch is built to handle everything from tough workouts to daily wear.</span></p>"},{"type":"html","id":"gg8WgM9xqkD_text","label":"gg8WgM9xqkD_text","default":"Sleep &amp; Stress Management"},{"type":"html","id":"ggubdVuEf4y_text","label":"ggubdVuEf4y_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:16px;\">Get insights into your sleep patterns, track stress levels, and recharge with your fitness watch's guided breathing exercises.</span></p>"},{"type":"html","id":"ggaWy5asr32_label","label":"ggaWy5asr32_label","default":"<p>SHOP THE FEMALE COLLECTION</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
