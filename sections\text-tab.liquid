{% style %}
  .text-tab-desc {
    display: none;
  }

  .text-tab-info.active .text-tab-desc {
    display: block;
  }
  .text-tab-info.active {
    opacity: 1;
  }
  .text-tab-img {
    display: none;
  }

  .text-tab-img.active {
    display: block;
  }
  .text-tab-main {
    background: linear-gradient(180deg, #ebeae6 50.13%, #f5f3ef 100%);
  }
  .text-tab-image-main {
    transform: translate(-50%, -50%);
  }
  @media screen and (max-width: 1024px) {
    .text-tab-image-main {
      transform: unset;
    }
  }
{% endstyle %}
{% if section.blocks.size > 0 %}
  <div class="text-tab tw-max-w-[1340px] tw-w-full tw-px-[48px] max-md:tw-px-[20px] tw-mx-auto max-lg:tw-pt-[100px]">
    <div class="text-tab-main max-lg:tw-flex max-lg:tw-flex-col tw-px-[32px] max-lg:tw-px-[20px] tw-py-[75px] max-lg:tw-py-0 tw-relative tw-rounded-[40px]">
      <div class="text-rotate">
        <div class="text-tab-image-main tw-max-w-[410px] max-lg:tw-max-w-full tw-w-full tw-absolute tw-mx-auto tw-top-[50%] tw-left-[50%] max-lg:tw-relative max-lg:tw-top-0 max-lg:tw-left-0 max-lg:tw-mt-[-100px]">
          {% for block in section.blocks %}
            {% if block.settings.block_image != blank %}
              {% if block.settings.block_image != blank %}
                <div class="text-tab-img tw-text-center" data-block-id="{{ block.id }}">
                  <img
                    src="{{ block.settings.block_image | image_url : width: 410 }}"
                    class="block-image"
                    alt="{{ block.settings.block_image.alt }}"
                  >
                </div>
              {% else %}
                <div class="text-tab-img section-image" style="display: none;" data-block-id="{{ block.id }}">
                  {% if section.settings.image != blank %}
                    <img
                      src="{{ section.settings.image | image_url : width: 410 }}"
                      class="block-image"
                      alt="{{ section.settings.image.alt }}"
                    >
                  {% endif %}
                </div>
              {% endif %}
            {% endif %}
          {% endfor %}
        </div>
        <div class="text-tab-content-main tw-grid tw-grid-cols-2 tw-gap-x-[410px] tw-gap-y-[30px] max-lg:tw-gap-y-[20px] max-lg:tw-gap-x-0 max-lg:tw-grid-cols-1">
          {% for block in section.blocks %}
            {% if block.settings.block_title != blank or block.settings.block_desc != blank %}
              {% if block.settings.block_title != blank or block.settings.block_desc != blank %}
                <div
                  class="text-tab-info tw-opacity-50 tw-border-0 tw-border-l tw-border-solid tw-border-[#4A4741] tw-pl-[29px] tw-cursor-pointer"
                  data-block-id="{{ block.id }}"
                >
                  {% if block.settings.block_title != blank %}
                    <h4
                      class="title text-tab-title !tw-mb-[15px] tw-leading-[1] !tw-text-[24px] max-md:!tw-text-[20px] tw-font-semibold !tw-text-[#4A4741] tw-font-dm-sans !tw-capitalize tw-tracking-normal"
                      data-block-id="{{ block.id }}"
                    >
                      {{ block.settings.block_title }}
                    </h4>
                  {% endif %}
                  {% if block.settings.block_desc != blank %}
                    <div
                      class="desc text-tab-desc tw-leading-[normal] !tw-text-[16px] tw-font-medium !tw-text-[#4A4741] tw-font-dm-sans tw-tracking-normal"
                      data-block-id="{{ block.id }}"
                    >
                      {{ block.settings.block_desc }}
                    </div>
                  {% endif %}
                </div>
              {% endif %}
            {% endif %}
          {% endfor %}
        </div>
      </div>
    </div>
  </div>
{% endif %}
{% schema %}
{
  "name": "Oddit Text tab",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Section Full Width",
      "default": false
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    }
  ],
  "blocks": [
    {
      "type": "tab",
      "name": "Tab",
      "limit": 6,
      "settings": [
        {
          "type": "image_picker",
          "id": "block_image",
          "label": "Image"
        },
        {
          "type": "text",
          "id": "block_title",
          "label": "Title"
        },
        {
          "type": "text",
          "id": "block_desc",
          "label": "Description"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Oddit Text tab"
    }
  ]
}
{% endschema %}
