{%- style -%}
  :root {
    --typeHeaderPrimary: {{ settings.type_header_font_family.family }};
    --typeHeaderFallback: {{ settings.type_header_font_family.fallback_families }};
    --typeHeaderSize: {{ settings.type_header_base_size | default: '32' | append: 'px' }};
    --typeHeaderWeight: {{ settings.type_header_font_family.weight }};
    --typeHeaderLineHeight: {{ settings.type_header_line_height | default: 1.4 }};
    --typeHeaderSpacing: {{ settings.type_header_spacing | default: '25' | divided_by: 1000.00 | append: 'em' }};

    --typeBasePrimary:{{ settings.type_base_font_family.family }};
    --typeBaseFallback:{{ settings.type_base_font_family.fallback_families }};
    --typeBaseSize: {{ settings.type_base_size | default: 16 | append: 'px' }};
    --typeBaseWeight: {{ settings.type_base_font_family.weight }};
    --typeBaseSpacing: {{ settings.type_base_spacing | default: '50' | divided_by: 1000.00 | append: 'em' }};
    --typeBaseLineHeight: {{ settings.type_base_line_height | default: 1.4 }};

    --typeCollectionTitle: {{ settings.type_collection_size | append: 'px' }};

    --iconWeight: {{ settings.icon_weight | default: '5px' }};
    --iconLinecaps: {{ settings.icon_linecaps | default: 'miter' }};

    {% if settings.button_style == 'round-slight' %}
      --buttonRadius: 3px;
    {% elsif settings.button_style == 'round' %}
      --buttonRadius: 50px;
    {% else %}
      --buttonRadius: 0px;
    {% endif %}

    --colorGridOverlayOpacity: {{ settings.collection_grid_opacity | divided_by: 100.0 }};
  }

  .placeholder-content {
    background-image: linear-gradient(100deg, {{ settings.color_small_image_bg }} 40%, {{ settings.color_small_image_bg | color_darken: 3 }} 63%, {{ settings.color_small_image_bg }} 79%);
  }
{%- endstyle -%}
