<section class="image_text_3"  style="background:{{section.settings.bg_color}}; {% if section.settings.bg_image != blank %}background-image: url({{ section.settings.bg_image | img_url: 'master' }}); background-size: cover; background-repeat: no-repeat; background-position: center;{% endif %};">
  <div class="page_wrapper">
    <div class="wrapper">
      <div class="text_block">
             {% if section.settings.sub_heading != blank %}
        <div class="sub_heading">{{ section.settings.sub_heading }}</div>
        {% endif %}
        {% if section.settings.heading != blank %}
        <h2 class="heading">{{ section.settings.heading }}</h2>
        {% endif %}
        {% if section.settings.content != blank %}
        <div class="content">{{ section.settings.content }}</div>
        {% endif %}
        <div class="timmer_wrap">
           {% for block in section.blocks %}
          <div class="timmer_item">
            <div class="sub_heading">{{ block.settings.sub_heading }}</div>
            <h3 class="item_heading">{{ block.settings.heading }}</h3>
             <p class="item_text">{{ block.settings.content }}</p>
          </div>
          {% endfor %}
        </div>
      </div>
      <div class="image_block">
        {% if section.settings.image != blank %}
        <img src="{{ section.settings.image |img_url :'master' }}">
        {% endif %}
      </div>
      
    </div>
  </div>
</section>

<style>



section.image_text_3 .wrapper .text_block {
    width: 100%;
    display: flex;
      max-width: 775px;
    flex-direction: column;

}
section.image_text_3 .wrapper .text_block h2.heading {
    font-size: 74px;
    font-family: Helvetica-Bold;
    margin: 0;
  color:#2a2b2b;
    padding-bottom: 20px;
    text-transform: none;
}
section.image_text_3 .wrapper .text_block .content p {
    margin: 0;
    font-size: 25px;
    font-family: Helvetica-Bold;
    color: #676666;
   font-weight: 700;
}
  section.image_text_3 .wrapper .image_block {
    width: 100%;
    font-size: 0;
}
  section.image_text_3 .wrapper .image_block img{
    width: 100%;
    }
  .image_text_3 .wrapper {
    grid-template-columns: 1.5fr 1fr;
    display: grid;
        align-items: center;
    gap:50px;
}
  section.image_text_3 .wrapper .text_block .content {
    width: 100%;

}
  section.image_text_3 .wrapper .text_block .sub_heading {
    margin: 0;
    font-size: 25px;
    font-family: Helvetica-Bold;
    color: #676666;
        font-weight: 700;
}
  section.image_text_3 .wrapper .text_block .timmer_wrap {
    display: grid
;
    grid-template-columns: 1fr 1fr;
        padding-top: 30px;
}
  section.image_text_3 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(1) {
    border-right: 2px solid #a6a6a6;
}
  section.image_text_3 .wrapper .text_block .timmer_wrap .timmer_item .item_text {
    margin: 0;
    font-size: 25px;
    font-family: Helvetica-Bold;
    color: #676666;
        font-weight: 700;
}
  section.image_text_3 .wrapper .text_block .item_heading {
    color: #ff832d;
    font-size: 74px;
    margin: 0;
    font-family: Helvetica-Bold;
}
  section.image_text_3 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(2) {
    display: flex
;
    flex-direction: column;
    justify-content: end;
    width: 100%;
    max-width: 330px;
    margin-left: auto;
}
    section.image_text_3 {
    padding: 50px 150px;
}

  @media only screen and (min-width: 2600px) {
       section.image_text_3 .wrapper .text_block .sub_heading {
    margin: 0;
    font-size: 50px;
     }
     section.image_text_3 .wrapper .text_block h2.heading {
    font-size: 135px;
       padding-bottom: 50px;
     }
     section.image_text_3 .wrapper .text_block .content p {
    margin: 0;
    font-size: 50px;
    
  }
    section.image_text_3 .wrapper .text_block .item_heading{
          font-size: 150px;
          
    }
    section.image_text_3 .wrapper .text_block .timmer_wrap .timmer_item .item_text {
    margin: 0;
    font-size: 50px;
      
  }
    section.image_text_3 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(2) {
    max-width: 620px;
    }
    section.image_text_3 .wrapper .text_block .timmer_wrap {
    padding-top: 50px;
}
    section.image_text_3 .wrapper .text_block {
    max-width: 1500px;
    }
  }
   @media only screen and (max-width: 1600px) {
  section.image_text_3 {
    padding: 25px 60px;
}
   }
  @media only screen and (max-width: 1280px) {
section.image_text_3 .wrapper .text_block h2.heading {
    font-size: 48px;
}
    section.image_text_3 .wrapper .text_block .item_heading {
    font-size: 48px;
    }
    section.image_text_3 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(2) {
  
    max-width: 250px;
    }
       section.image_text_3 .wrapper .text_block .content p {

    font-size: 20px;
      }
  }
  @media only screen and (max-width: 1024px) {
  section.image_text_3 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(2) {

    max-width: 200px;

  }
  }
  @media only screen and (max-width: 840px) {
.image_text_3 .wrapper {
    grid-template-columns: 1fr;
}
        section.image_text_3 {
        padding: 30px 20px;
    }
  }

    @media only screen and (max-width: 480px) {
      section.image_text_3 .wrapper .text_block .content p {

    font-size: 18px;
      }
section.image_text_3 .wrapper .text_block .sub_heading {
    margin: 0;
    font-size: 20px;
  }
      section.image_text_3 .wrapper .text_block h2.heading {
    font-size: 38px;
      }
      section.image_text_3 .wrapper .text_block .content p {
    font-size: 20px;
      }
      section.image_text_3 .wrapper .text_block .item_heading {
    font-size: 38px;

      }
      section.image_text_3 .wrapper .text_block .timmer_wrap .timmer_item .item_text {

    font-size: 20px;
      }
      section.image_text_3 .wrapper .text_block .timmer_wrap .timmer_item:nth-child(2) {
    max-width: 155px;
      }
    }
    @media only screen and (max-width: 390px) {
  section.image_text_3 .wrapper .text_block .item_heading {
        font-size: 30px;
    }
  }
</style>




  

{% schema %}
{
  "name": "Image Text 3",
  "settings": [
    {
      "type": "color",
      "id": "bg_color",
      "default": "transparent",
      "label": "Backgroud Color"
    },
       {
      "type": "image_picker",
      "id": "bg_image",
      "label": "Background Image"
    },
    
    {
          "type": "text",
          "id": "sub_heading",
          "label": "Sub_Heading"
        },
      {
          "type": "text",
          "id": "heading",
          "label": "Heading"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "content"
        },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    }
    
      
  ],

    "blocks": [
    {
      "type": "block",
      "name": "Block",
      "settings": [

        {
          "type": "text",
          "id": "sub_heading",
          "label": "Sub_Heading"
        },
      {
          "type": "text",
          "id": "heading",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "content",
          "label": "content"
        },
    
    
      
      ]
    }
  ],
  
  "presets": [
    {
      "name": "Image Text 3",
      "blocks": []
    }
  ]
}
{% endschema %}

