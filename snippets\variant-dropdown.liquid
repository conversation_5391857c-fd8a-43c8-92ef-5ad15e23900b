<div class="variant-wrapper variant-wrapper--dropdown js" data-type="dropdown">
  <label class="variant__label{% if option.name == 'Default' or option.name == 'Title' %} hidden-label{% endif %}{% unless variant_labels %} hidden-label{% endunless %}"
    for="SingleOptionSelector-{{ section_id }}-{{ product.id }}-option-{{ forloop.index0 }}">
    {{ option.name }} 
  </label>
  <div class="variant-input-wrap" data-index="option{{ forloop.index }}" data-handle="{{ option.name | handleize }}">
    <select
      form="{{ form_id }}"
      data-variant-input
      id="SingleOptionSelector-{{ section_id }}-{{ product.id }}-option-{{ forloop.index0 }}"
      data-index="option{{ forloop.index }}">
      {%- for value in option.values -%}
        {%- liquid
          assign variant_label_state = true
          if product.options.size == 1
            unless product.variants[forloop.index0].available
              assign variant_label_state = false
            endunless
          endif
        -%}
        <option
          value="{{ value | escape }}"
          {% if option.selected_value == value %} selected="selected"{% endif %}
          {% unless variant_label_state %} disabled="disabled"{% endunless %}
          name="{{ option.name }}"
          class="variant-input"
          data-index="option{{ forloop.index }}">
          {{ value }}
        </option>
      {%- endfor -%}
    </select>
  </div>
</div>
