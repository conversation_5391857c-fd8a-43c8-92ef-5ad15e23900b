

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-555716577707295521.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-555716577707295521.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-555716577707295521.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-555716577707295521.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-555716577707295521.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-555716577707295521.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-555716577707295521.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-555716577707295521.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-555716577707295521.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-555716577707295521.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-555716577707295521.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-555716577707295521.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-555716577707295521.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-555716577707295521.gps.gpsil [style*="--bbw:"]{border-bottom-width:var(--bbw)}.gps-555716577707295521.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-555716577707295521.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-555716577707295521.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-555716577707295521.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-555716577707295521.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-555716577707295521.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-555716577707295521.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-555716577707295521.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-555716577707295521.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-555716577707295521.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-555716577707295521.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-555716577707295521.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-555716577707295521.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-555716577707295521.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-555716577707295521.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-555716577707295521.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-555716577707295521.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-555716577707295521.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-555716577707295521.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-555716577707295521.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-555716577707295521.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-555716577707295521.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-555716577707295521.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-555716577707295521.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-555716577707295521.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-555716577707295521.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-555716577707295521.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-555716577707295521.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-555716577707295521.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-555716577707295521.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-555716577707295521.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-555716577707295521.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-555716577707295521.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-555716577707295521.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-555716577707295521.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-555716577707295521.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-555716577707295521.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-555716577707295521.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-555716577707295521.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-555716577707295521.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-555716577707295521.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-555716577707295521.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-555716577707295521.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-555716577707295521.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-555716577707295521.gps.gpsil [style*="--bbw-tablet:"]{border-bottom-width:var(--bbw-tablet)}.gps-555716577707295521.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-555716577707295521.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-555716577707295521.gps.gpsil [style*="--jc-tablet:"]{justify-content:var(--jc-tablet)}.gps-555716577707295521.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-555716577707295521.gps.gpsil [style*="--mb-tablet:"]{margin-bottom:var(--mb-tablet)}.gps-555716577707295521.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-555716577707295521.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-555716577707295521.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-555716577707295521.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-555716577707295521.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-555716577707295521.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-555716577707295521.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-555716577707295521.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-555716577707295521.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-555716577707295521.gps.gpsil [style*="--bbw-mobile:"]{border-bottom-width:var(--bbw-mobile)}.gps-555716577707295521.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-555716577707295521.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-555716577707295521.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-555716577707295521.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-555716577707295521.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-555716577707295521.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-555716577707295521.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-555716577707295521.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-555716577707295521.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-555716577707295521.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-555716577707295521.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-555716577707295521.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-555716577707295521.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-555716577707295521.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-555716577707295521.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-555716577707295521 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-555716577707295521 .\!gp-relative{position:relative!important}.gps-555716577707295521 .gp-relative{position:relative}.gps-555716577707295521 .gp-z-1{z-index:1}.gps-555716577707295521 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-555716577707295521 .gp-mb-0{margin-bottom:0}.gps-555716577707295521 .gp-block{display:block}.gps-555716577707295521 .gp-flex{display:flex}.gps-555716577707295521 .gp-inline-flex{display:inline-flex}.gps-555716577707295521 .gp-grid{display:grid}.gps-555716577707295521 .gp-contents{display:contents}.gps-555716577707295521 .\!gp-hidden{display:none!important}.gps-555716577707295521 .gp-hidden{display:none}.gps-555716577707295521 .gp-h-auto{height:auto}.gps-555716577707295521 .gp-h-full{height:100%}.gps-555716577707295521 .gp-w-full{width:100%}.gps-555716577707295521 .gp-max-w-full{max-width:100%}.gps-555716577707295521 .gp-flex-none{flex:none}.gps-555716577707295521 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-555716577707295521 .gp-flex-col{flex-direction:column}.gps-555716577707295521 .gp-flex-wrap{flex-wrap:wrap}.gps-555716577707295521 .gp-items-center{align-items:center}.gps-555716577707295521 .gp-justify-start{justify-content:flex-start}.gps-555716577707295521 .gp-justify-center{justify-content:center}.gps-555716577707295521 .gp-gap-y-0{row-gap:0}.gps-555716577707295521 .gp-overflow-hidden{overflow:hidden}.gps-555716577707295521 .gp-break-words{overflow-wrap:break-word}.gps-555716577707295521 .gp-rounded-none{border-radius:0}.gps-555716577707295521 .gp-text-center{text-align:center}.gps-555716577707295521 .gp-leading-\[0\]{line-height:0}.gps-555716577707295521 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-555716577707295521 .gp-no-underline{text-decoration-line:none}.gps-555716577707295521 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555716577707295521 .gp-duration-200{transition-duration:.2s}.gps-555716577707295521 .gp-duration-300{transition-duration:.3s}.gps-555716577707295521 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555716577707295521 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-555716577707295521 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-555716577707295521 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-555716577707295521 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-555716577707295521 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-555716577707295521 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-555716577707295521 .tablet\:gp-block{display:block}.gps-555716577707295521 .tablet\:\!gp-hidden{display:none!important}.gps-555716577707295521 .tablet\:gp-hidden{display:none}.gps-555716577707295521 .tablet\:gp-h-auto{height:auto}.gps-555716577707295521 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-555716577707295521 .mobile\:gp-block{display:block}.gps-555716577707295521 .mobile\:\!gp-hidden{display:none!important}.gps-555716577707295521 .mobile\:gp-hidden{display:none}.gps-555716577707295521 .mobile\:gp-h-auto{height:auto}.gps-555716577707295521 .mobile\:gp-flex-none{flex:none}}.gps-555716577707295521 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-555716577707295521 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}@media (max-width:1024px){.gps-555716577707295521 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-555716577707295521 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-555716577707295521 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-555716577707295521 .\[\&_p\]\:gp-inline p{display:inline}.gps-555716577707295521 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-555716577707295521 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-555716577707295521 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gtPmezi8c5" data-id="gtPmezi8c5"
        style="--blockPadding:base;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-2xl);--pb:var(--g-s-2xl);--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gtPmezi8c5 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g4UaaDaXNp gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gk-UF4vwMD" data-id="gk-UF4vwMD"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-l);--cg:8px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gk-UF4vwMD gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start;--jc-tablet:start"
      class="gy1Pv9cFHv gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gL1g6hb5xK" data-id="gL1g6hb5xK"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--pl:15px;--pr:15px;--pt-mobile:32px;--pl-mobile:24px;--pb-mobile:0px;--pr-mobile:24px;--pt-tablet:var(--g-s-3xl);--pl-tablet:15px;--pb-tablet:var(--g-s-3xl);--pr-tablet:15px;--cg:30px;--pc:start;--gtc:minmax(0, 12fr);--w:500px;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gL1g6hb5xK gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gSuVY9R3M4 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gvsKGIObFj">
    <div
      parentTag="Col"
        class="gvsKGIObFj "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-l);--mb-mobile:0px;--pl-mobile:auto;--pr-mobile:auto"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--c:#242424;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-heading, heading);--weight:700;--size:37px;--size-tablet:33px;--size-mobile:28px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggvsKGIObFj_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gX4rWAPiEX">
    <div
      parentTag="Col"
        class="gX4rWAPiEX "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:30px;--mt-mobile:var(--g-s-l);--mb-mobile:27px;--mb-tablet:25px"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:18px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggX4rWAPiEX_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  <gp-button >
  <div
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--mb:38px;--mb-mobile:27px;--ta:left"
    
  >
    <style>
    .gGTJo8VYzu.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }

    .gGTJo8VYzu:hover::before {
      
      
    }

    .gGTJo8VYzu:hover .gp-button-icon {
      color: undefined;
    }

     .gGTJo8VYzu .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gGTJo8VYzu:hover .gp-button-price {
      color: undefined;
    }

    .gGTJo8VYzu .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gGTJo8VYzu .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gGTJo8VYzu:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#guP0xNZBba" target="_self" data-id="gGTJo8VYzu" aria-label="<p>Shop the Men&#039;s Collection</p>"
      
      data-state="idle"
      class="gGTJo8VYzu gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center"
      style="--hvr-bg:#1180FF;--bg:#096de3;--radius:var(--g-radius-small);--shadow:none;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:32px;--pr:32px;--pt:12px;--pb:12px;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:600;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--tt:uppercase"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggGTJo8VYzu_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

       
      
    <div
      parentTag="Col" id="g0TUPWhO-M" data-id="g0TUPWhO-M"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:17px;--cg:8px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g0TUPWhO-M gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:center;--jc-mobile:center"
      class="gKWGcVX2Nt gp-relative gp-flex gp-flex-col"
    >
      
    
    <div
    data-id="gQz52k9YJT"
      
      data-id="gQz52k9YJT"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">

    <div
            class="gp-flex gp-flex-wrap"
            style="--cg:4px;--jc:left;--ai:start">
            <div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gXBsyLU_Fz ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gXnWR6x_Gj"
    >
      <div 
      data-id="gXnWR6x_Gj"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#FFB238;--t:rotate(0deg);--w:20px;--w-tablet:20px;--w-mobile:20px;--h:20px;--h-tablet:20px;--h-mobile:20px;--minw:20px;--minw-tablet:20px;--minw-mobile:20px;--height-desktop:20px;--height-tablet:20px;--height-mobile:20px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" data-id="508414815326568800" viewBox="0 0 256 256"><path d="m234.29 114.85-45 38.83L203 211.75a16.4 16.4 0 0 1-24.5 17.82L128 198.49l-50.53 31.08A16.4 16.4 0 0 1 53 211.75l13.76-58.07-45-38.83A16.46 16.46 0 0 1 31.08 86l59-4.76 22.76-55.08a16.36 16.36 0 0 1 30.27 0l22.75 55.08 59 4.76a16.46 16.46 0 0 1 9.37 28.86Z"/></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gp8OcPKXGv ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gKRrCh8eER"
    >
      <div 
      data-id="gKRrCh8eER"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#FFB238;--t:rotate(0deg);--w:20px;--w-tablet:20px;--w-mobile:20px;--h:20px;--h-tablet:20px;--h-mobile:20px;--minw:20px;--minw-tablet:20px;--minw-mobile:20px;--height-desktop:20px;--height-tablet:20px;--height-mobile:20px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" data-id="508414815326568800" viewBox="0 0 256 256"><path d="m234.29 114.85-45 38.83L203 211.75a16.4 16.4 0 0 1-24.5 17.82L128 198.49l-50.53 31.08A16.4 16.4 0 0 1 53 211.75l13.76-58.07-45-38.83A16.46 16.46 0 0 1 31.08 86l59-4.76 22.76-55.08a16.36 16.36 0 0 1 30.27 0l22.75 55.08 59 4.76a16.46 16.46 0 0 1 9.37 28.86Z"/></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gF3Rp70whE ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gV8j29IEtr"
    >
      <div 
      data-id="gV8j29IEtr"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#ffb238;--t:rotate(0deg);--w:20px;--w-tablet:20px;--w-mobile:20px;--h:20px;--h-tablet:20px;--h-mobile:20px;--minw:20px;--minw-tablet:20px;--minw-mobile:20px;--height-desktop:20px;--height-tablet:20px;--height-mobile:20px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" data-id="508414815326568800" viewBox="0 0 256 256"><path d="m234.29 114.85-45 38.83L203 211.75a16.4 16.4 0 0 1-24.5 17.82L128 198.49l-50.53 31.08A16.4 16.4 0 0 1 53 211.75l13.76-58.07-45-38.83A16.46 16.46 0 0 1 31.08 86l59-4.76 22.76-55.08a16.36 16.36 0 0 1 30.27 0l22.75 55.08 59 4.76a16.46 16.46 0 0 1 9.37 28.86Z"/></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="guqIeU9iH1 ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] g1oaswqpFq"
    >
      <div 
      data-id="g1oaswqpFq"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#ffb238;--t:rotate(0deg);--w:20px;--w-tablet:20px;--w-mobile:20px;--h:20px;--h-tablet:20px;--h-mobile:20px;--minw:20px;--minw-tablet:20px;--minw-mobile:20px;--height-desktop:20px;--height-tablet:20px;--height-mobile:20px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" data-id="508414815326568800" viewBox="0 0 256 256"><path d="m234.29 114.85-45 38.83L203 211.75a16.4 16.4 0 0 1-24.5 17.82L128 198.49l-50.53 31.08A16.4 16.4 0 0 1 53 211.75l13.76-58.07-45-38.83A16.46 16.46 0 0 1 31.08 86l59-4.76 22.76-55.08a16.36 16.36 0 0 1 30.27 0l22.75 55.08 59 4.76a16.46 16.46 0 0 1 9.37 28.86Z"/></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gHluUQIuiI ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gGKqUDgP4L"
    >
      <div 
      data-id="gGKqUDgP4L"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#ffb238;--t:rotate(0deg);--w:20px;--w-tablet:20px;--w-mobile:20px;--h:20px;--h-tablet:20px;--h-mobile:20px;--minw:20px;--minw-tablet:20px;--minw-mobile:20px;--height-desktop:20px;--height-tablet:20px;--height-mobile:20px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817759863701864">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M234.29,114.85l-45,38.83L203,211.75a16.4,16.4,0,0,1-24.5,17.82L128,198.49,77.47,229.57A16.4,16.4,0,0,1,53,211.75l13.76-58.07-45-38.83A16.46,16.46,0,0,1,31.08,86l59-4.76,22.76-55.08a16.36,16.36,0,0,1,30.27,0l22.75,55.08,59,4.76a16.46,16.46,0,0,1,9.37,28.86Z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div>
         </div>
    </div>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:center;--jc-mobile:center"
      class="gpbUabBVpv gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="ge2_U-aqdx">
    <div
      parentTag="Col"
        class="ge2_U-aqdx "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gge2_U-aqdx_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    <div gp-el-wrapper style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:17px;--pt:12px;--pb:12px" class="gFPiAQSuNg ">
      
    <div
    data-id="gFPiAQSuNg"
      class="gp-flex gp-justify-start"
    >
      <div
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--t:gp-rotate(0deg);--bc:var(--g-c-line-2, line-2)"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:var(--g-c-line-2, line-2);--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:var(--g-c-line-2, line-2);--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--minw:100px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:100%;--w-tablet:100%;--w-mobile:100%;--bc:var(--g-c-line-2, line-2);--bs:solid;--bbw:1px;--bbw-tablet:1px;--bbw-mobile:1px;--minw:100px"
    ></div>
  
      </div>
    </div>
  
      </div> 
  <script src="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-marquee.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  <gp-marquee
   data-id="gCu1BZPn4l"
    
  gp-data='{"setting":{"activeItem":"0","childItem":["Item 1","Item 2","Item 3","Item 4"],"direction":"left","hasItemShadow":false,"hoverItem":"0","iconSeparatorSvg":"<svg height=\"100%\" width=\"100%\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 256 256\" fill=\"currentColor\">\n              <path fill=\"currentColor\" strokelinecap=\"round\" strokelinejoin=\"round\" d=\"M232,128A104,104,0,1,1,128,24,104.13,104.13,0,0,1,232,128Z\"></path></svg>","isPreview":false,"isShowIconSeparator":false,"itemWidthType":{"desktop":"FIT_CONTENT","mobile":"FIT_CONTENT","tablet":"FIT_CONTENT"},"speed":"0.4","stopOnHover":false,"uid":"gCu1BZPn4l"},"styles":{"align":{"desktop":"center"},"backgroundColor":{"desktop":"transparent"},"iconSeparatorColor":"#0C0C0C","iconSeparatorSize":{"desktop":24,"mobile":24,"tablet":24},"itemBackgroundColor":{"desktop":"transparent"},"itemBorderStyle":{"border":"none","borderType":"none","borderWidth":"1px","color":"#000000","isCustom":false,"width":"1px 1px 1px 1px"},"itemCorner":{"radiusType":"none"},"itemMaxWidth":{"desktop":"300px","mobile":"300px","tablet":"300px"},"itemShadow":{"angle":90,"blur":"12px","color":"#121212","distance":"4px","spread":"0px","type":"shadow-1"},"itemSpacing":{"desktop":"25px","mobile":"56px","tablet":"56px"},"sizeSetting":{"desktop":{"height":"auto","shapeLinked":false,"width":"100%"}}}}'
  class="gCu1BZPn4l"
  >
   <div
      class="gp-flex gp-w-full gp-relative"
      style="--jc:center"
    >
  <div class="!gp-relative gp-overflow-hidden" style="--h:auto;--h-tablet:auto;--h-mobile:auto;--w:100%;--w-tablet:100%;--w-mobile:100%;--bg:transparent;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:17px;--pt:12px;--pb:12px">
        <div
    class="gem-marquee gp-overflow-hidden gp-w-full gp-h-full"
     
    >
     <div class="gp-overflow-hidden gp-w-full gp-h-full">
       
   <div
      style="--pause-on-hover:running;--pause-on-click:running;--width:100%;--transform:none;overflow:hidden;min-width:100%"
      class="rfm-marquee-container"
    >
      <div
        class="rfm-marquee"
        style="--play:running;--direction:normal;--delay:0s;--iteration-count:infinite;--min-width:auto;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto"
      >
        <div class="rfm-initial-child-container">
          
            <div style="--transform:none;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto" class="rfm-child">
               <div class="gp-flex gem-child-marquee-item gp-items-center" style="[object Object]">
       <div
                  class="gp-inline-flex gp-items-center gp-relative"
                  style="--pr:25px;--pr-tablet:56px;--pr-mobile:56px;text-wrap:wrap;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto;--maxw:unset;--maxw-tablet:unset;--maxw-mobile:unset"
                >
                  <div
      class="geFJYgU0k5 gem-marquee-item gem-marquee-item-geFJYgU0k5 gp-overflow-hidden"
      style="--bg:transparent;--b:none;--bw:1px 1px 1px 1px;--bc:#000000;--shadow:none;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%)"
    >
    
     
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmt1nkcSHj">
    <div
      label="Text" tag="Text" type="component" isNotFullWidth="true" isNotLazyload="true"
        class="gmt1nkcSHj "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pt:8px;--pb:8px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--weight:bold;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggmt1nkcSHj_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
                   
                </div> <div
                  class="gp-inline-flex gp-items-center gp-relative"
                  style="--pr:25px;--pr-tablet:56px;--pr-mobile:56px;text-wrap:wrap;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto;--maxw:unset;--maxw-tablet:unset;--maxw-mobile:unset"
                >
                  <div
      class="gSolahBNv6 gem-marquee-item gem-marquee-item-gSolahBNv6 gp-overflow-hidden"
      style="--bg:transparent;--b:none;--bw:1px 1px 1px 1px;--bc:#000000;--shadow:none;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%)"
    >
    
     
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gBBbe8VrqI">
    <div
      label="Text" tag="Text" type="component" isNotFullWidth="true" isNotLazyload="true"
        class="gBBbe8VrqI "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pt:8px;--pb:8px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--weight:bold;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggBBbe8VrqI_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
                   
                </div> <div
                  class="gp-inline-flex gp-items-center gp-relative"
                  style="--pr:25px;--pr-tablet:56px;--pr-mobile:56px;text-wrap:wrap;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto;--maxw:unset;--maxw-tablet:unset;--maxw-mobile:unset"
                >
                  <div
      class="g_kW1jfh0R gem-marquee-item gem-marquee-item-g_kW1jfh0R gp-overflow-hidden"
      style="--bg:transparent;--b:none;--bw:1px 1px 1px 1px;--bc:#000000;--shadow:none;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%)"
    >
    
     
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gvqZnntiba">
    <div
      label="Text Block" tag="Text" type="component" isNotFullWidth="true" isNotLazyload="true"
        class="gvqZnntiba "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--weight:bold;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggvqZnntiba_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gvqZnntiba">
        .gvqZnntiba {

}
.gvqZnntiba p {

}
      </style>
    
    </div>
                   
                </div> <div
                  class="gp-inline-flex gp-items-center gp-relative"
                  style="--pr:25px;--pr-tablet:56px;--pr-mobile:56px;text-wrap:wrap;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto;--maxw:unset;--maxw-tablet:unset;--maxw-mobile:unset"
                >
                  <div
      class="gxUIpoqsyM gem-marquee-item gem-marquee-item-gxUIpoqsyM gp-overflow-hidden"
      style="--bg:transparent;--b:none;--bw:1px 1px 1px 1px;--bc:#000000;--shadow:none;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%)"
    >
    
     
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gRom6UPhqa">
    <div
      label="Text" tag="Text" type="component" isNotFullWidth="true" isNotLazyload="true"
        class="gRom6UPhqa "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pt:8px;--pb:8px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--weight:bold;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggRom6UPhqa_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
                   
                </div>
    </div>
            </div>
          
        </div>
      </div>
      <div
        class="rfm-marquee placeholder-marquee"
        style="--play:running;--direction:normal;--delay:0s;--iteration-count:infinite;--min-width:auto;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto"
      ></div>
    </div>
  
     </div>
    </div>
  
  </div>
   </div>
  </gp-marquee>
<div
    
     data-id="g8NY0RvipS"
    role="presentation"
    class="gp-group/image g8NY0RvipS gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:none;--d-mobile:block;--d-tablet:none;--op:100%;--mb:38px;--mb-mobile:0px;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="{{ "gempages_553400155311702965-dfb9de1d-ddc9-466e-b70a-8e6462a697ca.png" | file_url }}" />
      <source media="(max-width: 1024px)" srcset="https://ucarecdn.com/ee6d5074-1640-4cc7-8933-47c8589c3dee/-/format/auto/" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://ucarecdn.com/ee6d5074-1640-4cc7-8933-47c8589c3dee/-/format/auto/"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--aspect-tablet:auto;--aspect-mobile:auto;--objf:cover;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--radiusType:custom;--shadow:none"
      />
    
  </picture>
      </div>
  </div>
    </div>
    </div>
   
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start;--jc-tablet:start"
      class="g0l2zRgc_K gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gNaJNg_EBV"
    role="presentation"
    class="gp-group/image gNaJNg_EBV gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:none;--d-tablet:block;--op:100%;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="{{ "gempages_553400155311702965-dfb9de1d-ddc9-466e-b70a-8e6462a697ca.png" | file_url }}" />
      <source media="(max-width: 1024px)" srcset="{{ "gempages_553400155311702965-dfb9de1d-ddc9-466e-b70a-8e6462a697ca.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_553400155311702965-dfb9de1d-ddc9-466e-b70a-8e6462a697ca.png" | file_url }}"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--aspect-tablet:auto;--aspect-mobile:auto;--objf:cover;--h:750px;--h-tablet:750px;--h-mobile:750px;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--bblr:12px;--bbrr:12px;--btlr:12px;--btrr:12px;--radiusType:custom;--shadow:none"
      />
    
  </picture>
      </div>
  </div>
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    
<style {% if section.settings.section_preload == "false" %} class="gps-link" type="text/disabled-css" {% endif %}>@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWubEbVmUiAw.woff) format('woff');
}
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmUiAw.woff) format('woff');
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBHMdazQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBHMdazQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style>

{% schema %}
  {
    
    "name": "Section 1",
    "tag": "section",
    "class": "gps-555716577707295521 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555716577622754081&sectionId=555716577707295521)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggvsKGIObFj_text","label":"ggvsKGIObFj_text","default":"<strong>SMARTWATCHES FOR MEN - PRECISION, POWER AND STYLE IN EVERY GARDPRO</strong>"},{"type":"html","id":"ggX4rWAPiEX_text","label":"ggX4rWAPiEX_text","default":"<p>From everyday wear to intense training, explore GardPro’s collection of <strong>high-performance smartwatches for men</strong> who demand more—style, durability, and cutting-edge technology.</p>"},{"type":"html","id":"ggGTJo8VYzu_label","label":"ggGTJo8VYzu_label","default":"<p>Shop the Men's Collection</p>"},{"type":"html","id":"gge2_U-aqdx_text","label":"gge2_U-aqdx_text","default":"<p>4,016 Reviews across the collection.</p>"},{"type":"html","id":"ggmt1nkcSHj_text","label":"ggmt1nkcSHj_text","default":"<p>ADVANCED HEALTH TRACKING</p>"},{"type":"html","id":"ggBBbe8VrqI_text","label":"ggBBbe8VrqI_text","default":"<p>LONG-LASTING BATTERY</p>"},{"type":"html","id":"ggvqZnntiba_text","label":"ggvqZnntiba_text","default":"<p>DURABLE &amp; WATERPROOF</p>"},{"type":"html","id":"ggRom6UPhqa_text","label":"ggRom6UPhqa_text","default":"<p>IOS &amp; ANDROID COMPATIBILITY</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
