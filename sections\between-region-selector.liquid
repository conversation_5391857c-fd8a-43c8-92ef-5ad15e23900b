{%- comment -%}
  Between Region Selector Section
  Renders a region/language selector based on configurable blocks and colors.
{%- endcomment -%}

{%- assign wrapper_id = 'between-' | append: section.id -%}



{%- assign logo_url = section.settings.logo | image_url: width: 200 -%}
{%- if logo_url == blank -%}
  {%- assign logo_url = 'logo-gardpro.png' | asset_url -%}
{%- endif -%}

<div id="{{ wrapper_id }}">
  <div class="logo-container">
    {%- if logo_url != blank -%}
      <img src="{{ logo_url }}" alt="{{ section.settings.logo_alt | default: 'GARD PRO' | escape }}" class="logo">
    {%- endif -%}
  </div>

  <div class="main-container">
    {%- if section.settings.title != blank -%}
      <h1>{{ section.settings.title }}</h1>
    {%- endif -%}
    {%- if section.settings.subtitle != blank -%}
      <p class="subtitle">{{ section.settings.subtitle }}</p>
    {%- endif -%}

    <div class="regions-container">
      {%- for block in section.blocks -%}
        <div class="region-group" {{ block.shopify_attributes }}>
          <a href="{{ block.settings.link_url }}" class="region-link"{% if section.settings.open_links_in_new_tab %} target="_blank" rel="noopener"{% endif %}>
            <div class="store-info">
              <span class="store-name">{{ block.settings.store_name }}</span>
              {%- if block.settings.store_regions != blank -%}
                <span class="store-regions">{{ block.settings.store_regions }}</span>
              {%- endif -%}
            </div>
            <span class="manual-text">
              {{ block.settings.manual_text }}
              {%- if section.settings.show_arrow -%}
                <span class="arrow">→</span>
              {%- endif -%}
            </span>
          </a>
        </div>
      {%- endfor -%}
    </div>
  </div>

  {%- if section.settings.show_footer and section.settings.footer_text != blank -%}
    <div class="footer">
      <p>{{ section.settings.footer_text }}</p>
    </div>
  {%- endif -%}
</div>

{%- if section.settings.enable_console_logs -%}
  <script>
    (function(){
      var root = document.getElementById('{{ wrapper_id }}');
      if (!root) return;
      var links = root.querySelectorAll('.region-link');
      links.forEach(function(link){
        link.addEventListener('click', function(){
          var storeName = this.querySelector('.store-name');
          var manualText = this.querySelector('.manual-text');
          console.log('Store selected:', storeName ? storeName.textContent.trim() : '', '- Manual:', manualText ? manualText.textContent.trim() : '');
        });
      });
      var logo = root.querySelector('.logo');
      if (logo && logo.src) {
        var img = new Image();
        img.src = logo.src;
      }
    })();
  </script>
{%- endif -%}

{% schema %}
{
  "name": "Between - Region selector",
  "tag": "section",
  "class": "between-region-selector",
  "max_blocks": 20,
  "settings": [
    { "type": "text", "id": "title", "label": "Title", "default": "Select Your Region" },
    { "type": "textarea", "id": "subtitle", "label": "Subtitle", "default": "Please select your country or region to access the correct manual and store" },
    { "type": "checkbox", "id": "title_uppercase", "label": "Uppercase title", "default": true },

    { "type": "image_picker", "id": "logo", "label": "Logo image" },
    { "type": "text", "id": "logo_alt", "label": "Logo alt text", "default": "GARD PRO" },
    { "type": "range", "id": "logo_height_desktop", "min": 24, "max": 120, "step": 1, "unit": "px", "label": "Logo height (desktop)", "default": 50 },
    { "type": "range", "id": "logo_height_mobile", "min": 16, "max": 100, "step": 1, "unit": "px", "label": "Logo height (mobile)", "default": 40 },

    { "type": "range", "id": "max_width", "min": 360, "max": 1200, "step": 10, "unit": "px", "label": "Container max width", "default": 600 },
    { "type": "checkbox", "id": "open_links_in_new_tab", "label": "Open links in new tab", "default": false },
    { "type": "checkbox", "id": "show_arrow", "label": "Show arrow on links", "default": true },
    { "type": "checkbox", "id": "show_footer", "label": "Show footer", "default": true },
    { "type": "text", "id": "footer_text", "label": "Footer text", "default": "© 2024 GARD PRO. All rights reserved." },
    { "type": "checkbox", "id": "enable_console_logs", "label": "Enable console logging (debug)", "default": false },

  ],
  "blocks": [
    {
      "type": "region",
      "name": "Region / Language",
      "settings": [
        { "type": "text", "id": "store_name", "label": "Store name", "default": "International Store" },
        { "type": "text", "id": "store_regions", "label": "Regions (subtext)", "default": "USA • UK • Australia • New Zealand • Ireland • Canada" },
        { "type": "text", "id": "manual_text", "label": "Manual label", "default": "USER MANUAL" },
        { "type": "url", "id": "link_url", "label": "Link" }
      ]
    }
  ],
  "presets": [
    {
      "name": "Between - Region selector",
      "blocks": [
        { "type": "region", "settings": { "store_name": "International Store", "store_regions": "USA • UK • Australia • New Zealand • Ireland • Canada", "manual_text": "USER MANUAL", "link_url": "/pages/instruction-overview" } },
        { "type": "region", "settings": { "store_name": "Nederland / België", "store_regions": "Netherlands • Belgium", "manual_text": "GEBRUIKERSHANDLEIDING", "link_url": "/pages/instruction-overview-nl" } },
        { "type": "region", "settings": { "store_name": "France", "store_regions": "France • Belgique (FR) • Suisse (FR)", "manual_text": "MANUEL D'UTILISATION", "link_url": "/pages/instruction-overview-fr" } },
        { "type": "region", "settings": { "store_name": "Deutschland", "store_regions": "Deutschland • Österreich • Schweiz (DE)", "manual_text": "BENUTZERHANDBUCH", "link_url": "/pages/instruction-overview-de" } },
        { "type": "region", "settings": { "store_name": "Italia", "store_regions": "Italia • Svizzera (IT)", "manual_text": "MANUALE UTENTE", "link_url": "/pages/instruction-overview-it" } }
      ]
    }
  ]
}
{% endschema %}


