{%- if template.suffix == 'oddit' -%}
  <style>
    .template-oddit .loox-rating .loox-rating-content .loox-rating-label {
      font-size: 14px;
      font-weight: 400;
      color: #262626;
      letter-spacing: 0;
      line-height: normal;
      text-decoration: underline;
      font-family: DM <PERSON>, sans-serif;
      margin-left: 5px;
      display: none;
    }
    .template-oddit .loox-rating .loox-rating-content {
      gap: 4px;
    }
    .template-oddit .loox-rating .loox-rating-content .loox-icon {
      color: #be9657;
    }
    .template-oddit .product__price.product__price--compare {
      font-size: 24px !important;
    }
    .template-oddit .product-block.richtext ul {
      list-style: none;
      margin: 0;
      padding: 0;
    }
    .template-oddit .product-block.richtext ul li {
      margin-bottom: 20px;
      line-height: normal;
      color: #262626;
      font-family: DM Sans, sans-serif;
      font-weight: 400;
      font-size: 15px;
      position: relative;
      padding-left: 20px;
    }
    .template-oddit .product-block.richtext ul li strong {
      font-weight: 500;
    }
    .template-oddit .product-block.richtext ul li::before {
      background-image: url("data:image/svg+xml,%3Csvg width='11' height='11' viewBox='0 0 11 11' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M10.7667 0.899798C10.8285 0.946709 10.8805 1.00534 10.9196 1.07235C10.9587 1.13936 10.9842 1.21342 10.9946 1.29029C11.0051 1.36716 11.0003 1.44535 10.9805 1.52036C10.9607 1.59537 10.9263 1.66574 10.8792 1.72745L4.58533 9.98819C4.53428 10.0551 4.4695 10.1103 4.39535 10.1501C4.3212 10.1899 4.23939 10.2134 4.15541 10.219C4.07143 10.2246 3.98722 10.2122 3.90845 10.1825C3.82967 10.1529 3.75815 10.1068 3.69868 10.0472L0.158364 6.50687C0.0541368 6.39502 -0.00260518 6.24708 9.19279e-05 6.09421C0.00278903 5.94135 0.0647147 5.7955 0.172823 5.68739C0.280931 5.57928 0.42678 5.51736 0.579644 5.51466C0.732508 5.51196 0.880451 5.56871 0.992305 5.67293L4.05586 8.7357L9.94065 1.0123C10.0354 0.887959 10.1755 0.806277 10.3304 0.785185C10.4853 0.764092 10.6422 0.805313 10.7667 0.899798Z' fill='%23262626'/%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-size: contain;
      width: 12px;
      height: 12px;
      content: '';
      display: block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
    .template-oddit .variant-input-wrap input[type='radio']:checked + label {
      box-shadow: 0 0 0 1px #262626 !important;
    }
    .template-oddit .image-card-container::-webkit-scrollbar {
      height: 4px;
    }
    .template-oddit .main-upsell-product-swiper .loox-rating .loox-icon {
      width: 12px;
      height: 12px;
      color: #ffca40;
    }
    .template-oddit .main-upsell-product-swiper .loox-rating .loox-rating-label {
      font-size: 12px;
      color: #262626;
      font-weight: 400;
      line-height: normal;
      font-family: DM Sans, sans-serif;
      letter-spacing: 0;
      text-decoration: none;
      margin-left: 1px;
      display: block;
    }
    .template-oddit .product__main-photos .grid-product__tag {
      background-color: #ef4444 !important;
    }
    .template-oddit .product__main-photos .flickity-page-dots {
      display: none;
    }
    .template-oddit .product__photos .product__thumbs {
      display: block !important;
    }
    .template-oddit .variant-selector-main .custom-upsell-add.disabled {
      opacity: 0.5;
      pointer-events: none;
    }
    .template-oddit .main-variant-selector-select .option-card.active .option-value {
      display: block;
    }
    .template-oddit .product__photo-zoom {
      display: none;
    }
    .template-oddit .image-card-container + .product-block {
      border-top: 1px solid #d2d2d7;
    }
    @media only screen and (max-width: 767px) {
      .template-oddit .main-upsell-product-swiper .loox-rating .loox-icon {
        color: #be9657;
      }
    }
  </style>
{%- endif -%}

{%- liquid
  assign current_variant = product.selected_or_first_available_variant

  unless thumbnail_position
    assign thumbnail_position = 'beside'
  endunless

  assign product_zoom_size = '1800x1800'
  assign product_image_size = '620x'

  case image_container_width
    when 'small'
      assign product_image_width = 'medium-up--two-fifths'
      assign product_description_width = 'medium-up--three-fifths'
      assign product_image_size = '480x'
    when 'medium'
      assign product_image_width = 'medium-up--one-half'
      assign product_description_width = 'medium-up--one-half'
      assign product_image_size = '620x'
    when 'large'
      assign product_image_width = 'medium-up--three-fifths'
      assign product_description_width = 'medium-up--two-fifths'
      assign product_image_size = '740x'
  endcase

  assign product_img_structure = product.featured_media | img_url: '1x1' | replace: '_1x1.', '_{width}x.'
-%}

{%- liquid
  assign connect_to_sizechart = false

  for block in section.blocks
    if block.type == 'size_chart'
      assign sizechart_index = forloop.index0
      assign next_block_index = sizechart_index | plus: 1
      assign variant_block = section.blocks[next_block_index]

      if variant_block.type == 'variant_picker' and variant_block.settings.picker_type == 'button' and product.has_only_default_variant == false
        for option in product.options_with_values
          assign size_trigger = 'products.general.size_trigger' | t | downcase
          assign downcased_option = option.name | downcase

          if downcased_option contains size_trigger
            assign connect_to_sizechart = true
          endif
        endfor
      endif
    endif
  endfor
-%}

<div
  id="ProductSection-{{ section_id }}-{{ product.id }}"
  class="product-section custom-product-section"
  data-section-id="{{ section_id }}"
  data-product-id="{{ product.id }}"
  data-section-type="product"
  data-product-handle="{{ product.handle }}"
  data-product-title="{{ product.title | escape }}"
  data-product-url="{{ product.url | within: collection }}"
  data-aspect-ratio="{{ 100 | divided_by: product.featured_media.aspect_ratio }}"
  data-img-url="{{ product_img_structure }}"
  {% unless isModal %}
    data-history="true"
  {% endunless %}
  data-modal="{{ isModal }}"
>
  {%- render 'product-template-variables', product: product, current_variant: current_variant -%}

  <div class="page-content page-content--product !tw-pt-[40px] !tw-pb-0 max-lg:!tw-pt-[20px] max-lg:!tw-pb-[20px]">
    <div class="page-width !tw-max-w-[1440px] !tw-px-[48px] max-md:!tw-px-[20px] !tw-mx-auto">
      <div class="grid tw-flex tw-flex-wrap tw-bg-[#FFFFFF] tw-rounded-[40px] max-lg:tw-rounded-[30px] !tw-p-[30px] max-lg:!tw-px-0 max-lg:!tw-pt-0 !tw-ml-0 max-lg:tw-overflow-hidden {% unless image_position == 'left' %} grid--product-images-right{% endunless %}{% if mobile_layout == 'partial' %} grid--product-images--partial{% endif %}">
        {%- if image_position == 'left' -%}
          <div class="grid__item {{ product_image_width }} product-single__sticky !tw-pl-0 !tw-w-[58%] max-lg:!tw-w-full max-lg:!tw-static max-lg:!tw-top-0">
            {%- render 'product-images',
              section_id: section_id,
              product: product,
              isModal: isModal,
              image_position: image_position,
              product_zoom_enable: product_zoom_enable,
              product_zoom_size: product_zoom_size,
              product_image_size: product_image_size,
              thumbnail_arrows: thumbnail_arrows,
              thumbnail_height: thumbnail_height,
              thumbnail_position: thumbnail_position,
              video_looping: video_looping,
              video_style: video_style,
              section: section
            -%}
          </div>
        {%- endif -%}

        <div class="grid__item {{ product_description_width }} !tw-pl-0 !tw-w-[42%] max-lg:!tw-w-full max-lg:!tw-px-[16px] tw-overflow-hidden">
          <div class="product-single__meta !tw-pl-[33px] max-lg:!tw-pl-0 tw-pt-[40px] max-lg:tw-pt-[15px] max-md:!tw-mt-0">
            <div class="product-block product-block--header">
              {%- if settings.show_breadcrumbs and isModal != true -%}
                {%- render 'breadcrumbs' -%}
              {%- endif -%}

              {%- if settings.vendor_enable -%}
                <div class="product-single__vendor">
                  {%- assign vendor_handle = product.vendor | handleize -%}
                  {%- if collections[vendor_handle] != empty -%}
                    <a href="{{ routes.collections_url }}/{{ collections[vendor_handle].handle }}">
                      {{ collections[vendor_handle].title }}
                    </a>
                  {%- else -%}
                    {{ product.vendor | link_to_vendor }}
                  {%- endif -%}
                </div>
              {%- endif -%}

              {% if template.suffix == 'oddit' %}
                <div class="tw-mb-[12px] tw-flex tw-items-center tw-gap-x-[12px]">
                  <div
                    class="loox-rating tw-flex"
                    data-id="{{ product.id }}"
                    data-rating="{{ product.metafields.loox.avg_rating }}"
                    data-raters="{{ product.metafields.loox.num_reviews }}"
                  ></div>
                  <span
                    class="tw-text-[14px] tw-text-darkblack tw-font-normal tw-font-dm-sans tw-leading-[1] tw-tracking-normal tw-underline"
                    onclick="document.querySelector('#looxReviews').scrollIntoView({behavior: 'smooth'});"
                  >
                    {{ product.metafields.loox.num_reviews }}
                    {{ product.metafields.loox.num_reviews | pluralize: 'Review', 'Reviews' }}
                  </span>
                </div>
              {% endif %}
              {%- if isModal -%}
                <p class="h2 product-single__title">
                  {{ product.title }}
                </p>
              {%- else -%}
                <h1 class="h2 !tw-mb-[12px] product-single__title !tw-text-[32px] max-md:!tw-text-[27px] !tw-font-semibold max-md:!tw-font-bold tw-text-darkblack !tw-font-dm-sans !tw-leading-[normal] !tw-capitalize">
                  {%- unless product.empty? -%}
                    {{ product.title }}
                  {%- else -%}
                    {{ 'home_page.onboarding.product_title' | t }}
                  {%- endunless -%}
                </h1>
              {%- endif -%}

              {%- if sku_enable -%}
                <p data-sku class="product-single__sku">
                  {%- if current_variant.sku -%}
                    {{ current_variant.sku }}
                  {%- endif -%}
                </p>
              {%- endif -%}
            </div>

            <div data-product-blocks>
              {%- capture form_id -%}AddToCartForm-{{ section_id }}-{{ product.id }}{%- endcapture -%}
              {%- for block in blocks -%}
                {%- case block.type -%}
                  {%- when '@app' -%}
                    {% render block %}
                  {%- when 'richtext' -%}
                    {% if block.settings.richtext != blank %}
                      <div class="product-block richtext">{{ block.settings.richtext }}</div>
                    {% endif %}
                  {%- when 'separator' -%}
                    <div class="product-block" {{ block.shopify_attributes }}><hr></div>
                  {%- when 'text' -%}
                    <div
                      class="product-block !tw-mb-0 {{ block.type }} tw-text-[15px] max-md:tw-text-[12px] tw-font-normal tw-text-black tw-font-dm-sans tw-leading-[normal] tw-py-[15px] tw-text-center"
                      {{ block.shopify_attributes }}
                    >
                      {{ block.settings.text }}
                    </div>
                  {%- when 'tab' -%}
                    {% assign tab_id = block.id | append: product.id %}
                    {% capture tab_content %}
                      {{ block.settings.content }}
                      {{ block.settings.page.content }}
                      {% if block.settings.image != blank %}
                        <img src="{{ block.settings.image | img_url: 'master' }}" alt="{{ block.settings.image.alt }}">
                      {% endif %}
                    {% endcapture %}
                    {% unless tab_content == blank and block.settings.title == blank %}
                      <div class="product-block product-block--tab" {{ block.shopify_attributes }}>
                        {%- render 'tab', id: tab_id, title: block.settings.title, content: tab_content -%}
                      </div>
                    {% endunless %}
                  {%- when 'contact' -%}
                    <div class="product-block product-block--tab" {{ block.shopify_attributes }}>
                      {% assign tab_id = block.id | append: product.id %}
                      {%- render 'tab-contact', id: tab_id, block: block -%}
                    </div>
                  {%- when 'description' -%}
                    <div
                      class="product-block{% if block.settings.is_tab %} product-block--tab{% endif %}"
                      {{ block.shopify_attributes }}
                    >
                      {%- assign id = block.id | append: product.id -%}
                      {%- render 'product-description', id: id, product: product, is_tab: block.settings.is_tab -%}
                    </div>
                  {%- when 'klarna' -%}
                    <div
                      class="product-block{% if block.settings.is_tab %} product-block--tab{% endif %}"
                      {{ block.shopify_attributes }}
                    >
                      <div class="klarna_container">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          width="615"
                          zoomAndPan="magnify"
                          viewBox="0 0 461.25 414.749983"
                          height="553"
                          preserveAspectRatio="xMidYMid meet"
                          version="1.0"
                        >
                          <defs><clipPath id="1108a3a79a"><path d="M 0 0.210938 L 460.5 0.210938 L 460.5 414.289062 L 0 414.289062 Z M 0 0.210938 " clip-rule="nonzero"/></clipPath><clipPath id="f03dc21fe1"><path d="M 0 0.210938 L 460.5 0.210938 L 460.5 414 L 0 414 Z M 0 0.210938 " clip-rule="nonzero"/></clipPath><image x="0" y="0" width="615" xlink:href="data:image/jpeg;base64,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" id="8619ca6ff6" height="553" preserveAspectRatio="xMidYMid meet"/></defs><g clip-path="url(#1108a3a79a)"><path fill="#ffffff" d="M 0 0.210938 L 461.25 0.210938 L 461.25 422.523438 L 0 422.523438 Z M 0 0.210938 " fill-opacity="1" fill-rule="nonzero"/><path fill="#ffffff" d="M 0 0.210938 L 460.5 0.210938 L 460.5 414.289062 L 0 414.289062 Z M 0 0.210938 " fill-opacity="1" fill-rule="nonzero"/><path fill="#ffffff" d="M 0 0.210938 L 460.5 0.210938 L 460.5 414.289062 L 0 414.289062 Z M 0 0.210938 " fill-opacity="1" fill-rule="nonzero"/></g><g clip-path="url(#f03dc21fe1)"><g transform="matrix(0.748782, 0, 0, 0.748782, 0, 0.211842)"><image x="0" y="0" width="615" xlink:href="data:image/jpeg;base64,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" height="553" preserveAspectRatio="xMidYMid meet"/></g></g>
                        </svg>
                        <p class="klarna_container_info">{{ block.settings.text_info }}</p>
                      </div>
                    </div>
                    <style>
                      .klarna_container svg {
                        width: 22px;
                        height: 22px;
                        margin: 0 0 0 3px;
                      }
                      .product-block.product-block--sales-point,
                      .product-block--sales-point + .product-block--sales-point,
                      .product-block--sales-point + .product-block--sales-point + .product-block {
                        margin: 0;
                      }
                      .product-block ul.sales-points {
                        margin: 5px 0;
                      }
                      .klarna_container {
                        display: flex;
                        align-items: center;
                        justify-content: flex-start;
                        gap: 5px;
                      }
                      p.klarna_container_info {
                        margin: 0 0 0 5px;
                      }
                    </style>
                  {%- when 'price' -%}
                    <div
                      class="product-block product-block--price tw-flex tw-items-center"
                      {{ block.shopify_attributes }}
                    >
                      {% if template.suffix == 'oddit' %}
                        <span
                          data-product-price
                          class="product__price{% if current_variant.compare_at_price > current_variant.price %} on-sale{% endif %} !tw-text-[32px] max-md:!tw-text-[27px] !tw-text-[#008001] tw-tracking-normal !tw-m-0 !tw-p-0 !tw-mr-[15px] max-md:!tw-mr-[10px] tw-font-dm-sans tw-leading-[normal]"
                        >
                          {%- unless product.empty? -%}
                            {{ current_variant.price | money }}
                          {%- else -%}
                            {{ 1999 | money }}
                          {%- endunless -%}
                        </span>
                      {% endif %}

                      {%- assign hide_sale_price = true -%}
                      {%- if product.compare_at_price_max > product.price -%}
                        {%- if current_variant.compare_at_price > current_variant.price -%}
                          {%- assign hide_sale_price = false -%}
                        {%- endif -%}
                        <span
                          data-a11y-price
                          class="visually-hidden"
                          aria-hidden="{{ hide_sale_price }}"
                        >
                          {{ 'products.general.regular_price' | t }}
                        </span>
                        <span data-product-price-wrap class="{% if hide_sale_price %} hide{% endif %}">
                          <span
                            data-compare-price
                            class="product__price product__price--compare !tw-text-[24px] !tw-text-[#80868B] !tw-font-normal !tw-font-dm-sans !tw-p-0 !tw-m-0"
                          >
                            {%- if current_variant.compare_at_price > current_variant.price -%}
                              {% if template.name == 'product' and template.suffix contains 'oddit' %}
                                {{ current_variant.compare_at_price | money_without_trailing_zeros }}
                              {% else %}
                                {{ current_variant.compare_at_price | money }}
                              {% endif %}
                            {%- endif -%}
                          </span>
                        </span>
                        <span data-compare-price-a11y class="visually-hidden">
                          {{- 'products.general.sale_price' | t -}}
                        </span>
                      {%- else -%}
                        <span data-a11y-price class="visually-hidden">{{ 'products.general.regular_price' | t }}</span>
                      {%- endif -%}

                      {%- unless template.suffix == 'oddit' -%}
                        <span
                          data-product-price
                          class="product__price{% if current_variant.compare_at_price > current_variant.price %} on-sale{% endif %}"
                        >
                          {%- unless product.empty? -%}
                            {{ current_variant.price | money }}
                          {%- else -%}
                            {{ 1999 | money }}
                          {%- endunless -%}
                        </span>
                      {%- endunless -%}
                      {%- if template.suffix == 'oddit' -%}
                        {%- liquid
                          assign compare_at_price = product.compare_at_price
                          assign price = product.price | default: 1999

                          if compare_at_price > price
                            assign sale_percentage = compare_at_price | minus: price | times: 100.0 | divided_by: compare_at_price | money_without_currency | times: 100 | remove: '.0'
                          endif
                        -%}
                        {%- if sale_percentage -%}
                          <span class="tw-ml-[15px] max-md:tw-ml-[10px] tw-text-[15px] max-md:tw-text-[14px] tw-text-white tw-font-bold tw-font-dm-sans tw-uppercase tw-leading-[normal] tw-bg-[#008001] tw-rounded-[20px] tw-px-[7px] tw-py-[2px] tw-min-w-max"
                            >SAVE {{ sale_percentage }}%</span
                          >
                        {%- endif -%}
                      {%- endif -%}

                      {%- if settings.product_save_amount -%}
                        {%- if settings.product_save_type == 'dollar' -%}
                          {%- capture saved_amount -%}{{ current_variant.compare_at_price | minus: current_variant.price | money }}{%- endcapture -%}
                        {%- else -%}
                          {%- capture saved_amount -%}{{ current_variant.compare_at_price | minus: current_variant.price | times: 100.0 | divided_by: current_variant.compare_at_price | round }}%{%- endcapture -%}
                        {%- endif -%}
                        <span data-save-price class="product__price-savings{% if hide_sale_price %} hide{% endif %}">
                          {%- unless hide_sale_price -%}
                            {{ 'products.general.save_html' | t: saved_amount: saved_amount }}
                          {%- endunless -%}
                        </span>
                      {%- endif -%}

                      <div
                        data-unit-price-wrapper
                        class="product__unit-price product__unit-price--spacing {% unless current_variant.unit_price_measurement %} hide{% endunless %}"
                      >
                        {%- capture unit_price_base_unit -%}
                          <span data-unit-base>
                            {%- if current_variant.unit_price_measurement -%}
                              {%- if current_variant.unit_price_measurement.reference_value != 1 -%}
                                {{ current_variant.unit_price_measurement.reference_value }}
                              {%- endif -%}
                              {{ current_variant.unit_price_measurement.reference_unit }}
                            {%- endif -%}
                          </span>
                        {%- endcapture -%}

                        <span data-unit-price>{{ current_variant.unit_price | money }}</span>/{{ unit_price_base_unit }}
                      </div>

                      {%- if shop.taxes_included or shop.shipping_policy.body != blank -%}
                        <div class="product__policies rte small--text-center">
                          {%- if shop.taxes_included -%}
                            {{ 'products.product.include_taxes' | t }}
                          {%- endif -%}
                          {%- if shop.shipping_policy.body != blank -%}
                            {{ 'products.product.shipping_policy_html' | t: link: shop.shipping_policy.url }}
                          {%- endif -%}
                        </div>
                      {%- endif -%}
                    </div>
                  {%- when 'quantity_selector' -%}
                    <div class="product-block" {{ block.shopify_attributes }}>
                      <div class="product__quantity">
                        {% assign qty_id = section_id | append: product.id %}
                        <label for="Quantity-{{ qty_id }}">{{ 'products.product.quantity' | t }}</label>
                        {%- render 'quantity-input', form_id: form_id, id: qty_id, qty: 1, min: 1 -%}
                      </div>
                    </div>
                  {%- when 'size_chart' -%}
                    {% if connect_to_sizechart == false %}
                      {%- capture size_chart_title -%}
                        {{ 'products.general.size_chart' | t }}<svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-size-chart" viewBox="0 0 64 64"><defs><style>.a{fill:none;stroke:#000;stroke-width:2px}</style></defs><path class="a" d="M22.39 33.53c-7.46 0-13.5-3.9-13.5-8.72s6-8.72 13.5-8.72 13.5 3.9 13.5 8.72a12 12 0 0 1-.22 1.73"/><ellipse cx="22.39" cy="24.81" rx="3.28" ry="2.12"/><path class="a" d="M8.89 24.81V38.5c0 7.9 6.4 9.41 14.3 9.41h31.92V33.53H22.39M46.78 33.53v7.44M38.65 33.53v7.44M30.52 33.53v7.44M22.39 33.53v7.44"/></svg>
                      {%- endcapture -%}

                      <div class="size-chart__standalone" {{ block.shopify_attributes }}>
                        {%- render 'tool-tip-trigger',
                          title: size_chart_title,
                          content: block.settings.size_chart.content,
                          context: 'size-chart'
                        -%}
                        {% style %}
                          tool-tip-trigger {
                            text-transform: uppercase;
                          }
                        {% endstyle %}
                      </div>
                    {% endif %}
                  {%- when 'variant_picker' -%}
                    <div
                      class="product-block max-md:!tw-mb-[17px]"
                      {% if block.settings.product_dynamic_variants_enable %}
                        data-dynamic-variants-enabled
                      {% endif %}
                      {{ block.shopify_attributes }}
                    >
                      {%- unless product.has_only_default_variant -%}
                        {%- for option in product.options_with_values -%}
                          {%- liquid
                            if block.settings.color_swatches
                              assign is_color = false
                              assign color_option_index = 0
                              assign swatch_trigger = 'products.general.color_swatch_trigger' | t | downcase
                              assign color_option_index = forloop.index0
                              assign downcased_option = option.name | downcase
                              if downcased_option contains swatch_trigger
                                assign is_color = true
                              elsif swatch_trigger == 'color' and downcased_option contains 'colour'
                                assign is_color = true
                              endif
                            endif
                          -%}

                          {%- if block.settings.picker_type == 'button' -%}
                            {%- render 'variant-button',
                              block: block,
                              product: product,
                              form_id: form_id,
                              section_id: section_id,
                              option: option,
                              forloop: forloop,
                              variant_labels: block.settings.variant_labels,
                              is_color: is_color,
                              color_option_index: color_option_index,
                              connect_to_sizechart: connect_to_sizechart,
                              sizechart_index: sizechart_index
                            -%}
                          {%- else -%}
                            {%- render 'variant-dropdown',
                              product: product,
                              form_id: form_id,
                              section_id: section_id,
                              option: option,
                              forloop: forloop,
                              variant_labels: block.settings.variant_labels
                            -%}
                          {%- endif -%}
                        {%- endfor -%}
                      {%- endunless -%}
                    </div>
                  {%- when 'estimated-delivery' -%}
                    <div
                      id="delivery-estimate"
                      data-delivery-days="{% if product.metafields.custom.delivery_days != blank %}{{ product.metafields.custom.delivery_days }}{% elsif block.settings.delivery_days != blank %}{{ block.settings.delivery_days }}{% endif %}"
                      class="hide"
                    >
                      <p class="tw-mb-0 tw-text-[15px] max-md:tw-text-[14px] tw-font-normal tw-text-[#2D2A39] tw-font-dm-sans tw-leading-[normal] tw-py-[20px] tw-text-center tw-border-0 tw-border-t tw-border-b tw-border-solid tw-border-[#E6E3D9]">
                        {{ block.settings.delivery_text }}
                        <span id="delivery-zipcode" class="tw-text-[#0071E3] tw-underline"></span> by
                        <span id="delivery-date">...</span>
                      </p>
                    </div>
                    <script>
                      document.addEventListener('DOMContentLoaded', function () {
                        function getDeliveryDate() {
                          let deliveryDays = parseInt(
                            document.getElementById('delivery-estimate').dataset.deliveryDays
                          );
                          let currentDate = new Date();
                          currentDate.setDate(currentDate.getDate() + deliveryDays);
                          const options = { weekday: 'short', month: 'long', day: 'numeric' };
                          let formattedDate = currentDate.toLocaleDateString('en-US', options);
                          return formattedDate;
                        }
                        document.getElementById('delivery-date').textContent = getDeliveryDate();

                        function getZipcode() {
                          if (navigator.geolocation) {
                            // Get user's current position
                            navigator.geolocation.getCurrentPosition(showPosition, showError);
                          } else {
                            console.log('Geolocation is not supported by this browser.');
                          }
                        }
                        getZipcode();
                        // Function to handle successful geolocation
                        function showPosition(position) {
                          let latitude = position.coords.latitude;
                          let longitude = position.coords.longitude;

                          // Construct Nominatim API URL for reverse geocoding
                          let url = `https://nominatim.openstreetmap.org/reverse?lat=${latitude}&lon=${longitude}&format=json&addressdetails=1`;

                          // Make a request to the Nominatim API
                          fetch(url)
                            .then((response) => response.json())
                            .then((data) => {
                              if (data && data.address && data.address.postcode) {
                                let zipcode = data.address.postcode;
                                document.getElementById('delivery-zipcode').innerHTML = zipcode;
                                document.querySelector('#delivery-estimate').classList.remove('hide');
                              } else {
                                console.log('Zipcode not found.');
                              }
                            })
                            .catch((error) => {
                              console.log('Error retrieving zipcode.-err');
                            });
                        }

                        // Function to handle geolocation errors
                        function showError(error) {
                          switch (error.code) {
                            case error.PERMISSION_DENIED:
                              console.log('User denied the request for Geolocation.');
                              break;
                            case error.POSITION_UNAVAILABLE:
                              console.log('Location information is unavailable.');
                              break;
                            case error.TIMEOUT:
                              console.log('The request to get user location timed out.');
                              break;
                            case error.UNKNOWN_ERROR:
                              console.log('An unknown error occurred.');
                              break;
                          }
                        }
                      });
                    </script>
                  {%- when 'main-image-card' -%}
                    {% assign image_cards = section.blocks | where: 'type', 'image-card' %}
                    <div class="image-card-container tw-flex tw-gap-[10px] tw-overflow-x-auto tw-pb-[10px] tw-mt-[15px] tw-mb-[20px]">
                      {% for block in image_cards %}
                        {% if block.settings.image != blank or block.settings.title != blank %}
                          <div class="image-card tw-min-w-[99px] tw-max-w-[99px]">
                            {% if block.settings.video != blank %}
                              {% assign poster_image = block.settings.image | image_url: width: '2000' %}

                              <div class="custom-video-container tw-relative">
                                <img
                                  src="{{ 'icon-play.svg' | asset_url }}"
                                  alt="Play Video"
                                  class="play-icon tw-absolute tw-left-0 tw-right-0 tw-top-[50%] tw-translate-y-[-50%] tw-z-[2] tw-mx-auto tw-cursor-pointer"
                                  id="play-icon-{{ forloop.index }}"
                                  data-video-id="video-{{ forloop.index }}"
                                  data-index="{{ forloop.index }}"
                                >

                                <div
                                  id="video-container-{{ forloop.index }}"
                                  class="video-container tw-relative before:tw-content-[''] before:tw-block before:tw-pt-[137%] tw-overflow-hidden tw-rounded-[10px]"
                                >
                                  {{
                                    block.settings.video
                                    | video_tag:
                                      width: '100%',
                                      height: 'auto',
                                      controls: false,
                                      poster: poster_image,
                                      class: 'tw-w-full tw-h-full tw-object-cover tw-block tw-absolute tw-left-0 tw-top-0'
                                  }}
                                </div>
                              </div>

                            {% else %}
                              {% if block.settings.image != blank %}
                                <img
                                  src="{{ block.settings.image | img_url: 'master' }}"
                                  alt="{{ block.settings.image.alt }}"
                                  class="
                                    tw-w-full tw-rounded-
                                    [10px] tw-object-cover tw-flex
                                  "
                                >
                              {% endif %}
                            {% endif %}

                            {% if block.settings.title != blank %}
                              <p class="tw-m-0 tw-mt-[15px] tw-text-[13px] tw-font-medium tw-text-[#4A4741] tw-leading-[16px] tw-tracking-normal tw-text-center tw-font-dm-sans">
                                {{ block.settings.title }}
                              </p>
                            {% endif %}
                          </div>
                        {% endif %}
                      {% endfor %}
                    </div>
                  {%- when 'buy_buttons' -%}
                    <div class="product-block !tw-mb-0" {{ block.shopify_attributes }}>
                      {%- unless product.empty? -%}
                        <div class="product-block !tw-mb-0">
                          {%- render 'product-form',
                            form_id: form_id,
                            product: product,
                            show_dynamic_checkout: block.settings.show_dynamic_checkout,
                            current_variant: current_variant,
                            enable_product_title: true
                          -%}
                        </div>
                      {%- endunless -%}

                      {%- if block.settings.surface_pickup_enable -%}
                        <div
                          data-store-availability-holder
                          data-product-name="{{ product.title | escape }}"
                          data-base-url="{{ shop.url }}{{ routes.root_url }}"
                        ></div>
                      {%- endif -%}
                    </div>
                  {%- when 'sales_point' -%}
                    {%- unless block.settings.text == blank -%}
                      <div class="product-block product-block--sales-point" {{ block.shopify_attributes }}>
                        <ul class="sales-points">
                          <li class="sales-point">
                            <span class="icon-and-text">
                              {% case block.settings.icon %}
                                {% when 'checkmark' %}
                                  <svg
                                    aria-hidden="true"
                                    focusable="false"
                                    role="presentation"
                                    class="icon icon-circle-checkmark"
                                    viewBox="0 0 64 64"
                                  >
                                    <defs><style>.a{fill:none;stroke:#000;stroke-width:2px}</style></defs><path class="a" d="M52.68 24.48A22 22 0 1 1 47 15.93M21 32l8.5 8.5L57 13"/>
                                  </svg>
                                {% when 'gift' %}
                                  <svg
                                    aria-hidden="true"
                                    focusable="false"
                                    role="presentation"
                                    class="icon icon-gift"
                                    viewBox="0 0 64 64"
                                  >
                                    <defs><style>.a{fill:none;stroke:#000;stroke-width:2px}</style></defs><path class="a" d="M9 20.23h46v8.68H9zM13.34 28.91h36.89v24.3H13.34zM27.82 20.66v32.98M35.91 20.88v32.98M20.52 11.43a3.73 3.73 0 0 0-.74 2.87 4.57 4.57 0 0 0 .8 2.27C22 18.09 25.15 20 32 20.23a12 12 0 0 0-2.43-7 7.52 7.52 0 0 0-5.66-3.12 4 4 0 0 0-3.39 1.32z"/><path class="a" d="M43.37 11.43a3.73 3.73 0 0 1 .74 2.87 4.48 4.48 0 0 1-.8 2.27c-1.42 1.52-4.57 3.41-11.42 3.66a12.08 12.08 0 0 1 2.43-7A7.56 7.56 0 0 1 40 10.14a4 4 0 0 1 3.37 1.29z"/>
                                  </svg>
                                {% when 'globe' %}
                                  <svg
                                    aria-hidden="true"
                                    focusable="false"
                                    role="presentation"
                                    class="icon icon-globe"
                                    viewBox="0 0 64 64"
                                  >
                                    <defs><style>.a{fill:none;stroke:#000;stroke-width:2px}</style></defs><circle class="a" cx="32" cy="32" r="22"/><path class="a" d="M13 21h38M10 32h44M13 43h38M32 10c-12 8-12 37 0 44M32 10c12 8 12 37 0 44"/>
                                  </svg>
                                {% when 'heart' %}
                                  <svg
                                    aria-hidden="true"
                                    focusable="false"
                                    role="presentation"
                                    class="icon icon-heart"
                                    viewBox="0 0 64 64"
                                  >
                                    <defs><style>.a{fill:none;stroke:#000;stroke-width:2px}</style></defs><path class="a" d="M51.27 15.05a13 13 0 0 0-18.44 0l-.83.83-.83-.83a13 13 0 0 0-18.44 18.44l.83.83L32 52.77l18.44-18.45.83-.83a13 13 0 0 0 0-18.44z"/>
                                  </svg>
                                {% when 'leaf' %}
                                  <svg
                                    aria-hidden="true"
                                    focusable="false"
                                    role="presentation"
                                    class="icon icon-leaf"
                                    viewBox="0 0 64 64"
                                  >
                                    <defs><style>.a{fill:none;stroke:#000;stroke-miterlimit:10;stroke-width:2px}</style></defs><path class="a" d="M52 11L13 50M35 13v15h16"/><path class="a" d="M52 11c6 24-16.72 47.29-33 32C7 22 29 10 52 11z"/>
                                  </svg>
                                {% when 'lock' %}
                                  <svg
                                    aria-hidden="true"
                                    focusable="false"
                                    role="presentation"
                                    class="icon icon-lock"
                                    viewBox="0 0 64 64"
                                  >
                                    <defs><style>.a{fill:none;stroke:#000;stroke-width:2px}</style></defs><path class="a" d="M19.45 23.6v-3.2c0-7 5.89-11.75 12.55-11.75 7.21 0 12.55 5.49 12.55 11.75v3.2M10 23.6h44v31.75H10zM32 37.47v7.47"/><circle class="a" cx="32" cy="35.87" r="1.6"/>
                                  </svg>
                                {% when 'package' %}
                                  <svg
                                    aria-hidden="true"
                                    focusable="false"
                                    role="presentation"
                                    class="icon icon-package"
                                    viewBox="0 0 64 64"
                                  >
                                    <defs><style>.a{fill:none;stroke:#000;stroke-width:2px}</style></defs><path class="a" d="M32 54.52L10 41.95v-19.9L32 9.48l22 12.57v19.9L32 54.52z"/><path class="a" d="M32 54.52v-19.9l22-12.57M32 34.62L10 22.05M41.7 15.02L21 28.33v8.38"/>
                                  </svg>
                                {% when 'phone' %}
                                  <svg
                                    aria-hidden="true"
                                    focusable="false"
                                    role="presentation"
                                    class="icon icon-phone"
                                    viewBox="0 0 64 64"
                                  >
                                    <defs><style>.a{fill:none;stroke:#000;stroke-width:2px}</style></defs><path class="a" d="M18.4 9.65l10.2 10.2-6.32 6.32c2.1 7 6.89 12.46 15.55 15.55l6.32-6.32 10.2 10.2-8.75 8.75C25.71 50.3 13.83 38.21 9.65 18.4z"/>
                                  </svg>
                                {% when 'ribbon' %}
                                  <svg
                                    aria-hidden="true"
                                    focusable="false"
                                    role="presentation"
                                    class="icon icon-ribbon"
                                    viewBox="0 0 64 64"
                                  >
                                    <defs><style>.a{fill:none;stroke:#000;stroke-width:2px}</style></defs><path class="a" d="M28.57 42.31l-8.36 13.07-2.6-7.17-7.61.65 8.35-13.07a17.05 17.05 0 0 0 8.12 5.94 17.3 17.3 0 0 0 1.74.5zM54 48.86l-7.61-.65-2.61 7.17-8.36-13.07.37-.08a16.4 16.4 0 0 0 1.73-.5 17 17 0 0 0 8.12-5.93z"/><path class="a" d="M49 25.64a16.79 16.79 0 0 1-3.14 9.85 3.55 3.55 0 0 1-.23.31 17 17 0 0 1-8.12 5.93 16.4 16.4 0 0 1-1.73.5l-.37.08a16.74 16.74 0 0 1-3.42.35 16.91 16.91 0 0 1-3.43-.35l-.36-.08a17.3 17.3 0 0 1-1.74-.5 17.05 17.05 0 0 1-8.12-5.94c-.07-.09-.15-.19-.22-.3A16.86 16.86 0 0 1 15 25.64a17 17 0 0 1 34 0z"/><path class="a" d="M36.09 27.78l.96 5.63L32 30.75l-5.05 2.66.96-5.63-4.09-3.98 5.65-.82L32 17.86l2.53 5.12 5.64.82-4.08 3.98z"/>
                                  </svg>
                                {% when 'shield' %}
                                  <svg
                                    aria-hidden="true"
                                    focusable="false"
                                    role="presentation"
                                    class="icon icon-shield"
                                    viewBox="0 0 64 64"
                                  >
                                    <defs><style>.a{fill:none;stroke:#000;stroke-width:2px}</style></defs><path class="a" d="M21.18 31.69L28.54 39l15.91-15.88"/><path class="a" d="M53 12.54v15.24a31.41 31.41 0 0 1-3.7 14.95A25.46 25.46 0 0 1 32 56a25.46 25.46 0 0 1-17.3-13.27A31.41 31.41 0 0 1 11 27.78V12.54C24.88 13.67 32 8 32 8s7.12 5.67 21 4.54z"/>
                                  </svg>
                                {% when 'tag' %}
                                  <svg
                                    aria-hidden="true"
                                    focusable="false"
                                    role="presentation"
                                    class="icon icon-tag"
                                    viewBox="0 0 64 64"
                                  >
                                    <defs><style>.a{fill:none;stroke:#000;stroke-width:2px}</style></defs><path class="a" d="M46.69 10.34l-10.55.07-25.8 25.8 17.45 17.45 25.8-25.8.07-10.55-6.97-6.97z"/><circle class="a" cx="43.95" cy="20.05" r="3.53"/><path class="a" d="M14.4 32.15L31.85 49.6"/>
                                  </svg>
                                {% when 'truck' %}
                                  <svg
                                    aria-hidden="true"
                                    focusable="false"
                                    role="presentation"
                                    class="icon icon-truck"
                                    viewBox="0 0 64 64"
                                  >
                                    <defs><style>.a{fill:none;stroke:#000;stroke-miterlimit:10;stroke-width:2px}</style></defs><path class="a" d="M16.5 43.22H6.88V16.5h33.14v26.72H23.94M45.42 43.22h-5.4V30.4h17.1v12.82h-4.23M57.12 30.4l-6.41-8.56H40.02"/><circle class="a" cx="20.24" cy="43.76" r="3.74"/><path class="a" d="M45.42 43.23a3.82 3.82 0 1 1 0 .37v-.37"/>
                                  </svg>
                              {% endcase %}
                              <span>{{ block.settings.text }}</span>
                            </span>
                          </li>
                        </ul>
                      </div>
                    {%- endunless -%}
                  {%- when 'inventory_status' -%}
                    {%- render 'product-inventory', product: product, current_variant: current_variant, block: block -%}
                  {%- when 'share' -%}
                    <div class="product-block" {{ block.shopify_attributes }}>
                      {%- render 'social-sharing',
                        share_title: product.title,
                        share_permalink: product.url,
                        share_image: product
                      -%}
                    </div>
                  {%- when 'trust_badge' -%}
                    <div class="product-block" {{ block.shopify_attributes }}>
                      <div
                        class="aos-animate"
                        style="max-width: {{ block.settings.trust_image.width }}px; margin: 0 auto;"
                      >
                        <div
                          class="image-wrap "
                          style="height: 0; padding-bottom: {{ 100 | divided_by: block.settings.trust_image.aspect_ratio }}%;"
                        >
                          {%- assign img_url = block.settings.trust_image
                            | img_url: '1x1'
                            | replace: '_1x1.', '_{width}x.'
                          -%}
                          <img
                            class="lazyload"
                            data-src="{{ img_url }}"
                            data-widths="[360, 540, 700, 1024]"
                            data-aspectratio="{{ block.settings.trust_image.aspect_ratio }}"
                            data-sizes="auto"
                            alt="{{ block.settings.trust_image.alt }}"
                          >
                          <noscript>
                            <img
                              class="lazyloaded"
                              src="{{ block.settings.trust_image | img_url: '540x' }}"
                              alt="{{ block.settings.trust_image.alt }}"
                            >
                          </noscript>
                        </div>
                      </div>
                    </div>
                  {%- when 'custom' -%}
                    <div class="product-block" {{ block.shopify_attributes }}>
                      {{ block.settings.code }}
                    </div>
                  {%- when 'pair-it-with' -%}
                    <upsell-product
                      data-product-id="{{ product.id }}"
                      data-section-id="{{ section.id }}"
                      data-url="{{ routes.product_recommendations_url }}?limit={{ block.settings.products_to_show }}"
                      class="tw-pt-[13px] tw-overflow-hidden tw-block max-sm:tw-overflow-visible"
                    >
                      <h2
                        class="pair-it-with__heading !tw-text-[20px] !tw-font-bold !tw-text-darkblack !tw-leading-[normal] !tw-font-dm-sans !tw-capitalize"
                      >
                        {{ block.settings.heading }}
                      </h2>
                      <div class="swiper !tw-overflow-visible main-upsell-product-swiper">
                        <ul class="pair-it-with-grid swiper-wrapper tw-pb-[15px]">
                          {% for recommendation in recommendations.products %}
                            <li class="grid__item swiper-slide tw-border tw-border-solid tw-border-[#4A4741] tw-rounded-[10px] !tw-pl-0 !tw-flex tw-items-center !tw-mb-0 !tw-h-auto max-md:tw-bg-[#FFFDFB]">
                              <div class="image tw-rounded-[10px_0_0_10px] tw-bg-[#F6F6F6] tw-w-[120px] tw-h-full tw-border-0 tw-border-r tw-border-solid tw-border-[#4A4741]">
                                <img
                                  src="{{ recommendation.featured_image | img_url: 'master' }}"
                                  alt="{{ recommendation.featured_image.alt }}"
                                  class="tw-w-full tw-h-full tw-object-contain"
                                >
                              </div>
                              <div class="right-part tw-w-[calc(100%-120px)] tw-p-[5px_16px] lg:tw-p-[16px]">
                                <div
                                  class="loox-rating tw-mb-[8px] md:tw-mb-[14px]"
                                  data-id="{{ recommendation.id }}"
                                  data-rating="{{ recommendation.metafields.loox.avg_rating }}"
                                  data-raters="{{ recommendation.metafields.loox.num_reviews }}"
                                ></div>
                                <a href="{{ recommendation.url }}">
                                  <h3 class="tw-text-[16px] tw-font-bold tw-text-black tw-leading-[normal] !tw-capitalize tw-mb-[16px]">
                                    {{ recommendation.title }}
                                  </h3>
                                </a>
                                <div class="variant-selector-main tw-flex tw-items-center tw-justify-between max-sm:tw-flex-wrap max-sm:tw-gap-[8px]">
                                  {%- unless recommendation.has_only_default_variant -%}
                                    <div class="main-variant-selector-select tw-w-[calc(100%-85px)] max-sm:tw-w-[calc(100%-79px)]">
                                      {%- for option in recommendation.options_with_values -%}
                                        <div class="option-card tw-relative">
                                          {%- assign selected_value = recommendation.selected_or_first_available_variant.options[forloop.index0] -%}
                                          <div class="button-option tw-relative tw-border tw-border-solid tw-border-[#EFEFF2] tw-rounded-[5px] tw-text-[15px] max-sm:tw-text-[14px] tw-font-normal tw-leading-[normal] tw-font-dm-sans tw-text-black tw-tracking-normal tw-py-[9px] tw-px-[9px] lg:tw-px-[12px] tw-pr-[29px] lg:tw-pr-[34px] tw-cursor-pointer">
                                            {{ option.name }}:
                                            <span class="selected-option-value">{{ selected_value }}</span>
                                            <svg
                                              class="tw-absolute tw-top-[50%] tw-translate-y-[-50%] tw-right-[17px]"
                                              xmlns="http://www.w3.org/2000/svg"
                                              width="12"
                                              height="7"
                                              viewBox="0 0 12 7"
                                              fill="none"
                                            >
                                              <path d="M0.863095 0.529354C0.925022 0.46727 0.99859 0.418012 1.07958 0.384404C1.16058 0.350795 1.24741 0.333496 1.33509 0.333496C1.42278 0.333496 1.50961 0.350795 1.59061 0.384404C1.6716 0.418012 1.74517 0.46727 1.80709 0.529354L6.00176 4.72535L10.1964 0.529354C10.3216 0.404172 10.4914 0.333845 10.6684 0.333845C10.8455 0.333845 11.0152 0.404172 11.1404 0.529354C11.2656 0.654536 11.3359 0.82432 11.3359 1.00135C11.3359 1.17839 11.2656 1.34817 11.1404 1.47335L6.47376 6.14002C6.41183 6.20211 6.33827 6.25136 6.25727 6.28497C6.17628 6.31858 6.08945 6.33588 6.00176 6.33588C5.91407 6.33588 5.82724 6.31858 5.74625 6.28497C5.66526 6.25136 5.59169 6.20211 5.52976 6.14002L0.863095 1.47335C0.80101 1.41143 0.751753 1.33786 0.718145 1.25687C0.684536 1.17587 0.667236 1.08904 0.667236 1.00135C0.667236 0.913664 0.684536 0.826836 0.718145 0.745843C0.751753 0.664849 0.80101 0.591282 0.863095 0.529354Z" fill="#262626"/>
                                            </svg>
                                          </div>
                                          <ul class="option-value tw-bg-white tw-hidden tw-absolute tw-top-auto tw-bottom-[105%] tw-max-h-[110px] tw-overflow-y-auto tw-left-0 tw-list-none tw-m-0 tw-p-[12px] tw-border tw-border-solid tw-border-[#EFEFF2] tw-rounded-[5px] tw-mt-[5px] tw-w-full tw-z-[9999] max-lg:tw-top-auto max-lg:tw-bottom-[110%]">
                                            {%- for value in option.values -%}
                                              <li
                                                data-value="{{ value }}"
                                                class="{% if selected_value == value %}active{% endif %} tw-cursor-pointer"
                                              >
                                                {{ value }}
                                              </li>
                                            {%- endfor -%}
                                          </ul>
                                        </div>
                                      {%- endfor -%}
                                      <select class="default-select-box hide">
                                        {%- for variant in recommendation.variants -%}
                                          {%- if variant.available -%}
                                            <option
                                              {% if variant == recommendation.selected_or_first_available_variant %}
                                                selected="selected"
                                              {% endif %}
                                              data-value="{{ variant.id }}"
                                              data-title="{{ variant.title }}"
                                            >
                                              {{ variant.title }}
                                            </option>
                                          {%- else -%}
                                            <option
                                              disabled="disabled"
                                              data-value="{{ variant.id }}"
                                              data-title="{{ variant.title }}"
                                            >
                                              {{ variant.title }} - {{ 'products.product.sold_out' | t }}
                                            </option>
                                          {%- endif -%}
                                        {%- endfor -%}
                                      </select>
                                    </div>
                                  {%- endunless -%}
                                  <add-item
                                    class="custom-upsell-add tw-min-w-[73px] max-sm:tw-min-w-[67px] tw-py-[11px] tw-rounded-[50px] tw-bg-[#0071E3] tw-text-[12px] tw-font-bold tw-text-white tw-uppercase tw-flex tw-items-center tw-justify-center tw-leading-[normal] tw-cursor-pointer"
                                    data-id="{{ recommendation.selected_or_first_available_variant.id }}"
                                  >
                                    Add
                                  </add-item>
                                </div>
                              </div>
                            </li>
                          {% endfor %}
                        </ul>
                        <div class="swiper-scrollbar !tw-z-0"></div>
                      </div>
                    </upsell-product>

                    <script>
                      class UpsellProduct extends HTMLElement {
                        constructor() {
                          super();
                        }

                        connectedCallback() {
                          this.initializeUpsellProduct(this.dataset.productId);
                        }

                        initializeUpsellProduct(productId) {
                          if (!productId) return;
                          fetch(`${this.dataset.url}&product_id=${productId}&section_id=${this.dataset.sectionId}`)
                            .then((response) => response.text())
                            .then((text) => {
                              const parser = new DOMParser();
                              const html = parser.parseFromString(text, 'text/html');
                              const upsellProduct = html.querySelector('upsell-product');
                              let $this = this;
                              window.addEventListener('resize', () => resizeWidth($this, upsellProduct));
                              resizeWidth($this, upsellProduct);
                              function resizeWidth($this, upsellProduct) {
                                const mobileUpsell = document.querySelector('.mobile-upsell-main-pdp');

                                if (window.innerWidth >= 768) {
                                  if (mobileUpsell) {
                                    mobileUpsell.innerHTML = '';
                                  }
                                  if (
                                    upsellProduct &&
                                    upsellProduct.querySelectorAll('.pair-it-with-grid .grid__item').length > 0
                                  ) {
                                    $this.innerHTML = upsellProduct.innerHTML;
                                    new Swiper('.main-upsell-product-swiper', {
                                      slidesPerView: 1.1,
                                      slidesPerGroup: 1,
                                      spaceBetween: 10,
                                      freeMode: true,
                                      scrollbar: {
                                        el: '.swiper-scrollbar',
                                        draggable: true,
                                      },
                                      mousewheel: {
                                        forceToAxis: true,
                                      },
                                    });
                                    upsellProductVariant();
                                  } else if (upsellProduct) {
                                    upsellProduct.classList.add('hide');
                                  }
                                } else {
                                  if ($this) {
                                    $this.innerHTML = '';
                                  }
                                  if (mobileUpsell && mobileUpsell.innerHTML === '') {
                                    mobileUpsell.innerHTML = upsellProduct.innerHTML;
                                    new Swiper('.main-upsell-product-swiper', {
                                      slidesPerView: 1.1,
                                      slidesPerGroup: 1,
                                      spaceBetween: 10,
                                      freeMode: true,
                                      scrollbar: {
                                        el: '.swiper-scrollbar',
                                        draggable: true,
                                      },
                                      mousewheel: {
                                        forceToAxis: true,
                                      },
                                      breakpoints: {
                                        0: {
                                          slidesPerView: 1.02,
                                        },
                                        767: {
                                          slidesPerView: 1.1,
                                        },
                                      },
                                    });
                                    upsellProductVariant();
                                  }
                                }
                              }
                            })
                            .catch((e) => {
                              console.error(e);
                            });
                        }
                      }
                      customElements.define('upsell-product', UpsellProduct);

                      function upsellProductVariant() {
                        let optionLists = document.querySelectorAll('.main-variant-selector-select li');
                        if (optionLists) {
                          optionLists.forEach(function (item) {
                            item.addEventListener('click', function () {
                              let optionCard = this.closest('.option-card');
                              optionCard.querySelectorAll('.option-value li').forEach(function (i) {
                                i.classList.remove('active');
                              });
                              this.classList.add('active');
                              let buttonOption = optionCard.querySelector('.button-option span');
                              if (buttonOption) {
                                buttonOption.textContent = this.dataset.value;
                                optionCard.classList.remove('active');
                              }

                              let selectedValues = [];
                              this.closest('.main-variant-selector-select')
                                .querySelectorAll('.option-card')
                                .forEach((card) => {
                                  let activeItem = card.querySelector('.option-value li.active');
                                  if (activeItem) {
                                    selectedValues.push(activeItem.dataset.value);
                                  }
                                });

                              let result = selectedValues.join(' / ');

                              this.closest('.main-variant-selector-select')
                                .querySelectorAll(`.default-select-box option[data-title="${result}"]`)
                                .forEach((option) => {
                                  this.closest('.variant-selector-main')
                                    .querySelector('add-item')
                                    .setAttribute('data-id', option.dataset.value);
                                  if (option && option.hasAttribute('disabled')) {
                                    this.closest('.variant-selector-main')
                                      .querySelector('add-item')
                                      .classList.add('disabled');
                                  } else {
                                    this.closest('.variant-selector-main')
                                      .querySelector('add-item')
                                      .classList.remove('disabled');
                                  }
                                });
                            });
                          });
                        }

                        let buttonOption = document.querySelectorAll('.main-variant-selector-select .button-option');
                        if (buttonOption) {
                          buttonOption.forEach(function (item) {
                            item.addEventListener('click', function () {
                              this.closest('.option-card').classList.toggle('active');
                            });
                          });
                        }

                        const main = document.querySelectorAll('.product-section');
                        main.forEach((button) => {
                          button.onclick = function (event) {
                            let dropdowns = document.querySelectorAll('.main-variant-selector-select');
                            dropdowns.forEach((dropdown) => {
                              if (!dropdown.contains(event.target)) {
                                dropdown.querySelectorAll('.option-card').forEach((i) => {
                                  i.classList.remove('active');
                                });
                              }
                            });
                          };
                        });
                      }
                    </script>
                {%- endcase -%}
              {%- else -%}
                <div
                  data-blocks-holder
                  data-url="{{ product.url | within: collection }}"
                  data-template="{{ product.template_suffix }}"
                >
                  <div class="placeholder-content" style="min-height: 86px;"></div>
                </div>
              {%- endfor -%}
            </div>

            {%- unless product.empty? -%}
              <textarea class="hide" aria-hidden="true" aria-label="Product JSON" data-variant-json>
                {{ product.variants | json }}
              </textarea>
              {%- if product.options.size > 1 -%}
                <textarea class="hide" aria-hidden="true" aria-label="Variant JSON" data-current-variant-json>
                  {{ current_variant | json }}
                </textarea>
              {%- endif -%}
            {%- endunless -%}
          </div>
        </div>

        {%- unless image_position == 'left' -%}
          <div class="grid__item {{ product_image_width }} product-single__sticky">
            {%- render 'product-images',
              section_id: section_id,
              product: product,
              isModal: isModal,
              image_position: image_position,
              product_zoom_enable: product_zoom_enable,
              product_zoom_size: product_zoom_size,
              product_image_size: product_image_size,
              thumbnail_arrows: thumbnail_arrows,
              thumbnail_position: thumbnail_position,
              video_looping: video_looping,
              video_style: video_style,
              section: section
            -%}
          </div>
        {%- endunless -%}
      </div>
    </div>
  </div>
  {% if template.name == 'product' and template.suffix contains 'oddit' %}
    <div
      class="mobile-upsell-main-pdp main-upsell-product empty:tw-hidden tw-overflow-hidden tw-mt-[15px] tw-px-[16px] tw-pb-[20px]"
    ></div>
  {% endif %}
</div>
