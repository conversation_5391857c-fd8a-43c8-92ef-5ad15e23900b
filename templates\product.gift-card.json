/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-product",
      "blocks": {
        "a584c1ac-582e-4489-b565-91d79d4ca217": {
          "type": "price",
          "settings": {}
        },
        "description": {
          "type": "description",
          "settings": {
            "is_tab": false
          }
        },
        "separator": {
          "type": "separator",
          "settings": {}
        },
        "variant_picker": {
          "type": "variant_picker",
          "settings": {
            "variant_labels": true,
            "picker_type": "button",
            "product_dynamic_variants_enable": true,
            "color_swatches": false
          }
        },
        "buy_buttons": {
          "type": "buy_buttons",
          "settings": {
            "show_dynamic_checkout": false,
            "surface_pickup_enable": true
          }
        },
        "share": {
          "type": "share",
          "settings": {}
        },
        "b84b798a-de53-4b7c-b64e-e728311d3df3": {
          "type": "tab",
          "settings": {
            "title": "Shipping and delivery",
            "content": "<p>Gift cards are delivered by email and include instructions on how to redeem them. </p><p>After payment, the gift certificate code will be sent to your email address where you can redeem it at checkout.</p>",
            "page": ""
          }
        }
      },
      "block_order": [
        "a584c1ac-582e-4489-b565-91d79d4ca217",
        "description",
        "separator",
        "variant_picker",
        "buy_buttons",
        "share",
        "b84b798a-de53-4b7c-b64e-e728311d3df3"
      ],
      "settings": {
        "sku_enable": false,
        "image_position": "left",
        "image_size": "medium",
        "product_zoom_enable": true,
        "thumbnail_position": "beside",
        "thumbnail_height": "flexible",
        "thumbnail_arrows": false,
        "mobile_layout": "partial",
        "enable_video_looping": true,
        "product_video_style": "muted"
      }
    },
    "sub": {
      "type": "product-full-width",
      "disabled": true,
      "settings": {
        "max_width": true
      }
    },
    "recently-viewed": {
      "type": "recently-viewed",
      "disabled": true,
      "settings": {
        "recent_count": 5
      }
    },
    "alireviews-section-1672312873": {
      "type": "apps",
      "settings": {
        "full_width": false,
        "space_around": true
      }
    }
  },
  "order": [
    "main",
    "sub",
    "recently-viewed",
    "alireviews-section-1672312873"
  ]
}
