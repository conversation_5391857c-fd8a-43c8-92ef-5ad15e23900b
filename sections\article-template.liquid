{%- liquid
  assign number_of_comments = article.comments_count
  if comment.status == 'pending'
    assign number_of_comments = article.comments_count | plus: 1
  endif
-%}

{%- if section.settings.image_hero and article.image -%}
  <div class="collection-hero">
    {%- assign img_url = article.image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}

    <img class="collection-hero__image image-fit lazyload"
      src=""
      data-src="{{ img_url }}"
      data-aspectratio="{{ article.image.aspect_ratio }}"
      data-sizes="auto"
      alt="{{ article.image.alt | escape }}">
    <noscript>
      <img class="collection-hero__image image-fit"
        src="{{ article.image | img_url: '1400x' }}"
        alt="{{ article.image.alt | escape }}">
    </noscript>

    <div class="collection-hero__content">
      <div class="page-width">
        <header class="section-header section-header--hero">
          <div class="section-header__shadow">
            {%- render 'breadcrumbs' -%}
            {%- if section.settings.blog_show_date or article.tags.size > 0 or articles.comments_count > 0 -%}
              <div class="article__date">
                {%- if section.settings.blog_show_tags and article.tags.size > 0 -%}
                  {%- for tag in article.tags -%}
                    {% if tag contains "_" %}{%- assign tag_starts_with = tag | slice: 0 -%}{% if tag_starts_with == "_" %}{% if tag_count %}{%- assign tag_count = tag_count | minus: 1 | at_least: 0 -%}{% endif %}{% continue %}{% endif %}{% endif %}
                    <a href="{{ blog.url }}/tagged/{{ tag | handle }}">{{ tag }}</a> &middot;
                  {%- endfor -%}
                {%- endif -%}
                {%- if section.settings.blog_show_comments and article.comments_count > 0 -%}
                  <a href="#comments">
                    {{ 'blogs.comments.with_count' | t: count: number_of_comments }}
                  </a> &middot;
                {%- endif -%}
                {%- if section.settings.blog_show_date -%}
                  {{ article.published_at | time_tag: format: 'month_day_year' }}
                {%- endif -%}
              </div>
            {%- endif -%}

            <h1 class="section-header__title">
              {{ article.title }}
            </h1>

            {%- if section.settings.blog_show_author -%}
              <div class="article__author">by {{ article.author }}</div>
            {%- endif -%}
          </div>
        </header>
      </div>
    </div>
  </div>
{%- endif -%}

<div class="page-width page-width--narrow page-content">
  <article class="article">
    {%- unless section.settings.image_hero and article.image -%}
      {%- render 'breadcrumbs' -%}

      <header class="section-header">
        {%- if section.settings.blog_show_date or article.tags.size > 0 or articles.comments_count > 0 -%}
          <div class="article__date">
            {%- if section.settings.blog_show_tags and article.tags.size > 0 -%}
              {%- for tag in article.tags -%}
                {% if tag contains "_" %}{%- assign tag_starts_with = tag | slice: 0 -%}{% if tag_starts_with == "_" %}{% if tag_count %}{%- assign tag_count = tag_count | minus: 1 | at_least: 0 -%}{% endif %}{% continue %}{% endif %}{% endif %}
                <a href="{{ blog.url }}/tagged/{{ tag | handle }}">{{ tag }}</a> &middot;
              {%- endfor -%}
            {%- endif -%}
            {%- if section.settings.blog_show_comments and article.comments_count > 0 -%}
              <a href="#comments">
                {{ 'blogs.comments.with_count' | t: count: number_of_comments }}
              </a> &middot;
            {%- endif -%}
            {%- if section.settings.blog_show_date -%}
              {{ article.published_at | time_tag: format: 'month_day_year' }}
            {%- endif -%}
          </div>
        {%- endif -%}
        <h1 class="section-header__title">{{ article.title }}</h1>
        {%- if section.settings.blog_show_author -%}
          <div class="article__author">by {{ article.author }}</div>
        {%- endif -%}
      </header>
    {%- endunless -%}

    <div class="article__body rte">
      {{ article.content }}
    </div>

    {%- if section.settings.social_sharing_blog -%}
      {%- render 'social-sharing', share_title: article.title, share_permalink: article.url, share_image: article.image -%}
    {%- endif -%}

    {%- if blog.comments_enabled? -%}

      {%- if number_of_comments > 0 -%}
        <hr class="hr--large">
        <h3>{{ 'blogs.comments.with_count' | t: count: number_of_comments }}</h3>
        <hr class="hr--small hr--clear">
      {%- endif -%}

      {%- paginate article.comments by 5 -%}
        <div id="comments">
          {%- if comment and comment.errors == blank -%}
            <hr class="hr--small hr--clear">
            <p class="note note--success">
              {%- if blog.moderated? -%}
                {{ 'blogs.comments.success_moderated' | t }}
              {%- else -%}
                {{ 'blogs.comments.success' | t }}
              {%- endif -%}
            </p>
          {%- endif -%}

          {%- if number_of_comments > 0 -%}
            <ul class="no-bullets">
              {%- if comment.status == 'pending' -%}
                <li id="Comment-{{ comment.id }}" class="article__comment">
                  {%- render 'comment', comment: comment -%}
                </li>
              {%- endif -%}

              {%- for comment in article.comments -%}
                <li id="Comment-{{ comment.id }}" class="article__comment">
                  {%- render 'comment', comment: comment -%}
                </li>

                {%- unless forloop.last -%}
                  <li><hr class="hr--clear"></li>
                {%- endunless -%}
              {%- endfor -%}
            </ul>

            {%- if paginate.pages > 1 -%}
              {%- render 'pagination', paginate: paginate, hash: '#comments' -%}
            {%- endif -%}

          {%- endif -%}

          <hr class="hr--large">

          <header class="section-header{% unless settings.type_headers_align_text %} section-header--with-link{% endunless %}">
            <h3 class="section-header__title">{{ 'blogs.comments.title' | t }}</h3>
          </header>

          <div class="form-vertical">
            {%- form 'new_comment', article -%}

              {{ form.errors | default_errors }}

              <div class="grid grid--small">
                <div class="grid__item medium-up--one-half">
                  <label for="CommentAuthor">{{ 'blogs.comments.name' | t }}</label>
                  <input class="input-full{% if form.errors contains 'author' %} error{% endif %}" type="text" name="comment[author]" id="CommentAuthor" value="{{ form.author }}" autocapitalize="words">
                </div>
                <div class="grid__item medium-up--one-half">
                  <label for="CommentEmail">{{ 'blogs.comments.email' | t }}</label>
                  <input class="input-full{% if form.errors contains 'email' %} error{% endif %}" type="email" name="comment[email]" id="CommentEmail" value="{{ form.email }}" autocorrect="off" autocapitalize="off">
                </div>
              </div>

              <label for="CommentBody">{{ 'blogs.comments.message' | t }}</label>
              <textarea class="input-full{% if form.errors contains 'body' %} error{% endif %}" name="comment[body]" id="CommentBody">{{ form.body }}</textarea>

              {%- if blog.moderated? -%}
                <p><small>{{ 'blogs.comments.moderated' | t }}</small></p>
              {%- endif -%}

              <button type="submit" class="btn btn--secondary">
                {{ 'blogs.comments.post' | t }}
              </button>

              {% comment %}
                Remove the following three lines of code to remove the note
                about being protected by Google's reCAPTCHA service.
                By removing it, the small reCAPTCHA widget will appear in the
                bottom right corner of the page.
              {% endcomment %}
              {{ 'shopify.online_store.spam_detection.disclaimer_html' | t }}

            {%- endform -%}
          </div>

        </div>
      {%- endpaginate -%}
    {%- endif -%}

    <hr class="hr--large">

  </article>

  <div class="text-center">
    <a href="{{ blog.url }}" class="btn return-link"><svg aria-hidden="true" focusable="false" role="presentation" class="icon icon--wide icon-arrow-left" viewBox="0 0 50 15"><path d="M50 5.38v4.25H15V15L0 7.5 15 0v5.38z"/></svg> {{ 'blogs.article.back_to_blog' | t: title: blog.title }}</a>
  </div>
</div>

<script type="application/ld+json">
{
  "@context": "http://schema.org",
  "@type": "Article",
  "articleBody": {{ article.content | strip_html | json }},
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": {{ shop.url | append: article.url | json }}
  },
  "headline": {{ article.title | json }},
  {% if article.excerpt != blank %}
    "description": {{ article.excerpt | strip_html | json }},
  {% endif %}
  {% if article.image %}
    {% assign image_size = article.image.width | append: 'x' %}
    "image": [
      {{ article | img_url: image_size | prepend: "https:" | json }}
    ],
  {% endif %}
  "datePublished": {{ article.published_at | date: '%Y-%m-%dT%H:%M:%SZ' | json }},
  "dateModified": {{ article.updated_at | date: '%Y-%m-%dT%H:%M:%SZ' | json }},
  "dateCreated": {{ article.created_at | date: '%Y-%m-%dT%H:%M:%SZ' | json }},
  "author": {
    "@type": "Person",
    "name": {{ article.author | json }}
  },
  "publisher": {
    "@type": "Organization",
    {% if page_image %}
      {% assign image_size = page_image.width | append: 'x' %}
      "logo": {
        "@type": "ImageObject",
        "height": {{ page_image.height | json }},
        "url": {{ page_image | img_url: image_size | prepend: "https:" | json }},
        "width": {{ page_image.width | json }}
      },
    {% endif %}
    "name": {{ shop.name | json }}
  }
}
</script>

{% schema %}
{
  "name": "t:sections.article-template.name",
  "settings": [
    {
      "type": "checkbox",
      "id": "image_hero",
      "label": "t:sections.article-template.settings.image_hero.label",
      "info": "t:sections.article-template.settings.image_hero.info",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "blog_show_tags",
      "label": "t:sections.article-template.settings.blog_show_tags.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "blog_show_date",
      "label": "t:sections.article-template.settings.blog_show_date.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "blog_show_comments",
      "label": "t:sections.article-template.settings.blog_show_comments.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "blog_show_author",
      "label": "t:sections.article-template.settings.blog_show_author.label"
    },
    {
      "type": "checkbox",
      "id": "social_sharing_blog",
      "label": "t:sections.article-template.settings.social_sharing_blog.label",
      "default": true
    }
  ]
}
{% endschema %}
