{% comment %}ly_liquid_scripts_begin{% endcomment %}{% capture ly_liquid_content %}{% comment %}ly_liquid_scripts_end{% endcomment %}<!doctype html>
<html class="no-js" lang="{{ request.locale.iso_code }}" dir="{% comment %}ly_dir_replace_for_[{{ settings.text_direction }}]_begin{% endcomment %}{%- assign language_code = '_' | append: request.locale.iso_code -%}{%- if '_ar_ha_he_fa_ks_ku_ps_ur_yi' contains language_code -%}{%- assign dir = 'rtl' -%}{%- else -%}{%- capture dir -%}{{ settings.text_direction }}{%- endcapture -%}{%- endif -%}{{- dir -}}{% comment %}ly_dir_replace_end{% endcomment %}">
<head>
    <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta name="theme-color" content="{{ settings.color_button }}">
   {%- if page_description -%}
  <meta name="description" content="{{ page_description | escape }}">
  {%- endif -%}
 
  {%- if settings.favicon != blank -%}
    <link rel="shortcut icon" href="{{ settings.favicon | img_url: '32x32' }}" type="image/png" />
  {%- endif -%} 
  {%- render 'social-meta-tags' -%}
  <link rel="preload" href="https://gardpro.com/cdn/shop/t/5/assets/country-flags.css" as="style">
  <link rel="preload" href="https://cdn.shopify.com/s/files/1/0603/7530/2276/t/1/assets/optimizers.js" as="script">
  <link rel="preload" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js" as="script">
{% render 'replo-head' %}
{% render 'visually_io_sdk' %}

  <meta name="google-site-verification" content="N8X_i2albUS_u6hQFXI5Wxap3OTrlAEUS9goueXZhKY" />
<meta name="facebook-domain-verification" content="2xww5zxj3m4cle6bzw3gmzgde1cvoz" />

  {% comment %} WebSite info {% endcomment %}
  <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": {{ shop.name | json }},
      "url": "{{ shop.secure_url }}"
    }
  </script>


  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" as="style">
<link rel="preload" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css" as='style'>
<link rel="preload" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css" as="style">
  <link rel="preload" href="https://code.jquery.com/jquery-3.4.1.js" as="script" integrity="sha256-WpOohJOqMqqyKL9FccASB9O0KwACQJpFTUBLTYOVvVU=" crossorigin="anonymous">

<link rel='preconnect dns-prefetch' href='https://api.config-security.com/' crossorigin />


  {% render 'pagefly-head' %}

  <link rel="canonical" href="{{ canonical_url }}">
  <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>
  <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
  <link rel="dns-prefetch" href="https://productreviews.shopifycdn.com">
  <link rel="dns-prefetch" href="https://ajax.googleapis.com">
  <link rel="dns-prefetch" href="https://maps.googleapis.com">
  <link rel="dns-prefetch" href="https://maps.gstatic.com">

  {% if template.name == 'product' and template.suffix contains 'oddit' %}
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap" rel="stylesheet">
    {{ '_custom.css' | asset_url | stylesheet_tag: preload: true }}
    
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css">
    <link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css">
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
  {% endif %}

  {%- render 'seo-title' -%}

 
  {%- render 'font-face' -%}
  {{ 'theme.css' | asset_url | stylesheet_tag: preload: true }}
  {{ 'custom.css' | asset_url | stylesheet_tag }}
  {{ 'my-custom.css' | asset_url | stylesheet_tag }}
  {%- render 'css-variables' -%}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
  <script>
    document.documentElement.className = document.documentElement.className.replace('no-js', 'js');

    window.theme = window.theme || {};
    theme.routes = {
      home: "{{ routes.root_url }}",
      cart: "{{ routes.cart_url | append: '.js' }}",
      cartPage: "{{ routes.cart_url }}",
      cartAdd: "{{ routes.cart_add_url | append: '.js' }}",
      cartChange: "{{ routes.cart_change_url | append: '.js' }}",
      search: "{{ routes.search_url }}"
    };
    theme.strings = {
      soldOut: {{ 'products.product.sold_out' | t | json }},
      unavailable: {{ 'products.product.unavailable' | t | json }},
      inStockLabel: {{ 'products.product.in_stock_label' | t | json }},
      oneStockLabel: {{ 'products.product.stock_label.one' | t: count: '[count]' | json }},
      otherStockLabel: {{ 'products.product.stock_label.other' | t: count: '[count]' | json }},
      willNotShipUntil: {{ 'products.product.will_not_ship_until' | t: date: '[date]' | json }},
      willBeInStockAfter: {{ 'products.product.will_be_in_stock_after' | t: date: '[date]' | json }},
      waitingForStock: {{ 'products.product.waiting_for_stock' | t | json }},
      savePrice: {{ 'products.general.save_html' | t: saved_amount: '[saved_amount]' | json }},
      cartEmpty: {{ 'cart.general.empty' | t | json }},
      cartTermsConfirmation: {{ 'cart.general.terms_confirm' | t | json }},
      searchCollections: {{ 'general.search.collections' | t | json }},
      searchPages: {{ 'general.search.pages' | t | json }},
      searchArticles: {{ 'general.search.articles' | t | json }},
      productFrom: {{ 'products.general.from_text_html' | t: price: '' | json }},
    };
    theme.settings = {
      cartType: {{ settings.cart_type | json }},
      isCustomerTemplate: {% if request.page_type contains 'customers/' %}true{% else %}false{% endif %},
      moneyFormat: {{ shop.money_format | json }},
      saveType: {{ settings.product_save_type | json }},
      productImageSize: {{ settings.product_grid_image_size | json }},
      productImageCover: {{ settings.product_grid_image_fill }},
      predictiveSearch: {{ settings.predictive_search_enabled }},
      predictiveSearchType: {{ settings.search_type | json }},
      predictiveSearchVendor: {{ settings.predictive_search_show_vendor | json }},
      predictiveSearchPrice: {{ settings.predictive_search_show_price | json }},
      quickView: {{ settings.quick_shop_enable }},
      themeName: 'Impulse',
      themeVersion: "7.1.0"
    };
  </script>

  {{ content_for_header }}

  <script src="{{ 'vendor-scripts-v11.js' | asset_url | split: '?' | first }}" defer="defer"></script>

  {%- if shop.enabled_currencies.size > 1 -%}
    <link rel="stylesheet" href="{{ 'country-flags.css' | asset_url | split: '?' | first }}">
  {%- endif -%}

  <script src="{{ 'theme.js' | asset_url }}" defer="defer"></script>

  {%- if request.page_type contains 'customers/' -%}
    <script src="{{ 'shopify_common.js' | shopify_asset_url }}" defer="defer"></script>
  {%- endif -%}
  {% include "globo" %}
  

	{% render 'vitals-loader' %}
  {% if request.design_mode %}
    <script src="https://api.archetypethemes.co/design-mode.js" defer="defer"></script>
  {% endif %}

{% comment %}ly_dir_styles_begin{% endcomment %}{%- assign language_code = '_' | append: request.locale.iso_code -%}{%- if '_ar_ha_he_fa_ks_ku_ps_ur_yi' contains language_code -%}{{- 'ly-rtl.css' | asset_url | stylesheet_tag -}}{%- endif -%}{% comment %}ly_dir_styles_end{% endcomment %}{% include 'alireviews_core' %} 
 
	{{ shop.metafields.loox["global_html_head"] }}

    <!-- Add the slick-theme.css if you want default styling -->
<link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>
<!-- Add the slick-theme.css if you want default styling -->
<link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css"/>

  <script
  src="https://code.jquery.com/jquery-3.4.1.js"
  integrity="sha256-WpOohJOqMqqyKL9FccASB9O0KwACQJpFTUBLTYOVvVU="
  crossorigin="anonymous">
</script>

<script type="text/javascript" src="https://www.bugherd.com/sidebarv2.js?apikey=**********************" async="true"></script>
  
</head>

<body class="{% if template.name == 'product' and template.suffix contains 'oddit' %}template-{{ template.suffix }} !tw-bg-[#F5F3EF]{% endif %} template-{{ template | replace: '.', ' ' | truncatewords: 1, '' | handle }}{% if request.path == '/challenge' %} template-challange{% endif %}" data-center-text="{{ settings.type_body_align_text }}" data-button_style="{{ settings.button_style }}" data-type_header_capitalize="{{ settings.type_header_capitalize }}" data-type_headers_align_text="{{ settings.type_headers_align_text }}" data-type_product_capitalize="{{ settings.type_product_capitalize }}" data-swatch_style="{{ settings.swatch_style }}" {% if settings.disable_animations %}data-disable-animations="true"{% endif %}>{% capture layout %}
    {%- if template.suffix == 'oddit' -%}
      {%- section 'oddit-header' -%}
    {% else %}          
      {%- section 'header' -%}
    {%- endif -%}
    {%- section 'newsletter-popup' -%}
    {%- section 'age-verification-popup' -%}

      <main class="main-content" id="MainContent">
        {{ content_for_layout }}
      </main>

      {%- section 'footer-promotions' -%}
      {%- section 'footer' -%}

    </div>
  </div>
    {% endcapture %}</body>
</html>
{%- endcapture -%}