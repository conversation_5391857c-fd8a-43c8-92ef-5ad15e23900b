<div class="site-nav">
  <div class="site-nav__icons">
    <span class="ly-custom-769"></span>
    {%- if shop.customer_accounts_enabled -%}
      <a class="site-nav__link site-nav__link--icon small--hide" href="{{ routes.account_url }}">
        {{ 'icon-account.svg' | inline_asset_content }}
        <span class="icon__fallback-text">
          {%- if customer -%}
            {{ 'layout.customer.account' | t }}
          {%- else -%}
            {{ 'layout.customer.log_in' | t }}
          {%- endif -%}
        </span>
      </a>
    {%- endif -%}
    {%- if settings.search_enable -%}
      <a
        href="{{ routes.search_url }}"
        class="site-nav__link site-nav__link--icon js-search-header{% if section.settings.main_menu_alignment == 'center' or section.settings.main_menu_alignment == 'center-split' %} medium-up--hide{% endif %}"
      >
        {{ 'icon-search.svg' | inline_asset_content }}
        <span class="icon__fallback-text">{{ 'general.search.title' | t }}</span>
      </a>
    {%- endif -%}

    {%- if section.settings.main_menu_alignment == 'left'
      or section.settings.main_menu_alignment == 'left-center'
      or section.settings.main_menu_alignment == 'left-drawer'
    -%}
      <button
        type="button"
        class="desktop_nav site-nav__link site-nav__link--icon js-drawer-open-nav{% if section.settings.main_menu_alignment == 'left' or section.settings.main_menu_alignment == 'left-center' %} medium-up--hide{% endif %}"
        aria-controls="NavDrawer"
      >
        {{ 'icon-menu.svg' | inline_asset_content }}
        <span class="icon__fallback-text">{{ 'general.drawers.navigation' | t }}</span>
      </button>
    {%- endif -%}
    {%- if section.settings.main_menu_alignment_mob == 'left'
      or section.settings.main_menu_alignment_mob == 'left-center'
      or section.settings.main_menu_alignment_mob == 'left-drawer'
    -%}
      <button
        type="button"
        class="mobile_nav site-nav__link site-nav__link--icon js-drawer-open-nav{% if section.settings.main_menu_alignment_mob == 'left' or section.settings.main_menu_alignment_mob == 'left-center' %} medium-up--hide{% endif %}"
        aria-controls="NavDrawer"
      >
        {{ 'icon-menu.svg' | inline_asset_content }}
        <span class="icon__fallback-text">{{ 'general.drawers.navigation' | t }}</span>
      </button>
    {%- endif -%}
    <a
      href="{{ routes.cart_url }}"
      class="site-nav__link site-nav__link--icon js-drawer-open-cart"
      aria-controls="CartDrawer"
      data-icon="{{ settings.cart_icon }}"
    >
      <span class="cart-link">
        {%- if settings.cart_icon == 'cart' -%}
          <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-cart" viewBox="0 0 64 64">
            <path fill="none" d="M14 17.44h46.79l-7.94 25.61H20.96l-9.65-35.1H3"/><circle cx="27" cy="53" r="2"/><circle cx="47" cy="53" r="2"/>
          </svg>
        {%- elsif settings.cart_icon == 'bag-minimal' -%}
          {{ 'icon-bag.svg' | inline_asset_content }}
        {%- else -%}
          <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-bag" viewBox="0 0 64 64">
            <g fill="none" stroke="#000" stroke-width="2"><path d="M25 26c0-15.79 3.57-20 8-20s8 4.21 8 20"/><path d="M14.74 18h36.51l3.59 36.73h-43.7z"/></g>
          </svg>
        {%- endif -%}
        <span class="icon__fallback-text">{{ 'layout.cart.title' | t }}</span>
        <span class="cart-link__bubble{% if cart.item_count > 0 %} cart-link__bubble--visible{% endif %}"></span>
      </span>
    </a>
  </div>
</div>
