{%- liquid
  assign fixed_aspect_ratio = false
  unless section.settings.image_size == 'natural'
    assign fixed_aspect_ratio = true
  endunless

  assign show_section = true
  if template == 'index' and section.settings.hide_homepage
    assign show_section = false
  endif -%
-%}

{%- if show_section -%}
  {%- if section.blocks.size > 0 -%}
    {%- liquid
      assign grid_item_width = 'medium-up--one-half'
      if section.blocks.size == 3
        assign grid_item_width = 'medium-up--one-third'
      endif
    -%}

    <div class="index-section">
      <div class="section--divider">
        <div class="page-width footer-promotions">
          <div class="grid grid--flush-bottom">
            {%- for block in section.blocks -%}
              <div class="grid__item {{ grid_item_width }}" {{ block.shopify_attributes }} data-aos="row-of-3">
                {%- if block.settings.enable_image -%}
                  <a href="{{ block.settings.button_link }}" class="article__grid-image" aria-label="{{ block.settings.title }}">
                    {%- if block.settings.image != blank -%}
                      {%- if fixed_aspect_ratio -%}
                        <div class="image-wrap">
                          <div
                            class="grid__image-ratio grid__image-ratio--{{ section.settings.image_size }}">
                            {% assign img_url = block.settings.image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' %}
                            <img class="lazyload grid__image-cover"
                                data-src="{{ img_url }}"
                                data-widths="[360, 540, 720, 900, 1080]"
                                data-aspectratio="{{ block.settings.image.aspect_ratio }}"
                                data-sizes="auto"
                                alt="{{ block.settings.image.alt | escape }}">
                          </div>
                          <noscript>
                            <img class="lazyloaded" src="{{ block.settings.image | img_url: '400x' }}" alt="{{ block.settings.title | escape }}">
                          </noscript>
                        </div>
                      {%- else -%}
                        <div class="image-wrap" style="height: 0; padding-bottom: {{ 100 | divided_by: block.settings.image.aspect_ratio }}%;">
                          {%- assign img_url = block.settings.image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}
                          <img class="lazyload"
                              data-src="{{ img_url }}"
                              data-widths="[180, 360, 540, 720, 900, 1080]"
                              data-aspectratio="{{ block.settings.image.aspect_ratio }}"
                              data-sizes="auto"
                              alt="{{ block.settings.image.alt | escape }}">
                          <noscript>
                            <img class="lazyloaded" src="{{ block.settings.image | img_url: '400x' }}" alt="{{ block.settings.title | escape }}">
                          </noscript>
                        </div>
                      {%- endif -%}
                    {%- else -%}
                      {%- if fixed_aspect_ratio -%}
                        <div class="image-wrap">
                          <div
                            class="grid__image-ratio grid__image-ratio--{{ section.settings.image_size }}">
                            {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg' }}
                          </div>
                        </div>
                      {%- else -%}
                        {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg' }}
                      {%- endif -%}
                    {%- endif -%}
                  </a>
                {%- endif -%}
                {%- if block.settings.title != blank -%}
                  <div class="h3">{{ block.settings.title }}</div>
                {%- endif -%}
                {%- if block.settings.text != blank -%}
                  <div class="rte-setting text-spacing">{{ block.settings.text }}</div>
                {%- endif -%}
                {%- if block.settings.button_label != blank -%}
                  <a href="{{ block.settings.button_link }}" class="btn btn--secondary btn--small">
                    {{ block.settings.button_label }}
                  </a>
                {%- endif -%}
              </div>
            {%- endfor -%}
          </div>
        </div>
      </div>
    </div>
  {%- endif -%}
{%- endif -%}

{% schema %}
{
  "name": "t:sections.footer-promotions.name",
  "max_blocks": 3,
  "class": "index-section--footer",
  "settings": [
    {
      "type": "checkbox",
      "id": "hide_homepage",
      "label": "t:sections.footer-promotions.settings.hide_homepage.label"
    },
    {
      "type": "select",
      "id": "image_size",
      "label": "t:sections.footer-promotions.settings.image_size.label",
      "default": "wide",
      "options": [
        {
          "value": "natural",
          "label": "t:sections.footer-promotions.settings.image_size.options.natural.label"
        },
        {
          "value": "square",
          "label": "t:sections.footer-promotions.settings.image_size.options.square.label"
        },
        {
          "value": "landscape",
          "label": "t:sections.footer-promotions.settings.image_size.options.landscape.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.footer-promotions.settings.image_size.options.portrait.label"
        },
        {
          "value": "wide",
          "label": "t:sections.footer-promotions.settings.image_size.options.wide.label"
        }
      ]
    }
  ],
  "blocks": [
    {
      "type": "promotion",
      "name": "t:sections.footer-promotions.blocks.column.name",
      "settings": [
        {
          "type": "checkbox",
          "id": "enable_image",
          "label": "t:sections.footer-promotions.blocks.column.settings.enable_image.label",
          "default": true
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.footer-promotions.blocks.column.settings.image.label"
        },
        {
          "type": "text",
          "id": "title",
          "label": "t:sections.footer-promotions.blocks.column.settings.title.label",
          "default": "Site-wide promotion"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "t:sections.footer-promotions.blocks.column.settings.text.label",
          "default": "<p>Use this section to promote content throughout every page of your site. Add images for further impact.</p>"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "t:sections.footer-promotions.blocks.column.settings.button_label.label",
          "default": "Optional button"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.footer-promotions.blocks.column.settings.button_link.label"
        }
      ]
    }
  ],
  "default": {
    "blocks": [
      {
        "type": "promotion"
      },
      {
        "type": "promotion"
      },
      {
        "type": "promotion"
      }
    ]
  }
}
{% endschema %}
