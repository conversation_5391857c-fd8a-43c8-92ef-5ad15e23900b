{%- if section.settings.space_around -%}
  <div class="index-section">
{%- endif -%}

{%- unless section.settings.full_width -%}
  <div class="page-width">
{%- endunless -%}

{%- for block in section.blocks -%}
  {% render block %}
{%- endfor -%}

{%- unless section.settings.full_width -%}
  </div>
{%- endunless -%}

{%- if section.settings.space_around -%}
  </div>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.apps.name",
  "tag": "section",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.apps.settings.full_width.label",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "space_around",
      "label": "t:sections.apps.settings.space_around.label",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "@app"
    }
  ],
  "presets": [
    {
      "name": "t:sections.apps.presets.apps.name"
    }
  ]
}
{% endschema %}
