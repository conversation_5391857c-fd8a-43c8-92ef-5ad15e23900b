

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-557158016361693971.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-557158016361693971.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-557158016361693971.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-557158016361693971.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-557158016361693971.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-557158016361693971.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-557158016361693971.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-557158016361693971.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-557158016361693971.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-557158016361693971.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-557158016361693971.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-557158016361693971.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-557158016361693971.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-557158016361693971.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-557158016361693971.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-557158016361693971.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-557158016361693971.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-557158016361693971.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-557158016361693971.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-557158016361693971.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-557158016361693971.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-557158016361693971.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-557158016361693971.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-557158016361693971.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-557158016361693971.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-557158016361693971.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-557158016361693971.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-557158016361693971.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-557158016361693971.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-557158016361693971.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-557158016361693971.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-557158016361693971.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-557158016361693971.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-557158016361693971.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-557158016361693971.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-557158016361693971.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-557158016361693971.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-557158016361693971.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-557158016361693971.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-557158016361693971.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-557158016361693971.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-557158016361693971.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-557158016361693971.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-557158016361693971.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-557158016361693971.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-557158016361693971.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-557158016361693971.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-557158016361693971.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-557158016361693971.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-557158016361693971.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-557158016361693971.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-557158016361693971.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-557158016361693971.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-557158016361693971.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-557158016361693971.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-557158016361693971.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-557158016361693971.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-557158016361693971.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-557158016361693971.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-557158016361693971.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-557158016361693971.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-557158016361693971.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-557158016361693971.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-557158016361693971.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-557158016361693971.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-557158016361693971.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-557158016361693971.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-557158016361693971.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-557158016361693971.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-557158016361693971.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-557158016361693971.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-557158016361693971.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-557158016361693971 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-557158016361693971 .gp-relative{position:relative}.gps-557158016361693971 .gp-z-1{z-index:1}.gps-557158016361693971 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-557158016361693971 .gp-mb-0{margin-bottom:0}.gps-557158016361693971 .gp-flex{display:flex}.gps-557158016361693971 .gp-inline-flex{display:inline-flex}.gps-557158016361693971 .gp-grid{display:grid}.gps-557158016361693971 .\!gp-hidden{display:none!important}.gps-557158016361693971 .gp-hidden{display:none}.gps-557158016361693971 .gp-h-full{height:100%}.gps-557158016361693971 .gp-max-w-full{max-width:100%}.gps-557158016361693971 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-557158016361693971 .gp-flex-col{flex-direction:column}.gps-557158016361693971 .gp-items-center{align-items:center}.gps-557158016361693971 .gp-justify-center{justify-content:center}.gps-557158016361693971 .gp-gap-y-0{row-gap:0}.gps-557158016361693971 .gp-overflow-hidden{overflow:hidden}.gps-557158016361693971 .gp-break-words{overflow-wrap:break-word}.gps-557158016361693971 .gp-rounded-none{border-radius:0}.gps-557158016361693971 .gp-text-center{text-align:center}.gps-557158016361693971 .gp-leading-\[0\]{line-height:0}.gps-557158016361693971 .gp-text-g-line-3{color:var(--g-c-line-3)}.gps-557158016361693971 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-557158016361693971 .gp-no-underline{text-decoration-line:none}.gps-557158016361693971 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-557158016361693971 .gp-duration-200{transition-duration:.2s}.gps-557158016361693971 .gp-duration-300{transition-duration:.3s}.gps-557158016361693971 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-557158016361693971 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-557158016361693971 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-557158016361693971 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-557158016361693971 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-557158016361693971 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-557158016361693971 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-557158016361693971 .tablet\:\!gp-hidden{display:none!important}.gps-557158016361693971 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-557158016361693971 .mobile\:\!gp-hidden{display:none!important}.gps-557158016361693971 .mobile\:gp-hidden{display:none}}.gps-557158016361693971 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-557158016361693971 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}@media (max-width:1024px){.gps-557158016361693971 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-557158016361693971 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-557158016361693971 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-557158016361693971 .\[\&_p\]\:gp-inline p{display:inline}.gps-557158016361693971 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-557158016361693971 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-557158016361693971 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gJoYsXtvcy" data-id="gJoYsXtvcy"
        style="--blockPadding:base;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-2xl);--pb:var(--g-s-2xl);--pt-mobile:24px;--pl-mobile:24px;--pb-mobile:12px;--pr-mobile:24px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gJoYsXtvcy gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gsnBxpgHx2 gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gri1IoSHFX" data-id="gri1IoSHFX"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-l);--cg:8px;--pc:start;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gri1IoSHFX gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g4BPCVNYUv gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gYE2roOLrm">
    <div
      parentTag="Col"
        class="gYE2roOLrm "
        style="--ta:center;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:16px;--mb-mobile:23px"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:40px;--size-tablet:30px;--size-mobile:26px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggYE2roOLrm_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gzC52daNLQ">
    <div
      parentTag="Col"
        class="gzC52daNLQ "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:64px;--mb-mobile:32px"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:80%;--w-tablet:80%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#424242;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:16px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggzC52daNLQ_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
       
      
    <div
      parentTag="Col" id="gh7rRcfS_e" data-id="gh7rRcfS_e"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:34px;--cg:25px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gh7rRcfS_e gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gFmvUOVeKJ gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:28px;--mb-mobile:17px;--ta:center"
      class="gp-leading-[0] g0751O7Md_"
    >
      <div 
      data-id="g0751O7Md_"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:80px;--w-tablet:80px;--w-mobile:60px;--h:80px;--h-tablet:80px;--h-mobile:60px;--minw:80px;--minw-tablet:80px;--minw-mobile:60px;--height-desktop:80px;--height-tablet:80px;--height-mobile:60px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817629910991208">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M72,142H32a6,6,0,0,1,0-12H68.79L83,108.67a6,6,0,0,1,10,0l27,40.51,11-16.51a6,6,0,0,1,5-2.67h24a6,6,0,0,1,0,12H139.21L125,163.33a6,6,0,0,1-10,0L88,122.82,77,139.33A6,6,0,0,1,72,142ZM178,42c-21,0-39.26,9.47-50,25.34C117.26,51.47,99,42,78,42a60.07,60.07,0,0,0-60,60q0,1.09,0,2.19a6,6,0,0,0,12-.38c0-.6,0-1.21,0-1.81A48.05,48.05,0,0,1,78,54c20.28,0,37.31,10.83,44.45,28.27a6,6,0,0,0,11.1,0C140.69,64.83,157.72,54,178,54a48.05,48.05,0,0,1,48,48c0,55.73-81.61,105.65-98,115.11-9.84-5.66-43.09-25.82-68.16-53.16a6,6,0,1,0-8.84,8.1c30.94,33.77,72.41,56.29,74.16,57.23a6,6,0,0,0,5.68,0,334.68,334.68,0,0,0,53.06-37C219.8,161.59,238,131.2,238,102A60.07,60.07,0,0,0,178,42Z" /></svg></span>
    </div>
      </div>
    </div>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gdSCPKYgx2">
    <div
      parentTag="Col"
        class="gdSCPKYgx2 "
        style="--ta:center;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:9px"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:90%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-heading, heading);--weight:600;--size:22px;--size-tablet:24px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:var(--g-c-text-2, text-2);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggdSCPKYgx2_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gMATmAsMsX">
    <div
      parentTag="Col"
        class="gMATmAsMsX "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb-mobile:17px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggMATmAsMsX_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gSGvwc2McF gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:28px;--mb-mobile:17px;--ta:center"
      class="gp-leading-[0] gaMqlNGxNY"
    >
      <div 
      data-id="gaMqlNGxNY"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:80px;--w-tablet:80px;--w-mobile:60px;--h:80px;--h-tablet:80px;--h-mobile:60px;--minw:80px;--minw-tablet:80px;--minw-mobile:60px;--height-desktop:80px;--height-tablet:80px;--height-mobile:60px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817629908500840">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M249.92,113.4,182.47,45.49a94.12,94.12,0,0,0-133,.06L35.68,59.78a6,6,0,0,0,8.63,8.35L58,54A82,82,0,0,1,78.55,39l49,49L30.09,185.4a14,14,0,0,0,0,19.81L50.78,225.9a14,14,0,0,0,19.8,0L168,128.46l33.42,33.42a14,14,0,0,0,19.8,0l28.68-28.69A14,14,0,0,0,249.92,113.4ZM62.1,217.41a2,2,0,0,1-2.83,0L38.58,196.72a2,2,0,0,1,0-2.83L108,124.46,131.51,148ZM163.76,115.73,140,139.49,116.48,116l23.76-23.76a6,6,0,0,0,0-8.49L90.54,34A82.07,82.07,0,0,1,174,54l45.62,45.93L188,131.49l-15.76-15.76A6,6,0,0,0,163.76,115.73Zm77.66,9-28.69,28.7a2,2,0,0,1-2.83,0L196.49,140l31.56-31.57,13.37,13.46A2,2,0,0,1,241.42,124.7Z" /></svg></span>
    </div>
      </div>
    </div>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g7JxeO-ACf">
    <div
      parentTag="Col"
        class="g7JxeO-ACf "
        style="--ta:center;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:9px"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:80%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-heading, heading);--weight:600;--size:22px;--size-tablet:24px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:var(--g-c-text-2, text-2);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg7JxeO-ACf_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gLCQhCe39j">
    <div
      parentTag="Col"
        class="gLCQhCe39j "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb-mobile:17px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggLCQhCe39j_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gxYKbH0sRn gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:28px;--ta:center"
      class="gp-leading-[0] gyoo5PpWZd"
    >
      <div 
      data-id="gyoo5PpWZd"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:80px;--w-tablet:80px;--w-mobile:60px;--h:80px;--h-tablet:80px;--h-mobile:60px;--minw:80px;--minw-tablet:80px;--minw-mobile:60px;--height-desktop:80px;--height-tablet:80px;--height-mobile:60px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817637004869992">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M238,96a6,6,0,0,1-6,6H214v18a6,6,0,0,1-12,0V102H184a6,6,0,0,1,0-12h18V72a6,6,0,0,1,12,0V90h18A6,6,0,0,1,238,96ZM144,54h10V64a6,6,0,0,0,12,0V54h10a6,6,0,0,0,0-12H166V32a6,6,0,0,0-12,0V42H144a6,6,0,0,0,0,12Zm71.25,100.28a6,6,0,0,1,1.07,6A94,94,0,1,1,95.76,39.68a6,6,0,0,1,7.94,6.79A90.11,90.11,0,0,0,192,154a90.9,90.9,0,0,0,17.53-1.7A6,6,0,0,1,215.25,154.28Zm-14.37,11.34q-4.42.38-8.88.38A102.12,102.12,0,0,1,90,64q0-4.45.38-8.88a82,82,0,1,0,110.5,110.5Z" /></svg></span>
    </div>
      </div>
    </div>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g4v_ZnDVUs">
    <div
      parentTag="Col"
        class="g4v_ZnDVUs "
        style="--ta:center;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:9px"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:90%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-heading, heading);--weight:600;--size:22px;--size-tablet:24px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:var(--g-c-text-2, text-2);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg4v_ZnDVUs_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g4PX6ghejP">
    <div
      parentTag="Col"
        class="g4PX6ghejP "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb-mobile:17px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg4PX6ghejP_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gKxKuOfumu gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:28px;--mb-mobile:17px;--ta:center"
      class="gp-leading-[0] g1fJaTj87g"
    >
      <div 
      data-id="g1fJaTj87g"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:80px;--w-tablet:80px;--w-mobile:60px;--h:80px;--h-tablet:80px;--h-mobile:60px;--minw:80px;--minw-tablet:80px;--minw-mobile:60px;--height-desktop:80px;--height-tablet:80px;--height-mobile:60px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817612743901544">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M195.6,171.2,138,128l57.6-43.2a6,6,0,0,0,0-9.6l-64-48A6,6,0,0,0,122,32v84L67.6,75.2a6,6,0,0,0-7.2,9.6L118,128,60.4,171.2a6,6,0,0,0,7.2,9.6L122,140v84a6,6,0,0,0,9.6,4.8l64-48a6,6,0,0,0,0-9.6ZM134,44l48,36-48,36Zm0,168V140l48,36ZM60,138a10,10,0,1,1,10-10A10,10,0,0,1,60,138Zm154-10a10,10,0,1,1-10-10A10,10,0,0,1,214,128Z" /></svg></span>
    </div>
      </div>
    </div>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g_tYqPq4Us">
    <div
      parentTag="Col"
        class="g_tYqPq4Us "
        style="--ta:center;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:9px"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:80%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-heading, heading);--weight:600;--size:22px;--size-tablet:24px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:var(--g-c-text-2, text-2);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg_tYqPq4Us_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g8NrpJuRTs">
    <div
      parentTag="Col"
        class="g8NrpJuRTs "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg8NrpJuRTs_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:24px;--ta:center"
    
  >
    <style>
    .gFvEwDmvwe.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      
    }

    .gFvEwDmvwe:hover::before {
      
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .gFvEwDmvwe:hover .gp-button-icon {
      color: undefined;
    }

     .gFvEwDmvwe .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gFvEwDmvwe:hover .gp-button-price {
      color: undefined;
    }

    .gFvEwDmvwe .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gFvEwDmvwe .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gFvEwDmvwe:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#g5UWRTEtPs" target="_self" data-id="gFvEwDmvwe" aria-label="<p>SHOP THE FEMALE COLLECTION</p>"
      
      data-state="idle"
      class="gFvEwDmvwe gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center"
      style="--hvr-bg:#1180FF;--bg:#096de3;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--w:400px;--w-tablet:Auto;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:32px;--pr:32px;--pt:12px;--pb:12px;--pl-tablet:32px;--pr-tablet:32px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:32px;--pr-mobile:32px;--pt-mobile:12px;--pb-mobile:12px;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--fs:normal;--weight:600;--ls:1px;--lh-tablet:150%"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggFvEwDmvwe_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 6",
    "tag": "section",
    "class": "gps-557158016361693971 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555482666321838914&sectionId=557158016361693971)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggYE2roOLrm_text","label":"ggYE2roOLrm_text","default":"YOUR EVERYDAY HEALTH COMPANION"},{"type":"html","id":"ggzC52daNLQ_text","label":"ggzC52daNLQ_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:18px;\">Stay on top of your health with real-time insights. Track your vitals, manage stress, and measure your progress—all from your wrist. Our fitness tracker watches for women are designed to enhance your daily wellness journey.</span></p>"},{"type":"html","id":"ggdSCPKYgx2_text","label":"ggdSCPKYgx2_text","default":"Comprehensive Health Monitoring"},{"type":"html","id":"ggMATmAsMsX_text","label":"ggMATmAsMsX_text","default":"<p>Monitor your heart rate, blood pressure, and SpO₂ levels for a holistic view of your wellness with this women's smartwatch.</p>"},{"type":"html","id":"gg7JxeO-ACf_text","label":"gg7JxeO-ACf_text","default":"Designed For Durability"},{"type":"html","id":"ggLCQhCe39j_text","label":"ggLCQhCe39j_text","default":"<p>Waterproof, dustproof, and shockproof—this waterproof wristwatch is built to handle everything from tough workouts to daily wear.</p>"},{"type":"html","id":"gg4v_ZnDVUs_text","label":"gg4v_ZnDVUs_text","default":"Sleep &amp; Stress Management"},{"type":"html","id":"gg4PX6ghejP_text","label":"gg4PX6ghejP_text","default":"<p>Get insights into your sleep patterns, track stress levels, and recharge with your fitness watch's guided breathing exercises.</p>"},{"type":"html","id":"gg_tYqPq4Us_text","label":"gg_tYqPq4Us_text","default":"Effortless Connectivity"},{"type":"html","id":"gg8NrpJuRTs_text","label":"gg8NrpJuRTs_text","default":"<p>Check messages, control your music, and receive app notifications on the go—so you never miss a beat with our top rated fitness and health tracker.</p>"},{"type":"html","id":"ggFvEwDmvwe_label","label":"ggFvEwDmvwe_label","default":"<p>SHOP THE FEMALE COLLECTION</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
