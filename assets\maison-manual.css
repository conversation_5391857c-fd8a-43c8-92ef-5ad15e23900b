body {
    font-family: sans-serif;
    margin: 0;
    background-color: #f4f4f4;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.maison_heyfeels_timeline_container {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    width: 80%;
    max-width: 600px;
}

.maison_heyfeels_timeline_title {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
    opacity: 0;
    animation: maison_heyfeels_fade_in 0.5s ease forwards;
}

.maison_heyfeels_timeline_event {
    display: flex;
    align-items: flex-start; /* Align items to the start for better icon placement */
    margin-bottom: 25px;
    position: relative; /* For pseudo-element line */
}

/* Animation for timeline events */
.maison_heyfeels_animate {
    opacity: 0;
    transform: translateY(20px);
    animation: maison_heyfeels_slide_in 0.5s ease forwards;
    animation-delay: calc(var(--animation-order) * 0.3s);
}

@keyframes maison_heyfeels_fade_in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes maison_heyfeels_slide_in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Basic line connecting events - you can enhance this */
.maison_heyfeels_timeline_event:not(:last-child)::before {
    content: '';
    position: absolute;
    left: 18px; /* Adjust based on icon size and padding */
    top: 40px; /* Start below the icon */
    bottom: -25px; /* Extend to the next event's margin-bottom */
    width: 2px;
    background-color: #ddd;
    transform: scaleY(0);
    transform-origin: top;
    animation: maison_heyfeels_line_draw 0.5s ease forwards;
    animation-delay: calc((var(--animation-order) * 0.3s) + 0.3s);
}

@keyframes maison_heyfeels_line_draw {
    from {
        transform: scaleY(0);
    }
    to {
        transform: scaleY(1);
    }
}

.maison_heyfeels_timeline_icon {
    margin-right: 20px;
    flex-shrink: 0; /* Prevent icon from shrinking */
    width: 40px; /* Fixed width for icon container */
    height: 40px; /* Fixed height for icon container */
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #eee; /* Placeholder background */
    border-radius: 50%; /* Circular icons */
    color: #555;
    font-weight: bold;
    z-index: 1; /* Ensure icon is above the line */
    transform: scale(0);
    animation: maison_heyfeels_pop_in 0.3s ease forwards;
    animation-delay: calc((var(--animation-order) * 0.3s) + 0.15s);
}

@keyframes maison_heyfeels_pop_in {
    from {
        transform: scale(0);
    }
    to {
        transform: scale(1);
    }
}

.maison_heyfeels_icon_placeholder {
    font-size: 12px; /* Adjust as needed */
}

.maison_heyfeels_timeline_content {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 6px;
    flex-grow: 1; /* Allow content to take remaining space */
}

.maison_heyfeels_timeline_content p {
    margin: 0;
    color: #555;
    line-height: 1.6;
} 