@charset "UTF-8";

/*!
  Impulse, by Archetype Themes
  http://archetypethemes.co
*/

:root{
  --colorBtnPrimary:{{ settings.color_button | default: "#000" }};
  --colorBtnPrimaryLight:{{ settings.color_button | default: "#000" | color_lighten: 10 }};
  --colorBtnPrimaryDim:{{ settings.color_button | default: "#000" | color_darken: 5 }};
  --colorBtnPrimaryText:{{ settings.color_button_text | default: "#fff" }};
  --colorCartDot:{{ settings.color_cart_dot | default: "#ff4f33" }};

  --colorLink:{{ settings.color_body_text | default: "#1c1d1d" }};

  --colorTextBody:{{ settings.color_body_text | default: "#1c1d1d" }};
  --colorPrice:{{ settings.color_price | default: "#1c1d1d" }};
  --colorTextSavings:{{ settings.color_savings_text | default: "#1c1d1d" }};
  --colorSaleTag:{{ settings.color_sale_tag | default: "#1c1d1d" }};
  --colorSaleTagText:{{ settings.color_sale_tag_text | default: "#ffffff" }};

  --colorBody:{{ settings.color_body_bg | default: "#fff" }};
  --colorBodyDim:{{ settings.color_body_bg | default: "#1c1d1d" | color_darken: 5 }};

  --colorFooter:{{ settings.color_footer | default: "#111" }};
  --colorFooterText:{{ settings.color_footer_text | default: "#fff" }};

  --colorBorder:{{ settings.color_borders | default: "#1c1d1d" }};

  --colorNav:{{ settings.color_header | default: "#fff" }};
  --colorNavText:{{ settings.color_header_text | default: "#000" }};
  --colorAnnouncement:{{ settings.color_announcement | default: "#1c1d1d" }};
  --colorAnnouncementText:{{ settings.color_announcement_text | default: "#fff" }};

  --colorHeroText:{{ settings.color_image_text | default: "#fff" }};

  --colorModalBg:{{ settings.color_modal_overlays | default: "#000" }};

  --colorImageOverlay:{{ settings.color_image_overlay | default: "#000" }};
  --colorImageOverlayOpacity:{{ settings.color_image_overlay_opacity | divided_by: 100.0 }};
  --colorImageOverlayTextShadow:{{ settings.color_image_overlay_text_shadow | divided_by: 100.0 }};

  --colorSmallImageBg:{{ settings.color_small_image_bg | default: "#eee" }};
  --colorLargeImageBg:{{ settings.color_large_image_bg | default: "#1c1d1d" }};
  --colorGridOverlay:{{ settings.collection_grid_tint | default: "#000" }};
  --colorGridOverlayOpacity:0.1;

  --colorDrawers:{{ settings.color_drawer_background | default: "#1c1d1d" }};
  --colorDrawersDim:{{ settings.color_drawer_background | default: "#1c1d1d" | color_darken: 5 }};
  --colorDrawerBorder:{{ settings.color_drawer_border | default: "#343535" }};
  --colorDrawerText:{{ settings.color_drawer_text | default: "#fff" }};
  --colorDrawerTextDark:{{ settings.color_drawer_text | default: "#fff" | color_darken: 15 }};
  --colorDrawerButton:{{ settings.color_drawer_button | default: "#a26b25" }};
  --colorDrawerButtonText:{{ settings.color_drawer_button_text | default: "#fff" }};

  --grid-gutter:17px;
  --drawer-gutter:20px;

  --sizeChartMargin:25px 0;
  --sizeChartIconMargin:5px;

  --newsletterReminderPadding:40px;
  --color-body-text:{{ settings.color_body_text | default: "#1c1d1d" }};
  --color-body:{{ settings.color_body_bg | default: "#fff" }};
  --color-bg:{{ settings.color_body_bg | default: "#fff" }};
}

@media only screen and (min-width:769px){
  :root{
    --grid-gutter:22px;
    --drawer-gutter:30px;
  }
}

@keyframes spin{
  0%{
    transform:rotate(0deg);
  }

  to{
    transform:rotate(360deg);
  }
}

@keyframes preloading{
  0%{   transform-origin:0% 50%; transform:scaleX(0); opacity:0; }
  40%{  transform-origin:0% 50%; transform:scaleX(1); opacity:1; }
  41%{  transform-origin:100% 50%; transform:scaleX(1); opacity:1; }
  to{ transform-origin:100% 50%; transform:scaleX(0); opacity:1; }
}

@keyframes slideshowBars{
  0%{ transform:translateX(-100%); }
  to{ transform:translateX(0); }
}

@keyframes grid-product__loading{
  0%{ opacity:1; }
  60%{ opacity:0; }
  to{ opacity:1; }
}

@keyframes shine{
  to{
    left:-200%;
  }
}

@keyframes overlay-on{
  0%{ opacity:0; }
  to{ opacity:0.6; }
}

@keyframes overlay-off{
  0%{ opacity:0.6; }
  to{ opacity:0; }
}

@keyframes full-overlay-on{
  0%{ opacity:0; }
  to{ opacity:1; }
}

@keyframes full-overlay-off{
  0%{ opacity:1; }
  to{ opacity:0; }
}

@keyframes modal-open{
  0%{
    opacity:0;
    transform:translateY(30px);
  }
  to{
    opacity:1;
    transform:translateY(0);
  }
}

@keyframes modal-closing{
  0%{
    opacity:1;
    transform:scale(1);
  }
  to{
    opacity:0;
    transform:scale(0.9);
  }
}

@keyframes rise-up{
  0%{
    opacity:1;
    transform:translateY(120%);
  }
  to{
    opacity:1;
    transform:translateY(0%);
  }
}

@keyframes rise-up-out{
  0%{
    opacity:1;
    transform:translateY(0%);
  }
  to{
    opacity:1;
    transform:translateY(-120%);
  }
}

@keyframes fade-in{
  0%{
    opacity:0;
  }
  to{
    opacity:1;
  }
}

@keyframes fade-out{
  0%{
    opacity:1;
  }
  to{
    opacity:0;
  }
}

@keyframes zoom-fade{
  0%{
    opacity:0;
    transform:scale(1.3);
  }
  to{
    opacity:1;
    transform:scale(1);
  }
}

@keyframes placeholder-shimmer{
  0%{
    background-position:-150% 0;
  }
  to{
    background-position:150% 0;
  }
}

.flickity-enabled{
  position:relative;
}

.flickity-enabled:focus{ outline:none; }

.flickity-viewport{
  overflow:hidden;
  position:relative;
  transition:height 0.35s;
  height:100%;
}

.flickity-slider{
  position:absolute;
  width:100%;
  height:100%;
}

.flickity-enabled.is-draggable{
  -webkit-user-select:none;
          user-select:none;
}

.flickity-enabled.is-draggable .flickity-viewport{
  cursor:move;
  cursor:grab;
}

.flickity-enabled.is-draggable .flickity-viewport.is-pointer-down{
  cursor:grabbing;
}

.flickity-button{
  position:absolute;
  border:none;
  color:{{ settings.color_button_text | default: "#fff" }};
  color:var(--colorBtnPrimaryText);
  background:{{ settings.color_button | default: "#000" }};
  background:var(--colorBtnPrimary);
  border-radius:50%
}

.hero .flickity-button{
    color:{{ settings.color_body_text | default: "#1c1d1d" }};
    color:var(--colorTextBody);
    background-color:{{ settings.color_body_bg | default: "#fff" }};
    background-color:var(--colorBody);
    box-shadow:0 5px 5px rgba(0, 0, 0, 0.1)
}

.flickity-button:hover{
  cursor:pointer;
  opacity:1;
}

.flickity-button:disabled{
  display:none;
  cursor:auto;
  pointer-events:none;
}

.flickity-prev-next-button{
  top:50%;
  width:40px;
  height:40px;
  transform:translateY(-50%)
}

@media only screen and (max-width:768px){

.flickity-prev-next-button{
    width:33px;
    height:33px
}
  }

.flickity-prev-next-button:hover{
  transform:translateY(-50%) scale(1.12);
}

.flickity-prev-next-button:active{
  transform:translateY(-50%) scale(1);
  transition:transform 0.05s ease-out;
}

.flickity-previous{ left:10px; }

.flickity-next{ right:10px; }

.flickity-rtl .flickity-previous{
  left:auto;
  right:10px;
}

.flickity-rtl .flickity-next{
  right:auto;
  left:10px;
}

.flickity-button-icon{
  position:absolute;
  left:35%;
  top:35%;
  width:30%;
  height:30%;
  fill:currentColor;
}

.flickity-page-dots{
  position:absolute;
  width:100%;
  bottom:-25px;
  padding:0;
  margin:0;
  list-style:none;
  text-align:center;
  line-height:1;
  color:currentColor
}

.hero .flickity-page-dots{
    bottom:20px;
    color:#fff
}

.flickity-rtl .flickity-page-dots{ direction:rtl; }

.flickity-page-dots .dot{
  display:inline-block;
  vertical-align:middle;
  width:6px;
  height:6px;
  margin:0 5px;
  border-radius:100%;
  cursor:pointer;
  background-color:currentColor;
  opacity:0.4
}

.flickity-page-dots .dot:hover{
    opacity:0.6;
  }

.flickity-page-dots .dot.is-selected{
  opacity:1;
  background-color:currentColor;
  width:9px;
  height:9px;
}

.flickity-enabled.is-fade .flickity-slider>*{
  pointer-events:none;
  z-index:0;
}

.flickity-enabled.is-fade .flickity-slider>.is-selected{
  pointer-events:auto;
  z-index:1;
}

.hero[data-arrows=true]{
  overflow:visible;
  z-index:5
}

.hero[data-arrows=true] .flickity-prev-next-button{
    top:auto;
    bottom:-20px;
    transform:none;
  }

.hero[data-arrows=true] .flickity-prev-next-button:hover{
    transform:scale(1.12);
  }

.hero[data-arrows=true] .flickity-prev-next-button:active{
    transform:scale(1);
  }

.hero[data-arrows=true] .flickity-previous{
    left:auto;
    right:90px;
  }

.hero[data-arrows=true] .flickity-next{
    right:40px;
  }

.hero[data-arrows=true].flickity-rtl .flickity-previous{
    right:auto;
    left:90px;
  }

.hero[data-arrows=true].flickity-rtl .flickity-next{
    right:auto;
    left:40px;
  }

@media only screen and (max-width:768px){
    .hero[data-arrows=true] .flickity-prev-next-button{
      bottom:-16px;
    }
    .hero[data-arrows=true] .flickity-previous{
      right:60px;
    }
    .hero[data-arrows=true] .flickity-next{
      right:20px;
    }
    .hero[data-arrows=true].flickity-rtl .flickity-previous{
      left:60px;
    }
    .hero[data-arrows=true].flickity-rtl .flickity-next{
      left:20px;
    }
  }

[data-bars=true].hero .flickity-page-dots{
    bottom:0;
    height:6px;
    line-height:6px;
    z-index:6;
  }

[data-bars=true] .flickity-page-dots .dot{
    position:relative;
    border-radius:0;
    width:120px;
    height:6px;
    border:0;
    opacity:1;
    vertical-align:top;
    background:none;
    overflow:hidden
  }

@media only screen and (max-width:768px){

[data-bars=true] .flickity-page-dots .dot{
      width:45px
  }
    }

[data-bars=true] .flickity-page-dots .dot:after,[data-bars=true] .flickity-page-dots .dot:before{
      content:"";
      display:block;
      position:absolute;
      left:0;
      height:100%;
      width:100%;
      background-color:currentColor;
    }

[data-bars=true] .flickity-page-dots .dot:before{
      opacity:0.4;
    }

[data-bars=true] .flickity-page-dots .dot:hover:before{
      opacity:0.6;
    }

[data-bars=true] .flickity-page-dots .dot:after{
      transform:translateX(-100%);
      transition:none;
    }

[data-bars=true] .flickity-page-dots .dot.is-selected:after{
      animation:slideshowBars 0s linear forwards;
    }

.noUi-target,.noUi-target *{
   -webkit-touch-callout:none;
   -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
   -webkit-user-select:none;
   touch-action:none;
   user-select:none;
   box-sizing:border-box;
 }

.noUi-target{
   position:relative;
 }

.noUi-base,.noUi-connects{
   width:100%;
   height:100%;
   position:relative;
   z-index:1;
 }

.noUi-connects{
   overflow:hidden;
   z-index:0;
 }

.noUi-connect,.noUi-origin{
   will-change:transform;
   position:absolute;
   z-index:1;
   top:0;
   right:0;
   -ms-transform-origin:0 0;
   -webkit-transform-origin:0 0;
   -webkit-transform-style:preserve-3d;
   transform-origin:0 0;
   transform-style:flat;
 }

.noUi-connect{
   height:100%;
   width:100%;
 }

.noUi-origin{
   height:10%;
   width:10%;
 }

.noUi-txt-dir-rtl.noUi-horizontal .noUi-origin{
   left:0;
   right:auto;
 }

.noUi-vertical .noUi-origin{
   width:0;
 }

.noUi-horizontal .noUi-origin{
   height:0;
 }

.noUi-handle{
   -webkit-backface-visibility:hidden;
   backface-visibility:hidden;
   position:absolute;
 }

.noUi-touch-area{
   height:100%;
   width:100%;
 }

.noUi-state-tap .noUi-connect,.noUi-state-tap .noUi-origin{
   transition:transform 0.3s;
 }

.noUi-state-drag *{
   cursor:inherit !important;
 }

.noUi-horizontal{
   height:18px;
 }

.noUi-horizontal .noUi-handle{
   width:34px;
   height:28px;
   right:-17px;
   top:-6px;
 }

.noUi-vertical{
   width:18px;
 }

.noUi-vertical .noUi-handle{
   width:28px;
   height:34px;
   right:-6px;
   top:-17px;
 }

.noUi-txt-dir-rtl.noUi-horizontal .noUi-handle{
   left:-17px;
   right:auto;
 }

.noUi-target{
   background:#fafafa;
   border-radius:4px;
   border:1px solid #d3d3d3;
   box-shadow:inset 0 1px 1px #f0f0f0,0 3px 6px -5px #bbb;
 }

.noUi-connects{
   border-radius:3px;
 }

.noUi-connect{
   background:#3fb8af;
 }

.noUi-draggable{
   cursor:ew-resize;
 }

.noUi-vertical .noUi-draggable{
   cursor:ns-resize;
 }

.noUi-handle{
   border:1px solid #d9d9d9;
   border-radius:3px;
   background:#fff;
   cursor:default;
   box-shadow:inset 0 0 1px #fff,inset 0 1px 7px #ebebeb,0 3px 6px -3px #bbb;
 }

.noUi-active{
   box-shadow:inset 0 0 1px #fff,inset 0 1px 7px #ddd,0 3px 6px -3px #bbb;
 }

.noUi-handle:after,.noUi-handle:before{
   content:"";
   display:block;
   position:absolute;
   height:14px;
   width:1px;
   background:#e8e7e6;
   left:14px;
   top:6px;
 }

.noUi-handle:after{
   left:17px;
 }

.noUi-vertical .noUi-handle:after,.noUi-vertical .noUi-handle:before{
   width:14px;
   height:1px;
   left:6px;
   top:14px;
 }

.noUi-vertical .noUi-handle:after{
   top:17px;
 }

[disabled] .noUi-connect{
   background:#b8b8b8;
 }

[disabled].noUi-handle,[disabled] .noUi-handle,[disabled].noUi-target{
   cursor:not-allowed;
 }

.noUi-pips,.noUi-pips *{
   box-sizing:border-box;
 }

.noUi-pips{
   position:absolute;
   color:#999;
 }

.noUi-value{
   position:absolute;
   white-space:nowrap;
   text-align:center;
 }

.noUi-value-sub{
   color:#ccc;
   font-size:10px;
 }

.noUi-marker{
   position:absolute;
   background:#ccc;
 }

.noUi-marker-sub{
   background:#aaa;
 }

.noUi-marker-large{
   background:#aaa;
 }

.noUi-pips-horizontal{
   padding:10px 0;
   height:80px;
   top:100%;
   left:0;
   width:100%;
 }

.noUi-value-horizontal{
   transform:translate(-50%, 50%);
 }

.noUi-rtl .noUi-value-horizontal{
   transform:translate(50%, 50%);
 }

.noUi-marker-horizontal.noUi-marker{
   margin-left:-1px;
   width:2px;
   height:5px;
 }

.noUi-marker-horizontal.noUi-marker-sub{
   height:10px;
 }

.noUi-marker-horizontal.noUi-marker-large{
   height:15px;
 }

.noUi-pips-vertical{
   padding:0 10px;
   height:100%;
   top:0;
   left:100%;
 }

.noUi-value-vertical{
   transform:translateY(-50%);
   padding-left:25px;
 }

.noUi-rtl .noUi-value-vertical{
   transform:translateY(50%);
 }

.noUi-marker-vertical.noUi-marker{
   width:5px;
   height:2px;
   margin-top:-1px;
 }

.noUi-marker-vertical.noUi-marker-sub{
   width:10px;
 }

.noUi-marker-vertical.noUi-marker-large{
   width:15px;
 }

.noUi-tooltip{
   display:block;
   position:absolute;
   border:1px solid #d9d9d9;
   border-radius:3px;
   background:#fff;
   color:#000;
   padding:5px;
   text-align:center;
   white-space:nowrap;
 }

.noUi-horizontal .noUi-tooltip{
   transform:translate(-50%);
   left:50%;
   bottom:120%;
 }

.noUi-vertical .noUi-tooltip{
   transform:translateY(-50%);
   top:50%;
   right:120%;
 }

.noUi-horizontal .noUi-origin>.noUi-tooltip{
   transform:translate(50%);
   left:auto;
   bottom:10px;
 }

.noUi-vertical .noUi-origin>.noUi-tooltip{
   transform:translateY(-18px);
   top:auto;
   right:28px;
 }

tool-tip{
  display:none;
}

tool-tip[data-tool-tip-open=true]{
  display:flex;
  justify-content:center;
  align-items:center;
  z-index:10001;
  position:fixed;
  top:0;
  left:0;
  width:100%;
  height:100%
}

tool-tip[data-tool-tip-open=true]:before{
    content:"";
    position:fixed;
    top:0;
    left:0;
    width:100%;
    height:100%;
    background-color:#e0e0e0;
    background-color:{{ settings.color_modal_overlays | default: "#000" }};
    background-color:var(--colorModalBg);
    animation:overlay-on 0.3s forwards;
    cursor:pointer;
  }

.tool-tip__inner{
  animation:modal-close 0.3s forwards;
  opacity:0
}

tool-tip[data-tool-tip-open=true] .tool-tip__inner{
    animation:modal-open 0.3s forwards;
    display:block;
    position:fixed;
    background:{{ settings.color_body_bg | default: "#fff" }};
    background:var(--colorBody);
    box-shadow:0 10px 20px rgba(0, 0, 0, 0.0902);
    padding:30px;
    max-width:720px;
    min-width:250px;
    min-height:250px
}

@media only screen and (max-width:768px){

tool-tip[data-tool-tip-open=true] .tool-tip__inner{
      width:100%;
      max-width:93%
}
    }

.tool-tip__content{
  overflow:auto;
  max-height:80vh
}

@media only screen and (max-width:768px){

.tool-tip__content{
    font-size:0.85em
}
  }

.tool-tip__close{
  position:absolute;
  top:0;
  right:0;
}

.tool-tip__close .icon{
  width:28px;
  height:28px;
}

.tool-tip-trigger{
  background:none;
  border:0;
  cursor:pointer;
}

.tool-tip-trigger .icon{
  width:28px;
  height:28px;
}

.tool-tip-trigger__title{
  display:inline-block;
}

.tool-tip-trigger__title:focus,.tool-tip-trigger__title:hover{
  text-decoration:underline;
  text-underline-offset:2px;
}

.tool-tip-trigger__content{
  display:none !important;
}

.size-chart__standalone{
  margin:25px 0;
  margin:var(--sizeChartMargin)
}

.size-chart__standalone svg{
    margin-left:5px;
    margin-left:var(--sizeChartIconMargin);
  }

.text-with-icons__blocks{
  display:flex;
  flex-wrap:wrap;
  justify-content:center;
  margin-left:-30px;
  margin-right:-30px
}

@media only screen and (max-width:768px){

.text-with-icons__blocks{
    flex-direction:column;
    margin:0
}
  }

.has-1-per-row .text-with-icons__block{
  width:100%;
}

.has-2-per-row .text-with-icons__block{
  width:50%;
}

.has-3-per-row .text-with-icons__block{
  width:33.333%;
}

.has-4-per-row .text-with-icons__block{
  width:25%;
}

.has-5-per-row .text-with-icons__block{
  width:20%;
}

.text-with-icons__blocks .text-with-icons__block{
  display:flex;
  flex-direction:column;
  flex:none;
  padding:30px
}

@media only screen and (max-width:768px){

.text-with-icons__blocks .text-with-icons__block{
    width:100%;
    padding:0 0 60px
}
  }

.text-with-icons__block-icon{
  display:block;
  margin-bottom:10px
}

.text-with-icons__block-icon .icon{
    width:70px;
    height:70px
  }

@media only screen and (max-width:768px){

.text-with-icons__block-icon .icon{
      width:60px;
      height:60px
  }
    }

.text-with-icons__button{
  display:flex;
  justify-content:center;
}

newsletter-reminder{
  position:fixed;
  left:20px;
  bottom:20px;
  transition:opacity 0.3s ease-in-out;
  box-shadow:0px 12px 25px rgba(0, 0, 0, 0.15);
  max-width:240px;
  z-index:10
}

@media only screen and (max-width:768px){

newsletter-reminder{
    max-width:calc(100% - 40px)
}
  }

newsletter-reminder[data-enabled=false]{
    opacity:0;
    visibility:hidden;
    pointer-events:none;
  }

newsletter-reminder[class*=color-scheme-]:not(.color-scheme-none){
    position:fixed;
  }

newsletter-reminder .color-scheme-none{
    color:{{ settings.color_button_text | default: "#fff" }};
    color:var(--colorBtnPrimaryText);
    background-color:{{ settings.color_button | default: "#000" }};
    background-color:var(--colorBtnPrimary);
  }

.newsletter-reminder__message.h3{
  cursor:pointer;
  padding:40px;
  padding:var(--newsletterReminderPadding);
  margin:0;
}

.parallax-image{
  height:100%;
  overflow:hidden;
  transition:transform 0.05s linear;
}

.countdown-wrapper{
  position:relative;
}

.countdown__background-image-wrapper{
  width:100%;
  height:100%;
  position:absolute;
}

.countdown__background-image{
  background-size:cover;
  width:100%;
  height:100%;
  background-position:var(--countdown-background-image-position);
}

.countdown__mobile-image-wrapper{
  width:100%;
  height:100%;
  position:absolute;
}

.countdown__mobile-image{
  height:100%;
  -o-object-fit:cover;
     object-fit:cover;
}

.countdown-layout--banner .countdown__content{
    padding:2rem
  }

@media only screen and (min-width:769px){

.countdown-layout--banner .countdown__content{
      display:flex;
      flex-wrap:nowrap;
      flex-direction:row;
      justify-content:space-around;
      grid-gap:1rem;
      gap:1rem;
      padding:1.6rem 2rem
  }
    }

.countdown-layout--banner.countdown-blocks--2.page-width .countdown__content{
      justify-content:space-around;
    }

.countdown-layout--banner.countdown-blocks--2 .countdown__content{
      justify-content:center;
    }

@media only screen and (min-width:769px){

.countdown-layout--banner .countdown__block{
      width:33%
  }
    }

.countdown__content{
  width:100%;
  height:auto;
  display:flex;
  flex-direction:column;
  align-items:center;
  justify-content:center;
  padding:5rem 2rem;
  text-align:center
}

.page-width .countdown__content{
    position:relative
}

@media only screen and (min-width:769px){

.countdown-layout--hero .countdown__content{
      height:100vh
}
    }

.countdown__block{
  width:100%;
  margin:1rem auto;
  z-index:3;
}

.countdown__block--content{
  display:flex;
  flex-direction:column;
  justify-content:center;
  align-items:center
}

.countdown__block--content .countdown__block--button{
    width:100%;
    margin:0;
  }

@media only screen and (min-width:769px){

.countdown-blocks--2.countdown-layout--banner .countdown__block--content,.countdown-blocks--2.countdown-layout--banner .countdown__block--timer{
      width:50%
}
    }

@media only screen and (min-width:769px){

.countdown-blocks--2.countdown-layout--banner .countdown__block--button.button-block-active{
      width:50%
}
    }

.countdown__text-wrapper{
  display:flex;
  flex-direction:column;
  justify-content:center
}

@media only screen and (min-width:769px){

.countdown-layout--hero .countdown__text-wrapper{
      width:50%
}
    }

.countdown__text-wrapper--content-alignment-left{
  text-align:left;
}

.countdown__text-wrapper--content-alignment-right{
  text-align:right;
}

.countdown__display{
  display:flex;
  justify-content:center;
}

.countdown__display--visible+.countdown__timer-message--visible{
  margin-top:1.5rem;
}

.countdown__display--hidden{
  visibility:hidden;
  opacity:0;
  width:0;
  height:0;
  margin:0;
  transition:opacity 3s ease-out;
}

.countdown__display-block{
  display:flex;
  flex-wrap:wrap;
  flex-direction:column;
  align-content:center;
  border-right:1px solid;
  padding:0 1rem
}

.countdown__display-block h2{
    margin-bottom:16px
  }

@media only screen and (min-width:769px){

.countdown__display-block h2{
      margin-bottom:4px
  }
    }

.countdown__display-block span{
    font-size:0.6rem;
    letter-spacing:1.7px
  }

@media only screen and (min-width:769px){

.countdown__display-block span{
      font-size:0.75rem
  }
    }

.countdown__display-block:last-child{
    border-right:none;
  }

.countdown__timer-message{
  opacity:0;
  visibility:hidden;
  margin:0;
  height:0;
}

.countdown__timer-message--visible{
  opacity:1;
  visibility:visible;
  transition:opacity 1s ease-in;
  height:auto;
}

.countdown__block--hidden{
  opacity:0;
  visibility:hidden;
  margin:0;
  transition:opacity 1s ease-out;
  width:0 !important;
}

.countdown__overlay:after{
    content:"";
    position:absolute;
    width:100%;
    height:100%;
    top:0;
    left:0;
    right:0;
    bottom:0;
    z-index:2;
    background-color:var(--countdown-overlay-rgba);
  }

@media only screen and (max-width:768px){

.hotspots-section .index-section{
    margin-bottom:10px
}
  }

.hotspots-section .hotspots-wrapper{
  display:flex;
  flex-wrap:wrap
}

.hotspots-section .hotspots-wrapper.is-reverse{
    flex-direction:row-reverse;
  }

.hotspots__title{
  width:100%;
  padding-top:1.5rem;
}

.hotspots{
  position:relative;
  width:70%
}

@media only screen and (max-width:768px){

.hotspots{
    width:100%
}
  }

.hotspots .grid__image-ratio img{
    opacity:1;
    position:absolute;
    top:0;
    left:0;
    width:100%;
    height:100%;
    -o-object-fit:cover;
       object-fit:cover
  }

[data-animate_images=true] .hotspots .grid__image-ratio img{
      opacity:1
  }

.hotspots__buttons{
  position:absolute;
  top:0;
  left:0;
  width:100%;
  height:100%;
}

.hotspot__button{
  padding:10px;
  border-radius:50%;
  position:absolute;
  line-height:0;
  transform:translate(-50%, -50%)
}

@media only screen and (max-width:768px){

.hotspot__button{
    padding:6px
}
  }

.hotspot__button:hover .hotspot__button-content{
    opacity:1;
    visibility:visible;
    pointer-events:auto;
  }

.hotspot__button-content{
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody);
  opacity:0;
  visibility:hidden;
  pointer-events:none;
  padding:1rem;
  position:absolute;
  top:calc(100% + 1rem);
  left:50%;
  transform:translateX(-50%);
  transition:opacity 0.3s ease-in-out;
  min-width:5rem;
  border-radius:5px;
  box-shadow:3px 3px 10px 3px rgba(0, 0, 0, 0.2)
}

.hotspot__button-content p,.hotspot__button-content span{
    white-space:nowrap;
    margin-bottom:0
  }

.hotspot__button-content:before{
    position:absolute;
    top:-10px;
    left:50%;
    content:"";
    width:0px;
    height:0px;
    border-style:solid;
    border-width:0 5px 10px 5px;
    transform:translateX(-50%);
    border-color:transparent transparent {{ settings.color_body_bg | default: "#fff" }} transparent;
    border-color:transparent transparent var(--colorBody) transparent;
  }

.hotspot__button-content .content__prices{
    display:flex;
    flex-wrap:nowrap;
  }

.hotspots__content{
  width:30%;
  padding:24px;
  display:flex;
  align-items:center
}

.page-width .hotspots__content{
    padding-right:0;
    padding-left:40px
}

@media only screen and (max-width:768px){

.page-width .hotspots__content{
      padding-left:0
}
    }

.page-width.is-reverse .hotspots__content{
    padding-left:0;
    padding-right:40px
}

@media only screen and (max-width:768px){

.page-width.is-reverse .hotspots__content{
      padding-right:20px
}
    }

@media only screen and (max-width:768px){

.hotspots__content{
    width:100%;
    padding:1rem 20px 0
}
  }

.hotspot-content__block{
  display:none;
  max-height:0;
  width:100%;
  animation:fade-in .5s ease 0s forwards;
  position:sticky;
  top:0

}

@media only screen and (max-width:768px){

.hotspot-content__block{
    align-items:center;
    position:relative;
    padding-top:0

}
  }

.hotspot-content__block.is-active{
    display:block;
    max-height:none;
  }

.hotspot-content__block .grid-product__image-wrap{
    margin:0;
  }

.hotspot-content__block .grid__item{
    display:block;
    float:none;
    padding:0;
  }

.hotspot-content__block .grid-product__tags{
    margin-left:0;
  }

@media only screen and (max-width:768px){

.hotspot-content__block .grid-product__tag{
      right:auto;
      left:0
  }
    }

.hotspot-content__block .grid-item__meta,.hotspot-content__block .grid-product__meta{
    padding-top:10px
  }

@media only screen and (max-width:768px){

.hotspot-content__block .grid-item__meta,.hotspot-content__block .grid-product__meta{
      display:flex;
      justify-content:center;
      flex-direction:column;
      flex-wrap:wrap;
      padding-left:10px;
      padding-top:0;
      text-align:left
  }

      .hotspot-content__block .grid-item__meta .grid-item__meta-main,.hotspot-content__block .grid-item__meta .grid-item__meta-secondary,.hotspot-content__block .grid-product__meta .grid-item__meta-main,.hotspot-content__block .grid-product__meta .grid-item__meta-secondary{
        width:100%;
        flex:none;
      }
    }

@media only screen and (max-width:768px){

    .hotspot-content__block .grid-product{
      padding-right:0;
      padding-left:0;
    }

    .hotspot-content__block .quick-add-btn{
      display:none;
    }

    .hotspot-content__block .grid-item__link,.hotspot-content__block .grid-product__link{
      display:flex;
      flex-wrap:nowrap;
    }

    .hotspot-content__block .grid-product__image-mask,.hotspot-content__block .grid-product__image-wrap,.hotspot-content__block .product-slider{
      width:30%;
    }

    .hotspot-content__block .grid-product__actions{
      right:auto;
      left:10px;
      top:10px;
    }
  }

.hotspot-content__block .modal .grid__item{
  float:left;
}

image-compare{
  position:relative;
  display:block;
  width:100%;
  background:#222;
  overflow:hidden;
}

.comparison__draggable{
  position:absolute;
  top:0;
  height:100%;
  width:50%;
  overflow:hidden;
}

.comparison__image{
  width:100%;
  height:100%;
  max-width:none;
  -o-object-fit:cover;
     object-fit:cover;
  display:block;
  -webkit-user-select:none;
          user-select:none;
}

.comparison__button{
  width:64px;
  height:64px;
  position:absolute;
  border-radius:50%;
  left:50%;
  top:50%;
  transform:translate(-32px, -50%);
  border:0;
  cursor:pointer;
  z-index:3;
  display:flex;
  flex-wrap:nowrap;
  justify-content:center;
  align-items:center;
  border:3px solid #fff
}

.comparison--style-minimal .comparison__button{
    border:0;
    background:transparent;
    width:auto;
    transform:translate(-56px, -50%)
}

.comparison--style-minimal .comparison__button svg{
      margin:0 20px
    }

@supports (-webkit-touch-callout:none){

.comparison--style-minimal .comparison__button svg{
        position:absolute
    }

        .comparison--style-minimal .comparison__button svg.icon-chevron-left{
          left:2px;
          width:9px;
        }

        .comparison--style-minimal .comparison__button svg.icon-chevron-right{
          right:2px;
          width:9px;
        }
      }

@media only screen and (max-width:768px){

.comparison--style-classic .comparison__button{
      width:48px;
      height:48px;
      transform:translate(-24px, -50%)
}
    }

.comparison__button svg{
    width:12px;
    height:auto;
    pointer-events:none;
    margin:0 5px;
  }

.comparison__button:before{
    content:"";
    width:2px;
    position:absolute;
    background-color:#fff !important;
  }

.comparison__button:after{
    content:"";
    width:2px;
    height:50%;
    position:absolute;
    bottom:0%;
    left:50%;
    transform:translate(-50%);
    background-color:#fff !important;
  }

.age-verification-popup .rte{
    margin-top:20px;
    margin-bottom:20px
  }

@media only screen and (min-width:769px){

.age-verification-popup .rte{
      margin-bottom:30px
  }
    }

.age-verification-popup .age-verification-popup__btns-wrapper{
    display:flex;
    flex-wrap:wrap;
    justify-content:center;
    grid-gap:10px;
    gap:10px;
  }

.age-verification-popup__background-image-wrapper{
  overflow:hidden;
  width:100%;
  height:100%;
  position:absolute;
}

.age-verification-popup__background-image{
  background-size:cover;
  width:100%;
  height:100%;
}

.age-verification-popup__content--active{
  opacity:1;
  transition:opacity 1.5s ease-in;
}

.age-verification-popup__content--inactive,.age-verification-popup__content--inactive *,.age-verification-popup__content--inactive .btn{
  opacity:0;
  width:0;
  height:0;
  position:absolute;
  visibility:hidden;
  padding:0;
  border:0;
  margin:0;
  line-height:0;
  font-size:0;
}

.age-verification-popup__decline-content--inactive,.age-verification-popup__decline-content--inactive *,.age-verification-popup__decline-content--inactive .btn{
  opacity:0;
  width:0;
  height:0;
  position:absolute;
  visibility:hidden;
  padding:0;
  border:0;
  margin:0;
  line-height:0;
  font-size:0;
}

.age-verification-popup__decline-content--active{
  opacity:1;
  transition:opacity 1.5s ease-in;
}

.age-verification-popup__content-wrapper{
  text-align:center;
  max-width:520px;
  margin:0 auto;
}

@media only screen and (max-width:768px){

.spr-starrating.spr-summary-starrating{
    justify-content:center !important
}
  }

*,:after,:before,input{
  box-sizing:border-box;
  
}

body,html{
  padding:0;
  margin:0;
}

article,aside,details,figcaption,figure,footer,header,hgroup,main,nav,section,summary{
  display:block;
}

audio,canvas,progress,video{
  display:inline-block;
  vertical-align:baseline;
}

input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{
  height:auto;
}

input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration{
  -webkit-appearance:none;
}

.grid:after{content:"";display:table;clear:both;}

.grid{
  list-style:none;
  margin:0;
  padding:0;
  margin-left:-22px
}

@media only screen and (max-width:768px){

.grid{
    margin-left:-17px
}

html[dir=rtl] .grid{
      margin-left:0;
      margin-right:-17px
}
  }

html[dir=rtl] .grid{
    margin-left:0;
    margin-right:-22px
}

.grid--small{
  margin-left:-10px
}

.grid--small .grid__item{
    padding-left:10px;
  }

.grid__item{
  float:left;
  padding-left:22px;
  width:100%;
  min-height:1px
}

@media only screen and (max-width:768px){

.grid__item{
    padding-left:17px
}

html[dir=rtl] .grid__item{
      padding-left:0;
      padding-right:17px
}
  }

html[dir=rtl] .grid__item{
    float:right;
    padding-left:0;
    padding-right:22px
}

.grid--no-gutters{
  margin-left:0
}

.grid--no-gutters .grid__item{
    padding-left:0;
  }

.grid--flush-bottom{
  margin-bottom:-22px;
  overflow:auto
}

.grid--flush-bottom>.grid__item{
    margin-bottom:22px;
  }

.grid--center{
  text-align:center
}

.grid--center .grid__item{
    float:none;
    display:inline-block;
    vertical-align:top;
    text-align:left
  }

html[dir=rtl] .grid--center .grid__item{
      text-align:right
  }

.grid--full{
  margin-left:0
}

.grid--full>.grid__item{
    padding-left:0;
  }

@media only screen and (min-width:769px){
  .grid--table-large{
    display:table;
    width:100%;
    table-layout:fixed
  }

    .grid--table-large>.grid__item{
      display:table-cell;
      vertical-align:middle;
      float:none;
    }
}

@media only screen and (max-width:768px){
  .small--grid--flush{
    margin-left:-2px
  }
  .page-width .small--grid--flush{
      margin-left:-17px;
      margin-right:-15px
  }

    .small--grid--flush>.grid__item{
      padding-left:2px;
    }
}

.one-whole{width:100%;}

.one-half{width:50%;}

.one-third{width:33.33333%;}

.two-thirds{width:66.66667%;}

.one-quarter{width:25%;}

.two-quarters{width:50%;}

.three-quarters{width:75%;}

.one-fifth{width:20%;}

.two-fifths{width:40%;}

.three-fifths{width:60%;}

.four-fifths{width:80%;}

.one-sixth{width:16.66667%;}

.two-sixths{width:33.33333%;}

.three-sixths{width:50%;}

.four-sixths{width:66.66667%;}

.five-sixths{width:83.33333%;}

@media only screen and (max-width:768px){.small--one-whole{width:100%;}.small--one-half{width:50%;}.small--one-third{width:33.33333%;}.small--two-thirds{width:66.66667%;}.grid--uniform .small--one-half:nth-of-type(odd),.grid--uniform .small--one-third:nth-of-type(3n+1){clear:both;}.small--one-quarter{width:25%;}.small--two-quarters{width:50%;}.small--three-quarters{width:75%;}.grid--uniform .small--one-quarter:nth-of-type(4n+1){clear:both;}.small--one-fifth{width:20%;}.small--two-fifths{width:40%;}.small--three-fifths{width:60%;}.small--four-fifths{width:80%;}.grid--uniform .small--one-fifth:nth-of-type(5n+1){clear:both;}.small--one-sixth{width:16.66667%;}.small--two-sixths{width:33.33333%;}.small--three-sixths{width:50%;}.small--four-sixths{width:66.66667%;}.small--five-sixths{width:83.33333%;}.grid--uniform .small--one-sixth:nth-of-type(6n+1),.grid--uniform .small--three-sixths:nth-of-type(odd),.grid--uniform .small--two-sixths:nth-of-type(3n+1){clear:both;}}

@media only screen and (min-width:769px){.medium-up--one-whole{width:100%;}.medium-up--one-half{width:50%;}.medium-up--one-third{width:33.33333%;}.medium-up--two-thirds{width:66.66667%;}.grid--uniform .medium-up--one-half:nth-of-type(odd),.grid--uniform .medium-up--one-third:nth-of-type(3n+1){clear:both;}.medium-up--one-quarter{width:25%;}.medium-up--two-quarters{width:50%;}.medium-up--three-quarters{width:75%;}.grid--uniform .medium-up--one-quarter:nth-of-type(4n+1){clear:both;}.medium-up--one-fifth{width:20%;}.medium-up--two-fifths{width:40%;}.medium-up--three-fifths{width:60%;}.medium-up--four-fifths{width:80%;}.grid--uniform .medium-up--one-fifth:nth-of-type(5n+1){clear:both;}.medium-up--one-sixth{width:16.66667%;}.medium-up--two-sixths{width:33.33333%;}.medium-up--three-sixths{width:50%;}.medium-up--four-sixths{width:66.66667%;}.medium-up--five-sixths{width:83.33333%;}.grid--uniform .medium-up--one-sixth:nth-of-type(6n+1),.grid--uniform .medium-up--three-sixths:nth-of-type(odd),.grid--uniform .medium-up--two-sixths:nth-of-type(3n+1){clear:both;}}

.show{display:block !important;}

.hide{display:none !important;}

.text-left{text-align:left !important;}

.text-right{text-align:right !important;}

.text-center{text-align:center !important;}

@media only screen and (max-width:768px){.small--show{display:block !important;}.small--hide{display:none !important;}.small--text-left{text-align:left !important;}.small--text-right{text-align:right !important;}.small--text-center{text-align:center !important;}}

@media only screen and (max-width:959px){.medium-down--show{display:block !important;}.medium-down--hide{display:none !important;}.medium-down--text-left{text-align:left !important;}.medium-down--text-right{text-align:right !important;}.medium-down--text-center{text-align:center !important;}}

@media only screen and (min-width:769px){.medium-up--show{display:block !important;}.medium-up--hide{display:none !important;}.medium-up--text-left{text-align:left !important;}.medium-up--text-right{text-align:right !important;}.medium-up--text-center{text-align:center !important;}}

.flex-grid{
  display:flex;
  flex-wrap:wrap;
  flex:1 1 100%
}

[data-center-text=true] .flex-grid{
    justify-content:center
}

.flex-grid--center{
  align-items:center;
}

.flex-grid--gutters{
  margin-top:-15px;
  margin-left:-15px;
}

.flex-grid__item{
  flex:0 1 100%;
  display:flex;
  align-items:stretch
}

.flex-grid--gutters .flex-grid__item{
    padding-top:15px;
    padding-left:15px
}

.flex-grid__item>*{
    flex:1 1 100%;
  }

.flex-grid__item--stretch{
  flex:1 1 100%
}

.flex-grid__item--stretch:first-child{
    min-width:250px;
  }

@media only screen and (min-width:769px){
  .flex-grid__item--33{
    flex-basis:33.33%;
  }

  .flex-grid__item--50{
    flex-basis:50%;
  }
}

@media only screen and (max-width:768px){
  .flex-grid__item--mobile-second{
    order:2;
  }
}

.clearfix:after{content:"";display:table;clear:both;}

html:not(.no-js):not(.tab-outline) :focus{
  outline:none;
}

.is-transitioning{
  display:block !important;
  visibility:visible !important;
}

.display-table{
  display:table;
  table-layout:fixed;
  width:100%;
}

.display-table-cell{
  display:table-cell;
  vertical-align:middle;
  float:none;
}

@media only screen and (min-width:769px){
  .medium-up--display-table{
    display:table;
    table-layout:fixed;
    width:100%;
  }

  .medium-up--display-table-cell{
    display:table-cell;
    vertical-align:middle;
    float:none;
  }
}

.visually-hidden{
  border:0;
  clip:rect(0 0 0 0);
  height:1px;
  margin:-1px;
  overflow:hidden;
  padding:0;
  position:absolute;
  width:1px;
}

.visually-invisible{
  opacity:0 !important;
}

.skip-link:focus{
  clip:auto;
  width:auto;
  height:auto;
  margin:0;
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody);
  padding:10px;
  opacity:1;
  z-index:10000;
  transition:none;
}

html{
  touch-action:manipulation;
}

html[dir=rtl]{
  direction:rtl;
}

body,html{
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody);
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
}

@media only screen and (max-width:768px){
  .supports-touch.lock-scroll{
    overflow:hidden
  }
    @supports (-webkit-touch-callout:none){
  .supports-touch.lock-scroll{
      overflow:visible;
      overflow:initial
  }
    }
}

.page-width{
  max-width:1500px;
  margin:0 auto;
}

.page-full,.page-width{
  padding:0 17px
}

@media only screen and (min-width:769px){

.page-full,.page-width{
    padding:0 40px
}
  }

.page-width--narrow{
  max-width:1000px;
}

.page-width--tiny{
  max-width:450px;
}

@media only screen and (max-width:768px){
  .page-width--flush-small{
    padding:0;
  }
}

.page-content,.shopify-email-marketing-confirmation__container,.shopify-policy__container{
  padding-top:40px;
  padding-bottom:40px
}

@media only screen and (min-width:769px){

.page-content,.shopify-email-marketing-confirmation__container,.shopify-policy__container{
    padding-top:75px;
    padding-bottom:75px
}
  }

.shopify-email-marketing-confirmation__container{
  text-align:center;
}

.page-content--top,.page-content--with-blocks{
  padding-bottom:0;
}

.page-content--bottom{
  padding-top:0;
}

.main-content{
  display:block;
  min-height:300px
}

@media only screen and (min-width:769px){

.main-content{
    min-height:700px
}
  }

.template-challange .main-content{
    min-height:0
}

.hr--large,.hr--medium,.hr--small,hr{
  height:1px;
  border:0;
  border-top:1px solid;
  border-top-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-top-color:var(--colorBorder);
}

.hr--small{
  margin:15px auto;
}

.hr--medium{
  margin:25px auto
}

@media only screen and (min-width:769px){

.hr--medium{
    margin:35px auto
}
  }

.hr--large{
  margin:30px auto
}

@media only screen and (min-width:769px){

.hr--large{
    margin:45px auto
}
  }

.page-blocks+.hr--large,.page-blocks+[data-section-type=recently-viewed] .hr--large{
    margin-top:0
}

.hr--clear{
  border:0;
}

@media only screen and (max-width:768px){
    .table--responsive thead{
      display:none;
    }

    .table--responsive tr{
      display:block;
    }

    .table--responsive td,.table--responsive tr{
      float:left;
      clear:both;
      width:100%;
    }

    .table--responsive td,.table--responsive th{
      display:block;
      text-align:right;
      padding:15px;
    }

    .table--responsive td:before{
      content:attr(data-label);
      float:left;
      font-size:12px;
      padding-right:10px;
    }
  }

@media only screen and (max-width:768px){
  .table--small-hide{
    display:none !important;
  }

  .table__section+.table__section{
    position:relative;
    margin-top:10px;
    padding-top:15px
  }

    .table__section+.table__section:after{
      content:"";
      display:block;
      position:absolute;
      top:0;
      left:15px;
      right:15px;
      border-bottom:1px solid;
      border-bottom-color:{{ settings.color_borders | default: "#1c1d1d" }};
      border-bottom-color:var(--colorBorder);
    }
}

.faux-select,body,button,input,select,textarea{
  font-family:var(--typeBasePrimary),var(--typeBaseFallback);
  font-size:calc(var(--typeBaseSize)*0.92);
  letter-spacing:var(--typeBaseSpacing);
  line-height:var(--typeBaseLineHeight);
}

@media only screen and (min-width:769px){

.faux-select,body,button,input,select,textarea{
    font-size:var(--typeBaseSize);
}
  }

.faux-select,body,button,input,select,textarea{
  -webkit-font-smoothing:antialiased;
  -webkit-text-size-adjust:100%;
  text-rendering:optimizeSpeed;
}

body{
  font-weight:var(--typeBaseWeight);
}

p{
  margin:0 0 15px 0
}

p img{
    margin:0;
  }

em{
  font-style:italic;
}

b,strong{
  font-weight:300;
}

p[data-spam-detection-disclaimer],small{
  font-size:0.85em;
}

sub,sup{
  position:relative;
  font-size:60%;
  vertical-align:baseline;
}

sup{
  top:-0.5em;
}

sub{
  bottom:-0.5em;
}

.rte blockquote,blockquote{
  margin:0;
  padding:15px 30px 40px
}

.rte blockquote p,blockquote p{
    margin-bottom:0
  }

.rte blockquote p+cite,blockquote p+cite{
      margin-top:15px;
    }

.rte blockquote cite,blockquote cite{
    display:block;
  }

code,pre{
  background-color:#faf7f5;
  font-family:Consolas,monospace;
  font-size:1em;
  border:0 none;
  padding:0 2px;
  color:#51ab62;
}

pre{
  overflow:auto;
  padding:15px;
  margin:0 0 30px;
}

.label,label:not(.variant__button-label):not(.text-label){
  text-transform:uppercase;
  letter-spacing:0.2em;
  font-family: var(--typeHeaderPrimary),var(--typeHeaderFallback);
  font-size:0.8em;
  font-weight:700;

}

label{
  display:block;
  margin-bottom:10px;
}

.label-info{
  display:block;
  margin-bottom:10px;
}

.h1,.h2,.h3,.h4,.h5,.h6,h1,h2,h3,h4,h5,h6{
  display:block;
  margin:0 0 10px
}

@media only screen and (min-width:769px){

.h1,.h2,.h3,.h4,.h5,.h6,h1,h2,h3,h4,h5,h6{
    margin:0 0 15px
}
  }

.h1 a,.h2 a,.h3 a,.h4 a,.h5 a,.h6 a,h1 a,h2 a,h3 a,h4 a,h5 a,h6 a{
    text-decoration:none;
    font-weight:inherit;
  }

.h1,.h2,.h3,h1,h2,h3{
  font-family:var(--typeHeaderPrimary),var(--typeHeaderFallback);
  font-weight:var(--typeHeaderWeight);
  letter-spacing:var(--typeHeaderSpacing);
  line-height:var(--typeHeaderLineHeight);
}

[data-type_header_capitalize=true] .h1,[data-type_header_capitalize=true] .h2,[data-type_header_capitalize=true] .h3,[data-type_header_capitalize=true] h1,[data-type_header_capitalize=true] h2,[data-type_header_capitalize=true] h3{
    text-transform:uppercase;
}

.h1,h1{
  font-size:calc(var(--typeHeaderSize)*0.85);
}

.h2,h2{
  font-size:calc(var(--typeHeaderSize)*0.73);
}

.h3,h3{
  font-size:calc(var(--typeHeaderSize)*0.62);
}

@media only screen and (min-width:769px){
  .h1,h1{
    font-size:var(--typeHeaderSize);
  }

  .h2,h2{
    font-size:calc(var(--typeHeaderSize)*0.85);
  }

  .h3,h3{
    font-size:calc(var(--typeHeaderSize)*0.65);
  }
}

.h4,h4{
  text-transform:uppercase;
  letter-spacing:0.3em;
  font-size:0.8em;
}

.h5,.h6,h5,h6{
  text-transform:uppercase;
  letter-spacing:0.3em;
  font-size:0.8em;
  margin-bottom:10px
}

@media only screen and (max-width:768px){

.h5,.h6,h5,h6{
    margin-bottom:5px
}
  }

.subheading{
  text-transform:uppercase;
  letter-spacing:0.3em;
}

.text-spacing,.text-spacing.rte:last-child{
  margin-bottom:15px;
}

@media only screen and (max-width:768px){

      .rte table td,.rte table th{
        padding:6px 8px;
      }
    }

.collapsible-content .rte table td,.collapsible-content .rte table th{
        padding:6px 8px;
      }

.comment-author{
  margin-bottom:0;
}

.comment-date{
  font-size:calc(var(--typeBaseSize)*0.85);
  display:block;
  margin-top:3px
}

@media only screen and (max-width:768px){

.comment-date{
    margin-bottom:15px
}
  }

.ajaxcart__subtotal{
  text-transform:uppercase;
  letter-spacing:0.2em;
  font-weight: 600;
}

.rte .enlarge-text{
    margin:0;
    font-size:1.3em;
  }

@media only screen and (min-width:769px){

.rte .enlarge-text--offset p{
        padding-right:15%
    }

.text-center .rte .enlarge-text--offset p{
          padding:0 5%
    }
      }

@media only screen and (min-width:769px){
  .table--small-text{
    font-size:calc(var(--typeBaseSize)*0.85);
  }
}

.index-section--footer h3{
    font-size:1.5em;
  }

html[dir=rtl] .text-left{
    text-align:right !important;
  }

html[dir=rtl] .text-right{
    text-align:left !important;
  }

.icon-and-text{
  display:flex;
  flex-wrap:nowrap;
  align-items:center
}

.icon-and-text .icon{
    flex:0 0 auto;
  }

ol,ul{
  margin:0 0 15px 30px;
  padding:0;
  text-rendering:optimizeLegibility;
}

ol ol{
  list-style:lower-alpha;
}

ol{ list-style:decimal; }

ol ol,ol ul,ul ol,ul ul{ margin:4px 0 5px 20px; }

li{ margin-bottom:0.25em; }

ul.square{ list-style:square outside; }

ul.disc{ list-style:disc outside; }

ol.alpha{ list-style:lower-alpha outside; }

.no-bullets{
  list-style:none outside;
  margin-left:0;
}

.inline-list{
  padding:0;
  margin:0
}

.inline-list li{
    display:inline-block;
    margin-bottom:0;
    vertical-align:middle;
  }

table{
  width:100%;
  border-spacing:1px;
  position:relative;
  border:0 none;
  background:{{ settings.color_borders | default: "#1c1d1d" }};
  background:var(--colorBorder);
}

.table-wrapper{
  max-width:100%;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
}

td,th{
  border:0 none;
  text-align:left;
  padding:10px 15px;
  background:{{ settings.color_body_bg | default: "#fff" }};
  background:var(--colorBody)
}

html[dir=rtl] td,html[dir=rtl] th{
    text-align:right
}

th{
  font-weight:700;
}

.table__title,th{
  font-weight:700;
}

.text-link,a{
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  text-decoration:none;
  background:transparent
}

.text-link:hover,a:hover{
    color:{{ settings.color_body_text | default: "#1c1d1d" }};
    color:var(--colorTextBody);
  }

.text-link{
  display:inline;
  border:0 none;
  background:none;
  padding:0;
  margin:0;
}

.rte a,.shopify-email-marketing-confirmation__container a,.shopify-policy__container a{
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorLink);
}

button{
  overflow:visible;
  color:currentColor;
}

button[disabled],html input[disabled]{
  cursor:default;
}

.btn,.rte .btn,.shopify-payment-button .shopify-payment-button__button--unbranded,.spr-container .spr-button,.spr-container .spr-summary-actions a{
  line-height:1.42;
  text-decoration:none;
  text-align:center;
  white-space:normal;

  font-size:calc(var(--typeBaseSize) - 4px);
  font-size:max(calc(var(--typeBaseSize) - 4px), 13px);
  font-family: var(--typeHeaderPrimary),var(--typeHeaderFallback);
  font-weight:700;
  text-transform:uppercase;
  letter-spacing:0.2em;

  display:inline-block;
  padding:11px 20px;
  margin:0;
  width:auto;
  min-width:90px;
  vertical-align:middle;
  cursor:pointer;
  border:1px solid transparent;
  -webkit-user-select:none;
          user-select:none;
  -webkit-appearance:none;
  -moz-appearance:none;
  border-radius:var(--buttonRadius);
  color:{{ settings.color_button_text | default: "#fff" }};
  color:var(--colorBtnPrimaryText);
  background:{{ settings.color_button | default: "#000" }};
  background:var(--colorBtnPrimary)
}

@media only screen and (max-width:768px){

.btn,.rte .btn,.shopify-payment-button .shopify-payment-button__button--unbranded,.spr-container .spr-button,.spr-container .spr-summary-actions a{
    padding:9px 17px;
    font-size:calc(var(--typeBaseSize) - 5px);
    font-size:max(calc(var(--typeBaseSize) - 5px), 11px)
}
  }

.btn:hover,.rte .btn:hover,.shopify-payment-button .shopify-payment-button__button--unbranded:hover,.spr-container .spr-button:hover,.spr-container .spr-summary-actions a:hover{
    color:{{ settings.color_button_text | default: "#fff" }};
    color:var(--colorBtnPrimaryText);
    background-color:{{ settings.color_button | default: "#000" }};
    background-color:var(--colorBtnPrimary);
  }

.btn.disabled,.btn[disabled],.rte .btn.disabled,.rte .btn[disabled],.shopify-payment-button .shopify-payment-button__button--unbranded.disabled,.shopify-payment-button .shopify-payment-button__button--unbranded[disabled],.spr-container .spr-button.disabled,.spr-container .spr-button[disabled],.spr-container .spr-summary-actions a.disabled,.spr-container .spr-summary-actions a[disabled]{
    cursor:default;
    color:#b6b6b6;
    background-color:#f6f6f6
  }

.btn.disabled:hover,.btn[disabled]:hover,.rte .btn.disabled:hover,.rte .btn[disabled]:hover,.shopify-payment-button .shopify-payment-button__button--unbranded.disabled:hover,.shopify-payment-button .shopify-payment-button__button--unbranded[disabled]:hover,.spr-container .spr-button.disabled:hover,.spr-container .spr-button[disabled]:hover,.spr-container .spr-summary-actions a.disabled:hover,.spr-container .spr-summary-actions a[disabled]:hover{
      color:#b6b6b6;
      background-color:#f6f6f6;
    }

[data-button_style=angled] .btn.disabled:after,[data-button_style=angled] .btn.disabled:before,[data-button_style=angled] .btn[disabled]:after,[data-button_style=angled] .btn[disabled]:before,[data-button_style=angled] .rte .btn.disabled:after,[data-button_style=angled] .rte .btn.disabled:before,[data-button_style=angled] .rte .btn[disabled]:after,[data-button_style=angled] .rte .btn[disabled]:before,[data-button_style=angled] .shopify-payment-button .shopify-payment-button__button--unbranded.disabled:after,[data-button_style=angled] .shopify-payment-button .shopify-payment-button__button--unbranded.disabled:before,[data-button_style=angled] .shopify-payment-button .shopify-payment-button__button--unbranded[disabled]:after,[data-button_style=angled] .shopify-payment-button .shopify-payment-button__button--unbranded[disabled]:before,[data-button_style=angled] .spr-container .spr-button.disabled:after,[data-button_style=angled] .spr-container .spr-button.disabled:before,[data-button_style=angled] .spr-container .spr-button[disabled]:after,[data-button_style=angled] .spr-container .spr-button[disabled]:before,[data-button_style=angled] .spr-container .spr-summary-actions a.disabled:after,[data-button_style=angled] .spr-container .spr-summary-actions a.disabled:before,[data-button_style=angled] .spr-container .spr-summary-actions a[disabled]:after,[data-button_style=angled] .spr-container .spr-summary-actions a[disabled]:before{
        background-color:#f6f6f6;
        border-top:1px solid;
        border-top-color:#b6b6b6;
        border-bottom:1px solid;
        border-bottom-color:#b6b6b6;
      }

[data-button_style=angled] .btn,[data-button_style=angled] .rte .btn,[data-button_style=angled] .shopify-payment-button .shopify-payment-button__button--unbranded,[data-button_style=angled] .spr-container .spr-button,[data-button_style=angled] .spr-container .spr-summary-actions a{
    position:relative;
    border:0;
    margin-left:10px;
    margin-right:10px
}

[data-button_style=angled] .btn:after,[data-button_style=angled] .btn:before,[data-button_style=angled] .rte .btn:after,[data-button_style=angled] .rte .btn:before,[data-button_style=angled] .shopify-payment-button .shopify-payment-button__button--unbranded:after,[data-button_style=angled] .shopify-payment-button .shopify-payment-button__button--unbranded:before,[data-button_style=angled] .spr-container .spr-button:after,[data-button_style=angled] .spr-container .spr-button:before,[data-button_style=angled] .spr-container .spr-summary-actions a:after,[data-button_style=angled] .spr-container .spr-summary-actions a:before{
      content:"";
      position:absolute;
      display:block;
      top:0;
      bottom:0;
      width:20px;
      transform:skewX(-12deg);
      background-color:inherit;
    }

[data-button_style=angled] .btn:before,[data-button_style=angled] .rte .btn:before,[data-button_style=angled] .shopify-payment-button .shopify-payment-button__button--unbranded:before,[data-button_style=angled] .spr-container .spr-button:before,[data-button_style=angled] .spr-container .spr-summary-actions a:before{
      left:-6px;
    }

[data-button_style=angled] .btn:after,[data-button_style=angled] .rte .btn:after,[data-button_style=angled] .shopify-payment-button .shopify-payment-button__button--unbranded:after,[data-button_style=angled] .spr-container .spr-button:after,[data-button_style=angled] .spr-container .spr-summary-actions a:after{
      right:-6px;
    }

[data-button_style=angled] .btn.btn--small:before,[data-button_style=angled] .rte .btn.btn--small:before,[data-button_style=angled] .shopify-payment-button .shopify-payment-button__button--unbranded.btn--small:before,[data-button_style=angled] .spr-container .spr-button.btn--small:before,[data-button_style=angled] .spr-container .spr-summary-actions a.btn--small:before{
        left:-5px;
      }

[data-button_style=angled] .btn.btn--small:after,[data-button_style=angled] .rte .btn.btn--small:after,[data-button_style=angled] .shopify-payment-button .shopify-payment-button__button--unbranded.btn--small:after,[data-button_style=angled] .spr-container .spr-button.btn--small:after,[data-button_style=angled] .spr-container .spr-summary-actions a.btn--small:after{
        right:-5px;
      }

[data-button_style=square] .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static),[data-button_style=square] .rte .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static),[data-button_style=square] .shopify-payment-button .shopify-payment-button__button--unbranded:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static),[data-button_style=square] .spr-container .spr-button:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static),[data-button_style=square] .spr-container .spr-summary-actions a:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static),[data-button_style^=round] .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static),[data-button_style^=round] .rte .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static),[data-button_style^=round] .shopify-payment-button .shopify-payment-button__button--unbranded:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static),[data-button_style^=round] .spr-container .spr-button:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static),[data-button_style^=round] .spr-container .spr-summary-actions a:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static){
      position:relative;
      overflow:hidden;
      transition:background 0.2s ease 0s
    }

[data-button_style=square] .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):after,[data-button_style=square] .rte .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):after,[data-button_style=square] .shopify-payment-button .shopify-payment-button__button--unbranded:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):after,[data-button_style=square] .spr-container .spr-button:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):after,[data-button_style=square] .spr-container .spr-summary-actions a:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):after,[data-button_style^=round] .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):after,[data-button_style^=round] .rte .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):after,[data-button_style^=round] .shopify-payment-button .shopify-payment-button__button--unbranded:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):after,[data-button_style^=round] .spr-container .spr-button:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):after,[data-button_style^=round] .spr-container .spr-summary-actions a:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):after{
        content:"";
        position:absolute;
        top:0;
        left:150%;
        width:200%;
        height:100%;
        transform:skewX(-20deg);
        background-image:linear-gradient(90deg,transparent, hsla(0, 0%, 100%, 0.25),transparent);
      }

[data-button_style=square] .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover:after,[data-button_style=square] .rte .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover:after,[data-button_style=square] .shopify-payment-button .shopify-payment-button__button--unbranded:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover:after,[data-button_style=square] .spr-container .spr-button:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover:after,[data-button_style=square] .spr-container .spr-summary-actions a:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover:after,[data-button_style^=round] .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover:after,[data-button_style^=round] .rte .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover:after,[data-button_style^=round] .shopify-payment-button .shopify-payment-button__button--unbranded:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover:after,[data-button_style^=round] .spr-container .spr-button:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover:after,[data-button_style^=round] .spr-container .spr-summary-actions a:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover:after{
        animation:shine 0.75s cubic-bezier(0.01, 0.56, 1, 1);
      }

[data-button_style=square] .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover,[data-button_style=square] .rte .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover,[data-button_style=square] .shopify-payment-button .shopify-payment-button__button--unbranded:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover,[data-button_style=square] .spr-container .spr-button:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover,[data-button_style=square] .spr-container .spr-summary-actions a:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover,[data-button_style^=round] .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover,[data-button_style^=round] .rte .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover,[data-button_style^=round] .shopify-payment-button .shopify-payment-button__button--unbranded:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover,[data-button_style^=round] .spr-container .spr-button:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover,[data-button_style^=round] .spr-container .spr-summary-actions a:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):hover{
        background:{{ settings.color_button | default: "#000" | color_lighten: 10 }};
        background:var(--colorBtnPrimaryLight);
        transition-delay:0.25s;
      }

[data-button_style=square] .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):active,[data-button_style=square] .rte .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):active,[data-button_style=square] .shopify-payment-button .shopify-payment-button__button--unbranded:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):active,[data-button_style=square] .spr-container .spr-button:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):active,[data-button_style=square] .spr-container .spr-summary-actions a:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):active,[data-button_style^=round] .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):active,[data-button_style^=round] .rte .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):active,[data-button_style^=round] .shopify-payment-button .shopify-payment-button__button--unbranded:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):active,[data-button_style^=round] .spr-container .spr-button:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):active,[data-button_style^=round] .spr-container .spr-summary-actions a:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):not(.btn--body):not(.btn--static):active{
        background:{{ settings.color_button | default: "#000" }};
        background:var(--colorBtnPrimary);
        transition-delay:0s;
      }

.shopify-payment-button .shopify-payment-button__button--unbranded:hover:not([disabled]){
  color:{{ settings.color_button_text | default: "#fff" }};
  color:var(--colorBtnPrimaryText);
  background-color:{{ settings.color_button | default: "#000" }};
  background-color:var(--colorBtnPrimary);
}

.shopify-payment-button__more-options{
  color:inherit;
}

.btn--secondary,.rte .btn--secondary{
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  border:1px solid;
  border-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-color:var(--colorBorder);
  background-color:transparent
}

[data-button_style=angled] .btn--secondary,[data-button_style=angled] .rte .btn--secondary{
    border-left:0;
    border-right:0;
    border-top:1px solid;
    border-bottom:1px solid;
    border-top-color:{{ settings.color_borders | default: "#1c1d1d" }};
    border-top-color:var(--colorBorder);
    border-bottom-color:{{ settings.color_borders | default: "#1c1d1d" }};
    border-bottom-color:var(--colorBorder)
}

[data-button_style=angled] .btn--secondary:after,[data-button_style=angled] .btn--secondary:before,[data-button_style=angled] .rte .btn--secondary:after,[data-button_style=angled] .rte .btn--secondary:before{
      background-color:transparent;
      top:-1px;
      bottom:-1px;
    }

[data-button_style=angled] .btn--secondary:before,[data-button_style=angled] .rte .btn--secondary:before{
      border-left:1px solid;
      border-left-color:{{ settings.color_borders | default: "#1c1d1d" }};
      border-left-color:var(--colorBorder);
      border-bottom:1px solid;
      border-bottom-color:{{ settings.color_borders | default: "#1c1d1d" }};
      border-bottom-color:var(--colorBorder);
    }

[data-button_style=angled] .btn--secondary:after,[data-button_style=angled] .rte .btn--secondary:after{
      border-top:1px solid;
      border-top-color:{{ settings.color_borders | default: "#1c1d1d" }};
      border-top-color:var(--colorBorder);
      border-right:1px solid;
      border-right-color:{{ settings.color_borders | default: "#1c1d1d" }};
      border-right-color:var(--colorBorder);
    }

.btn--secondary:hover,.rte .btn--secondary:hover{
    color:{{ settings.color_body_text | default: "#1c1d1d" }};
    color:var(--colorTextBody);
    border-color:{{ settings.color_body_text | default: "#1c1d1d" }};
    border-color:var(--colorTextBody);
    background-color:transparent;
    transition:border 0.3s ease
  }

.btn--secondary:hover:before,.rte .btn--secondary:hover:before{
      border-color:{{ settings.color_body_text | default: "#1c1d1d" }};
      border-color:var(--colorTextBody);
      transition:border 0.3s ease;
    }

.btn--secondary:hover:after,.rte .btn--secondary:hover:after{
      border-color:{{ settings.color_body_text | default: "#1c1d1d" }};
      border-color:var(--colorTextBody);
      transition:border 0.3s ease;
    }

.btn--tertiary,.rte .btn--tertiary{
  font-weight:400;
  text-transform:none;
  letter-spacing:normal;
  background-color:transparent;
  border:1px solid;
  border-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-color:var(--colorBorder);
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  padding:8px 10px;
  white-space:nowrap
}

.btn--tertiary:hover,.rte .btn--tertiary:hover{
    background-color:transparent;
    color:{{ settings.color_body_text | default: "#1c1d1d" }};
    color:var(--colorTextBody);
  }

.btn--tertiary.disabled,.btn--tertiary[disabled],.rte .btn--tertiary.disabled,.rte .btn--tertiary[disabled]{
    cursor:default;
    color:#b6b6b6;
    background-color:#f6f6f6;
    border-color:#b6b6b6;
  }

[data-button_style=angled] .btn--tertiary,[data-button_style=angled] .rte .btn--tertiary{
    margin-left:0;
    margin-right:0
}

[data-button_style=angled] .btn--tertiary:after,[data-button_style=angled] .btn--tertiary:before,[data-button_style=angled] .rte .btn--tertiary:after,[data-button_style=angled] .rte .btn--tertiary:before{
      content:none;
    }

.btn--tertiary-active{
  color:{{ settings.color_button_text | default: "#fff" }};
  color:var(--colorBtnPrimaryText);
  background:{{ settings.color_button | default: "#000" }};
  background:var(--colorBtnPrimary);
  border-color:{{ settings.color_button | default: "#000" }};
  border-color:var(--colorBtnPrimary)
}

.btn--tertiary-active:hover{
    color:{{ settings.color_button_text | default: "#fff" }};
    color:var(--colorBtnPrimaryText);
    background:{{ settings.color_button | default: "#000" }};
    background:var(--colorBtnPrimary);
  }

.btn--body{
  border:1px solid;
  border-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-color:var(--colorBorder);
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody);
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody)
}

.btn--body:active,.btn--body:hover{
    border:1px solid;
    border-color:{{ settings.color_borders | default: "#1c1d1d" }};
    border-color:var(--colorBorder);
    background-color:{{ settings.color_body_bg | default: "#fff" }};
    background-color:var(--colorBody);
    color:{{ settings.color_body_text | default: "#1c1d1d" }};
    color:var(--colorTextBody);
  }

.btn--circle{
  padding:10px;
  border-radius:50%;
  min-width:0;
  line-height:1
}

.btn--circle .icon{
    width:20px;
    height:20px;
  }

.btn--circle:after,.btn--circle:before{
    content:none;
    background:none;
    width:auto;
  }

.btn--circle.btn--large .icon{
    width:30px;
    height:30px;
  }

.btn--circle.btn--large{
    padding:15px;
  }

[data-button_style=angled] .btn--circle{
    border:1px solid;
    border-color:{{ settings.color_borders | default: "#1c1d1d" }};
    border-color:var(--colorBorder)
}

[data-button_style=angled] .btn--circle:after,[data-button_style=angled] .btn--circle:before{
      display:none;
    }

.btn--small{
  padding:8px 14px;
  background-position:150% 45%;
  min-width:90px;
  font-size:calc(var(--typeBaseSize) - 6px);
  font-size:max(calc(var(--typeBaseSize) - 6px), 12px)
}

[data-button_style=angled] .btn--small{
    margin-left:10px;
    padding-left:16px;
    padding-right:16px
}

.btn--secondary.btn--small{
  font-weight:400;
}

.btn--large{
  padding:15px 20px;
}

.btn--full{
  width:100%;
  padding:11px 20px;
  transition:none;
  padding:13px 20px
}

[data-button_style=angled] .btn--full{
    max-width:94%
}

[data-button_style=angled] .shopify-payment-button .shopify-payment-button__button--unbranded{
    max-width:94%;
}

.btn--inverse{
  background-color:transparent;
  color:#fff;
  border:2px solid #fff
}

.btn--inverse:focus,.btn--inverse:hover{
    background-color:transparent;
  }

[data-button_style=angled] .btn--inverse{
    border-left:0;
    border-right:0;
    border-top:2px solid;
    border-bottom:2px solid
}

[data-button_style=angled] .btn--inverse:after,[data-button_style=angled] .btn--inverse:before{
      background-color:transparent;
      border-color:#fff;
      top:-2px;
      bottom:-2px;
    }

[data-button_style=angled] .btn--inverse:before{
      border-left:2px solid;
      border-bottom:2px solid;
    }

[data-button_style=angled] .btn--inverse:after{
      border-top:2px solid;
      border-right:2px solid;
    }

.hero__link .btn--inverse{
    color:{{ settings.color_image_text | default: "#fff" }};
    color:var(--colorHeroText);
    border-color:{{ settings.color_image_text | default: "#fff" }};
    border-color:var(--colorHeroText)
}

[data-button_style=angled] .hero__link .btn--inverse:before{
        border-color:{{ settings.color_image_text | default: "#fff" }};
        border-color:var(--colorHeroText);
      }

[data-button_style=angled] .hero__link .btn--inverse:after{
        border-color:{{ settings.color_image_text | default: "#fff" }};
        border-color:var(--colorHeroText);
      }

.btn--loading{
  position:relative;
  text-indent:-9999px;
  background-color:{{ settings.color_button | default: "#000" | color_darken: 5 }};
  background-color:var(--colorBtnPrimaryDim);
  color:{{ settings.color_button | default: "#000" | color_darken: 5 }};
  color:var(--colorBtnPrimaryDim)
}

.btn--loading:active,.btn--loading:hover{
    background-color:{{ settings.color_button | default: "#000" | color_darken: 5 }};
    background-color:var(--colorBtnPrimaryDim);
    color:{{ settings.color_button | default: "#000" | color_darken: 5 }};
    color:var(--colorBtnPrimaryDim);
  }

.btn--loading:before{
    content:"";
    display:block;
    width:24px;
    height:24px;
    position:absolute;
    left:50%;
    top:50%;
    margin-left:-12px;
    margin-top:-12px;
    border-radius:50%;
    border:3px solid;
    border-color:{{ settings.color_button_text | default: "#fff" }};
    border-color:var(--colorBtnPrimaryText);
    border-top-color:transparent;
    animation:spin 1s linear infinite;
  }

[data-button_style=angled] .btn--loading:before{
      left:50%;
      top:50%;
      width:24px;
      height:24px;
      transform:none;
      border:3px solid;
      border-color:{{ settings.color_button_text | default: "#fff" }} !important;
      border-color:var(--colorBtnPrimaryText) !important;
      border-top-color:transparent !important;
    }

[data-button_style=angled] .btn--loading:after{
      background-color:{{ settings.color_button | default: "#000" | color_darken: 5 }};
      background-color:var(--colorBtnPrimaryDim);
    }

[data-button_style=angled] .btn--loading.btn--secondary:after{
      bottom:1px;
    }

.btn--loading.btn--secondary{
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  background:transparent
}

.btn--loading.btn--secondary:before{
    border-color:{{ settings.color_body_text | default: "#1c1d1d" }};
    border-color:var(--colorTextBody);
    border-top-color:transparent;
  }

.return-link{
  text-align:center;
  padding:15px 25px;
  margin-top:50px
}

@media only screen and (max-width:768px){

.return-link{
    padding:22px 17px;
    width:100%
}

[data-button_style=angled] .return-link{
      width:90%
}
  }

.return-link .icon{
    width:20px;
    margin-right:8px;
  }

.collapsible-trigger-btn{
  text-align:left
}

[data-center-text=true] .collapsible-trigger-btn{
    text-align:left
}

.collapsible-trigger-btn{
  text-transform:uppercase;
  font-weight:700;
  letter-spacing:0.2em;
  font-size:0.8em;
  display:block;
  width:100%;
  padding:15px 10px 15px 0
}

.collection-sidebar__group .collapsible-trigger-btn{
    text-align:left
}

.collapsible-trigger-btn--borders{
  border:1px solid;
  border-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-color:var(--colorBorder);
  border-bottom:0;
  padding:12px
}

.collapsible-trigger-btn--borders .collapsible-trigger__icon{
    right:12px;
  }

@media only screen and (min-width:769px){

.collapsible-trigger-btn--borders{
    padding:15px
}

    .collapsible-trigger-btn--borders .collapsible-trigger__icon{
      right:15px;
    }
  }

.collapsible-content+.collapsible-trigger-btn--borders{
    margin-top:-1px
}

.collapsible-trigger-btn--borders+.collapsible-content .collapsible-content__inner{
    border:1px solid;
    border-color:{{ settings.color_borders | default: "#1c1d1d" }};
    border-color:var(--colorBorder);
    border-top:0;
    padding:0 20px 20px;
    font-size:calc(var(--typeBaseSize)*0.92);
  }

.collapsible-trigger-btn--borders+.collapsible-content--expanded{
    margin-bottom:30px
  }

.collapsible-trigger-btn--borders+.collapsible-content--expanded:last-child{
      margin-bottom:-1px;
    }

.collapsible-trigger-btn--borders-top{
  border-top:1px solid;
  border-top-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-top-color:var(--colorBorder);
}

.shopify-payment-button{
  margin-top:10px;
}

.shopify-payment-button .shopify-payment-button__button--unbranded{
  display:block;
  width:100%;
  transition:none;
}

.payment-buttons .add-to-cart,.payment-buttons .shopify-payment-button,.payment-buttons .shopify-payment-button__button--unbranded{
    min-height:50px;
  }

.add-to-cart.btn--secondary{
  border:1px solid;
  border-color:{{ settings.color_body_text | default: "#1c1d1d" }};
  border-color:var(--colorTextBody)
}

[data-button_style=angled] .add-to-cart.btn--secondary{
    border-left:0;
    border-right:0
}

[data-button_style=angled] .add-to-cart.btn--secondary:after,[data-button_style=angled] .add-to-cart.btn--secondary:before{
      border-color:{{ settings.color_body_text | default: "#1c1d1d" }};
      border-color:var(--colorTextBody);
    }

.add-to-cart.btn--secondary.disabled,.add-to-cart.btn--secondary[disabled]{
    border-color:#b6b6b6
  }

[data-button_style=angled] .add-to-cart.btn--secondary.disabled:after,[data-button_style=angled] .add-to-cart.btn--secondary.disabled:before,[data-button_style=angled] .add-to-cart.btn--secondary[disabled]:after,[data-button_style=angled] .add-to-cart.btn--secondary[disabled]:before{
        border-color:#b6b6b6;
      }

.shopify-payment-button__button--hidden{
  display:none !important;
}

img{
  border:0 none;
}

svg:not(:root){
  overflow:hidden;
}

iframe,img{
  max-width:100%;
}

img[data-sizes=auto]{
  display:block;
  width:100%;
}

.lazyload{
  opacity:0
}

.no-js .lazyload{
    display:none
}

.lazyloaded{
  opacity:1;
  transition:opacity 0.4s ease;
}

.video-wrapper{
  position:relative;
  overflow:hidden;
  max-width:100%;
  padding-bottom:56.25%;
  height:0;
  height:auto
}

.video-wrapper iframe,.video-wrapper video{
    position:absolute;
    top:0;
    left:0;
    width:100%;
    height:100%;
  }

.video-wrapper--modal{
  width:1000px;
}

.grid__image-ratio{
  position:relative;
  background-color:{{ settings.color_small_image_bg | default: "#eee" }};
  background-color:var(--colorSmallImageBg)
}

.grid__image-ratio img{
    opacity:0;
    position:absolute;
    top:0;
    left:0;
    width:100%;
    height:100%;
    -o-object-fit:cover;
       object-fit:cover
  }

.grid__image-ratio img.lazyloaded{
      opacity:1;
      animation:fade-in 1s cubic-bezier(0.26, 0.54, 0.32, 1) 0s forwards;
      transition:none;
    }

.grid__image-ratio img.grid__image-contain{
      -o-object-fit:contain;
         object-fit:contain;
    }

.grid__image-ratio:before{
    content:"";
    display:block;
    height:0;
    width:100%;
  }

.grid__image-ratio .placeholder-svg{
    position:absolute;
    top:0;
    right:0;
    bottom:0;
    left:0;
  }

.grid__image-ratio--object{
  opacity:1;
}

.grid__image-ratio--wide:before{
  padding-bottom:56.25%;
}

.grid__image-ratio--landscape:before{
  padding-bottom:75%;
}

.grid__image-ratio--square:before{
  padding-bottom:100%;
}

.grid__image-ratio--portrait:before{
  padding-bottom:150%;
}

.image-fit{
  position:relative;
  width:100%;
  height:100%;
  -o-object-fit:cover;
     object-fit:cover;
  font-family:"object-fit: cover";
  z-index:1;
}

.parallax-container{
  position:absolute;
  top:-30%;
  left:0;
  height:160%;
  width:100%;
}

.parallax-image{
  position:absolute;
  top:-5%;
  left:0;
  width:100%;
  height:110%
}

.parallax-image img{
    display:block;
    -o-object-fit:cover;
       object-fit:cover;
    width:100%;
    height:100%;
  }

form{
  margin:0;
}

[data-center-text=true] .form-vertical{
    text-align:center
}

.form-vertical{
  margin-bottom:15px
}

.form-vertical label{
    text-align:left;
  }

.inline{
  display:inline;
}

@media only screen and (max-width:959px){
  input,select,textarea{
    font-size:16px !important;
  }
}

button,input,textarea{
  -webkit-appearance:none;
  -moz-appearance:none;
}

button{
  background:none;
  border:none;
  display:inline-block;
  cursor:pointer;
}

fieldset{
  border:1px solid;
  border-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-color:var(--colorBorder);
  padding:15px;
}

legend{
  border:0;
  padding:0;
}

button,input[type=submit]{
  cursor:pointer;
}

input,select,textarea{
  border:1px solid;
  border-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-color:var(--colorBorder);
  max-width:100%;
  padding:8px 10px;
  border-radius:7px
}

input.disabled,input[disabled],select.disabled,select[disabled],textarea.disabled,textarea[disabled]{
    cursor:default;
    border-color:#b6b6b6;
  }

input.input-full,select.input-full,textarea.input-full{
    width:100%;
  }

textarea{
  min-height:100px;
}

input[type=checkbox],input[type=radio]{
  margin:0 10px 0 0;
  padding:0;
  width:auto;
}

input[type=checkbox]{
  -webkit-appearance:checkbox;
  -moz-appearance:checkbox;
}

input[type=radio]{
  -webkit-appearance:radio;
  -moz-appearance:radio;
}

input[type=image]{
  padding-left:0;
  padding-right:0;
}

.faux-select,select{
  -webkit-appearance:none;
          appearance:none;
  background-color:transparent;
  padding-right:28px;
  text-indent:0.01px;
  text-overflow:"";
  cursor:pointer;
  color:inherit;
}

select{
  background-position:100%;
  background-image:url({{ "ico-select.svg" | asset_url | split: '?' | first }});
  background-repeat:no-repeat;
  background-position:right 10px center;
  background-size:11px
}

.is-light select{
    background-image:url({{ "ico-select-white.svg" | asset_url | split: '?' | first }})
}

.faux-select .icon{
  position:absolute;
  right:10px;
  top:50%;
  transform:translateY(-50%);
  width:11px;
  height:11px;
}

optgroup{
  font-weight:700;
}

option{
  color:#000;
  background-color:#fff
}

option[disabled]{
    color:#ccc;
  }

select::-ms-expand{
  display:none;
}

.hidden-label{
  border:0;
  clip:rect(0 0 0 0);
  height:1px;
  margin:-1px;
  overflow:hidden;
  padding:0;
  position:absolute;
  width:1px;
}

label[for]{
  cursor:pointer;
}

.form-vertical input,.form-vertical select,.form-vertical textarea{
    display:block;
    margin-bottom:30px;
  }

.form-vertical .btn,.form-vertical input[type=checkbox],.form-vertical input[type=radio]{
    display:inline-block;
  }

.form-vertical .btn:not(:last-child){
    margin-bottom:30px;
  }

small{
  display:block;
}

input.error,textarea.error{
    border-color:#d02e2e;
    background-color:#fff6f6;
    color:#d02e2e;
  }

label.error{
  color:#d02e2e;
}

.selector-wrapper label{
    margin-right:10px;
  }

.selector-wrapper+.selector-wrapper{
    margin-top:15px;
  }

.input-group{
  display:flex
}

.input-group .input-group-btn:first-child .btn,.input-group .input-group-field:first-child,.input-group input[type=hidden]:first-child+.input-group-field{
    border-radius:0 0 0 0;
  }

.input-group .input-group-field:last-child{
    border-radius:0 0 0 0;
  }

.input-group .input-group-btn:first-child .btn,.input-group input[type=hidden]:first-child+.input-group-btn .btn{
    border-radius:var(--buttonRadius) 0 0 var(--buttonRadius);
  }

.input-group .input-group-btn:last-child .btn{
    border-radius:0 var(--buttonRadius) var(--buttonRadius) 0;
  }

.input-group input::-moz-focus-inner{
      border:0;
      padding:0;
      margin-top:-1px;
      margin-bottom:-1px;
    }

.input-group-field{
  flex:1 1 auto;
  margin:0;
  min-width:0;
}

.input-group-btn{
  flex:0 1 auto;
  margin:0;
  display:flex
}

.input-group-btn .icon{
    vertical-align:baseline;
    vertical-align:initial;
  }

[data-button_style=angled] .input-group-btn{
    position:relative;
    left:-8px
}

.icon{
  display:inline-block;
  width:20px;
  height:20px;
  vertical-align:middle;
  fill:currentColor
}

.no-svg .icon{
    display:none
}

.icon--full-color{
  fill:initial;
}

svg.icon:not(.icon--full-color) circle,svg.icon:not(.icon--full-color) ellipse,svg.icon:not(.icon--full-color) g,svg.icon:not(.icon--full-color) line,svg.icon:not(.icon--full-color) path,svg.icon:not(.icon--full-color) polygon,svg.icon:not(.icon--full-color) polyline,svg.icon:not(.icon--full-color) rect,symbol.icon:not(.icon--full-color) circle,symbol.icon:not(.icon--full-color) ellipse,symbol.icon:not(.icon--full-color) g,symbol.icon:not(.icon--full-color) line,symbol.icon:not(.icon--full-color) path,symbol.icon:not(.icon--full-color) polygon,symbol.icon:not(.icon--full-color) polyline,symbol.icon:not(.icon--full-color) rect{
      fill:inherit;
      stroke:inherit;
    }

.icon-bag-minimal circle,.icon-bag-minimal ellipse,.icon-bag-minimal g,.icon-bag-minimal line,.icon-bag-minimal path,.icon-bag-minimal polygon,.icon-bag-minimal polyline,.icon-bag-minimal rect,.icon-bag circle,.icon-bag ellipse,.icon-bag g,.icon-bag line,.icon-bag path,.icon-bag polygon,.icon-bag polyline,.icon-bag rect,.icon-cart circle,.icon-cart ellipse,.icon-cart g,.icon-cart line,.icon-cart path,.icon-cart polygon,.icon-cart polyline,.icon-cart rect,.icon-chevron-down circle,.icon-chevron-down ellipse,.icon-chevron-down g,.icon-chevron-down line,.icon-chevron-down path,.icon-chevron-down polygon,.icon-chevron-down polyline,.icon-chevron-down rect,.icon-circle-checkmark circle,.icon-circle-checkmark ellipse,.icon-circle-checkmark g,.icon-circle-checkmark line,.icon-circle-checkmark path,.icon-circle-checkmark polygon,.icon-circle-checkmark polyline,.icon-circle-checkmark rect,.icon-close circle,.icon-close ellipse,.icon-close g,.icon-close line,.icon-close path,.icon-close polygon,.icon-close polyline,.icon-close rect,.icon-email circle,.icon-email ellipse,.icon-email g,.icon-email line,.icon-email path,.icon-email polygon,.icon-email polyline,.icon-email rect,.icon-filter circle,.icon-filter ellipse,.icon-filter g,.icon-filter line,.icon-filter path,.icon-filter polygon,.icon-filter polyline,.icon-filter rect,.icon-gift circle,.icon-gift ellipse,.icon-gift g,.icon-gift line,.icon-gift path,.icon-gift polygon,.icon-gift polyline,.icon-gift rect,.icon-globe circle,.icon-globe ellipse,.icon-globe g,.icon-globe line,.icon-globe path,.icon-globe polygon,.icon-globe polyline,.icon-globe rect,.icon-hamburger circle,.icon-hamburger ellipse,.icon-hamburger g,.icon-hamburger line,.icon-hamburger path,.icon-hamburger polygon,.icon-hamburger polyline,.icon-hamburger rect,.icon-heart circle,.icon-heart ellipse,.icon-heart g,.icon-heart line,.icon-heart path,.icon-heart polygon,.icon-heart polyline,.icon-heart rect,.icon-leaf circle,.icon-leaf ellipse,.icon-leaf g,.icon-leaf line,.icon-leaf path,.icon-leaf polygon,.icon-leaf polyline,.icon-leaf rect,.icon-lock circle,.icon-lock ellipse,.icon-lock g,.icon-lock line,.icon-lock path,.icon-lock polygon,.icon-lock polyline,.icon-lock rect,.icon-package circle,.icon-package ellipse,.icon-package g,.icon-package line,.icon-package path,.icon-package polygon,.icon-package polyline,.icon-package rect,.icon-phone circle,.icon-phone ellipse,.icon-phone g,.icon-phone line,.icon-phone path,.icon-phone polygon,.icon-phone polyline,.icon-phone rect,.icon-ribbon circle,.icon-ribbon ellipse,.icon-ribbon g,.icon-ribbon line,.icon-ribbon path,.icon-ribbon polygon,.icon-ribbon polyline,.icon-ribbon rect,.icon-search circle,.icon-search ellipse,.icon-search g,.icon-search line,.icon-search path,.icon-search polygon,.icon-search polyline,.icon-search rect,.icon-shield circle,.icon-shield ellipse,.icon-shield g,.icon-shield line,.icon-shield path,.icon-shield polygon,.icon-shield polyline,.icon-shield rect,.icon-size-chart circle,.icon-size-chart ellipse,.icon-size-chart g,.icon-size-chart line,.icon-size-chart path,.icon-size-chart polygon,.icon-size-chart polyline,.icon-size-chart rect,.icon-tag circle,.icon-tag ellipse,.icon-tag g,.icon-tag line,.icon-tag path,.icon-tag polygon,.icon-tag polyline,.icon-tag rect,.icon-tcwi-bills circle,.icon-tcwi-bills ellipse,.icon-tcwi-bills g,.icon-tcwi-bills line,.icon-tcwi-bills path,.icon-tcwi-bills polygon,.icon-tcwi-bills polyline,.icon-tcwi-bills rect,.icon-tcwi-calendar circle,.icon-tcwi-calendar ellipse,.icon-tcwi-calendar g,.icon-tcwi-calendar line,.icon-tcwi-calendar path,.icon-tcwi-calendar polygon,.icon-tcwi-calendar polyline,.icon-tcwi-calendar rect,.icon-tcwi-cart circle,.icon-tcwi-cart ellipse,.icon-tcwi-cart g,.icon-tcwi-cart line,.icon-tcwi-cart path,.icon-tcwi-cart polygon,.icon-tcwi-cart polyline,.icon-tcwi-cart rect,.icon-tcwi-charity circle,.icon-tcwi-charity ellipse,.icon-tcwi-charity g,.icon-tcwi-charity line,.icon-tcwi-charity path,.icon-tcwi-charity polygon,.icon-tcwi-charity polyline,.icon-tcwi-charity rect,.icon-tcwi-chat circle,.icon-tcwi-chat ellipse,.icon-tcwi-chat g,.icon-tcwi-chat line,.icon-tcwi-chat path,.icon-tcwi-chat polygon,.icon-tcwi-chat polyline,.icon-tcwi-chat rect,.icon-tcwi-gears circle,.icon-tcwi-gears ellipse,.icon-tcwi-gears g,.icon-tcwi-gears line,.icon-tcwi-gears path,.icon-tcwi-gears polygon,.icon-tcwi-gears polyline,.icon-tcwi-gears rect,.icon-tcwi-gift circle,.icon-tcwi-gift ellipse,.icon-tcwi-gift g,.icon-tcwi-gift line,.icon-tcwi-gift path,.icon-tcwi-gift polygon,.icon-tcwi-gift polyline,.icon-tcwi-gift rect,.icon-tcwi-globe circle,.icon-tcwi-globe ellipse,.icon-tcwi-globe g,.icon-tcwi-globe line,.icon-tcwi-globe path,.icon-tcwi-globe polygon,.icon-tcwi-globe polyline,.icon-tcwi-globe rect,.icon-tcwi-open-envelope circle,.icon-tcwi-open-envelope ellipse,.icon-tcwi-open-envelope g,.icon-tcwi-open-envelope line,.icon-tcwi-open-envelope path,.icon-tcwi-open-envelope polygon,.icon-tcwi-open-envelope polyline,.icon-tcwi-open-envelope rect,.icon-tcwi-package circle,.icon-tcwi-package ellipse,.icon-tcwi-package g,.icon-tcwi-package line,.icon-tcwi-package path,.icon-tcwi-package polygon,.icon-tcwi-package polyline,.icon-tcwi-package rect,.icon-tcwi-phone circle,.icon-tcwi-phone ellipse,.icon-tcwi-phone g,.icon-tcwi-phone line,.icon-tcwi-phone path,.icon-tcwi-phone polygon,.icon-tcwi-phone polyline,.icon-tcwi-phone rect,.icon-tcwi-plant circle,.icon-tcwi-plant ellipse,.icon-tcwi-plant g,.icon-tcwi-plant line,.icon-tcwi-plant path,.icon-tcwi-plant polygon,.icon-tcwi-plant polyline,.icon-tcwi-plant rect,.icon-tcwi-recycle circle,.icon-tcwi-recycle ellipse,.icon-tcwi-recycle g,.icon-tcwi-recycle line,.icon-tcwi-recycle path,.icon-tcwi-recycle polygon,.icon-tcwi-recycle polyline,.icon-tcwi-recycle rect,.icon-tcwi-ribbon circle,.icon-tcwi-ribbon ellipse,.icon-tcwi-ribbon g,.icon-tcwi-ribbon line,.icon-tcwi-ribbon path,.icon-tcwi-ribbon polygon,.icon-tcwi-ribbon polyline,.icon-tcwi-ribbon rect,.icon-tcwi-sales-tag circle,.icon-tcwi-sales-tag ellipse,.icon-tcwi-sales-tag g,.icon-tcwi-sales-tag line,.icon-tcwi-sales-tag path,.icon-tcwi-sales-tag polygon,.icon-tcwi-sales-tag polyline,.icon-tcwi-sales-tag rect,.icon-tcwi-shield circle,.icon-tcwi-shield ellipse,.icon-tcwi-shield g,.icon-tcwi-shield line,.icon-tcwi-shield path,.icon-tcwi-shield polygon,.icon-tcwi-shield polyline,.icon-tcwi-shield rect,.icon-tcwi-stopwatch circle,.icon-tcwi-stopwatch ellipse,.icon-tcwi-stopwatch g,.icon-tcwi-stopwatch line,.icon-tcwi-stopwatch path,.icon-tcwi-stopwatch polygon,.icon-tcwi-stopwatch polyline,.icon-tcwi-stopwatch rect,.icon-tcwi-store circle,.icon-tcwi-store ellipse,.icon-tcwi-store g,.icon-tcwi-store line,.icon-tcwi-store path,.icon-tcwi-store polygon,.icon-tcwi-store polyline,.icon-tcwi-store rect,.icon-tcwi-thumbs-up circle,.icon-tcwi-thumbs-up ellipse,.icon-tcwi-thumbs-up g,.icon-tcwi-thumbs-up line,.icon-tcwi-thumbs-up path,.icon-tcwi-thumbs-up polygon,.icon-tcwi-thumbs-up polyline,.icon-tcwi-thumbs-up rect,.icon-tcwi-trophy circle,.icon-tcwi-trophy ellipse,.icon-tcwi-trophy g,.icon-tcwi-trophy line,.icon-tcwi-trophy path,.icon-tcwi-trophy polygon,.icon-tcwi-trophy polyline,.icon-tcwi-trophy rect,.icon-tcwi-truck circle,.icon-tcwi-truck ellipse,.icon-tcwi-truck g,.icon-tcwi-truck line,.icon-tcwi-truck path,.icon-tcwi-truck polygon,.icon-tcwi-truck polyline,.icon-tcwi-truck rect,.icon-tcwi-wallet circle,.icon-tcwi-wallet ellipse,.icon-tcwi-wallet g,.icon-tcwi-wallet line,.icon-tcwi-wallet path,.icon-tcwi-wallet polygon,.icon-tcwi-wallet polyline,.icon-tcwi-wallet rect,.icon-truck circle,.icon-truck ellipse,.icon-truck g,.icon-truck line,.icon-truck path,.icon-truck polygon,.icon-truck polyline,.icon-truck rect,.icon-user circle,.icon-user ellipse,.icon-user g,.icon-user line,.icon-user path,.icon-user polygon,.icon-user polyline,.icon-user rect{
    fill:none !important;
    stroke-width:var(--iconWeight);
    stroke:currentColor !important;
    stroke-linecap:var(--iconLinecaps);
    stroke-linejoin:var(--iconLinecaps);
  }

.icon-cart circle{
  fill:currentColor !important;
}

.icon__fallback-text{
  border:0;
  clip:rect(0 0 0 0);
  height:1px;
  margin:-1px;
  overflow:hidden;
  padding:0;
  position:absolute;
  width:1px;
}

.js-drawer-open{
  overflow:hidden;
}

.drawer{
  display:none;
  position:fixed;
  overflow:hidden;
  -webkit-overflow-scrolling:touch;
  top:0;
  bottom:0;
  max-width:95%;
  z-index:30;
  color:{{ settings.color_drawer_text | default: "#fff" }};
  color:var(--colorDrawerText);
  background-color:{{ settings.color_drawer_background | default: "#1c1d1d" }};
  background-color:var(--colorDrawers);
  box-shadow:0 0 150px rgba(0, 0, 0, 0.1);
  transition:transform 0.25s cubic-bezier(0.165, 0.84, 0.44, 1)
}

@media screen and (max-height:400px){

.drawer{
    overflow:scroll
}

    .drawer .drawer__contents{
      height:auto;
    }
  }

.drawer a:not(.btn){
    color:{{ settings.color_drawer_text | default: "#fff" }};
    color:var(--colorDrawerText)
  }

.drawer a:not(.btn):hover{
      color:{{ settings.color_drawer_text | default: "#fff" }};
      color:var(--colorDrawerText);
    }

.drawer input,.drawer textarea{
    border-color:{{ settings.color_drawer_border | default: "#343535" }};
    border-color:var(--colorDrawerBorder);
  }

.drawer .btn{
    background-color:{{ settings.color_drawer_button | default: "#a26b25" }};
    background-color:var(--colorDrawerButton);
    color:{{ settings.color_drawer_button_text | default: "#fff" }};
    color:var(--colorDrawerButtonText);
  }

.drawer--left{
  width:350px;
  left:-350px
}

.drawer--left.drawer--is-open{
    display:block;
    transform:translateX(350px);
    transition-duration:0.45s;
  }

.drawer--right{
  width:350px;
  right:-350px
}

@media only screen and (min-width:769px){

.drawer--right{
    width:450px;
    right:-450px
}
  }

.drawer--right.drawer--is-open{
    display:block;
    transform:translateX(-350px);
    transition-duration:0.45s
  }

@media only screen and (min-width:769px){

.drawer--right.drawer--is-open{
      transform:translateX(-450px)
  }
    }

.js-drawer-closing .main-content:after,.js-drawer-open .main-content:after{
  content:"";
  display:block;
  position:fixed;
  top:0;
  left:0;
  right:0;
  bottom:0;
  background-color:{{ settings.color_modal_overlays | default: "#000" }};
  background-color:var(--colorModalBg);
  opacity:0;
  z-index:26;
}

.js-drawer-open .main-content:after{
  animation:overlay-on 0.35s forwards;
}

.js-drawer-closing .main-content:after{
  animation:overlay-off 0.25s forwards;
}

.drawer__fixed-header,.drawer__footer,.drawer__header,.drawer__scrollable{
  padding-left:20px;
  padding-left:var(--drawer-gutter);
  padding-right:20px;
  padding-right:var(--drawer-gutter);
}

.drawer__header{
  display:table;
  height:70px;
  width:100%;
  padding:11.53846px 0;
  margin-bottom:0;
  border-bottom:1px solid;
  border-bottom-color:{{ settings.color_drawer_border | default: "#343535" }};
  border-bottom-color:var(--colorDrawerBorder);
}

.drawer__fixed-header{
  height:70px;
  overflow:visible;
}

@media only screen and (min-width:769px){
  .drawer__fixed-header,.drawer__header{
    height:80px;
  }
}

.drawer__close,.drawer__title{
  display:table-cell;
  vertical-align:middle;
}

.drawer__title{
  width:100%;
}

.drawer__close{
  width:1%;
  text-align:center;
}

.drawer__close-button{
  position:relative;
  height:100%;
  right:-20px;
  right:calc(var(--drawer-gutter)*-1);
  padding:0 20px;
  padding:0 var(--drawer-gutter);
  color:inherit
}

.drawer__close-button:active{
    background-color:{{ settings.color_drawer_background | default: "#1c1d1d" | color_darken: 5 }};
    background-color:var(--colorDrawersDim);
  }

.drawer__close-button .icon{
    height:28px;
    width:28px;
  }

.drawer__close--left{
  text-align:left
}

.drawer__close--left .drawer__close-button{
    right:auto;
    left:-20px;
    left:calc(var(--drawer-gutter)*-1);
  }

.drawer__contents{
  height:100%;
  display:flex;
  flex-direction:column;
}

.drawer__inner,.drawer__scrollable{
  flex:1 1 auto;
  display:flex;
  flex-direction:column;
  overflow-y:hidden;
}

.drawer__scrollable{
  padding-top:20px;
  padding-top:var(--drawer-gutter);
  overflow:hidden;
  overflow-y:auto;
  -webkit-overflow-scrolling:touch;
}

.drawer__footer{
  border-top:1px solid;
  border-top-color:{{ settings.color_drawer_border | default: "#343535" }};
  border-top-color:var(--colorDrawerBorder);
  padding-top:15px;
  padding-bottom:20px;
  padding-bottom:var(--drawer-gutter)
}

@media only screen and (min-width:769px){

.drawer__footer{
    padding-top:22.22222px
}
  }

.drawer__inner.is-loading .drawer__scrollable{
    transition:opacity 0.3s ease 0.7s;
    opacity:0.4;
  }

.cart-notes{
  margin-top:10px;
  margin-bottom:10px;
  min-height:60px;
  height:60px
}

@media only screen and (min-width:769px){

.cart-notes{
    min-height:80px;
    height:80px
}
  }

.placeholder-svg{
  fill:#999;
  background-color:#e1e1e1;
  width:100%;
  height:100%;
  max-width:100%;
  max-height:100%;
  display:block;
  padding:30px 0;
}

.placeholder-noblocks{
  padding:40px;
  text-align:center;
}

.placeholder-content{
  overflow:hidden;
  animation:placeholder-shimmer 1.3s linear infinite;
  background-size:400% 100%;
  margin-bottom:20px;
  border-radius:4px
}

@media only screen and (max-width:768px){

.placeholder-content{
    margin-left:auto;
    margin-right:auto
}
  }

.animation-delay-20{animation-delay:1.2s;}

.animation-delay-19{animation-delay:1.14s;}

.animation-delay-18{animation-delay:1.08s;}

.animation-delay-17{animation-delay:1.02s;}

.animation-delay-16{animation-delay:0.96s;}

.animation-delay-15{animation-delay:0.9s;}

.animation-delay-14{animation-delay:0.84s;}

.animation-delay-13{animation-delay:0.78s;}

.animation-delay-12{animation-delay:0.72s;}

.animation-delay-11{animation-delay:0.66s;}

.animation-delay-10{animation-delay:0.6s;}

.animation-delay-9{animation-delay:0.54s;}

.animation-delay-8{animation-delay:0.48s;}

.animation-delay-7{animation-delay:0.42s;}

.animation-delay-6{animation-delay:0.36s;}

.animation-delay-5{animation-delay:0.3s;}

.animation-delay-4{animation-delay:0.24s;}

.animation-delay-3{animation-delay:0.18s;}

.animation-delay-2{animation-delay:0.12s;}

.appear-delay-20{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.3s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.4s;}

.appear-delay-19{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.24s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.34s;}

.appear-delay-18{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.18s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.28s;}

.appear-delay-17{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.12s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.22s;}

.appear-delay-16{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.06s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.16s;}

.appear-delay-15{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 1s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.1s;}

.appear-delay-14{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.94s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.04s;}

.appear-delay-13{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.88s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.98s;}

.appear-delay-12{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.82s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.92s;}

.appear-delay-11{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.76s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.86s;}

.appear-delay-10{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.7s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.8s;}

.appear-delay-9{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.64s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.74s;}

.appear-delay-8{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.58s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.68s;}

.appear-delay-7{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.52s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.62s;}

.appear-delay-6{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.46s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.56s;}

.appear-delay-5{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.4s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.5s;}

.appear-delay-4{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.34s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.44s;}

.appear-delay-3{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.28s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.38s;}

.appear-delay-2{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.22s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.32s;}

.appear-delay-1{transition:transform 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.1s,opacity 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.2s;}

.animation-cropper{
  overflow:hidden;
  display:inline-flex;
}

.image-wrap{
  background:{{ settings.color_small_image_bg | default: "#eee" }};
  background:var(--colorSmallImageBg);
  overflow:hidden;
}

.image-wrap img:not([role=presentation]){
  display:block
}

.no-js .image-wrap img:not([role=presentation]).lazyload{
    display:none
}

.image-wrap .animate-me,.image-wrap img:not([role=presentation]),.image-wrap svg{
    opacity:0
  }

.no-js .image-wrap .animate-me,.no-js .image-wrap img:not([role=presentation]),.no-js .image-wrap svg{
      opacity:1
  }

.aos-animate .image-wrap .animate-me,.aos-animate .image-wrap .lazyloaded:not([role=presentation]),.aos-animate .image-wrap svg{
    animation:fade-in 1s cubic-bezier(0.26, 0.54, 0.32, 1) 0s forwards;
  }

[data-aos=row-of-3].aos-animate:nth-child(3n+2) .image-wrap img{
    animation-delay:150ms
}

[data-aos=row-of-3].aos-animate:nth-child(3n+3) .image-wrap img{
    animation-delay:300ms
}

[data-aos=row-of-4].aos-animate:nth-child(4n+2) .image-wrap img{
    animation-delay:120ms
}

[data-aos=row-of-4].aos-animate:nth-child(4n+3) .image-wrap img{
    animation-delay:240ms
}

[data-aos=row-of-4].aos-animate:nth-child(4n+4) .image-wrap img{
    animation-delay:360ms
}

[data-aos=row-of-5].aos-animate:nth-child(5n+2) .image-wrap img{
    animation-delay:75ms
}

[data-aos=row-of-5].aos-animate:nth-child(5n+3) .image-wrap img{
    animation-delay:150ms
}

[data-aos=row-of-5].aos-animate:nth-child(5n+4) .image-wrap img{
    animation-delay:225ms
}

[data-aos=row-of-5].aos-animate:nth-child(5n+5) .image-wrap img{
    animation-delay:300ms
}

[data-aos=row-of-6].aos-animate:nth-child(6n+2) .image-wrap img{
    animation-delay:50ms
}

[data-aos=row-of-6].aos-animate:nth-child(6n+3) .image-wrap img{
    animation-delay:100ms
}

[data-aos=row-of-6].aos-animate:nth-child(6n+4) .image-wrap img{
    animation-delay:150ms
}

[data-aos=row-of-6].aos-animate:nth-child(6n+5) .image-wrap img{
    animation-delay:200ms
}

[data-aos=row-of-6].aos-animate:nth-child(6n+6) .image-wrap img{
    animation-delay:250ms
}

[data-aos=row-of-3].aos-animate:nth-child(3n+2) .collection-image{
    animation-delay:150ms
}

[data-aos=row-of-3].aos-animate:nth-child(3n+3) .collection-image{
    animation-delay:300ms
}

[data-aos=row-of-4].aos-animate:nth-child(4n+2) .collection-image{
    animation-delay:120ms
}

[data-aos=row-of-4].aos-animate:nth-child(4n+3) .collection-image{
    animation-delay:240ms
}

[data-aos=row-of-4].aos-animate:nth-child(4n+4) .collection-image{
    animation-delay:360ms
}

[data-aos=row-of-5].aos-animate:nth-child(5n+2) .collection-image{
    animation-delay:75ms
}

[data-aos=row-of-5].aos-animate:nth-child(5n+3) .collection-image{
    animation-delay:150ms
}

[data-aos=row-of-5].aos-animate:nth-child(5n+4) .collection-image{
    animation-delay:225ms
}

[data-aos=row-of-5].aos-animate:nth-child(5n+5) .collection-image{
    animation-delay:300ms
}

[data-aos=row-of-6].aos-animate:nth-child(6n+2) .collection-image{
    animation-delay:50ms
}

[data-aos=row-of-6].aos-animate:nth-child(6n+3) .collection-image{
    animation-delay:100ms
}

[data-aos=row-of-6].aos-animate:nth-child(6n+4) .collection-image{
    animation-delay:150ms
}

[data-aos=row-of-6].aos-animate:nth-child(6n+5) .collection-image{
    animation-delay:200ms
}

[data-aos=row-of-6].aos-animate:nth-child(6n+6) .collection-image{
    animation-delay:250ms
}

.loading:after,.loading:before,.unload:after,.unload:before{
  content:"";
  position:absolute;
  width:100px;
  height:3px;
  background:{{ settings.color_body_bg | default: "#fff" }};
  background:var(--colorBody);
  left:50%;
  top:50%;
  margin:-1px 0 0 -50px;
  z-index:4;
  opacity:0
}

.no-js .loading:after,.no-js .loading:before,.no-js .unload:after,.no-js .unload:before{
    display:none
}

.loading:before,.unload:before{
  background:{{ settings.color_body_text | default: "#1c1d1d" }};
  background:var(--colorTextBody);
  opacity:1;
}

.loading:after,.unload:after{
  opacity:0;
  animation:preloading 0.5s ease 0.3s infinite;
}

.loading--delayed:before{
  animation-delay:0.8s !important;
  animation-duration:1s !important;
}

.loading--delayed:after{
  animation-delay:1.3s !important;
}

.appear-animation{
  opacity:0;
  transform:translateY(60px)
}

[data-disable-animations=true] .appear-animation{
    opacity:1;
    transform:none
}

.js-drawer-open .appear-animation{
  opacity:1;
  transform:translateY(0px);
}

.js-drawer-closing .appear-animation{
  transition-duration:0s;
  transition-delay:0.5s;
}

.shopify-product-reviews-badge{
  display:block;
  min-height:25px;
}

.product-single__meta .spr-icon{
  font-size:14px !important;
  vertical-align:text-bottom;
}

.spr-header-title{
  font-family:var(--typeHeaderPrimary),var(--typeHeaderFallback);
  font-weight:var(--typeHeaderWeight);
  letter-spacing:var(--typeHeaderSpacing);
  line-height:var(--typeHeaderLineHeight);
}

[data-type_header_capitalize=true] .spr-header-title{
    text-transform:uppercase;
}

.spr-header-title{
  font-size:calc(var(--typeHeaderSize)*0.7) !important;
  margin-bottom:20px !important;
}

.spr-container.spr-container{
  padding:0;
  border:0;
  text-align:center;
}

.spr-container .spr-summary-actions-newreview{
    float:none;
  }

.spr-container .spr-summary-starrating{
    justify-content:center;
  }

.spr-container .spr-summary.spr-summary{
    text-align:center;
  }

.spr-container .spr-form-label,.spr-container .spr-review-content-body{
    font-size:calc(var(--typeBaseSize) - 2px);
    line-height:1.563;
  }

.spr-container .spr-review-header-byline{
    font-size:11px;
    opacity:1
  }

.spr-container .spr-review-header-byline strong{
      font-weight:400;
    }

.spr-container .spr-review{
    border:none !important;
  }

.spr-container .spr-form-label{
    display:block;
    text-align:left;
    margin-top:20px;
  }

.spr-container .spr-summary-actions,.spr-container .spr-summary-caption{
    display:block;
  }

.spr-container .spr-summary-actions{
    margin-top:20px;
  }

.spr-form-contact-name label{
  text-align:left;
}

@media only screen and (min-width:769px){
    .product-full-width .spr-container.index-section{
      margin-top:0;
    }

    .product-full-width .spr-container .spr-form-title{
      display:none;
    }

    .product-full-width .spr-container .spr-form{
      max-width:650px;
      margin:0 auto;
      border-top:none;
    }

    .product-full-width .spr-container .spr-reviews{
      margin-top:45px;
      display:flex;
      flex-wrap:wrap
    }

    [data-type_headers_align_text=true] .product-full-width .spr-container .spr-reviews{
        justify-content:center
    }

    .product-full-width .spr-container .spr-review:first-child{
      margin-top:0;
    }

    .product-full-width .spr-container .spr-review{
      flex:1 1 40%;
      padding:20px;
      margin-left:22px;
      margin-bottom:22px
    }

    [data-type_headers_align_text=true] .product-full-width .spr-container .spr-review{
        max-width:30%
    }

      .product-full-width .spr-container .spr-review:nth-child(3n+1){
        margin-left:0;
      }

      .product-full-width .spr-container .spr-review:last-child{
        padding-bottom:20px;
      }
  }

.grid-product .spr-badge[data-rating="0.0"]{
    display:none;
  }

.grid-product .spr-badge{
    text-align:center;
  }

.grid-product .spr-badge-starrating{
    font-size:0.65em;
    display:inline-block;
  }

.grid-product .spr-icon{
    margin-right:1px;
  }

.grid-product .spr-badge-caption{
    font-size:11px;
    vertical-align:bottom;
    margin-left:4px
  }

@media only screen and (min-width:769px){

.grid-product .spr-badge-caption{
      font-size:12px
  }
    }

.spr-content .spr-review-header-title{
  font-family:var(--typeBasePrimary),var(--typeBaseFallback);
  font-size:calc(var(--typeBaseSize)*0.92);
  letter-spacing:var(--typeBaseSpacing);
  line-height:var(--typeBaseLineHeight);
}

@media only screen and (min-width:769px){

.spr-content .spr-review-header-title{
    font-size:var(--typeBaseSize);
}
  }

.spr-content .spr-review-header-title{
  font-weight:700;
  text-transform:none;
}

.spr-pagination{
  flex:1 1 100%;
}

.spr-review-reportreview{
  opacity:0.4;
  float:none !important;
  display:block;
}

.spr-summary-starrating{
  font-size:0.8em;
}

tool-tip[data-tool-tip-open=true] .tool-tip__inner{
  padding:45px
}

@media only screen and (max-width:768px){

tool-tip[data-tool-tip-open=true] .tool-tip__inner{
    padding:45px 20px 20px
}
  }

.tool-tip__close{
  padding:10px;
}

[data-center-text=true] .footer-promotions{
    text-align:center
}

@media only screen and (max-width:768px){
    .footer-promotions .grid__item{
      margin-bottom:32px
    }

      .footer-promotions .grid__item:last-child{
        margin-bottom:0;
      }
  }

.site-footer{
  padding-bottom:30px;
  background-color:{{ settings.color_footer | default: "#111" }};
  background-color:var(--colorFooter);
  color:{{ settings.color_footer_text | default: "#fff" }};
  color:var(--colorFooterText)
}

@media only screen and (min-width:769px){

.site-footer{
    padding-top:60px;
    padding-bottom:60px
}
  }

.site-footer .footer__collapsible{
    font-size:calc(var(--typeBaseSize)*0.85);
  }

@media only screen and (min-width:769px){
    .site-footer input,.site-footer select,.site-footer textarea{
      font-size:calc(var(--typeBaseSize)*0.85);
    }
  }

@media only screen and (max-width:768px){

[data-center-text=true] .site-footer{
font-weight:400;
    text-align:center
}

.site-footer{
    padding-bottom:0
}

    .site-footer .grid__item{
      padding-bottom:5px
    }

      .site-footer .grid__item:after{
        content:"";
        border-bottom:1px solid;
        border-bottom-color:{{ settings.color_footer_text | default: "#fff" }};
        border-bottom-color:var(--colorFooterText);
        opacity:0.12;
        display:block;
      }

      .site-footer .grid__item:first-child{
        padding-top:7.5px;
      }

      .site-footer .grid__item:last-child:after{
        display:none;
      }
  }

.site-footer a{
    color:{{ settings.color_footer_text | default: "#fff" }};
    color:var(--colorFooterText);
  }

.footer__small-text{
  font-size:max(calc(var(--typeBaseSize)*0.7), 12px);
  padding:7.5px 0;
  margin:0;
  text-align:center
}

ul+.footer__small-text{
    padding-top:15px
}

.footer__clear{
  clear:both;
  height:30px;
}

.footer__section{
  margin-bottom:15px
}

@media only screen and (min-width:769px){

.footer__section{
    margin-top:30px;
    margin-bottom:0
}
  }

@media only screen and (min-width:769px){
  .footer__item-padding{
    padding-right:60px;
  }
}

.footer__title{
  color:{{ settings.color_footer_text | default: "#fff" }};
  color:var(--colorFooterText)
}

@media only screen and (min-width:769px){

.footer__title{
    margin-bottom:20px
}
  }

@media only screen and (max-width:768px){

[data-center-text=true] .footer__title{
  font-weight:400;  
  text-align:center
}
  }

.site-footer__linklist{
  margin:0
}

.site-footer__linklist a{
    color:currentColor;
    display:inline-block;
    font-weight:400;
    padding:4px 0;
  }

.footer__newsletter{
  position:relative;
  display:inline-block;
  max-width:300px;
  width:100%;
}

.footer__newsletter-input{
  padding:10px 45px 10px 0;
  background-color:{{ settings.color_footer | default: "#111" }};
  background-color:var(--colorFooter);
  color:{{ settings.color_footer_text | default: "#fff" }};
  color:var(--colorFooterText);
  max-width:300px;
  width:100%;
  border:0;
  border-radius:0;
  border-bottom:2px solid;
  border-bottom-color:{{ settings.color_footer_text | default: "#fff" }};
  border-bottom-color:var(--colorFooterText)
}

.footer__newsletter-input:focus{
    border:0;
    border-bottom:2px solid;
    border-bottom-color:{{ settings.color_footer_text | default: "#fff" }};
    border-bottom-color:var(--colorFooterText);
  }

.footer__newsletter-input::-webkit-input-placeholder{
    color:{{ settings.color_footer_text | default: "#fff" }};
    color:var(--colorFooterText);
    opacity:1;
  }

.footer__newsletter-input:-moz-placeholder{
    color:{{ settings.color_footer_text | default: "#fff" }};
    color:var(--colorFooterText);
    opacity:1;
  }

.footer__newsletter-input::-moz-placeholder{
    color:{{ settings.color_footer_text | default: "#fff" }};
    color:var(--colorFooterText);
    opacity:1;
  }

.footer__newsletter-input:-ms-input-placeholder{
    color:{{ settings.color_footer_text | default: "#fff" }};
    color:var(--colorFooterText);
    opacity:1;
  }

.footer__newsletter-input::-ms-input-placeholder{
    color:{{ settings.color_footer_text | default: "#fff" }};
    color:var(--colorFooterText);
    opacity:1;
  }

.footer__newsletter-input--active{
  padding-right:80px;
}

.footer__newsletter-btn{
  position:absolute;
  top:50%;
  transform:translateY(-50%);
  right:0;
  color:{{ settings.color_footer_text | default: "#fff" }};
  color:var(--colorFooterText);
  padding:0
}

.footer__newsletter-btn .icon{
    width:26px;
    height:24px;
  }

.footer__newsletter-input--active+.footer__newsletter-btn .icon{
      display:none;
    }

.footer__newsletter-input--active+.footer__newsletter-btn .footer__newsletter-btn-label{
      display:block;
    }

.footer__newsletter-btn-label{
  display:none;
  font-size:calc(var(--typeBaseSize)*0.85);
}

.footer__logo{
  margin:15px 0
}

@media only screen and (min-width:769px){

.footer__logo{
    margin:0 0 20px
}
  }

.footer__logo a{
    display:block;
  }

.footer__logo img{
    display:inline-block;
    transform:translateZ(0);
    max-height:100%;
  }

.footer__social{
  margin:0
}

form+.footer__social{
    margin-top:30px
}

.footer__social li{
    display:inline-block;
    margin:0 15px 15px 0;
  }

.footer__social a{
    display:block;
  }

.footer__social .icon{
    width:22px;
    height:22px
  }

@media only screen and (min-width:769px){

.footer__social .icon{
      width:24px;
      height:24px
  }
    }

.footer__social .icon.icon--wide{
      width:40px;
    }

@media only screen and (max-width:768px){

.footer__collapsible{
    padding:0 0 15px 0
}
  }

@media only screen and (max-width:768px){

.footer_collapsible--disabled{
    padding-top:15px
}
  }

.collapsible-content__inner p a:after{
    content:"";
    position:absolute;
    bottom:-2px;
    left:0;
    width:0%;
    border-bottom:2px solid;
    border-bottom-color:{{ settings.color_footer_text | default: "#fff" }};
    border-bottom-color:var(--colorFooterText);
    transition:width 0.5s ease;
  }

.collapsible-content__inner p a{
    position:relative;
    text-decoration:none;
    border-bottom:2px solid;
    border-color:{{ settings.color_footer_text | default: "#fff" | color_modify: "alpha", 0.1 }}
  }

.collapsible-content__inner p a:focus:after,.collapsible-content__inner p a:hover:after{
      width:100%;
    }

.site-footer select{
    background-image:url({{ "ico-select-footer.svg" | asset_url | split: '?' | first }});
  }

[data-center-text=true] .payment-icons{
    text-align:center
}

.payment-icons{
  -webkit-user-select:none;
          user-select:none;
  cursor:default
}

.payment-icons li{
    cursor:default;
    margin:0 4px 0;
  }

.errors,.note{
  border-radius:0;
  padding:6px 12px;
  margin-bottom:15px;
  border:1px solid transparent;
  text-align:left
}

.errors ol,.errors ul,.note ol,.note ul{
    margin-top:0;
    margin-bottom:0;
  }

.errors li:last-child,.note li:last-child{
    margin-bottom:0;
  }

.errors p,.note p{
    margin-bottom:0;
  }

.note{
  border-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-color:var(--colorBorder);
}

.errors ul{
    list-style:disc outside;
    margin-left:20px;
  }

.note--success{
  color:#56ad6a;
  background-color:#ecfef0;
  border-color:#56ad6a
}

.note--success a{
    color:#56ad6a;
    text-decoration:underline
  }

.note--success a:hover{
      text-decoration:none;
    }

.errors,.form-error{
  color:#d02e2e;
  background-color:#fff6f6;
  border-color:#d02e2e
}

.errors a,.form-error a{
    color:#d02e2e;
    text-decoration:underline
  }

.errors a:hover,.form-error a:hover{
      text-decoration:none;
    }

.pagination{
  margin:0;
  padding:60px 0;
  text-align:center
}

.pagination>span{
    display:inline-block;
    vertical-align:middle;
    line-height:1;
  }

.pagination a{
    display:inline-block;
  }

.pagination .page.current,.pagination a{
    padding:8px 12px;
  }

.pagination .page.current{
    opacity:0.3;
  }

.pagination .next,.pagination .prev{
    color:{{ settings.color_button_text | default: "#fff" }};
    color:var(--colorBtnPrimaryText);
    background:{{ settings.color_button | default: "#000" }};
    background:var(--colorBtnPrimary);
    width:43px;
    margin:0 10px
  }

@media only screen and (max-width:768px){

.pagination .next,.pagination .prev{
      width:35px
  }
    }

.pagination .next a,.pagination .prev a{
      display:flex;
      align-items:center;
      justify-content:center;
      padding:15px;
    }

.pagination .next .icon,.pagination .prev .icon{
      color:{{ settings.color_button_text | default: "#fff" }};
      color:var(--colorBtnPrimaryText);
      width:13px;
      height:14px
    }

@media only screen and (max-width:768px){

.pagination .next .icon,.pagination .prev .icon{
        width:12px;
        height:12px
    }
      }

.rte:after{content:"";display:table;clear:both;}

.rte{
  margin-bottom:7.5px
}

@media only screen and (min-width:769px){

.rte{
    margin-bottom:15px
}
  }

.rte:last-child{
    margin-bottom:0;
  }

.rte+.rte{
    margin-top:30px;
  }

.rte ol,.rte p,.rte table,.rte ul{
    margin-bottom:15px
  }

@media only screen and (min-width:769px){

.rte ol,.rte p,.rte table,.rte ul{
      margin-bottom:25px
  }
    }

.rte ol:last-child,.rte p:last-child,.rte table:last-child,.rte ul:last-child{
      margin-bottom:0;
    }

.rte ul ul{
      margin-bottom:0;
    }

.rte h1,.rte h2,.rte h3,.rte h4,.rte h5,.rte h6{
    margin-top:60px;
    margin-bottom:25px;
  }

.rte h1:first-child,.rte h2:first-child,.rte h3:first-child,.rte h4:first-child,.rte h5:first-child,.rte h6:first-child{
      margin-top:0;
    }

.rte h1 a,.rte h2 a,.rte h3 a,.rte h4 a,.rte h5 a,.rte h6 a{
      text-decoration:none;
    }

.rte meta:first-child+h1,.rte meta:first-child+h2,.rte meta:first-child+h3,.rte meta:first-child+h4,.rte meta:first-child+h5,.rte meta:first-child+h6{
      margin-top:0;
    }

.rte>div{
    margin-bottom:15px
  }

.rte>div:last-child{
      margin-bottom:0;
    }

.rte li{
    margin-bottom:0;
  }

.rte table{
    table-layout:fixed;
  }

.rte--block{
  margin-bottom:8px
}

@media only screen and (min-width:769px){

.rte--block{
    margin-bottom:12px
}
  }

.rte-setting>p:last-child{
    margin-bottom:0;
  }

.rte-setting a,.rte a{
    text-decoration:none;
  }

.rte-setting img,.rte img{
    height:auto;
  }

.rte-setting a:not(.rte__image):not(.btn):not(.spr-summary-actions-newreview),.rte a:not(.rte__image):not(.btn):not(.spr-summary-actions-newreview){
    text-decoration:none;
    border-bottom:1px solid;
    border-bottom-color:{{ settings.color_body_text | default: "#1c1d1d" | color_modify: "alpha", 0.15 }};
    position:relative;
    display:inline-block;
  }

.rte-setting a:not(.btn):not(.spr-summary-actions-newreview):after,.rte a:not(.btn):not(.spr-summary-actions-newreview):after{
    content:"";
    position:absolute;
    bottom:-2px;
    left:0;
    width:0%;
    border-bottom:2px solid currentColor;
    transition:width 0.5s ease;
  }

.rte-setting a:not(.btn):focus:after,.rte-setting a:not(.btn):hover:after,.rte a:not(.btn):focus:after,.rte a:not(.btn):hover:after{
      width:100%;
    }

.rte-setting a.rte__image:after,.rte a.rte__image:after{
    content:none;
  }

.text-center.rte ol,.text-center .rte ol,.text-center.rte ul,.text-center .rte ul{
    list-style-position:inside;
    margin-left:0;
  }

.rte--nomargin{
  margin-bottom:0;
}

.header-layout{
  display:flex;
  justify-content:space-between;
}

.header-layout--center{
  align-items:center;
}

.header-item{
  display:flex;
  align-items:center;
  flex:1 1 auto;
}

.header-item--logo{
  flex:0 0 auto;
}

.header-item--icons{
  justify-content:flex-end;
  flex:0 1 auto;
}

.header-layout--left-center .header-item--icons,.header-layout--left-center .header-item--logo{
    flex:0 0 200px;
    max-width:50%
  }

@media only screen and (min-width:769px){

.header-layout--left-center .header-item--icons,.header-layout--left-center .header-item--logo{
      min-width:130px
  }
    }

@media only screen and (min-width:769px){

.header-layout[data-logo-align=center] .header-item--logo{
      margin:0 30px
  }
    }

.header-layout[data-logo-align=center] .header-item--icons,.header-layout[data-logo-align=center] .header-item--navigation{
    flex:1 1 130px;
  }

.header-layout[data-logo-align=left] .site-header__logo{
    margin-right:10px;
  }

.header-item--logo-split{
  display:flex;
  justify-content:center;
  align-items:center;
  flex:1 1 100%
}

.header-item--logo-split .header-item:not(.header-item--logo){
    text-align:center;
    flex:1 1 20%;
  }

.header-item--split-left{
  justify-content:flex-end;
}

.header-item--left .site-nav{
    margin-left:-12px
  }

@media only screen and (max-width:768px){

.header-item--left .site-nav{
      margin-left:-7.5px
  }
    }

.header-item--icons .site-nav{
    margin-right:-12px
  }

@media only screen and (max-width:768px){

.header-item--icons .site-nav{
      margin-right:-7.5px
  }
    }

.site-header{
  position:relative;
  padding:7px 0;
  background:{{ settings.color_header | default: "#fff" }};
  background:var(--colorNav)
}

@media only screen and (min-width:769px){

.site-header{
    padding:20px 0
}

.toolbar+.header-sticky-wrapper .site-header{
      border-top:1px solid;
      border-top-color:{{ settings.color_header_text | default: "#000" | color_modify: "alpha", 0.1 }}
}
  }

.site-header--stuck{
  position:fixed;
  left:0;
  right:0;
  top:0;
  transform:translate3d(0, -100%, 0);
  transition:none;
  z-index:20
}

.js-drawer-open--search .site-header--stuck{
    z-index:28
}

@media only screen and (min-width:769px){

.site-header--stuck{
    padding:10px 0
}
  }

@media screen and (min-width:700px) and (max-height:550px){
  .site-header--stuck{
    position:static;
  }
}

.site-header--opening{
  transform:translateZ(0);
  transition:transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.site-header__logo{
  position:relative;
  margin:10px 0;
  display:block;
  font-size:30px;
  z-index:6
}

@media only screen and (min-width:769px){

.text-center .site-header__logo{
      padding-right:0;
      margin:10px auto
}
  }

.header-layout[data-logo-align=center] .site-header__logo{
    margin-left:auto;
    margin-right:auto;
    text-align:center
}

.site-header__logo a{
    max-width:100%;
  }

.site-header__logo a,.site-header__logo a:hover{
    text-decoration:none;
  }

.site-header__logo img{
    display:block;
    position:absolute;
  	top:50%;
  	left:50%;
    transform:translate(-50%, -50%);
  }

.site-header__logo-link{
  display:flex;
  position:relative;
  align-items:center;
  color:{{ settings.color_header_text | default: "#000" }};
  color:var(--colorNavText)
}

.site-header__logo-link:hover{
    color:{{ settings.color_header_text | default: "#000" }};
    color:var(--colorNavText);
  }

@media only screen and (max-width:768px){

.site-header__logo-link{
    margin:0 auto
}
  }

.header-sticky-wrapper{
  position:relative;
  z-index:28
}

.js-drawer-open:not(.js-drawer-open--search) .header-sticky-wrapper{
    z-index:6
}

.header-wrapper--sticky{
  position:absolute;
  top:0;
  left:0;
  right:0;
  z-index:6;
  background:none;
  background:linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%,transparent)
}

.header-wrapper--sticky .site-header:not(.site-header--stuck){
    background:none;
  }

.js-drawer-open--search .header-wrapper--sticky{
    z-index:28
}

.site-header__search-container{
  display:none;
  position:absolute;
  left:0;
  right:0;
  bottom:200%;
  height:100%;
  z-index:28;
  overflow:hidden
}

.site-header__search-container.is-active{
    display:block;
    overflow:visible;
    bottom:0;
  }

.site-header__search{
  position:absolute;
  top:0;
  left:0;
  bottom:0;
  right:0;
  z-index:28;
  display:flex;
  transform:translate3d(0, -110%, 0);
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody);
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  box-shadow:0 15px 45px rgba(0, 0, 0, 0.1)
}

.site-header__search .page-width{
    flex:1 1 100%;
    display:flex;
    align-items:stretch
  }

@media only screen and (max-width:768px){

.site-header__search .page-width{
      padding:0
  }
    }

.is-active .site-header__search{
    transform:translateZ(0)
}

.site-header__search .icon{
    width:30px;
    height:30px;
  }

.site-header__search-form{
  flex:1 1 auto;
  display:flex
}

@media only screen and (min-width:769px){

.site-header__search-form{
    padding:15px 0
}
  }

.site-header__search-input{
  border:0;
  width:100px;
  flex:1 1 auto
}

.site-header__search-input:focus{
    border:0;
    outline:0;
  }

.site-header__search-btn{
  padding:0 15px;
}

@media only screen and (min-width:769px){
  .site-header__search-btn--submit{
    padding:0 15px 0 0
  }

    .site-header__search-btn--submit .icon{
      position:relative;
      top:-1px;
      width:28px;
      height:28px;
    }
}

.predictive-results{
  position:absolute;
  top:100%;
  left:0;
  right:0;
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody);
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  max-height:70vh;
  max-height:calc(90vh - 100%);
  overflow:auto;
  box-shadow:0px 10px 20px rgba(0, 0, 0, 0.09)
}

@media only screen and (min-width:769px){

.predictive-results{
    max-height:calc(100vh - 100% - 33px)
}
  }

.predictive__label{
  border-bottom:1px solid;
  border-bottom-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-bottom-color:var(--colorBorder);
  padding-bottom:5px;
  margin-bottom:20px;
}

.predictive-result__layout{
  display:flex;
  flex-wrap:wrap;
  padding:10px;
  margin-left:-10px;
  margin-right:-10px
}

.predictive-result__layout>div{
    margin:0 10px 30px
  }

.predictive-result__layout>div:last-child{
      margin-bottom:0;
    }

.predictive-result__layout [data-type-products]{
    flex:1 1 60%;
    margin-bottom:0;
  }

.predictive-result__layout [data-type-collections],.predictive-result__layout [data-type-pages]{
    flex:1 1 200px;
  }

.predictive-result__layout [data-type-articles]{
    flex:1 1 60%;
  }

.predictive__image-wrap{
  position:absolute;
  top:0;
  left:0;
  right:0;
  bottom:0
}

.predictive__image-wrap img{
    -o-object-position:50% 0;
       object-position:50% 0;
  }

.predictive-results__footer{
  padding:0 0 30px;
}

.search-bar{
  max-width:100%;
}

.search-bar--page{
  max-width:300px;
  margin-top:-15px
}

[data-type_headers_align_text=true] .search-bar--page{
    margin:-15px auto 0
}

.search-bar--drawer{
  margin-bottom:15px;
  padding-bottom:15px;
  border-bottom:1px solid;
  border-bottom-color:{{ settings.color_drawer_border | default: "#343535" }};
  border-bottom-color:var(--colorDrawerBorder)
}

.search-bar--drawer input{
    border:0;
  }

.search-bar .icon{
  width:24px;
  height:24px;
  vertical-align:middle;
}

.toolbar{
  background:{{ settings.color_header | default: "#fff" }};
  background:var(--colorNav);
  color:{{ settings.color_header_text | default: "#000" }};
  color:var(--colorNavText);
  font-size:calc(var(--typeBaseSize)*0.85)
}

.toolbar a{
    color:{{ settings.color_header_text | default: "#000" }};
    color:var(--colorNavText);
  }

.site-header--stuck .toolbar{
    display:none
}

.toolbar--transparent{
  background-color:transparent;
  color:#fff;
  border-bottom:none
}

.toolbar--transparent a{
    color:#fff;
  }

.toolbar--transparent .toolbar__content{
    border-bottom:1px solid hsla(0, 0%, 100%, 0.2);
  }

.toolbar__content{
  display:flex;
  justify-content:flex-end;
  align-items:center;
}

.toolbar__item{
  flex:0 1 auto;
  padding:0 5px
}

.toolbar__item:first-child{
    padding-left:0;
  }

.toolbar__item:last-child{
    padding-right:0;
  }

.toolbar__item .faux-select,.toolbar__item select{
    font-size:14px;
    padding-top:5px;
    padding-bottom:5px;
    padding-left:5px;
  }

.toolbar__item--menu{
  flex:1 1 auto;
}

.toolbar__menu{
  margin-left:-10px
}

.toolbar__menu a{
    display:block;
    padding:5px 10px;
  }

.toolbar__social{
  text-align:right
}

.toolbar__social a{
    display:block;
    padding:5px;
  }

.toolbar__social .icon{
    position:relative;
    top:-2px;
    width:16px;
    height:16px;
  }

.section-header{
  margin-bottom:30px
}

@media only screen and (min-width:769px){

.section-header{
    margin-bottom:50px
}
  }

[data-type_headers_align_text=true] .section-header{
    text-align:center
}

.section-header select{
    display:inline-block;
    vertical-align:middle;
  }

.section-header--flush{
  margin-bottom:0;
}

.section-header--with-link{
  display:flex;
  align-items:center
}

.section-header--with-link select{
    flex:0 1 auto;
  }

.section-header--hero{
  position:relative;
  flex:1 1 100%;
  color:{{ settings.color_image_text | default: "#fff" }};
  color:var(--colorHeroText);
  margin-bottom:0
}

.section-header--hero a{
    color:{{ settings.color_image_text | default: "#fff" }};
    color:var(--colorHeroText);
  }

.section-header__shadow{
  position:relative;
  display:inline-block
}

.section-header__shadow:before{
    content:"";
    position:absolute;
    top:0;
    right:0;
    bottom:0;
    left:0;
    z-index:auto;
    background:radial-gradient(rgba(0, 0, 0, {{ settings.color_image_overlay_text_shadow | divided_by: 100.0 }}) 0%, transparent 60%);
    background:radial-gradient(rgba(0, 0, 0, var(--colorImageOverlayTextShadow)) 0%, transparent 60%);
    margin:-100px -200px -100px -200px;
    z-index:-1;
  }

.section-header__shadow .breadcrumb,.section-header__shadow .section-header__title{
    position:relative;
  }

.section-header__title{
  margin-bottom:0
}

.section-header--with-link .section-header__title{
    flex:1 1 auto
}

@media only screen and (min-width:769px){

.section-header--hero .section-header__title{
      font-size:calc(var(--typeHeaderSize)*1.45)
}
  }

.section-header__link{
  flex:0 1 auto;
  margin-top:15px
}

.section-header--with-link .section-header__link{
    margin-top:0
}

.section-header--404{
  padding-top:80px;
}

.section-header select{
  margin:10px 0;
}

.section-header p{
  margin:10px 0;
}

.site-nav{
  margin:0;
}

.text-center .site-navigation{
    margin:0 auto
}

.header-layout--left .site-navigation{
    padding-left:10px
}

.site-nav__icons{
  white-space:nowrap;
}

.site-nav__item{
  position:relative;
  display:inline-block;
  margin:0
}

.site-nav__item li{
    display:block;
  }

.site-nav__item .icon-chevron-down{
    width:10px;
    height:10px;
  }

.site-nav__link{
  display:inline-block;
  vertical-align:middle;
  text-decoration:none;
  padding:7.5px 15px;
  white-space:nowrap;
  color:{{ settings.color_header_text | default: "#000" }};
  color:var(--colorNavText)
}

.site-header--heading-style .site-nav__link{
    font-family:var(--typeHeaderPrimary),var(--typeHeaderFallback);
    font-weight:var(--typeHeaderWeight);
    letter-spacing:var(--typeHeaderSpacing);
    line-height:var(--typeHeaderLineHeight)
}

[data-type_header_capitalize=true] .site-header--heading-style .site-nav__link{
    text-transform:uppercase
}

.site-nav__link:hover{
    color:{{ settings.color_header_text | default: "#000" }};
    color:var(--colorNavText);
  }

.is-light .site-nav__link{
    color:#fff
}

.is-light .site-nav__link:hover{
      color:#fff;
    }

.site-nav--has-dropdown>.site-nav__link{
    position:relative;
    z-index:6
}

.site-nav__link .icon-chevron-down{
    margin-left:5px;
  }

@media only screen and (max-width:959px){

.site-nav__link{
    padding:7.5px
}

.header-layout--center .site-nav__link{
      padding-left:2px;
      padding-right:2px
}
  }

.site-nav__link--underline{
  position:relative
}

.site-nav__link--underline:after{
    content:"";
    display:block;
    position:absolute;
    bottom:0;
    left:0;
    right:100%;
    margin:0 15px;
    border-bottom:2px solid;
    border-bottom-color:{{ settings.color_header_text | default: "#000" }};
    border-bottom-color:var(--colorNavText);
    transition:right 0.5s
  }

[data-disable-animations=true] .site-nav__link--underline:after{
      transition:none
  }

.is-light .site-nav__item:not(.site-nav--has-dropdown) .site-nav__link--underline:after{
    border-bottom-color:#fff
}

.site-nav--has-dropdown .site-nav__link--underline:after{
    border-bottom-color:{{ settings.color_body_text | default: "#1c1d1d" }};
    border-bottom-color:var(--colorTextBody)
}

.site-nav__item:hover .site-nav__link--underline:after{
    right:0
}

.site-nav--has-dropdown{
  z-index:6
}

.site-nav--has-dropdown.is-focused,.site-nav--has-dropdown:hover{
    z-index:7;
  }

.site-nav--has-dropdown.is-focused>a,.site-nav--has-dropdown:hover>a{
  color:{{ settings.color_body_text | default: "#1c1d1d" }} !important;
  color:var(--colorTextBody) !important;
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody);
  opacity:1;
  transition:none;
}

.site-nav__link--icon{
  padding-left:12px;
  padding-right:12px
}

@media only screen and (max-width:768px){

.site-nav__link--icon{
    padding-left:7.5px;
    padding-right:7.5px
}

    .site-nav__link--icon+.site-nav__link--icon{
      margin-left:-4px;
    }
  }

.site-nav__link--icon .icon{
    width:30px;
    height:30px;
  }

.site-nav__dropdown{
  position:absolute;
  left:0;
  margin:0;
  z-index:5;
  display:block;
  visibility:hidden;
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody);
  min-width:100%;
  padding:10px 0 5px;
  box-shadow:0px 10px 20px rgba(0, 0, 0, 0.09);
  transform:translate3d(0px, -12px, 0px)
}

.is-focused>.site-nav__dropdown,.site-nav--has-dropdown:hover .site-nav__dropdown{
    display:block;
    visibility:visible;
    transform:translateZ(0px);
    transition:all 300ms cubic-bezier(0.2, 0.06, 0.05, 0.95)
}

.site-nav__dropdown li{
    margin:0;
  }

.site-nav__dropdown>li{
    position:relative
  }

.site-nav__dropdown>li>a{
      position:relative;
      z-index:6;
    }

.site-nav__dropdown a{
    background-color:{{ settings.color_body_bg | default: "#fff" }};
    background-color:var(--colorBody);
  }

.site-nav__deep-dropdown{
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody);
  box-shadow:0px 10px 20px rgba(0, 0, 0, 0.09);
  position:absolute;
  top:0;
  left:100%;
  margin:0;
  visibility:hidden;
  opacity:0;
  z-index:5;
  transform:translate3d(-12px, 0px, 0px)
}

.is-focused+.site-nav__deep-dropdown,.site-nav__deep-dropdown-trigger:hover .site-nav__deep-dropdown{
    visibility:visible;
    opacity:1;
    transform:translateZ(0px);
    transition:all 300ms cubic-bezier(0.2, 0.06, 0.05, 0.95)
}

.site-nav__deep-dropdown:before{
    content:"";
    display:block;
    position:absolute;
    top:0;
    left:0;
    bottom:0;
    width:10px;
    background-image:linear-gradient(90deg, rgba(0, 0, 0, 0.09), transparent);
    pointer-events:none;
  }

.site-nav__deep-dropdown-trigger:hover .site-nav__dropdown-link--has-children{
    background-color:{{ settings.color_body_bg | default: "#1c1d1d" | color_darken: 5 }};
    background-color:var(--colorBodyDim)
}

.site-nav__dropdown-link--has-children:focus,.site-nav__dropdown-link--has-children:hover{
    background-color:{{ settings.color_body_bg | default: "#1c1d1d" | color_darken: 5 }};
    background-color:var(--colorBodyDim);
  }

.site-nav__deep-dropdown-trigger .icon-chevron-down{
  position:absolute;
  top:50%;
  right:10px;
  width:10px;
  height:10px;
  transform:rotate(-90deg) translateX(50%);
}

.mobile-nav{
  margin:-20px -20px 0;
  margin:calc(var(--drawer-gutter)*-1) calc(var(--drawer-gutter)*-1) 0
}

.mobile-nav li{
    margin-bottom:0;
    list-style:none;
  }

.mobile-nav__search{
  padding:15px;
}

.mobile-nav__item{
  position:relative;
  display:block
}

.mobile-nav>.mobile-nav__item{
    background-color:{{ settings.color_drawer_background | default: "#1c1d1d" }};
    background-color:var(--colorDrawers)
}

.mobile-nav__item:after{
    content:"";
    position:absolute;
    bottom:0;
    left:20px;
    left:var(--drawer-gutter);
    right:20px;
    right:var(--drawer-gutter);
    border-bottom:1px solid;
    border-bottom-color:{{ settings.color_drawer_border | default: "#343535" }};
    border-bottom-color:var(--colorDrawerBorder);
  }

.mobile-nav__faux-link,.mobile-nav__link{
  display:block;
}

.mobile-nav__link--top-level{
  font-size:1.4em
}

.mobile-nav--heading-style .mobile-nav__link--top-level{
    font-family:var(--typeHeaderPrimary),var(--typeHeaderFallback);
    font-weight:var(--typeHeaderWeight);
    letter-spacing:var(--typeHeaderSpacing);
    line-height:var(--typeHeaderLineHeight)
}

[data-type_header_capitalize=true] .mobile-nav--heading-style .mobile-nav__link--top-level{
    text-transform:uppercase
}

.mobile-nav__faux-link,.mobile-nav__link,.mobile-nav__toggle .faux-button,.mobile-nav__toggle button{
  color:{{ settings.color_drawer_text | default: "#fff" }};
  color:var(--colorDrawerText);
  padding:15px 20px;
  padding:15px var(--drawer-gutter);
  text-decoration:none
}

.mobile-nav__faux-link:active,.mobile-nav__link:active,.mobile-nav__toggle .faux-button:active,.mobile-nav__toggle button:active{
    color:{{ settings.color_drawer_text | default: "#fff" | color_darken: 15 }};
    color:var(--colorDrawerTextDark);
  }

.mobile-nav__faux-link:active,.mobile-nav__link:active,.mobile-nav__toggle .faux-button:active,.mobile-nav__toggle button:active{
    background-color:{{ settings.color_drawer_background | default: "#1c1d1d" | color_darken: 5 }};
    background-color:var(--colorDrawersDim);
  }

.mobile-nav__child-item{
  display:flex
}

.mobile-nav__child-item .mobile-nav__link,.mobile-nav__child-item a{
    flex:1 1 auto;
  }

.mobile-nav__child-item .collapsible-trigger:not(.mobile-nav__link--button){
    flex:0 0 43px;
  }

.mobile-nav__child-item .collapsible-trigger__icon{
    padding:0;
    margin-right:15px;
  }

.mobile-nav__item--secondary a{
    padding-top:10px;
    padding-bottom:5px;
  }

.mobile-nav__item--secondary:after{
    display:none;
  }

.mobile-nav__item:not(.mobile-nav__item--secondary)+.mobile-nav__item--secondary{
  margin-top:10px;
}

.mobile-nav__has-sublist,.mobile-nav__link--button{
  display:flex
}

.mobile-nav__has-sublist>*,.mobile-nav__link--button>*{
    flex:1 1 auto;
    word-break:break-word;
  }

.mobile-nav__link--button{
  width:100%;
  text-align:left;
  padding:0;
}

.mobile-nav__toggle{
  flex:0 1 auto
}

.mobile-nav__toggle .icon{
    width:16px;
    height:16px;
  }

.mobile-nav__toggle .faux-button,.mobile-nav__toggle button{
    height:60%;
    padding:0 30px;
    margin:20% 0;
  }

.mobile-nav__toggle button{
    border-left:1px solid;
    border-left-color:{{ settings.color_drawer_border | default: "#343535" }};
    border-left-color:var(--colorDrawerBorder);
  }

.mobile-nav__sublist{
  margin:0
}

.mobile-nav__sublist .mobile-nav__item:after{
    top:0;
    bottom:auto;
    border-bottom:none;
  }

.mobile-nav__sublist .mobile-nav__item:last-child{
    padding-bottom:15px;
  }

.mobile-nav__sublist .mobile-nav__faux-link,.mobile-nav__sublist .mobile-nav__link{
    font-weight:400;
    padding:7.5px 25px 7.5px 20px;
    padding:7.5px 25px 7.5px var(--drawer-gutter);
  }

.mobile-nav__grandchildlist{
  margin:0
}

.mobile-nav__grandchildlist:before{
    content:"";
    display:block;
    position:absolute;
    width:1px;
    background:#000;
    left:17px;
    top:10px;
    bottom:10px;
  }

.mobile-nav__grandchildlist .mobile-nav__item:last-child{
    padding-bottom:0;
  }

.mobile-nav__grandchildlist .mobile-nav__link{
    padding-left:35px;
  }

.mobile-nav__social{
  list-style:none outside;
  display:flex;
  flex-wrap:wrap;
  justify-content:stretch;
  margin:15px 0 20px 0
}

@media only screen and (min-width:769px){

.mobile-nav__social{
    margin-left:-20px;
    margin-left:calc(var(--drawer-gutter)*-1);
    margin-right:-20px;
    margin-right:calc(var(--drawer-gutter)*-1)
}
  }

.mobile-nav__social a{
    display:block;
    padding:12px 30px
  }

.mobile-nav__social a .icon{
      position:relative;
      top:-1px;
    }

.mobile-nav__social-item{
  flex:0 1 33.33%;
  text-align:center;
  border:1px solid;
  border-color:{{ settings.color_drawer_border | default: "#343535" }};
  border-color:var(--colorDrawerBorder);
  margin:0 0 -1px
}

.mobile-nav__social-item:nth-child(3n-1){
    margin-right:-1px;
    margin-left:-1px;
  }

@media only screen and (min-width:769px){
  .site-nav__link--icon .icon{
    width:28px;
    height:28px
  }

    .site-nav__link--icon .icon.icon-user{
      position:relative;
      top:1px;
    }
}

.cart-link{
  position:relative;
  display:block;
}

.cart-link__bubble{
  display:none;
}

.cart-link__bubble--visible{
  display:block;
  position:absolute;
  top:50%;
  right:0px;
  width:15px;
  height:15px;
  background-color:{{ settings.color_cart_dot | default: "#ff4f33" }};
  background-color:var(--colorCartDot);
  border:2px solid;
  border-color:{{ settings.color_body_bg | default: "#fff" }};
  border-color:var(--colorBody);
  border-radius:50%
}

[data-icon=cart] .cart-link__bubble--visible{
    top:0;
    right:-4px
}

[data-icon=bag-minimal] .cart-link__bubble--visible{
    top:50%;
    right:0
}

[data-type_headers_align_text=true] .breadcrumb{
    text-align:center
}

.breadcrumb{
  font-size:calc(var(--typeBaseSize)*0.85);
  margin:-25px 0 10px
}

.template-product .breadcrumb{
    margin-top:10px
}

@media only screen and (min-width:769px){

.template-product .breadcrumb{
      text-align:left
}
    }

@media only screen and (max-width:768px){

.breadcrumb{
    margin-bottom:15px
}
  }

.breadcrumb__divider{
  color:currentColor;
}

.megamenu{
  padding:39px 0;
  line-height:1.8;
  transform:none;
  opacity:0;
  transition:all 300ms cubic-bezier(0.2, 0.06, 0.05, 0.95);
  transition-delay:0.3s
}

.is-focused>.megamenu,.site-nav--has-dropdown:hover .megamenu{
    opacity:1;
    transition-delay:0s
}

.is-focused>.megamenu .appear-animation,.site-nav--has-dropdown:hover .megamenu .appear-animation{
      opacity:1;
      transform:none;
    }

.site-nav--is-megamenu.site-nav__item{
  position:static;
}

.megamenu__colection-image{
  display:block;
  background-repeat:no-repeat;
  background-position:top;
  background-size:cover;
  height:0;
  padding-bottom:46%;
  margin-bottom:20px;
}

.text-center .megamenu .grid{
  text-align:center
}

.text-center .megamenu .grid .grid__item{
    float:none;
    display:inline-block;
    vertical-align:top;
    text-align:left;
  }

.megamenu .site-nav__dropdown-link:not(.site-nav__dropdown-link--top-level){
    font-size:calc(var(--typeBaseSize) - 1px);
    line-height:1.5;
}

.megamenu .h5,.megamenu h5{
  margin-bottom:5px;
  font-weight:700;
}

.modal{
  display:none;
  bottom:0;
  left:0;
  opacity:1;
  overflow:hidden;
  position:fixed;
  right:0;
  top:0;
  z-index:30;
  color:#fff;
  align-items:center;
  justify-content:center
}

.modal.modal--quick-shop{
    align-items:flex-start;
  }

.modal a,.modal a:hover{
    color:inherit;
  }

.modal .btn:not([disabled]):not(.btn--secondary):not(.btn--body),.modal .btn:not([disabled]):not(.btn--secondary):not(.btn--body):hover{
    color:{{ settings.color_button_text | default: "#fff" }};
    color:var(--colorBtnPrimaryText);
  }

.modal-open .modal .modal__inner{
      animation:modal-open 0.5s forwards;
    }

.modal-open .modal:before{
      content:"";
      position:fixed;
      top:0;
      left:0;
      width:100%;
      height:100%;
      background-color:{{ settings.color_modal_overlays | default: "#000" }};
      background-color:var(--colorModalBg);
      animation:overlay-on 0.5s forwards;
      cursor:pointer;
    }

.modal-closing .modal .modal__inner{
      animation:modal-closing 0.5s forwards;
    }

.modal-closing .modal:before{
      content:"";
      position:fixed;
      top:0;
      left:0;
      width:100%;
      height:100%;
      background-color:{{ settings.color_modal_overlays | default: "#000" }};
      background-color:var(--colorModalBg);
      animation:overlay-off 0.5s forwards;
    }

.modal-open--solid .modal:before{
    background-color:{{ settings.color_modal_overlays | default: "#000" }};
    background-color:var(--colorModalBg)
}

.modal-open .modal--solid:before{
      background-color:#000;
      animation:full-overlay-on 0.5s forwards;
    }

.modal-closing .modal--solid:before{
      background-color:#000;
      animation:full-overlay-off 0.5s forwards;
    }

.modal--is-closing{
  display:flex !important;
  overflow:hidden;
}

.modal--is-active{
  display:flex !important;
  overflow:hidden;
}

@media only screen and (min-width:769px){
  .modal-open{
    overflow:hidden;
  }
}

.modal__inner{
  transform-style:preserve-3d;
  flex:0 1 auto;
  margin:15px;
  max-width:calc(100% - 15px);
  display:flex;
  align-items:center
}

@media only screen and (min-width:769px){

.modal__inner{
    margin:40px;
    max-width:calc(100% - 80px)
}
  }

.modal--square .modal__inner{
    background-color:{{ settings.color_body_bg | default: "#fff" }};
    background-color:var(--colorBody);
    color:{{ settings.color_body_text | default: "#1c1d1d" }};
    color:var(--colorTextBody);
    box-shadow:0 15px 45px rgba(0, 0, 0, 0.1)
}

.modal__inner img{
    display:block;
    max-height:90vh;
  }

.modal__inner .image-wrap img{
    max-height:none;
  }

.modal__centered{
  position:relative;
  flex:0 1 auto;
  min-width:1px;
  max-width:100%;
}

.modal--square .modal__centered-content{
    max-height:80vh;
    padding:22.5px;
    min-width:200px;
    min-height:200px;
    overflow:auto;
    -webkit-overflow-scrolling:touch
}

@media only screen and (min-width:769px){

.modal--square .modal__centered-content{
      padding:45px;
      max-height:90vh;
      max-width:1200px
}
    }

.modal__close{
  border:0;
  padding:15px;
  position:fixed;
  top:0;
  right:0;
  color:#fff
}

@media only screen and (min-width:769px){

.modal__close{
    padding:30px
}
  }

.modal__close .icon{
    width:28px;
    height:28px;
  }

.modal__close:focus,.modal__close:hover{
    color:#fff;
  }

.modal--square .modal__close{
    position:absolute;
    color:{{ settings.color_body_text | default: "#1c1d1d" }};
    color:var(--colorTextBody);
    padding:10px
}

.modal--square .modal__close:focus,.modal--square .modal__close:hover{
      color:{{ settings.color_body_text | default: "#1c1d1d" }};
      color:var(--colorTextBody);
    }

.modal .page-content,.modal .page-width{
    padding:0;
  }

.popup-cta{
  margin-bottom:15px;
}

@media only screen and (max-width:768px){
  .modal--mobile-friendly{
    top:auto;
    bottom:0;
    overflow:auto
  }

    .modal--mobile-friendly.modal--square .modal__centered-content{
      padding:40px 20px;
    }

    .modal--mobile-friendly.modal--is-active{
      overflow:auto;
    }
      .modal-open .modal--mobile-friendly:before{
        display:none;
      }

    .modal--mobile-friendly .modal__inner{
      margin:0;
    }

    .modal--mobile-friendly .h1{
      padding-right:25px;
    }

    .modal--mobile-friendly input{
      font-size:16px !important;
    }

    .modal--mobile-friendly .text-close{
      display:none;
    }
}

.js-qty__wrapper{
  display:inline-block;
  position:relative;
  max-width:80px;
  min-width:60px;
  overflow:visible;
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody);
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody)
}

.js-qty__wrapper.is-loading{
    opacity:0.5;
    pointer-events:none;
  }

.js-qty__num{
  display:block;
  background:none;
  text-align:center;
  width:100%;
  padding:5px 20px;
  margin:0;
  z-index:1;
}

.js-qty__adjust{
  cursor:pointer;
  position:absolute;
  display:block;
  top:0;
  bottom:0;
  border:0 none;
  background:none;
  text-align:center;
  overflow:hidden;
  padding:0 10px;
  line-height:1;
  -webkit-user-select:none;
          user-select:none;
  -webkit-backface-visibility:hidden;
          backface-visibility:hidden;
  transition:background-color 0.1s ease-out;
  z-index:2;
  fill:{{ settings.color_body_text | default: "#1c1d1d" }};
  fill:var(--colorTextBody)
}

.js-qty__adjust .icon{
    display:block;
    font-size:8px;
    vertical-align:middle;
    width:10px;
    height:10px;
    fill:inherit;
  }

.js-qty__adjust:hover{
    background-color:{{ settings.color_body_bg | default: "#1c1d1d" | color_darken: 5 }};
    background-color:var(--colorBodyDim);
    color:{{ settings.color_body_text | default: "#1c1d1d" }};
    color:var(--colorTextBody);
  }

.js-qty__num:active~.js-qty__adjust,.js-qty__num:focus~.js-qty__adjust{
    border-color:{{ settings.color_body_text | default: "#1c1d1d" }};
    border-color:var(--colorTextBody)
}

.js-qty__adjust--plus{
  right:0;
}

.js-qty__adjust--minus{
  left:0;
}

.drawer .js-qty__wrapper{
    background-color:transparent;
    border-color:{{ settings.color_drawer_border | default: "#343535" }};
    border-color:var(--colorDrawerBorder);
  }

.drawer .js-qty__num{
    color:{{ settings.color_drawer_text | default: "#fff" }};
    color:var(--colorDrawerText);
    padding-top:2px;
    padding-bottom:2px;
  }

.drawer .js-qty__adjust{
    color:{{ settings.color_drawer_text | default: "#fff" }};
    color:var(--colorDrawerText);
    fill:{{ settings.color_drawer_text | default: "#fff" }};
    fill:var(--colorDrawerText)
  }

.drawer .js-qty__adjust:hover{
      background-color:{{ settings.color_drawer_text | default: "#fff" }};
      background-color:var(--colorDrawerText);
      color:{{ settings.color_drawer_background | default: "#1c1d1d" }};
      color:var(--colorDrawers);
      fill:{{ settings.color_drawer_background | default: "#1c1d1d" }};
      fill:var(--colorDrawers);
    }

.currency-flag{
  transform:scale(0.55);
  margin:-0.55rem;
}

@media only screen and (max-width:768px){
  [data-disclosure-currency] .disclosure-list{
    left:50%;
    transform:translateX(-50%);
    max-width:95vw;
  }
}

.collapsibles-wrapper--border-bottom{
  border-bottom:1px solid;
  border-bottom-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-bottom-color:var(--colorBorder);
}

.collapsibles-wrapper--border-bottom+.collapsibles-wrapper--border-bottom .collapsible-trigger-btn--borders{
  border-top:0;
}

.product-block--tab+.product-block--tab .collapsible-trigger-btn--borders{
  border-top:0;
}

.collapsible-trigger{
  color:inherit;
  position:relative;
}

.collapsible-trigger__icon{
  display:block;
  position:absolute;
  right:0;
  top:50%;
  width:10px;
  height:10px;
  transform:translateY(-50%)
}

@media only screen and (min-width:769px){

.collapsible-trigger__icon{
    width:12px;
    height:12px
}
  }

.mobile-nav__has-sublist .collapsible-trigger__icon{
    right:25px
}

.collapsible-trigger__icon .icon{
    display:block;
    width:10px;
    height:10px;
    transition:all 0.1s ease-in
  }

@media only screen and (min-width:769px){

.collapsible-trigger__icon .icon{
      width:12px;
      height:12px
  }
    }

.collapsible-trigger--inline{
  padding:11px 0 11px 40px
}

.collapsible-trigger--inline .collapsible-trigger__icon{
    right:auto;
    left:0;
  }

.collapsible-trigger__icon--circle{
  border:1px solid;
  border-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-color:var(--colorBorder);
  width:28px;
  height:28px;
  border-radius:28px;
  text-align:center
}

.collapsible-trigger__icon--circle .icon{
    position:absolute;
    top:50%;
    left:50%;
    transform:translate(-50%, -50%);
  }

.collapsible-trigger.is-open .collapsible-trigger__icon>.icon-chevron-down{
  transform:scaleY(-1);
}

.collapsible-trigger.is-open .collapsible-trigger__icon--circle>.icon-chevron-down{
  transform:translate(-50%, -50%) scaleY(-1);
}

.collapsible-content{
  transition:opacity 0.3s cubic-bezier(.25,.46,.45,.94),height 0.3s cubic-bezier(.25,.46,.45,.94)
}

.collapsible-content.is-open{
    visibility:visible;
    opacity:1;
    transition:opacity 1s cubic-bezier(.25,.46,.45,.94),height 0.35s cubic-bezier(.25,.46,.45,.94);
  }

.collapsible-content--all{
  visibility:hidden;
  overflow:hidden;
  -webkit-backface-visibility:hidden;
          backface-visibility:hidden;
  opacity:0;
  height:0
}

.collapsible-content--all .collapsible-content__inner{
    transform:translateY(40px);
  }

@media only screen and (max-width:768px){
  .collapsible-content--small{
    visibility:hidden;
    overflow:hidden;
    -webkit-backface-visibility:hidden;
            backface-visibility:hidden;
    opacity:0;
    height:0
  }

    .collapsible-content--small .collapsible-content__inner{
      transform:translateY(40px);
    }
}

.collapsible-content__inner{
  transition:transform 0.3s cubic-bezier(.25,.46,.45,.94)
}

.is-open .collapsible-content__inner{
    transform:translateY(0);
    transition:transform 0.5s cubic-bezier(.25,.46,.45,.94)
}

.rte.collapsible-content__inner--faq{
  padding-bottom:30px;
}

.collapsible-trigger[aria-expanded=true] .collapsible-label__closed{
    display:none
}

.collapsible-label__open{
  display:none
}

.collapsible-trigger[aria-expanded=true] .collapsible-label__open{
    display:inline-block
}

.collapsible-content--sidebar{
  visibility:hidden;
  overflow:hidden;
  -webkit-backface-visibility:hidden;
          backface-visibility:hidden;
  opacity:0;
  height:0
}

@media only screen and (min-width:769px){
    .collapsible-content--sidebar.is-open{
      overflow:visible;
      overflow:initial;
      visibility:visible;
      opacity:1;
      height:auto;
    }
  }

.pswp{
  display:none;
  position:absolute;
  width:100%;
  height:100%;
  left:0;
  top:0;
  overflow:hidden;
  touch-action:none;
  z-index:1500;
  -webkit-text-size-adjust:100%;
  -webkit-backface-visibility:hidden;
  outline:none;
}

.pswp img{
  max-width:none;
}

.pswp--animate_opacity{
  opacity:0.001;
  will-change:opacity;
  transition:opacity 333ms cubic-bezier(0.4, 0, 0.22, 1); }

.pswp--open{
  display:block; }

.pswp--zoom-allowed .pswp__img{
  cursor:zoom-in; }

.pswp--zoomed-in .pswp__img{
  cursor:grab; }

.pswp--dragging .pswp__img{
  cursor:grabbing; }

.pswp__bg{
  position:absolute;
  left:0;
  top:0;
  width:100%;
  height:100%;
  background:{{ settings.color_body_bg | default: "#fff" }};
  background:var(--colorBody);
  opacity:0;
  transform:translateZ(0);
  -webkit-backface-visibility:hidden; }

.pswp__scroll-wrap{
  position:absolute;
  left:0;
  top:0;
  width:100%;
  height:100%;
  overflow:hidden; }

.pswp__container,.pswp__zoom-wrap{
  touch-action:none;
  position:absolute;
  left:0;
  right:0;
  top:0;
  bottom:0; }

.pswp__container,.pswp__img{
  -webkit-user-select:none;
          user-select:none;
  -webkit-tap-highlight-color:transparent;
  -webkit-touch-callout:none; }

.pswp__zoom-wrap{
  position:absolute;
  width:100%;
  transform-origin:left top;
  transition:transform 333ms cubic-bezier(0.4, 0, 0.22, 1); }

.pswp__bg{
  will-change:opacity;
  transition:opacity 333ms cubic-bezier(0.4, 0, 0.22, 1); }

.pswp--animated-in .pswp__bg,.pswp--animated-in .pswp__zoom-wrap{
  transition:none; }

.pswp__container,.pswp__zoom-wrap{
  -webkit-backface-visibility:hidden; }

.pswp__item{
  position:absolute;
  left:0;
  right:0;
  top:0;
  bottom:0;
  overflow:hidden; }

.pswp__img{
  position:absolute;
  width:auto;
  height:auto;
  top:0;
  left:0; }

.pswp__img--placeholder{
  -webkit-backface-visibility:hidden; }

.pswp--ie .pswp__img{
  width:100% !important;
  height:auto !important;
  left:0;
  top:0; }

.pswp__error-msg{
  position:absolute;
  left:0;
  top:50%;
  width:100%;
  text-align:center;
  line-height:16px;
  margin-top:-8px;
  color:#ccc; }

.pswp__error-msg a{
  color:#ccc;
  text-decoration:underline; }

.pswp__button{
  position:relative
}

.pswp__button:after{
    content:"";
    display:block;
    position:absolute;
    top:0;
    left:0;
    right:0;
    bottom:0;
  }

.pswp__button svg{
    pointer-events:none;
  }

.pswp__button--arrow--left .icon,.pswp__button--arrow--right .icon{
  width:13px;
  height:13px;
  margin:8px;
}

.pswp__button[disabled]{
  opacity:0;
  pointer-events:none;
}

.pswp__ui{
  position:absolute;
  display:flex;
  justify-content:center;
  align-items:center;
  bottom:30px;
  left:0;
  right:0;
  transform:translateY(0);
  transition:transform 0.25s 0.6s
}

.pswp__ui .btn{
    margin:15px;
  }

.pswp__ui--hidden{
  transform:translateY(150%);
  transition:transform 0.25s;
}

html.pswp-open-in-ios,html.pswp-open-in-ios body{
  background:#444;
  height:var(--window-inner-height);
  overflow:hidden;
  box-sizing:border-box;
}

.pswp-open-in-ios body>*{
  display:none; 
}

.pswp-open-in-ios body .pswp.pswp--open{
  display:block; 
}

.disclosure{
  position:relative;
}

.disclosure__toggle{
  white-space:nowrap;
}

.disclosure-list{
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody);
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  bottom:100%;
  padding:10px 0px;
  margin:0;
  position:absolute;
  display:none;
  min-height:92px;
  max-height:60vh;
  overflow-y:auto;
  border-radius:0;
  box-shadow:0px 0px 20px rgba(0, 0, 0, 0.09)
}

.disclosure-list a{
    color:currentColor;
  }

.disclosure-list--down{
  bottom:auto;
  top:100%;
  z-index:30;
}

.disclosure-list--left{
  right:0;
}

.disclosure-list--visible{
  display:block;
}

.disclosure-list__item{
  white-space:nowrap;
  padding:5px 15px 4px;
  text-align:left;
}

.disclosure-list__option:focus .disclosure-list__label,.disclosure-list__option:hover .disclosure-list__label{
      text-decoration:underline;
    }

.disclosure-list__item--current .disclosure-list__label{
    text-decoration:underline;
  }

.disclosure-list__label{
  display:inline-block;
  vertical-align:middle;
  text-underline-offset:0.2rem
}

[data-disclosure-currency] .disclosure-list__label{
    padding-left:10px
}

.disclosure-list__label span{
    border-bottom:2px solid transparent
  }

.is-active .disclosure-list__label span{
      border-bottom:2px solid currentColor
  }

.multi-selectors{
  display:flex;
  justify-content:center;
  align-items:center;
  flex-wrap:wrap;
}

.multi-selectors__item{
  margin:0 20px 10px
}

@media only screen and (max-width:768px){

.multi-selectors__item{
    margin:10px 20px
}
  }

.toolbar .multi-selectors__item{
    margin-right:0;
    margin-bottom:0
}

.shopify-model-viewer-ui .shopify-model-viewer-ui__controls-area{
    opacity:1;
    background:{{ settings.color_body_bg | default: "#fff" }};
    background:var(--colorBody);
    border-color:{{ settings.color_body_text | default: "#1c1d1d" | color_modify: "alpha", 0.05 }};
    border-radius:50px;
  }

.shopify-model-viewer-ui .shopify-model-viewer-ui__button{
    color:{{ settings.color_body_text | default: "#1c1d1d" }};
    color:var(--colorTextBody);
  }

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:hover{
      color:{{ settings.color_body_text | default: "#1c1d1d" }};
      color:var(--colorTextBody);
    }

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control.focus-visible:focus,.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:active{
      color:{{ settings.color_body_text | default: "#1c1d1d" }};
      color:var(--colorTextBody);
      background-color:{{ settings.color_body_text | default: "#1c1d1d" | color_modify: "alpha", 0.05 }};
    }

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:not(:last-child):after{
      border-color:{{ settings.color_body_text | default: "#1c1d1d" | color_modify: "alpha", 0.05 }};
    }

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster{
    background-color:{{ settings.color_body_text | default: "#1c1d1d" }};
    background-color:var(--colorTextBody);
    color:{{ settings.color_body_bg | default: "#fff" }};
    color:var(--colorBody);
    border-radius:100%;
    border:1px solid;
    border-color:{{ settings.color_body_bg | default: "#fff" | color_modify: "alpha", 0.05 }}
  }

.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:focus,.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:hover{
      color:{{ settings.color_body_bg | default: "#fff" }};
      color:var(--colorBody);
    }

.product-single__view-in-space{
  display:block;
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  background-color:{{ settings.color_body_text | default: "#1c1d1d" | color_modify: "alpha", 0.08 }};
  width:80%;
  width:calc(80% - 4px);
  margin:40px 10% 10px;
  padding:5px 10px 10px
}

.product-single__view-in-space[data-shopify-xr-hidden]{
    display:none;
  }

.product-single__view-in-space-text{
  font-size:calc(var(--typeBaseSize)*0.85);
  display:inline-block;
  vertical-align:middle;
  margin-left:5px;
}

.shopify-model-viewer-ui,.shopify-model-viewer-ui model-viewer{
  display:block;
  position:absolute;
  top:0;
  left:0;
  width:100%;
  height:100%;
}

.shopify-model-viewer-ui__button[hidden]{
  display:none;
}

.product-single__close-media{
  position:absolute;
  top:10px;
  right:10px;
  z-index:2;
}

.price-range__slider-wrapper{
  padding:0 8px 8px
}

@media only screen and (max-width:768px){

.price-range__slider-wrapper{
    padding:0 10px 8px
}
  }

.price-range__input{
  display:none;
}

.price-range__display-wrapper{
  display:flex;
  flex:1 1 auto;
  justify-content:space-between;
  padding:0;
  padding-bottom:8px;
  width:100%
}

@media only screen and (max-width:768px){

.price-range__display-wrapper{
    padding:0
}
  }

.noUi-horizontal .noUi-handle{
  border:0;
  border-radius:50%;
  background:{{ settings.color_body_text | default: "#1c1d1d" }};
  background:var(--colorTextBody);
  box-shadow:0 0 1px 2px #fff;
  width:12px;
  height:12px;
  cursor:pointer;
  right:-6px;
  top:-3px
}

.noUi-horizontal .noUi-handle:focus,.noUi-horizontal .noUi-handle:hover{
    width:14px;
    height:14px;
    right:-7px;
    top:-4px;
  }

.noUi-horizontal .noUi-handle:after,.noUi-horizontal .noUi-handle:before{
    content:none;
  }

.noUi-target{
  background:#f4f4f4;
  border:0;
  box-shadow:none;
}

.noUi-connect{
  background:{{ settings.color_body_text | default: "#1c1d1d" }};
  background:var(--colorTextBody);
}

.noUi-horizontal{
  height:6px;
}

.countdown__content{
  background-color:var(--countdown-background-color);
  color:var(--countdown-text-color);
}

.countdown__block--button .btn.btn--secondary{
    color:var(--accent-color);
  }

::-webkit-input-placeholder{
   color:inherit;
   opacity:0.5;
}

:-moz-placeholder{
   color:inherit;
   opacity:0.5;
}

:-ms-input-placeholder{
   color:inherit;
   opacity:0.5;
}

::-ms-input-placeholder{
   color:inherit;
   opacity:1;
}

input,select,textarea{
  background-color:inherit;
  color:inherit
}

input.disabled,input[disabled],select.disabled,select[disabled],textarea.disabled,textarea[disabled]{
    background-color:#f6f6f6;
    border-color:transparent;
  }

input:active,input:focus,select:active,select:focus,textarea:active,textarea:focus{
    border:1px solid;
    border-color:{{ settings.color_body_text | default: "#1c1d1d" }};
    border-color:var(--colorTextBody);
  }

input[type=image]{
  background-color:transparent;
}

[data-center-text=true] .social-sharing{
    text-align:center
}

.social-sharing .icon{
    height:18px;
    width:18px;
  }

.social-sharing__link{
  display:inline-block;
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  border-radius:2px;
  margin:0 18px 0 0;
  text-decoration:none;
  font-weight:400
}

.social-sharing__link:last-child{
    margin-right:0;
  }

.social-sharing__title{
  font-size:calc(var(--typeBaseSize)*0.85);
  display:inline-block;
  vertical-align:middle;
  padding-right:15px;
  padding-left:3px;
}

.grid-search{
  margin-bottom:30px;
}

.grid-search__product{
  position:relative;
  text-align:center;
}

.grid-search__page-link{
  display:block;
  background-color:{{ settings.color_body_bg | default: "#fff" | color_darken: 2 }};
  padding:20px;
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  overflow:hidden;
  text-overflow:ellipsis
}

.grid-search__page-link:focus,.grid-search__page-link:hover{
    background-color:{{ settings.color_body_bg | default: "#fff" | color_darken: 4 }};
  }

.grid-search__page-content{
  display:block;
  height:100%;
  overflow:hidden;
}

.grid-search__page-content img{
  display:block;
  margin-bottom:10px;
}

.grid-search__image{
  display:block;
  padding:20px;
  margin:0 auto;
  max-height:100%;
  max-width:100%
}

@media only screen and (min-width:769px){

.grid-search__image{
    position:absolute;
    top:50%;
    left:50%;
    transform:translate(-50%, -50%)
}
  }

.index-section{
  margin:40px 0;
}

.index-section--small{
  margin:18px 0;
}

.index-section+.index-section,.index-section+.index-section--hidden,.main-content+.index-section{
  margin-top:0;
}

.index-section--flush{
  margin:0;
}

.section--divider{
  border-top:1px solid;
  border-top-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-top-color:var(--colorBorder);
  padding-top:40px;
}

.index-section--faq{
  margin-bottom:40px;
}

.newsletter-section{
  padding:40px 0;
}

.newsletter-section--with-divider{
  border-top:1px solid;
  border-top-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-top-color:var(--colorBorder);
}

.template-challange .index-section--footer{
  display:none;
}

.testimonials-section{
  padding:25.5px 0;
}

.testimonials-section--with-divider{
  border-top:1px solid;
  border-top-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-top-color:var(--colorBorder);
}

@media only screen and (min-width:769px){
  .index-section{
    margin:75px 0;
  }

  .index-section--small{
    margin:30px 0;
  }

  .section--divider{
    padding-top:75px;
  }

  .index-section--faq{
    margin:75px 0 37.5px;
  }

  .newsletter-section{
    padding:75px 0;
  }

  .testimonials-section{
    padding:75px 0;
  }
}

.page-blocks--flush .page-width{
  padding:0;
}

.page-blocks>div:first-child .index-section{
    margin-top:0;
  }

.feature-row-wrapper{
  overflow:hidden;
  direction:ltr;
}

.feature-row{
  margin:0 auto;
  display:flex;
  justify-content:space-between;
  align-items:center
}

@media only screen and (min-width:1050px){

.feature-row{
    margin:0 6%
}
  }

@media only screen and (max-width:768px){

.feature-row{
    flex-direction:column;
    margin:0
}
  }

@media only screen and (max-width:959px){
  .feature-row--small-none{
    display:block;
  }
}

.feature-row__item{
  flex:0 1 57%;
  margin:0 auto
}

@media only screen and (max-width:768px){

.feature-row__item{
    flex:1 1 auto;
    max-width:100%;
    min-width:100%
}
  }

.feature-row__item--overlap-images{
  display:flex;
  justify-content:space-between;
  align-items:center;
  padding:0 0 15px;
  margin:0 0 0 -30px
}

@media only screen and (min-width:769px){

.feature-row__item--overlap-images{
    padding:50px 0;
    margin:0 auto
}
  }

.feature-row__item--overlap-images>*{
    width:50%
  }

.feature-row__item--overlap-images>:first-child{
      z-index:1;
      transform:translate(30px, 30px)
    }

@media only screen and (min-width:769px){

.feature-row__item--overlap-images>:first-child{
        transform:translate(50px, 50px)
    }
      }

.feature-row__item--overlap-images svg{
    border:2px solid;
    border-color:{{ settings.color_body_bg | default: "#fff" }};
    border-color:var(--colorBody);
  }

.feature-row__image{
  display:block;
  margin:0 auto;
  order:1
}

@media only screen and (min-width:769px){

.feature-row__image{
    order:2
}
  }

.feature-row__text{
  min-width:43%;
  flex:0 1 43%;
  padding:0
}

@media only screen and (max-width:768px){

.feature-row__text{
    order:2;
    margin-top:0;
    padding:30px 20px 0;
    padding-bottom:0
}
  }

.feature-row__text .rte{
    margin:0;
  }

.feature-row__text .btn{
    margin-top:15px;
  }

@media only screen and (min-width:769px){
  .feature-row__text--left{
    padding-left:60px;
  }

  .feature-row__text--right{
    padding-right:60px;
  }
}

.product-block{
  margin-bottom:25px
}

.product-block hr{
    margin:0;
  }

.product-block--tab+.product-block--tab{
  margin-top:-25px;
}

.product-block--sales-point+.product-block--sales-point{
  margin-top:-20px;
}

.product-block--header+.product-block--price{
  margin-top:-20px;
}

.theme-block{
  margin-bottom:30px
}

.theme-block:last-child{
    margin-bottom:0;
  }

[data-product-blocks] .spr-starrating{
  margin-bottom:15px;
  margin-top:-15px;
}

.size-chart__standalone .tool-tip-trigger__title{
  letter-spacing:0.3em;
  font-size:0.8em;
}

.slideshow-wrapper{
  position:relative;
}

.slideshow__pause:focus{
  clip:auto;
  width:auto;
  height:auto;
  margin:0;
  color:{{ settings.color_button_text | default: "#fff" }};
  color:var(--colorBtnPrimaryText);
  background-color:{{ settings.color_button | default: "#000" }};
  background-color:var(--colorBtnPrimary);
  padding:10px;
  z-index:10000;
  transition:none
}

.video-is-playing .slideshow__pause:focus{
    display:none
}

.slideshow__pause-stop{
  display:block
}

.is-paused .slideshow__pause-stop{
    display:none
}

.slideshow__pause-play{
  display:none
}

.is-paused .slideshow__pause-play{
    display:block
}

.slideshow__slide{
  display:none;
  width:100%;
  height:100%;
  position:relative;
  overflow:hidden
}

.slideshow__slide:first-child{
    display:block;
  }

.flickity-slider .slideshow__slide{
    display:block
}

.hero{
  position:relative;
  overflow:hidden;
  background:{{ settings.color_large_image_bg | default: "#1c1d1d" }};
  background:var(--colorLargeImageBg);
}

.hero__media-container{
  position:absolute;
  top:0;
  left:0;
  height:100%;
  width:100%;
}

.hero__image-wrapper,.hero__media{
  position:absolute;
  top:0;
  left:0;
  height:100%;
  width:100%
}

.hero__image-wrapper:before,.hero__media:before{
    content:"";
    position:absolute;
    top:0;
    right:0;
    bottom:0;
    left:0;
    z-index:3;
    background-color:{{ settings.color_image_overlay | default: "#000" }};
    background-color:var(--colorImageOverlay);
    opacity:{{ settings.color_image_overlay_opacity | divided_by: 100.0 }};
    opacity:var(--colorImageOverlayOpacity)
  }

.video-interactable .hero__image-wrapper:before,.video-interactable .hero__media:before{
      pointer-events:none
  }

.hero__image{
  position:relative;
  width:100%;
  height:100%;
  z-index:1;
  -o-object-fit:cover;
     object-fit:cover;
}

.hero__media{
  background-repeat:no-repeat;
  background-size:cover;
  background-position:top;
  z-index:1;
}

.hero__image-wrapper--no-overlay:before{
  content:none;
}

.hero__media iframe,.hero__media video{
  width:100%;
  height:100%;
  pointer-events:none
}

.video-interactable .hero__media iframe,.video-interactable .hero__media video{
    pointer-events:auto
}

.video-parent-section.loading .hero__media iframe,.video-parent-section.loading .hero__media video{
    opacity:0.01
}

.video-parent-section.loaded .hero__media iframe,.video-parent-section.loaded .hero__media video{
    opacity:1;
    animation:zoom-fade 2.5s cubic-bezier(0.26, 0.54, 0.32, 1) 0s forwards;
    transition:none
}

.hero__media video{
  position:relative;
  -o-object-fit:cover;
     object-fit:cover;
  font-family:"object-fit: cover";
}

.hero__media iframe{
  position:absolute;
  top:0;
  left:0;
  width:300%;
  left:-100%;
  max-width:none
}

@media screen and (min-width:1140px){

.hero__media iframe{
    width:100%;
    height:300%;
    left:auto;
    top:-100%
}
  }

.vimeo-mobile-trigger{
  display:block;
  position:absolute;
  width:100%;
  z-index:2;
  margin-top:90px
}

.hero__text-content .vimeo-mobile-trigger{
    bottom:120%
}

.vimeo-mobile-trigger .icon{
    width:40px;
    height:40px;
    background-color:#fff;
    border-radius:50%;
    padding:10px;
  }

.hero__slide-link{
  display:block;
  position:absolute;
  height:100%;
  width:100%;
  z-index:5
}

.hero__slide-link:hover~.hero__text-wrap .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse){
      background:{{ settings.color_button | default: "#000" | color_lighten: 10 }};
      background:var(--colorBtnPrimaryLight);
      transition-delay:0.25s
    }

.hero__slide-link:hover~.hero__text-wrap .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):after{
        animation:shine 0.75s cubic-bezier(0.01, 0.56, 1, 1);
      }

[data-button_style=angled] .hero__slide-link:hover~.hero__text-wrap .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):after{
          animation:none;
        }

.hero__text-wrap{
  position:relative;
  height:100%;
  color:{{ settings.color_image_text | default: "#fff" }};
  color:var(--colorHeroText)
}

.video-interactable .hero__text-wrap{
    pointer-events:none
}

.hero__text-wrap .page-width{
    display:table;
    width:100%;
    height:100%;
  }

.hero__text-content{
  position:relative;
  padding:15px 0;
  z-index:4
}

@media only screen and (min-width:769px){

.hero__text-content{
    padding:45px 0
}
  }

[data-dots=true] .hero__text-content{
    padding-bottom:40px
}

.hero__text-shadow{
  position:relative;
  display:inline-block
}

.hero__text-shadow:before{
    content:"";
    position:absolute;
    top:0;
    right:0;
    bottom:0;
    left:0;
    z-index:auto;
    background:radial-gradient(rgba(0, 0, 0, {{ settings.color_image_overlay_text_shadow | divided_by: 100.0 }}) 0%, transparent 60%);
    background:radial-gradient(rgba(0, 0, 0, var(--colorImageOverlayTextShadow)) 0%, transparent 60%);
    margin:-100px -200px -100px -200px;
    z-index:-1;
  }

.hero__top-subtitle{
  text-transform:uppercase;
  letter-spacing:0.3em;
  font-size:1.1em;
  margin-bottom:5px;
}

.hero__title{
  display:block;
  margin-bottom:0;
}

.hero__subtext{
  margin-top:20px;
}

.hero__subtitle{
  font-size:1.3em;
  display:block;
  vertical-align:middle;
  margin-top:5px;
  margin-bottom:5px;
}

.hero__link{
  position:relative;
  display:block
}

.video-interactable .hero__link{
    pointer-events:auto
}

.hero__link .btn{
    margin:4px 3px 2px 0
  }

@media only screen and (min-width:769px){

.hero__link .btn{
      margin-right:5px;
      margin-top:0
  }
    }

[data-button_style=angled] .hero__link .btn{
      margin-left:12px
  }

.hero__link .btn+.btn{
    margin-left:2px
  }

@media only screen and (min-width:769px){

.hero__link .btn+.btn{
      margin-left:10px
  }
    }

[data-button_style=angled] .hero__link .btn+.btn{
      margin-left:12px
  }

.hero__link .btn .icon-play{
    position:relative;
    top:-2px;
    margin-right:5px;
  }

[data-button_style=angled] .hero__link .animation-cropper{
      padding-right:10px;
    }

.hero__text-content{
  display:table-cell
}

.hero__text-content .hero__link{
    margin-top:7.5px
  }

@media only screen and (min-width:769px){

.hero__text-content .hero__link{
      margin-top:15px
  }
    }

.hero__text-content.horizontal-left{
    text-align:left;
  }

.hero__text-content.horizontal-center{
    text-align:center;
  }

.hero__text-content.horizontal-right{
    text-align:right;
  }

.hero__text-content.vertical-center{
    vertical-align:middle;
  }

.hero__text-content.vertical-bottom{
    vertical-align:bottom;
  }

.overlaid-header .index-section--hero:first-child .hero__text-content.vertical-center{
  padding-top:50px
}

@media only screen and (min-width:769px){

.overlaid-header .index-section--hero:first-child .hero__text-content.vertical-center{
    padding-top:90px
}
  }

.hero--450px{
  height:292.5px;
}

.hero--550px{
  height:357.5px;
}

.hero--650px{
  height:422.5px;
}

.hero--750px{
  height:487.5px;
}

.hero--100vh{
  height:100vh;
}

@media only screen and (min-width:769px){
  .hero--natural[data-natural]{
    position:absolute;
    top:0;
    left:0;
    right:0;
    bottom:0;
  }
  .hero--450px{
    height:450px;
  }
  .hero--550px{
    height:550px;
  }
  .hero--650px{
    height:650px;
  }
  .hero--750px{
    height:750px;
  }
}

@media only screen and (max-width:768px){
  .hero--mobile--auto[data-mobile-natural=true]{
    position:absolute;
    top:0;
    left:0;
    right:0;
    bottom:0;
    height:auto;
  }
  .hero--natural[data-mobile-natural=false]{
    height:500px;
  }
  .hero--mobile--250px[data-mobile-natural=false]{
    height:250px;
  }
  .hero--mobile--300px[data-mobile-natural=false]{
    height:300px;
  }
  .hero--mobile--400px[data-mobile-natural=false]{
    height:400px;
  }
  .hero--mobile--500px[data-mobile-natural=false]{
    height:500px;
  }
  .hero--mobile--100vh[data-mobile-natural=false]{
    height:90vh;
  }
}

.slideshow__slide .animation-cropper,[data-aos=hero__animation] .animation-cropper{
    opacity:0;
  }

.slideshow__slide .animation-contents,[data-aos=hero__animation] .animation-contents{
    opacity:0;
    transform:translateY(15px);
    transition:none;
  }

.slideshow__slide:not(.animate-out) .hero__image,[data-aos=hero__animation]:not(.animate-out) .hero__image{
    opacity:0;
    transition:none;
  }

.slideshow__slide .hero__link .btn,[data-aos=hero__animation] .hero__link .btn{
    opacity:0;
    transition:none;
  }

.slideshow__slide .hero__image-wrapper,[data-aos=hero__animation] .hero__image-wrapper{
    transform:translateX(200px);
    transition:none;
    opacity:0;
  }

.loaded .slideshow__slide.is-selected .animation-cropper,[data-aos=hero__animation].loaded .animation-cropper{
    opacity:1;
  }

.loaded .slideshow__slide.is-selected .animation-contents,[data-aos=hero__animation].loaded .animation-contents{
    transform:translateY(0px);
  }

.loaded .slideshow__slide.is-selected .hero__title .animation-contents,[data-aos=hero__animation].loaded .hero__title .animation-contents{
    animation:0.8s cubic-bezier(0.26, 0.54, 0.32, 1) 0.3s forwards;
    animation-name:rise-up;
  }

.loaded .slideshow__slide.is-selected .hero__subtitle .animation-contents,.loaded .slideshow__slide.is-selected .hero__top-subtitle .animation-contents,[data-aos=hero__animation].loaded .hero__subtitle .animation-contents,[data-aos=hero__animation].loaded .hero__top-subtitle .animation-contents{
    animation:1s cubic-bezier(0.26, 0.54, 0.32, 1) 0.7s forwards;
    animation-name:rise-up;
  }

.loaded .slideshow__slide.is-selected .hero__link .animation-contents,[data-aos=hero__animation].loaded .hero__link .animation-contents{
    opacity:1;
  }

.loaded .slideshow__slide.is-selected .hero__link .btn,[data-aos=hero__animation].loaded .hero__link .btn{
    animation:fade-in 2s ease 1.3s forwards;
  }

.loaded .slideshow__slide.is-selected .hero__link .btn:nth-child(2),[data-aos=hero__animation].loaded .hero__link .btn:nth-child(2){
    animation:fade-in 2s ease 1.6s forwards;
  }

.loaded .slideshow__slide.is-selected .hero__image--svg,.loaded .slideshow__slide.is-selected .hero__image.lazyloaded,.loaded .slideshow__slide.is-selected .hero__media,[data-aos=hero__animation].loaded .hero__image--svg,[data-aos=hero__animation].loaded .hero__image.lazyloaded,[data-aos=hero__animation].loaded .hero__media{
    opacity:1;
    transition:none;
  }

.loaded .slideshow__slide.is-selected .hero__image-wrapper,[data-aos=hero__animation].loaded .hero__image-wrapper{
    opacity:1;
    transform:translateX(0px);
    transition:transform 0.7s ease,opacity 0.7s ease;
  }

[data-aos=hero__animation].loaded .hero__image-wrapper{
    transition:opacity 0.7s ease !important;
  }

.no-js .hero__image,.no-js .hero__image-wrapper{
    opacity:1 !important;
    transform:none;
  }

.slideshow__slide.animate-out .animation-cropper{
    opacity:0;
    transition:none;
  }

.slideshow__slide.animate-out .hero__image-wrapper{
    opacity:0;
    transform:translateX(-200px);
    transition:transform 0.5s ease-in 0.05s,opacity 0.5s ease-in 0.05s;
  }

.slideshow__slide.animate-out .hero__link{
    opacity:0;
    transition:none;
  }

.collection-content{
  padding-top:22.5px
}

@media only screen and (min-width:769px){

.collection-content{
    padding-top:45px
}
  }

.collection-filter{
  display:flex;
  align-items:center;
  justify-content:flex-end
}

@media only screen and (max-width:768px){

.collection-filter{
    flex-wrap:wrap;
    justify-content:space-between;
    position:sticky;
    top:17px;
    z-index:5
}

.sticky-header .collection-filter{
      top:86px
}
  }

.collection-filter .btn,.collection-filter select{
    height:44px;
    font-size:16px;
  }

.collection-filter select{
    display:block;
    width:100%;
  }

.collection-filter .btn{
    padding:0 20px;
    border:1px solid;
    border-color:{{ settings.color_borders | default: "#1c1d1d" }};
    border-color:var(--colorBorder)
  }

@media only screen and (max-width:768px){

.collection-filter .btn{
      width:100%;
      text-align:left
  }
    }

.collection-filter .btn .icon{
      position:relative;
      top:-2px;
      margin-right:10px;
    }

.collection-filter__item{
  flex:1 1 auto;
  margin-bottom:15px;
}

.collection-filter .btn:not(.btn--tertiary-active),.collection-filter__sort-container{
  background:{{ settings.color_body_bg | default: "#fff" }};
  background:var(--colorBody);
}

.collection-filter__item--drawer{
  flex:0 1 48%;
}

.collection-filter__item--count{
  flex:1 1 170%;
  text-align:center;
}

.collection-filter__item--sort{
  flex:0 1 48%;
}

@media only screen and (max-width:768px){
  .collection-filter__item--count{
    order:3;
    flex:1 1 100%;
  }
}

.rte.collection__description{
  margin-bottom:45px;
}

.collection-hero{
  position:relative;
  width:100%;
  height:250px;
  overflow:hidden;
  background:{{ settings.color_large_image_bg | default: "#1c1d1d" }};
  background:var(--colorLargeImageBg);
  box-sizing:content-box;
  box-sizing:initial
}

@media only screen and (min-width:769px){

.collection-hero{
    height:420px
}
  }

.collection-hero__content{
  position:absolute;
  top:0;
  left:0;
  bottom:0;
  width:100%;
  display:flex;
  align-items:flex-end;
  padding:15px 0;
  z-index:1
}

@media only screen and (min-width:769px){

.collection-hero__content{
    padding:30px 0
}
  }

[data-type_headers_align_text=true] .collection-hero__content{
    align-items:center
}

.overlaid-header .collection-hero__content{
    padding-top:70px
}

@media only screen and (min-width:769px){

.overlaid-header .collection-hero__content{
      padding-top:100px
}
    }

.collection-hero__content .page-width{
    width:100%;
  }

.collection-hero__content:before{
    content:"";
    position:absolute;
    top:0;
    right:0;
    bottom:0;
    left:0;
    z-index:auto;
    background-color:{{ settings.color_image_overlay | default: "#000" }};
    background-color:var(--colorImageOverlay);
    opacity:{{ settings.color_image_overlay_opacity | divided_by: 100.0 }};
    opacity:var(--colorImageOverlayOpacity);
    display:block;
  }

.collection-hero__content .section-header{
    opacity:0;
    animation:fade-in 0.5s 1s forwards
  }

[data-disable-animations=true] .collection-hero__content .section-header{
      opacity:1;
      animation:none
  }

.collection-hero__image{
  animation:zoom-fade 2.5s cubic-bezier(0.26, 0.54, 0.32, 1) 0s forwards;
  transition:none
}

[data-disable-animations=true] .collection-hero__image{
    animation:none;
    opacity:1
}

.collection-grid__wrapper.unload{
  min-height:180px;
  position:relative;
  opacity:0.2;
  transition:opacity 0.3s
}

.collection-grid__wrapper.unload [data-section-type=collection-grid]{
    animation:grid-product__loading 1.5s ease 1.5s infinite;
  }

.collection-sidebar{
  padding-right:10px;
  margin-bottom:20px;
}

@media only screen and (min-width:769px){
  .grid__item--sidebar{
    position:sticky;
    top:10px;
    max-height:90vh;
    overflow-y:auto;
  }
}

.collection-sidebar__group{
  border-top:1px solid;
  border-top-color:{{ settings.color_drawer_border | default: "#343535" }};
  border-top-color:var(--colorDrawerBorder);
  padding-top:5px;
  margin-top:5px;
  margin-bottom:5px;
  overflow:hidden
}

.collection-sidebar__group:first-child{
    margin-top:0;
  }

.collection-sidebar__group .collapsible-content__inner{
    padding-bottom:5px;
  }

.filter-form div:first-of-type .collection-sidebar__group{
  border-top:0;
  padding-top:0;
}

.is-light .site-header__logo .logo--has-inverted{
    display:none
}

.site-header__logo .logo--inverted{
  display:none
}

.is-light .site-header__logo .logo--inverted{
    display:block
}

@media only screen and (min-width:769px){

.site-header__logo{
    text-align:left
}
  }

.header-logo a,.site-header__logo a{
  color:{{ settings.color_header_text | default: "#000" }};
  color:var(--colorNavText)
}

.is-light .header-logo a,.is-light .site-header__logo a{
    color:#fff
}

.is-light .header-logo a:hover,.is-light .site-header__logo a:hover{
      color:#fff;
    }

.is-light .site-header{
    box-shadow:none
}

.site-header--password{
  color:{{ settings.color_header_text | default: "#000" }};
  color:var(--colorNavText)
}

.site-header--password a,.site-header--password a:hover{
    color:{{ settings.color_header_text | default: "#000" }};
    color:var(--colorNavText);
  }

.is-light .site-header--password{
    color:#fff
}

.is-light .site-header--password a,.is-light .site-header--password a:hover{
      color:#fff;
    }

.site-nav__dropdown-link{
  display:block;
  padding:8px 15px;
  white-space:nowrap
}

.megamenu .site-nav__dropdown-link{
    padding:4px 0;
    white-space:normal
}

.tag-list__header{
  text-align:left;
}

.tag-list .tag-list{
  margin-left:15px;
}

.tag-list a,.tag-list label{
  cursor:pointer;
  display:block;
  line-height:1.4;
}

.tag-list label:hover{
  text-decoration:underline;
  text-underline-offset:2px;
}

.tag--active>a,.tag--active>label{
  font-weight:900;
}

.tag-list--active-tags:empty{
  display:none;
}

.tag-list--checkboxes{
  padding-bottom:6px
}

.tag-list--checkboxes a{
    position:relative;
    padding-left:25px;
    font-size:calc(var(--typeBaseSize) - 2px);
    font-size:max(calc(var(--typeBaseSize) - 2px), 14px)
  }

.tag-list--checkboxes a:before{
      content:"";
      position:absolute;
      left:0;
      top:50%;
      transform:translateY(-50%);
    }

.tag-list--checkboxes a:before{
      border:1px solid;
      border-color:{{ settings.color_borders | default: "#1c1d1d" }};
      border-color:var(--colorBorder);
      height:16px;
      width:16px;
    }

.tag--active .tag__checkbox:after,.tag-list--checkboxes .tag--active a:after,input:checked~.tag__checkbox:after{
  background-color:{{ settings.color_body_text | default: "#1c1d1d" }};
  background-color:var(--colorTextBody);
  border-color:{{ settings.color_body_text | default: "#1c1d1d" }};
  border-color:var(--colorTextBody);
}

.tag--remove{
  position:relative
}

.tag--remove a{
    text-align:left;
  }

.tag--remove .icon{
    position:absolute;
    right:10px;
    top:50%;
    transform:translateY(-50%);
    pointer-events:none;
    color:{{ settings.color_button_text | default: "#fff" }};
    color:var(--colorBtnPrimaryText);
  }

.tag-list--swatches{
  margin-top:2px;
  margin-left:-2px
}

.drawer .tag-list--swatches{
    margin-left:-2px
}

.tag-list--swatches li{
    display:inline-block;
  }

label.tag__checkbox-wrapper{
  display:flex;
}

.tag__checkbox{
  position:relative;
  padding-left:25px
}

.tag__checkbox:after,.tag__checkbox:before{
    content:"";
    position:absolute;
    left:0;
    top:50%;
    transform:translateY(-50%);
  }

.tag__checkbox:before{
    border:1px solid;
    border-color:{{ settings.color_borders | default: "#1c1d1d" }};
    border-color:var(--colorBorder);
    height:16px;
    width:16px;
  }

.tag__checkbox:after{
    height:10px;
    width:10px;
    left:3px;
  }

.tag__input{
  position:absolute;
    opacity:0;
    cursor:pointer;
    height:0;
    width:0;
}

.variant-input-wrap{
  border:0;
  padding:0;
  margin:0 0 -12px;
  position:relative
}

.variant-input-wrap input{
    border:0;
    clip:rect(0 0 0 0);
    height:1px;
    margin:-1px;
    overflow:hidden;
    padding:0;
    position:absolute;
    width:1px;
  }

.variant-input-wrap label{
    font-family:var(--typeBasePrimary),var(--typeBaseFallback);
    font-size:calc(var(--typeBaseSize)*0.92);
    letter-spacing:var(--typeBaseSpacing);
    line-height:var(--typeBaseLineHeight)
  }

@media only screen and (min-width:769px){

.variant-input-wrap label{
    font-size:var(--typeBaseSize)
  }
  }

.variant-input-wrap label{
    position:relative;
    display:inline-block;
    font-weight:400;
    padding:7px 15px 7px;
    margin:0 8px 12px 0;
    background-color:{{ settings.color_body_bg | default: "#fff" }};
    background-color:var(--colorBody);
    box-shadow:0 0 0 1px {{ settings.color_borders | default:"#1c1d1d" }};
    box-shadow:0 0 0 1px var(--colorBorder);
    overflow:hidden;
    border-radius: 7px;
  }

[data-center-text=true] .variant-input-wrap label{
      margin:0 4px 12px
  }

.variant-input-wrap label.disabled{
      color:{{ settings.color_borders | default: "#1c1d1d" }};
      color:var(--colorBorder);
    }

.variant-input-wrap label.disabled:after{
      position:absolute;
      content:"";
      left:50%;
      top:0;
      bottom:0;
      border-left:1px solid;
      border-color:{{ settings.color_borders | default: "#1c1d1d" }};
      border-color:var(--colorBorder);
      transform:rotate(45deg);
    }

.variant-input-wrap input[type=radio]:focus+label{
    border-color:{{ settings.color_body_text | default: "#1c1d1d" | color_modify: "alpha", 0.05 }};
    box-shadow:0 0 0 1px {{ settings.color_body_text | default:"#1c1d1d" }};
    box-shadow:0 0 0 1px var(--colorTextBody);
  }

.variant-input-wrap input[type=radio]:checked+label{
    box-shadow:0 0 0 2px {{ settings.color_body_text | default:"#1c1d1d" }};
    box-shadow:0 0 0 2px var(--colorTextBody);
  }

.variant-input{
  display:inline-block
}

select .variant-input{
    display:block
}

.variant-wrapper+.variant-wrapper{
  margin-top:25px;
}

.no-js .variant-wrapper{
  display:none;
}

.variant-wrapper--dropdown{
  display:inline-block;
  max-width:100%;
  margin-right:15px;
  margin-bottom:0;
}

.variant__label[for]{
  display:block;
  margin-bottom:10px;
  cursor:default;
}

.variant__label-info{
  text-transform:none;
  font-weight:400;
}

.grid-product{
  margin-bottom:15px
}

@media only screen and (min-width:769px){

.grid-product{
    margin-bottom:30px
}
  }

@media only screen and (max-width:768px){

.grid-overflow-wrapper .grid-product{
      margin-bottom:0
}
  }

.grid-product__content{
  position:relative;
  text-align:left
}

html[dir=rtl] .grid-product__content{
    text-align:right
}

.grid-product__title--heading{
  font-family:var(--typeHeaderPrimary),var(--typeHeaderFallback);
  font-weight:var(--typeHeaderWeight);
  letter-spacing:var(--typeHeaderSpacing);
  line-height:var(--typeHeaderLineHeight);
}

[data-type_header_capitalize=true] .grid-product__title--heading{
    text-transform:uppercase;
}

.grid-product__title--heading{
  font-size:calc(var(--typeBaseSize) + 2px);
  line-height:1;
}

[data-type_product_capitalize=true] .grid-product__title{
    text-transform:uppercase;
    font-size:calc(var(--typeBaseSize) - 2px);
    letter-spacing:0.2em
  }

@media only screen and (max-width:768px){

[data-type_product_capitalize=true] .grid-product__title{
      font-size:calc(var(--typeBaseSize) - 3px)
  }
    }

.grid-product__title{
  word-break:break-word;
  -webkit-hyphens:auto;
          hyphens:auto;
}

.grid-product__link{
  display:block;
}

.grid-product__link--inline{
  display:flex
}

.grid-product__link--inline .grid-product__image-mask{
    flex:0 0 auto;
    margin-right:10px;
  }

.grid-product__link--inline .grid__image-ratio{
    width:80px;
  }

.grid-product__link--inline .grid-product__meta{
    text-align:left;
  }

.grid-product__image-mask{
  position:relative;
  overflow:hidden;
}

.grid-product__image{
  display:block;
  margin:0 auto;
  width:100%;
}

.grid-product__secondary-image{
  position:absolute;
  top:-1px;
  left:-1px;
  right:-1px;
  bottom:-1px;
  opacity:0;
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody)
}

.grid-product__secondary-image img{
    height:100%;
    width:100%;
    -o-object-fit:cover;
       object-fit:cover;
  }

.grid-product__image-mask:hover .grid-product__secondary-image{
    animation:fade-in 0.2s cubic-bezier(0.26, 0.54, 0.32, 1) 0s forwards
}

@media only screen and (max-width:768px){

.supports-touch .grid-product__secondary-image{
      display:none
}
  }

.grid-product__color-image{
  position:absolute;
  top:0;
  left:0;
  right:0;
  bottom:0;
  background-repeat:no-repeat;
  background-size:cover;
  background-position:50%;
  opacity:0;
  transition:opacity 0.4s ease;
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody)
}

.grid-product__color-image.is-active{
    animation:fade-in 0.5s cubic-bezier(0.26, 0.54, 0.32, 1) 0s forwards;
  }

[data-center-text=true] .grid-product__meta{
    text-align:center
}

.grid-product__meta{
  position:relative;
  padding:10px 0 6px 0;
  line-height:{{ settings.type_base_line_height | default: 1.4 | minus: 0.1 }}
}

@media only screen and (max-width:768px){

.small--grid--flush .grid-product__meta{
      padding-left:10px;
      padding-right:10px
}
  }

.grid-product__vendor{
  text-transform:uppercase;
  letter-spacing:0.3em;
  font-size:0.8em;
  margin-top:5px;
  opacity:0.65;
}

.grid-product__price{
  font-size:calc(var(--typeBaseSize)*0.85);
  color:{{ settings.color_price | default: "#1c1d1d" }};
  color:var(--colorPrice);
  margin-top:5px;
}

.grid-product__price--original{
  text-decoration:line-through;
  margin-right:5px;
}

.grid-product__price--savings{
  color:{{ settings.color_savings_text | default: "#1c1d1d" }};
  color:var(--colorTextSavings);
  margin-left:5px;
  white-space:nowrap;
}

.grid-product__tag{
  font-size:calc(var(--typeBaseSize)*0.65);
  position:absolute;
  top:0;
  right:0;
  line-height:1;
  padding:6px 5px 6px 7px;
  background-color:{{ settings.color_button | default: "#000" }};
  background-color:var(--colorBtnPrimary);
  color:{{ settings.color_button_text | default: "#fff" }};
  color:var(--colorBtnPrimaryText);
  z-index:2;
  transition:opacity 0.4s ease
}

.grid-product__tag.grid-product__tag--sold-out{
    background-color:{{ settings.color_body_bg | default: "#fff" }};
    background-color:var(--colorBody);
    color:{{ settings.color_body_text | default: "#1c1d1d" }};
    color:var(--colorTextBody);
  }

@media only screen and (min-width:769px){

.grid-product__tag{
    font-size:calc(var(--typeBaseSize)*0.85);
    padding:7px 7px 7px 9px
}
  }

.grid-product__tag--sale{
  background-color:{{ settings.color_sale_tag | default: "#1c1d1d" }};
  background-color:var(--colorSaleTag);
  color:{{ settings.color_sale_tag_text | default: "#ffffff" }};
  color:var(--colorSaleTagText);
}

.quick-product__btn{
  font-size:calc(var(--typeBaseSize)*0.85);
  position:absolute;
  bottom:10px;
  right:10px;
  left:10px;
  z-index:2;
  background-color:{{ settings.color_button | default: "#000" }};
  background-color:var(--colorBtnPrimary);
  color:{{ settings.color_button_text | default: "#fff" }};
  color:var(--colorBtnPrimaryText);
  overflow:hidden;
  padding:8px 5px;
  text-align:center;
  cursor:pointer;
  opacity:0;
  transform:translateY(5px);
  transition:opacity 0.25s ease,transform 0.25s ease-out,background 0.4s ease
}

.no-js .quick-product__btn{
    display:none
}

.grid-product__content:hover .quick-product__btn{
    opacity:1;
    transform:translateY(0);
    transition:opacity 0.25s ease,transform 0.25s ease-out
}

.supports-touch .quick-product__btn{
    display:none
}

.quick-product__btn--not-ready{
  pointer-events:none;
}

@media only screen and (max-width:768px){
  .grid-overflow-wrapper{
    overflow:hidden;
    overflow-x:scroll;
    -webkit-overflow-scrolling:touch
  }

    .grid-overflow-wrapper .grid{
      white-space:nowrap;
      display:flex;
    }

    .grid-overflow-wrapper .grid__item{
      width:39vw;
      flex:0 0 39vw;
      display:inline-block;
      float:none;
      white-space:normal
    }

      .grid-overflow-wrapper .grid__item:first-child{
        margin-left:17px;
      }

      .grid-overflow-wrapper .grid__item:last-child:after{
        content:"";
        display:inline-block;
        width:100%;
        margin-right:17px;
      }

    .grid-overflow-wrapper .grid__item--view-all{
      align-self:center;
    }

    .grid-overflow-wrapper .grid-product__price,.grid-overflow-wrapper .grid__item{
      font-size:0.75rem;
    }

  [data-aos=overflow__animation]{
    transform:translateX(100vw);
    transition:all 0.8s cubic-bezier(.25,.46,.45,.94)
  }

    [data-aos=overflow__animation].aos-animate{
      transform:translateX(0);
    }

  [data-disable-animations=true] [data-aos=overflow__animation]{
      transform:none;
      transition:none
  }
}

.grid-product__see-all{
  display:inline-block;
  padding:15px;
  text-align:center;
  border:1px solid;
  border-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-color:var(--colorBorder);
  margin-top:-60px;
}

[data-center-text=true] .grid-product__colors{
    text-align:center
}

.grid-product__colors{
  margin-top:3px;
  display:flex;
  flex-wrap:wrap;
  line-height:15px
}

@media only screen and (min-width:769px){

.grid-product__colors{
    line-height:19px
}
  }

[data-center-text=true] .grid-product__colors{
    align-items:center;
    justify-content:center
}

.grid-product__colors+.grid-product__colors{
    margin-top:4px;
  }

.sales-points{
  list-style:none;
  padding:0;
  margin:25px 0
}

.quick-add-modal .sales-points{
    display:none
}

.sales-point{
  display:block;
  margin-bottom:10px
}

.sales-point:last-child{
    margin-bottom:0;
  }

.sales-point .icon{
    position:relative;
    width:25px;
    height:25px;
    margin-right:10px
  }

[dir=rtl] .sales-point .icon{
      margin-right:0;
      margin-left:10px
  }

@media only screen and (max-width:768px){
  .sales-point .icon-and-text{
    justify-content:left;
  }
}

@keyframes inventory-pulse{
  0%{
    opacity:0.5;
  }
  to{
    transform:scale(2.5);
    opacity:0;
  }
}

.icon--inventory:after,.icon--inventory:before{
    width:9px;
    height:9px;
    background:#54c63a;
    border-radius:9px;
    position:absolute;
    left:0;
    top:0;
    content:"";
    margin:8px;
  }

.icon--inventory:before{
    animation:inventory-pulse 2s linear infinite;
  }

.inventory--low .icon--inventory:after,.inventory--low .icon--inventory:before{
      background:#f4af29;
    }

.color-swatch{
  position:relative;
  display:block;
  text-indent:-9999px;
  overflow:hidden;
  margin:0 4px 4px;
  background-position:50%;
  background-size:cover;
  background-repeat:no-repeat;
  height:40px;
  width:40px;
  box-shadow:0 0 0 1px {{ settings.color_borders | default:"#1c1d1d" }};
  box-shadow:0 0 0 1px var(--colorBorder);
  transition:box-shadow 0.2s ease
}

[data-swatch_style=round] .color-swatch{
    border-radius:100%
}

.color-swatch:before{
    content:"";
    position:absolute;
    top:0;
    left:0;
    right:0;
    bottom:0;
    z-index:2;
  }

.color-swatch:before{
    border:3px solid;
    border-color:{{ settings.color_body_bg | default: "#fff" }};
    border-color:var(--colorBody)
  }

[data-swatch_style=round] .color-swatch:before{
      border-radius:100%;
      border-width:4px;
      top:-1px;
      left:-1px;
      right:-1px;
      bottom:-1px
  }

.tag:not(.tag--active) label:hover .color-swatch:hover,a.color-swatch:hover{
  box-shadow:0 0 0 1px {{ settings.color_body_text | default:"#1c1d1d" }};
  box-shadow:0 0 0 1px var(--colorTextBody);
}

.tag--active .color-swatch{
  box-shadow:0 0 0 2px {{ settings.color_body_text | default:"#1c1d1d" }};
  box-shadow:0 0 0 2px var(--colorTextBody);
}

.color-swatch--small{
  width:15px;
  height:15px
}

@media only screen and (min-width:769px){

.color-swatch--small{
    width:19px;
    height:19px
}
  }

.color-swatch--small:before{
    border:2px solid;
    border-color:{{ settings.color_body_bg | default: "#fff" }};
    border-color:var(--colorBody)
  }

[data-swatch_style=round] .color-swatch--small:before{
      border-width:3px
  }

.tag--swatch{
  display:inline-block
}

.tag--swatch>label{
    margin-bottom:0;
  }

.color-swatch--filter{
  width:35px;
  height:35px;
}

.tag--active .color-swatch--filter:hover:after{
  position:absolute;
  content:"";
  left:50%;
  top:0;
  bottom:0;
  border-left:1px solid;
  border-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-color:var(--colorBorder);
  transform:rotate(45deg);
}

.collection-item{
  position:relative;
  display:block;
  margin-bottom:17px;
  overflow:hidden
}

@media only screen and (min-width:769px){

.collection-item{
    margin-bottom:22px
}
  }

.grid--no-gutters .collection-item{
    margin-bottom:0
}

.collection-item:hover .collection-image{
    transform:scale(1.03);
    transition:all 0.8s ease
  }

[data-disable-animations=true] .collection-item:hover .collection-image{
      transform:none;
      transition:none
  }

.collection-item:not(.collection-item--below):after{
    content:"";
    position:absolute;
    top:0;
    right:0;
    bottom:0;
    left:0;
    z-index:auto;
    background-color:{{ settings.collection_grid_tint | default: "#000" }};
    background-color:var(--colorGridOverlay);
    opacity:0.1;
    opacity:var(--colorGridOverlayOpacity);
    transition:opacity 0.8s ease;
  }

.collection-item:not(.collection-item--below):hover:after{
    opacity:0.25;
    opacity:calc(var(--colorGridOverlayOpacity) + 0.15);
  }

.collection-image{
  position:relative;
  transition:transform 0.3s ease
}

.collection-image img{
    position:absolute;
    top:0;
    left:0;
    height:100%;
    width:100%;
    -o-object-fit:cover;
       object-fit:cover;
  }

.collection-item--below img{
  -o-object-fit:contain;
     object-fit:contain;
}

.collection-image--placeholder{
  opacity:1
}

.collection-image--placeholder svg{
    position:absolute;
    top:0;
    left:0;
    right:0;
    bottom:0;
  }

.collection-image--square{
  padding-bottom:100%;
}

.collection-image--landscape{
  padding-top:75%;
}

.collection-image--portrait{
  padding-top:150%;
}

.collection-item__title{
  display:block;
  z-index:2;
  font-size:calc(var(--typeCollectionTitle)*0.8);
  line-height:1em
}

@media only screen and (min-width:769px){

.collection-item__title{
    font-size:var(--typeCollectionTitle)
}

.medium-up--one-fifth .collection-item__title{
      font-size:16px
}
  }

[data-type_product_capitalize=true] .collection-item__title span{
      text-transform:uppercase;
      font-size:0.8em;
      letter-spacing:0.2em;
    }

.collection-item--below .collection-item__title{
    margin-top:5px
}

.collection-item__title--heading{
  font-family:var(--typeHeaderPrimary),var(--typeHeaderFallback);
  font-weight:var(--typeHeaderWeight);
  letter-spacing:var(--typeHeaderSpacing);
  line-height:var(--typeHeaderLineHeight);
}

[data-type_header_capitalize=true] .collection-item__title--heading{
    text-transform:uppercase;
}

.collection-item__title--overlaid,.collection-item__title--overlaid-box{
  display:block;
  position:absolute;
  left:10px;
  right:10px;
}

.collection-item__title--overlaid{
  color:#fff;
  text-shadow:0 0 50px #000;
}

.collection-item__title--overlaid-box>span{
    display:inline-block;
    background-color:{{ settings.color_body_bg | default: "#fff" }};
    background-color:var(--colorBody);
    -webkit-box-decoration-break:clone;
            box-decoration-break:clone;
    padding:8px 15px;
    color:{{ settings.color_body_text | default: "#1c1d1d" }};
    color:var(--colorTextBody);
  }

.collection-item__title--bottom-center,.collection-item__title--center,.collection-item__title--top-center{
  text-align:center;
}

.collection-item__title--bottom-right,.collection-item__title--top-right .collection-item__title--right{
  text-align:right;
}

.collection-item__title--center[class*=collection-item__title--overlaid],.collection-item__title--left[class*=collection-item__title--overlaid],.collection-item__title--right[class*=collection-item__title--overlaid]{
    top:50%;
    transform:translateY(-50%);
  }

.collection-item__title--top-center[class*=collection-item__title--overlaid],.collection-item__title--top-left[class*=collection-item__title--overlaid],.collection-item__title--top-right[class*=collection-item__title--overlaid]{
    top:10px
  }

@media only screen and (min-width:769px){

.collection-item__title--top-center[class*=collection-item__title--overlaid],.collection-item__title--top-left[class*=collection-item__title--overlaid],.collection-item__title--top-right[class*=collection-item__title--overlaid]{
      top:18px
  }
    }

.collection-item__title--bottom-center[class*=collection-item__title--overlaid],.collection-item__title--bottom-left[class*=collection-item__title--overlaid],.collection-item__title--bottom-right[class*=collection-item__title--overlaid]{
    bottom:10px
  }

@media only screen and (min-width:769px){

.collection-item__title--bottom-center[class*=collection-item__title--overlaid],.collection-item__title--bottom-left[class*=collection-item__title--overlaid],.collection-item__title--bottom-right[class*=collection-item__title--overlaid]{
      bottom:18px
  }
    }

.custom-content{
  display:flex;
  align-items:stretch;
  flex-wrap:wrap;
  width:auto;
  margin-bottom:-22px;
  margin-left:-22px
}

@media only screen and (max-width:768px){

.custom-content{
    margin-bottom:-17px;
    margin-left:-17px
}
  }

.custom__item{
  flex:0 0 auto;
  margin-bottom:22px;
  padding-left:22px;
  max-width:100%
}

@media only screen and (max-width:768px){

.custom__item{
    flex:0 0 auto;
    padding-left:17px;
    margin-bottom:17px
}

    .custom__item.small--one-half{
      flex:1 0 50%;
      max-width:400px;
      margin-left:auto;
      margin-right:auto;
    }
  }

.custom__item img{
    display:block;
  }

.custom__item-inner{
  position:relative;
  display:inline-block;
  text-align:left;
  max-width:100%;
  width:100%;
}

.custom__item-inner--html,.custom__item-inner--video{
  display:block;
}

.custom__item-inner--image{
  width:100%;
}

.custom__item-inner--html img{
  display:block;
  margin:0 auto;
}

.custom__item-inner--placeholder-image{
  width:100%;
}

.align--top-middle{
  text-align:center;
}

.align--top-right{
  text-align:right;
}

.align--middle-left{
  align-self:center;
}

.align--center{
  align-self:center;
  text-align:center;
}

.align--middle-right{
  align-self:center;
  text-align:right;
}

.align--bottom-left{
  align-self:flex-end;
}

.align--bottom-middle{
  align-self:flex-end;
  text-align:center;
}

.align--bottom-right{
  align-self:flex-end;
  text-align:right;
}

.article__grid-image{
  display:block;
  text-align:center;
  margin-bottom:17px
}

@media only screen and (min-width:769px){

.article__grid-image{
    margin-bottom:20px
}
  }

.article__grid-image img{
    display:block;
  }

.article__title{
  font-size:calc(var(--typeBaseSize) + 2px);
}

.article__date{
  font-size:max(calc(var(--typeBaseSize)*0.7), 12px);
  margin-bottom:3px
}

.section-header .article__date{
    margin-bottom:15px
}

.article__author{
  margin-top:2px;
  font-size:max(calc(var(--typeBaseSize)*0.7), 12px);
}

.article__grid-meta{
  margin-bottom:30px
}

@media only screen and (min-width:769px){

[data-center-text=true] .article__grid-meta{
    text-align:center
}
  }

.article__excerpt{
  margin-top:10px;
}

.logo-bar{
  text-align:center;
  margin-bottom:-30px;
  display:flex;
  align-items:center;
  justify-content:center;
  flex-wrap:wrap;
}

.logo-bar__item{
  flex:0 1 110px;
  vertical-align:middle;
  margin:0 15px 20px
}

@media only screen and (min-width:769px){

.logo-bar__item{
    flex:0 1 160px;
    margin:0 20px 30px
}
  }

.logo-bar__image{
  display:block;
  margin:0 auto;
}

.logo-bar__link{
  display:block;
}

[data-aos=logo__animation] .logo-bar__item{
  opacity:0;
}

[data-aos=logo__animation].aos-animate .logo-bar__item{
  animation:fade-in 0.5s ease 0s forwards;
}

[data-aos=logo__animation].aos-animate .logo-bar__item:nth-child(2){
  animation-delay:0.2s;
}

[data-aos=logo__animation].aos-animate .logo-bar__item:nth-child(3){
  animation-delay:0.4s;
}

[data-aos=logo__animation].aos-animate .logo-bar__item:nth-child(4){
  animation-delay:0.6s;
}

[data-aos=logo__animation].aos-animate .logo-bar__item:nth-child(5){
  animation-delay:0.8s;
}

[data-aos=logo__animation].aos-animate .logo-bar__item:nth-child(6){
  animation-delay:1.0s;
}

[data-aos=logo__animation].aos-animate .logo-bar__item:nth-child(7){
  animation-delay:1.2s
}

[data-aos=logo__animation].aos-animate .logo-bar__item:nth-child(8){
  animation-delay:1.4s;
}

[data-aos=logo__animation].aos-animate .logo-bar__item:nth-child(9){
  animation-delay:1.6s;
}

[data-aos=logo__animation].aos-animate .logo-bar__item:nth-child(10){
  animation-delay:1.8s;
}

.background-media-text{
  position:absolute;
  width:100%;
  overflow:hidden;
  background:{{ settings.color_large_image_bg | default: "#1c1d1d" }};
  background:var(--colorLargeImageBg);
  background-size:cover;
}

.background-media-text__video{
  position:absolute;
  top:0;
  left:0;
  bottom:0;
  width:100%;
  background-size:cover;
  background-position:50% 50%;
  background-repeat:no-repeat;
  z-index:0;
}

@media only screen and (max-width:768px){

.background-media-text__video{
    width:300%;
    left:-100%
}
  }

.background-media-text__video iframe,.background-media-text__video video{
    position:absolute;
    top:0;
    left:0;
    height:100%;
    width:100%;
    pointer-events:none
  }

@media only screen and (min-width:769px){

.background-media-text__video iframe,.background-media-text__video video{
      height:120%;
      max-width:none;
      left:-100%;
      height:150%;
      width:300%
  }
    }

@media screen and (min-width:1140px){

.background-media-text__video iframe,.background-media-text__video video{
      width:100%;
      height:300%;
      left:auto;
      top:-100%
  }
    }

.video-interactable .background-media-text__video iframe,.video-interactable .background-media-text__video video{
    pointer-events:auto;
  }

.background-media-text__inner{
  position:absolute;
  z-index:2;
  width:100%;
  height:100%;
}

.background-media-text__aligner{
  margin:60px;
}

.background-media-text__text{
  text-align:left;
  font-size:1.1em;
  background:{{ settings.color_body_bg | default: "#fff" }};
  background:var(--colorBody);
  padding:30px;
  width:380px
}

html[dir=rtl] .background-media-text__text{
    text-align:right
}

@media only screen and (max-width:768px){

.background-media-text__text{
    text-align:center
}
  }

.background-media-text__text--framed{
  border:7px solid;
  border-color:{{ settings.color_body_bg | default: "#fff" }};
  border-color:var(--colorBody);
  box-shadow:inset 0 0 0 2px {{ settings.color_body_text | default:"#1c1d1d" }};
  box-shadow:inset 0 0 0 2px var(--colorTextBody)
}

@media only screen and (min-width:769px){

.background-media-text__text--framed{
    border-width:10px
}
  }

.background-media-text__text .btn{
  margin-top:15px;
}

@media only screen and (min-width:769px){
  .background-media-text--right .animation-cropper{
    float:right;
  }
}

.background-media-text__container{
  position:absolute;
  top:0;
  left:0;
  right:0;
  bottom:0;
}

@media only screen and (max-width:768px){
  .background-media-text{
    position:relative;
  }
  .background-media-text__inner{
    position:relative;
  }
  .background-media-text__container,.background-media-text__video{
    position:relative;
    height:240px
  }
  .promo-video .background-media-text__container,.promo-video .background-media-text__video{
      height:550px
  }
  .background-media-text__aligner{
    margin:-6px 6px 6px;
  }
  .background-media-text__text{
    padding:22.5px;
    width:auto;
  }
    .background-media-text.loading:after,.background-media-text.loading:before{
      top:117px;
    }
}

@media only screen and (min-width:769px){
  .background-media-text--450,.background-media-text--450 .background-media-text__video,.background-media-text__spacer.background-media-text--450{
    min-height:450px;
  }
  .background-media-text--550,.background-media-text--550 .background-media-text__video,.background-media-text__spacer.background-media-text--550{
    min-height:550px;
  }
  .background-media-text--650,.background-media-text--650 .background-media-text__video,.background-media-text__spacer.background-media-text--650{
    min-height:650px;
  }
  .background-media-text--750,.background-media-text--750 .background-media-text__video,.background-media-text__spacer.background-media-text--750{
    min-height:750px;
  }
}

[data-aos=background-media-text__animation] .background-media-text__image,[data-aos=background-media-text__animation] .background-media-text__image svg,[data-aos=background-media-text__animation] .background-media-text__video{
  animation:zoom-fade 2.5s cubic-bezier(0.26, 0.54, 0.32, 1) 0s forwards;
  transition:none
}

[data-disable-animations=true] [data-aos=background-media-text__animation] .background-media-text__image,[data-disable-animations=true] [data-aos=background-media-text__animation] .background-media-text__image svg,[data-disable-animations=true] [data-aos=background-media-text__animation] .background-media-text__video{
    animation:none;
    opacity:1
}

[data-aos=background-media-text__animation].aos-animate .background-media-text__image.lazyloaded,[data-aos=background-media-text__animation].aos-animate .background-media-text__image svg,[data-aos=background-media-text__animation].loaded.aos-animate .background-media-text__video{
  animation:zoom-fade 2.5s cubic-bezier(0.26, 0.54, 0.32, 1) 0s forwards;
  transition:none
}

[data-disable-animations=true] [data-aos=background-media-text__animation].aos-animate .background-media-text__image.lazyloaded,[data-disable-animations=true] [data-aos=background-media-text__animation].aos-animate .background-media-text__image svg,[data-disable-animations=true] [data-aos=background-media-text__animation].loaded.aos-animate .background-media-text__video{
    animation:none
}

@media only screen and (min-width:769px){
  [data-aos=background-media-text__animation] .background-media-text__inner .animation-contents{
    opacity:0
  }
  .no-js [data-aos=background-media-text__animation] .background-media-text__inner .animation-contents{
      animation:none;
      opacity:1
  }

  [data-aos=background-media-text__animation].loaded.aos-animate .background-media-text__inner .animation-contents{
    animation:rise-up 1s cubic-bezier(0.26, 0.54, 0.32, 1) 0.5s forwards
  }

  [data-disable-animations=true] [data-aos=background-media-text__animation].loaded.aos-animate .background-media-text__inner .animation-contents{
      animation:none;
      opacity:1
  }
}

.quote-icon{
  display:block;
  margin:0 auto 20px;
}

.testimonial-stars{
  display:block;
  font-size:16px;
  letter-spacing:0.2em;
  margin-bottom:10px
}

@media only screen and (min-width:769px){

.testimonial-stars{
    font-size:18px;
    margin-bottom:15px
}
  }

.testimonials-slide{
  display:none;
  opacity:0;
  padding:40px 0 55px;
  width:33%
}

.testimonials-slide:first-child{
    display:block;
  }

.flickity-slider .testimonials-slide{
    display:block;
    opacity:1
}

.testimonials-slide .testimonials-slider__text{
    transform:scale(0.95);
    transition:transform 0.5s ease,box-shadow 0.5s ease;
  }

.testimonials-slide.is-selected .testimonials-slider__text{
    transform:scale(1.1);
    box-shadow:0 10px 25px rgba(0, 0, 0, 0.1);
    position:relative;
    z-index:10;
  }

@media only screen and (max-width:768px){
    .testimonials-slide{
      width:100%;
    }

    .testimonials-slide .testimonials-slider__text{
      transform:scale(0.86);
    }

    .testimonials-slide.is-selected .testimonials-slider__text{
      transform:scale(1);
    }
  }

.testimonials-slider__text{
  margin:0 30px;
  padding:30px 15px;
  background:{{ settings.color_body_bg | default: "#fff" }};
  background:var(--colorBody);
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  margin-bottom:15px
}

@media only screen and (min-width:769px){

.testimonials-slider__text{
    margin:0;
    padding:30px;
    margin-bottom:0
}
  }

.testimonials-slider__text cite{
    font-style:normal;
    font-weight:700
  }

@media only screen and (min-width:769px){

.testimonials-slider__text cite{
      font-size:calc(var(--typeBaseSize) + 1px)
  }
    }

.testimonials-slider__text p{
    margin-bottom:7.5px
  }

.testimonials-slider__text p+cite{
      margin-top:0;
    }

.testimonials__info{
  font-size:calc(var(--typeBaseSize) - 1px);
}

.testimonail-image{
  max-width:142px;
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody)
}

.text-center .testimonail-image{
    margin-left:auto;
    margin-right:auto
}

.testimonail-image .image-wrap{
    background:none;
  }

.testimonail-image--round{
  width:65px;
  height:65px;
  max-width:none;
  border-radius:65px
}

.testimonail-image--round img{
    overflow:hidden;
    border-radius:65px;
  }

.testimonials-section .flickity-page-dots{
  bottom:0;
}

.announcement-bar{
  font-size:calc(var(--typeBaseSize)*0.75);
  position:relative;
  text-align:center;
  background-color:{{ settings.color_announcement | default: "#1c1d1d" }};
  background-color:var(--colorAnnouncement);
  color:{{ settings.color_announcement_text | default: "#fff" }};
  color:var(--colorAnnouncementText);
  padding:10px 0;
  border-bottom-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-bottom-color:var(--colorBorder)
}

@media only screen and (min-width:769px){

.announcement-bar{
    font-size:calc(var(--typeBaseSize)*0.85)
}
  }

.announcement-bar a,.announcement-bar a:visited{
    color:{{ settings.color_announcement_text | default: "#fff" }};
    color:var(--colorAnnouncementText)
  }

.announcement-bar a:active,.announcement-bar a:hover,.announcement-bar a:visited:active,.announcement-bar a:visited:hover{
      color:{{ settings.color_announcement_text | default: "#fff" }};
      color:var(--colorAnnouncementText);
    }

.announcement-slider__slide{
  position:relative;
  overflow:hidden;
  padding:0 5px;
  width:100%;
}

.announcement-link{
  display:block;
  color:{{ settings.color_announcement_text | default: "#fff" }};
  color:var(--colorAnnouncementText)
}

.announcement-link:active,.announcement-link:hover{
    color:{{ settings.color_announcement_text | default: "#fff" }};
    color:var(--colorAnnouncementText);
  }

.announcement-text{
  font-weight:700;
  display:block;
  text-transform:uppercase;
  letter-spacing:0.2em;
  font-size:0.9em;
}

.announcement-link-text{
  display:block
}

.announcement-link .announcement-link-text{
    text-decoration:underline
}

.announcement-link-text a{
    color:inherit;
  }

@media only screen and (min-width:769px){
    .announcement-slider[data-compact=true] .announcement-slider__slide{
      display:none
    }

      .announcement-slider[data-compact=true] .announcement-slider__slide:first-child{
        display:block;
      }
    .announcement-slider[data-compact=true] .announcement-link-text,.announcement-slider[data-compact=true] .announcement-text{
      display:inline;
    }

    .announcement-slider[data-compact=true] .announcement-text+.announcement-link-text{
      padding-left:5px;
    }

    .announcement-slider[data-compact=true].flickity-enabled .announcement-slider__slide{
      display:block;
    }
  .announcement-slider[data-compact=false]{
    display:flex
  }

    .announcement-slider[data-compact=false] .announcement-slider__slide{
      flex:1 1 33%;
    }
}

@media only screen and (max-width:768px){
    .announcement-slider .announcement-slider__slide{
      display:none
    }

      .announcement-slider .announcement-slider__slide:first-child{
        display:block;
      }

    .announcement-slider.flickity-enabled .announcement-slider__slide{
      display:block;
    }
}

.shopify-challenge__container{
  padding:30px 22px
}

@media only screen and (min-width:769px){

.shopify-challenge__container{
    padding:120px 0
}
  }

.newsletter{
  margin:0 auto;
  max-width:520px;
}

.newsletter-section .errors{
  margin-left:auto;
  margin-right:auto;
  max-width:520px;
}

.modal .newsletter .h1{
  margin-bottom:15px
}

@media only screen and (min-width:769px){

.modal .newsletter .h1{
    margin-bottom:30px
}
  }

.modal .newsletter .image-wrap{
  margin-bottom:7.5px
}

@media only screen and (min-width:769px){

.modal .newsletter .image-wrap{
    margin-bottom:15px
}
  }

.modal .newsletter .text-close{
  text-decoration:underline;
}

.newsletter__input-group{
  margin:0 auto 20px;
  max-width:400px
}

.newsletter__input-group:last-child{
    margin-bottom:0;
  }

.newsletter__input::-webkit-input-placeholder{
    color:{{ settings.color_body_text | default: "#1c1d1d" }};
    color:var(--colorTextBody);
    opacity:1;
  }

.newsletter__input:-moz-placeholder{
    color:{{ settings.color_body_text | default: "#1c1d1d" }};
    color:var(--colorTextBody);
    opacity:1;
  }

.newsletter__input::-moz-placeholder{
    color:{{ settings.color_body_text | default: "#1c1d1d" }};
    color:var(--colorTextBody);
    opacity:1;
  }

.newsletter__input:-ms-input-placeholder{
    color:{{ settings.color_body_text | default: "#1c1d1d" }};
    color:var(--colorTextBody);
  }

.newsletter__input::-ms-input-placeholder{
    color:{{ settings.color_body_text | default: "#1c1d1d" }};
    color:var(--colorTextBody);
    opacity:1;
  }

@media only screen and (max-width:768px){
  .form__submit--large{
    display:none;
  }

  .form__submit--small{
    display:block;
  }
}

@media only screen and (min-width:769px){
  .form__submit--large{
    display:block;
  }

  .form__submit--small{
    display:none;
  }
}

[data-has-image] .modal__close{
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody);
  border-radius:50%;
  padding:5px;
  top:10px;
  right:10px
}

@media only screen and (max-width:768px){

[data-has-image] .modal__close{
    background-color:transparent;
    top:0;
    right:0
}
  }

@media only screen and (max-width:768px){

#NewsletterPopup-newsletter-popup{
    bottom:20px;
    text-align:center
}

    #NewsletterPopup-newsletter-popup .modal__inner{
      max-width:calc(100% - 40px);
    }

    #NewsletterPopup-newsletter-popup.modal--square .modal__close{
      padding:5px;
    }
  }

#NewsletterPopup-newsletter-popup .h2{
    margin-bottom:0
  }

@media only screen and (min-width:769px){

#NewsletterPopup-newsletter-popup .h2{
      margin-bottom:0
  }
    }

#NewsletterPopup-newsletter-popup .rte{
    margin-top:20px;
    margin-bottom:0
  }

@media only screen and (min-width:769px){

#NewsletterPopup-newsletter-popup .rte{
      margin-top:20px;
      margin-bottom:0
  }
    }

#NewsletterPopup-newsletter-popup .popup-cta{
    margin-bottom:0;
  }

#NewsletterPopup-newsletter-popup .note--success{
    margin-top:15px;
    text-align:center;
  }

#NewsletterPopup-newsletter-popup .newsletter__input-group{
    margin-top:30px;
    margin-bottom:0
  }

@media only screen and (max-width:768px){

#NewsletterPopup-newsletter-popup .newsletter__input-group{
      margin-top:20px;
      margin-bottom:0
  }
    }

#NewsletterPopup-newsletter-popup .newsletter-button{
    margin-top:30px;
  }

#NewsletterPopup-newsletter-popup .social-icons a{
    display:inline-block;
    margin-top:30px;
    padding:5px;
  }

.modal__centered-content.newsletter--has-image{
  padding:0
}

@media only screen and (max-width:768px){

.modal__centered-content.newsletter--has-image{
    padding:0
}
  }

.modal__centered-content.newsletter--has-image .newsletter-popup{
    display:flex;
    max-width:800px;
    min-width:650px
  }

@media only screen and (max-width:768px){

.modal__centered-content.newsletter--has-image .newsletter-popup{
      max-width:none;
      min-width:0
  }
    }

.modal__centered-content.newsletter--has-image .newsletter-popup.newsletter-popup--image-reversed{
      flex-direction:row-reverse;
    }

.modal__centered-content.newsletter--has-image .form__submit--large{
    display:none;
  }

.modal__centered-content.newsletter--has-image .form__submit--small{
    display:block;
  }

.modal__centered-content.newsletter--has-image .newsletter-popup__image{
    width:50%;
    background-position:50%;
    background-size:cover;
    background-repeat:no-repeat
  }

@media only screen and (max-width:768px){

.modal__centered-content.newsletter--has-image .newsletter-popup__image{
      display:none
  }
    }

.modal__centered-content.newsletter--has-image .newsletter-popup__content{
    padding:40px;
    width:50%
  }

@media only screen and (max-width:768px){

.modal__centered-content.newsletter--has-image .newsletter-popup__content{
      padding:0 0 20px;
      width:100%
  }
    }

newsletter-reminder{
  color:{{ settings.color_button_text | default: "#fff" }};
  color:var(--colorBtnPrimaryText);
  background-color:{{ settings.color_button | default: "#000" }};
  background-color:var(--colorBtnPrimary)
}

newsletter-reminder.modal--square .modal__close{
    color:{{ settings.color_button_text | default: "#fff" }};
    color:var(--colorBtnPrimaryText)
  }

newsletter-reminder.modal--square .modal__close:hover{
      color:{{ settings.color_button_text | default: "#fff" }};
      color:var(--colorBtnPrimaryText);
      opacity:50%;
    }

newsletter-reminder .newsletter-reminder__message{
    font-size:calc(var(--typeHeaderSize)*0.5);
  }

.form__submit--small{
  line-height:0;
}

.map-section{
  position:relative;
  height:650px;
  width:100%;
  overflow:hidden
}

@media only screen and (min-width:769px){

.map-section{
    height:500px
}
  }

.map-section .page-width{
    height:100%;
  }

.map-section--load-error{
  height:auto;
}

.map-onboarding{
  position:absolute;
  top:0;
  left:0;
  bottom:0;
  width:100%;
  background-size:cover;
  background-position:50% 50%;
  background-repeat:no-repeat;
  z-index:0;
}

.map-section__overlay-wrapper{
  position:relative;
  height:100%;
}

.map-section__overlay{
  position:relative;
  display:inline-block;
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody);
  padding:30px;
  margin:30px;
  width:100%;
  max-width:calc(100% - 60px);
  z-index:3
}

@media only screen and (min-width:769px){

.map-section__overlay{
    position:absolute;
    left:30px;
    top:50%;
    transform:translateY(-50%);
    margin-top:0;
    width:30%
}
  }

.map-section--load-error .map-section__overlay{
    position:static;
    transform:translateY(0)
}

.map-section__link{
  display:block;
  position:absolute;
  top:0;
  left:0;
  width:100%;
  height:100%;
  z-index:2;
}

.map-section__container{
  position:absolute !important;
  top:0;
  left:0;
  width:100%;
  height:150%;
  margin-bottom:-50%
}

@media only screen and (min-width:769px){

.map-section__container{
    width:130%;
    height:100%;
    margin:0 -30% 0 0
}
  }

[data-aos=map-section__animation] .map-section__container{
  animation:fade-out 0.5s cubic-bezier(0.26, 0.54, 0.32, 1) 0s forwards;
  opacity:0;
}

[data-aos=map-section__animation].aos-animate .map-section__container{
  animation:zoom-fade 2.5s cubic-bezier(0.26, 0.54, 0.32, 1) 0s forwards;
}

.image-row:after{content:"";display:table;clear:both;}

.image-row__placeholder{
  float:left;
  width:33.33%
}

.image-row--gutters .image-row__placeholder{
    width:32%;
    margin:0 1% 2%
}

.image-row--gutters .image-row__placeholder:first-child{
      margin-left:0;
    }

.image-row--gutters .image-row__placeholder:last-child{
      margin-right:0;
    }

.image-row__image{
  position:relative;
  min-height:1px;
  float:left
}

.image-row__image:after{
    content:"";
    display:block;
    height:0;
    width:100%;
  }

.image-row__image img{
    display:block;
    position:absolute;
    top:0;
    left:0;
  }

[data-zoom=true] .image-row__image .js-photoswipe__zoom{
    cursor:zoom-in;
  }

[data-zoom=true] .image-row__image a .js-photoswipe__zoom{
    cursor:pointer;
  }

.promo-grid--space-top{
  padding-top:40px
}

@media only screen and (min-width:769px){

.promo-grid--space-top{
    padding-top:75px
}
  }

.promo-grid--space-bottom{
  padding-bottom:40px
}

@media only screen and (min-width:769px){

.promo-grid--space-bottom{
    padding-bottom:75px
}
  }

.promo-grid__container{
  display:flex;
  align-items:flex-start;
  position:relative;
  overflow:hidden;
  background-repeat:no-repeat
}

.promo-grid__container .btn{
    margin-right:10px
  }

@media only screen and (max-width:768px){

.promo-grid__container .btn{
      margin-right:7px
  }
    }

.promo-grid__container.horizontal-center{
    justify-content:center;
    text-align:center
  }

.promo-grid__container.horizontal-center .btn{
      margin:2px 5px;
    }

.promo-grid__container.horizontal-right{
    justify-content:flex-end;
    text-align:right;
  }

@media only screen and (max-width:768px){
    .promo-grid__container--boxed .promo-grid__bg{
      height:60%;
    }

    .promo-grid__container--boxed .promo-grid__content{
      width:100%;
      margin-top:55%;
      box-shadow:0 0 50px rgba(0, 0, 0, 0.1);
    }
  }

.promo-grid__container--framed:not(.promo-grid__container--boxed):after{
  content:"";
  position:absolute;
  top:0;
  right:0;
  bottom:0;
  left:0;
  z-index:auto;
  border:7px solid transparent;
  box-shadow:inset 0 0 0 2px {{ settings.color_image_text | default:"#fff" }};
  box-shadow:inset 0 0 0 2px var(--colorHeroText);
  z-index:3
}

@media only screen and (min-width:769px){

.promo-grid__container--framed:not(.promo-grid__container--boxed):after{
    border-width:10px
}
  }

.type-banner .promo-grid__container--framed:not(.promo-grid__container--boxed):after,.type-product .promo-grid__container--framed:not(.promo-grid__container--boxed):after,.type-sale_collection .promo-grid__container--framed:not(.promo-grid__container--boxed):after{
    box-shadow:inset 0 0 0 2px {{ settings.color_body_text | default:"#1c1d1d" }};
    box-shadow:inset 0 0 0 2px var(--colorTextBody)
}

.video-interactable.promo-grid__container--framed:not(.promo-grid__container--boxed):after{
  pointer-events:none;
}

.promo-grid__container--tint:before{
  content:"";
  position:absolute;
  top:0;
  right:0;
  bottom:0;
  left:0;
  z-index:auto;
  opacity:1;
  z-index:2;
  pointer-events:none;
}

.promo-grid__slide-link{
  display:block;
  position:absolute;
  width:100%;
  height:100%;
  z-index:5
}

.promo-grid__slide-link:hover~.promo-grid__content .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse){
      background:{{ settings.color_button | default: "#000" | color_lighten: 10 }};
      background:var(--colorBtnPrimaryLight);
      transition-delay:0.25s
    }

[data-button_style=square] .promo-grid__slide-link:hover~.promo-grid__content .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):after,[data-button_style^=round] .promo-grid__slide-link:hover~.promo-grid__content .btn:not(.btn--secondary):not(.btn--tertiary):not(.btn--inverse):after{
          animation:shine 0.75s cubic-bezier(0.01, 0.56, 1, 1);
        }

.promo-grid__content{
  flex:0 1 auto;
  padding:2em 2.5em;
  position:relative;
  min-width:200px;
  z-index:4
}

.promo-grid__container--framed:not(.promo-grid__container--boxed) .promo-grid__content{
    margin:1.5em
}

.promo-grid__content p:last-child{
    margin-bottom:0;
  }

.vertical-top .promo-grid__content{
    align-self:flex-start
}

.vertical-center .promo-grid__content{
    align-self:center
}

.vertical-bottom .promo-grid__content{
    align-self:flex-end
}

.video-interactable .promo-grid__content{
  pointer-events:none;
}

.promo-grid__content--boxed{
  background:{{ settings.color_body_bg | default: "#fff" }};
  background:var(--colorBody);
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  margin:15px
}

@media only screen and (max-width:768px){

.promo-grid__content--boxed{
    margin:10px
}
  }

.promo-grid__content--framed.promo-grid__content--boxed{
  border:7px solid;
  border-color:{{ settings.color_body_bg | default: "#fff" }};
  border-color:var(--colorBody);
  box-shadow:inset 0 0 0 2px {{ settings.color_body_text | default:"#1c1d1d" }};
  box-shadow:inset 0 0 0 2px var(--colorTextBody)
}

@media only screen and (min-width:769px){

.promo-grid__content--framed.promo-grid__content--boxed{
    border-width:10px
}
  }

.type-advanced .promo-grid__content:not(.promo-grid__content--boxed):not(.promo-grid__content--sale),.type-simple .promo-grid__content:not(.promo-grid__content--boxed):not(.promo-grid__content--sale){
    color:{{ settings.color_image_text | default: "#fff" }};
    color:var(--colorHeroText)
  }

.type-advanced .promo-grid__content:not(.promo-grid__content--boxed):not(.promo-grid__content--sale) a,.type-simple .promo-grid__content:not(.promo-grid__content--boxed):not(.promo-grid__content--sale) a{
      color:{{ settings.color_image_text | default: "#fff" }};
      color:var(--colorHeroText);
      border-bottom:2px solid;
      border-bottom-color:{{ settings.color_image_text | default: "#fff" }};
      border-bottom-color:var(--colorHeroText)
    }

[data-button_style=square] .type-advanced .promo-grid__content:not(.promo-grid__content--boxed):not(.promo-grid__content--sale) a:not(.btn--inverse),[data-button_style=square] .type-simple .promo-grid__content:not(.promo-grid__content--boxed):not(.promo-grid__content--sale) a:not(.btn--inverse),[data-button_style^=round] .type-advanced .promo-grid__content:not(.promo-grid__content--boxed):not(.promo-grid__content--sale) a:not(.btn--inverse),[data-button_style^=round] .type-simple .promo-grid__content:not(.promo-grid__content--boxed):not(.promo-grid__content--sale) a:not(.btn--inverse){
          border-bottom:0;
        }

.type-advanced .promo-grid__content:not(.promo-grid__content--boxed):not(.promo-grid__content--sale) .btn--inverse,.type-simple .promo-grid__content:not(.promo-grid__content--boxed):not(.promo-grid__content--sale) .btn--inverse{
      border-color:{{ settings.color_image_text | default: "#fff" }};
      border-color:var(--colorHeroText);
    }

.type-advanced .promo-grid__content:not(.promo-grid__content--boxed):not(.promo-grid__content--sale):after,.type-simple .promo-grid__content:not(.promo-grid__content--boxed):not(.promo-grid__content--sale):after{
      content:"";
      position:absolute;
      top:0;
      right:0;
      bottom:0;
      left:0;
      z-index:auto;
      background:radial-gradient(rgba(0, 0, 0, {{ settings.color_image_overlay_text_shadow | divided_by: 100.0 }}) 0%, transparent 60%);
      background:radial-gradient(rgba(0, 0, 0, var(--colorImageOverlayTextShadow)) 0%, transparent 60%);
      margin:-100px -200px -100px -200px;
      z-index:2;
    }

.type-advanced .rte--strong,.type-product .rte--strong,.type-sale_collection .rte--strong{
    font-family:var(--typeHeaderPrimary),var(--typeHeaderFallback);
    font-weight:var(--typeHeaderWeight);
    letter-spacing:var(--typeHeaderSpacing);
    line-height:var(--typeHeaderLineHeight);
  }

[data-type_header_capitalize=true] .type-advanced .rte--strong,[data-type_header_capitalize=true] .type-product .rte--strong,[data-type_header_capitalize=true] .type-sale_collection .rte--strong{
    text-transform:uppercase;
  }

.type-advanced .rte--strong,.type-product .rte--strong,.type-sale_collection .rte--strong{
    line-height:1.1;
  }

.type-advanced .rte--em,.type-product .rte--em,.type-sale_collection .rte--em{
    text-transform:uppercase;
    letter-spacing:0.3em;
    font-size:0.9375em;
    line-height:1.2;
  }

.type-advanced .rte--strong,.type-product .rte--strong{
    font-size:1.6em;
    line-height:1.1;
  }

@media only screen and (min-width:769px){
    .type-advanced .rte--strong,.type-product .rte--strong{
      font-size:2.125em;
    }
      .type-product.flex-grid__item--33 .rte--strong,.type-product.flex-grid__item--50 .rte--strong{
        font-size:1.6em;
      }
}

.promo-grid__bg{
  position:absolute;
  top:0;
  left:0;
  width:100%;
  height:100%;
  text-align:left
}

.promo-grid__container:not(.promo-grid__container--boxed) .promo-grid__bg:before{
    content:"";
    position:absolute;
    top:0;
    right:0;
    bottom:0;
    left:0;
    z-index:3;
    background-color:{{ settings.color_image_overlay | default: "#000" }};
    background-color:var(--colorImageOverlay);
    opacity:{{ settings.color_image_overlay_opacity | divided_by: 100.0 }};
    opacity:var(--colorImageOverlayOpacity)
}

.promo-grid__bg .placeholder-svg{
    position:absolute;
    top:0;
    left:50%;
    transform:translateX(-50%);
    max-width:none;
    width:auto;
    padding:0;
  }

.video-interactable .promo-grid__bg:before{
  pointer-events:none;
}

.promo-grid__bg-image{
  z-index:1;
  opacity:0
}

.no-js .promo-grid__bg-image{
    opacity:1
}

.promo-grid__bg-image.lazyloaded{
    animation:fade-in 1s cubic-bezier(0.26, 0.54, 0.32, 1) 0s forwards;
    transition:none;
  }

.promo-grid__text{
  position:relative;
  z-index:3
}

.promo-grid__text .btn{
    margin-top:2px;
    margin-bottom:2px;
  }

.type-advanced .promo-grid__content{
    padding:2em
  }

@media only screen and (max-width:768px){

.type-advanced .promo-grid__content{
      padding:1.5em
  }
    }

.type-advanced .btn{
    margin-bottom:10px
  }

@media only screen and (max-width:768px){

.type-advanced .btn{
      margin-bottom:7px
  }
    }

.type-sale_collection{
  flex-grow:1;
  max-height:600px
}

.type-sale_collection .promo-grid__container{
    background:{{ settings.color_small_image_bg | default: "#eee" }};
    background:var(--colorSmallImageBg);
    align-items:center;
  }

.type-sale_collection .promo-grid__content{
    padding:2em;
    flex:0 1 auto;
    min-width:0
  }

@media only screen and (max-width:768px){

.type-sale_collection .promo-grid__content{
      padding:1em;
      font-size:0.9em
  }
    }

@media only screen and (min-width:769px){
      .type-sale_collection .promo-grid__content:not(.promo-grid__content--small-text){
        font-size:1.5em;
      }
    }

.type-sale_collection .rte--block{
    margin-bottom:7.5px
  }

.type-sale_collection .rte--block:last-child{
      margin-bottom:0;
    }

.type-sale_collection .rte--strong{
    position:relative;
    display:block;
    font-size:3.375em;
    line-height:1;
    white-space:nowrap;
  }

.type-sale_collection small{
    display:inline;
    font-size:0.25em;
    margin-left:-2.9em;
    letter-spacing:0;
  }

.type-sale-images{
  flex:1 1 50%
}

.type-sale-images svg{
    display:block;
    width:50%;
    float:left;
  }

.type-sale-images__crop{
  overflow:hidden;
  width:100%;
}

.type-sale-images__image{
  width:50%;
  float:left;
}

.type-simple .promo-grid__content{
    padding:30px
  }

@media only screen and (max-width:768px){

.type-simple .promo-grid__content{
      padding:15px
  }
    }

.promo-grid__title:last-child{
  margin-bottom:0;
}

.type-image .promo-grid__container{
    background:none;
  }

.type-image .image-wrap,.type-image a,.type-image img{
    width:100%;
  }

.type-banner{
  flex:1 0 100%
}

.type-banner .promo-grid__container{
    background:none;
  }

.type-banner .promo-grid__container--framed:not(.promo-grid__container--boxed){
    padding:1em;
  }

.type-banner p{
    margin:5px 10px;
    display:inline-block;
    vertical-align:middle;
    line-height:1.2;
  }

.type-banner__link{
  display:block;
  flex:1 1 100%;
}

.type-banner__content{
  width:100%;
  display:flex;
  align-items:center;
  justify-content:center;
  padding:0 10px;
}

.type-banner__text{
  position:relative;
  flex:0 1 auto;
  z-index:3;
  padding:10px 20px;
}

.type-banner__image{
  flex:0 0 45%
}

@media only screen and (min-width:769px){

.type-banner__image{
    flex:0 0 200px
}
  }

.type-product__wrapper{
  flex:1 1 100%;
  align-self:center;
  position:relative;
  padding:2em 0;
  z-index:4
}

.type-product__wrapper.promo-grid__container--tint{
    padding:2em;
  }

.promo-grid__container--framed .type-product__wrapper{
    padding:2em
}

@media only screen and (max-width:768px){

.type-product__content{
    margin-top:15px
}
  }

.type-product__images{
  position:relative;
  width:100%;
  margin:10px 0 10px 10px;
}

.type-product__image{
  position:relative
}

.type-product__image:first-child{
    width:100%;
    max-width:75%;
  }

.type-product__image:nth-child(2){
    position:absolute;
    bottom:40px;
    right:0;
    width:100%;
    max-width:30%;
  }

.type-product__labels{
  position:absolute;
  top:-10px;
  right:-10px;
  z-index:3;
  text-align:right
}

@media only screen and (min-width:769px){
    .type-product__labels .flex-grid__item--33,.type-product__labels .flex-grid__item--50{
      font-size:0.9em;
    }
  }

.type-product__label{
  padding:4px 12px;
  background-color:{{ settings.color_button | default: "#000" }};
  background-color:var(--colorBtnPrimary);
  color:{{ settings.color_button_text | default: "#fff" }};
  color:var(--colorBtnPrimaryText);
  float:right;
  clear:both;
}

.type-product__label--secondary{
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody);
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
}

.store-availability{
  display:flex;
  justify-content:space-around
}

.store-availability .icon{
    margin:6px 0 0;
    width:12px;
    height:12px;
  }

.store-availability .icon-in-stock{
    fill:#56ad6a;
  }

.store-availability .icon-out-of-stock{
    fill:#d02e2e;
  }

.store-availability+.store-availability{
    margin-top:20px;
  }

.store-availability__info{
  flex:0 1 90%;
  text-align:left;
  margin-left:10px
}

.store-availability__info>div{
    margin-bottom:5px
  }

.store-availability__info>div:last-child{
      margin-bottom:0;
    }

.store-availability__info a{
    text-decoration:underline;
  }

.store-availability__small{
  font-size:0.8em
}

.store-availability__small a{
    display:block;
    margin-top:10px;
  }

.modal .store-availability__small--link{
    display:none
}

.age-verification-popup{
  top:0;
  bottom:0
}

.age-verification-popup.age-verification-popup--image-false{
    background-color:{{ settings.color_body_bg | default: "#fff" }};
    background-color:var(--colorBody)
  }

.age-verification-popup.age-verification-popup--image-false.modal:before{
      background-color:{{ settings.color_body_bg | default: "#fff" }};
      background-color:var(--colorBody);
      animation:none;
    }

.age-verification-popup.age-verification-popup--image-false .modal__inner{
      box-shadow:none;
    }

.age-verification-popup.modal--mobile-friendly.modal--square .modal__centered-content{
    background-color:{{ settings.color_drawer_background | default: "#1c1d1d" }};
    background-color:var(--colorDrawers);
  }

@media only screen and (min-width:769px){
  .cart__page{
    display:flex;
    flex-wrap:wrap;
    justify-content:space-between;
    align-items:flex-start;
  }

  .cart__page-col:first-child{
    flex:1 1 60%;
    padding-right:100px;
  }

  .cart__page-col:last-child{
    position:sticky;
    top:10%;
    flex:0 1 35%;
    padding:30px
  }

    .cart__page-col:last-child:after{
      content:"";
      position:absolute;
      top:0;
      right:0;
      bottom:0;
      left:0;
      z-index:-1;
      background-color:{{ settings.color_body_text | default: "#1c1d1d" }};
      background-color:var(--colorTextBody);
      opacity:0.03;
    }
}

.cart__item{
  display:flex;
  margin-bottom:20px;
  padding-bottom:20px;
  border-bottom:1px solid;
  border-bottom-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-bottom-color:var(--colorBorder)
}

.cart__item:first-child{
    padding-top:10px;
  }

.cart__item:last-child{
    margin-bottom:0;
    border-bottom:0;
  }

.cart__image{
  flex:0 0 150px;
  margin-right:17px;
  margin-right:var(--grid-gutter)
}

.cart__image a{
    display:block;
  }

.cart__image img{
    width:100%;
    display:block;
  }

.drawer .cart__image{
    flex:0 0 100px
}

.cart__item-details{
  flex:1 1 auto;
  display:flex;
  flex-wrap:wrap;
}

.cart__item-title{
  flex:1 1 100%
}

.drawer .cart__item-title{
    font-size:calc(var(--typeBaseSize)*0.85)
}

.cart__item-sub{
  flex:1 1 100%;
  display:flex;
  justify-content:space-between;
  line-height:1
}

.cart__item-sub>div:first-child{
    margin-right:10px;
  }

.cart__item-row{
  margin-bottom:20px;
}

.cart__remove a{
    display:inline-block;
    margin-top:10px;
  }

.drawer .cart__remove{
    display:none
}

.cart__checkout{
  width:100%;
}

.cart__item-name{
  display:block;
  font-size:calc(var(--typeBaseSize) + 1px);
  margin-bottom:8px;
  font-weight: 600;
}

.cart__item--variants{
  margin-bottom:10px
}

.cart__item--variants span{
    font-weight:700;
  }

.cart__price{
  display:block;
}

.cart__price--strikethrough{
  text-decoration:line-through;
}

.cart__discount{
  color:{{ settings.color_savings_text | default: "#1c1d1d" }};
  color:var(--colorTextSavings);
}

.cart__terms{
  display:flex;
  align-items:center;
  justify-content:center
}

.cart__terms label{
    margin-bottom:0;
    text-transform:none;
    letter-spacing:0;
  }

.cart__checkout-wrapper{
  margin-top:20px
}

.cart__checkout-wrapper .additional-checkout-buttons{
    margin-top:12px;
  }

.drawer .additional-checkout-buttons{
  margin:10px 0
}

.drawer .additional-checkout-buttons [data-shopify-buttoncontainer]{
    justify-content:center
  }

.drawer .additional-checkout-buttons [data-shopify-buttoncontainer]>*{
      height:auto !important;
    }

iframe.zoid-component-frame{
  z-index:1 !important;
}

.drawer__cart-empty{
  display:none;
}

.drawer.is-empty .drawer__inner{
    display:none;
  }

.drawer.is-empty .drawer__cart-empty{
    display:block;
  }

@media only screen and (min-width:769px){
  .product-single__sticky{
    position:sticky;
    top:20px
  }
  .sticky-header .product-single__sticky{
      top:140px
  }

  .modal--quick-shop .product-single__sticky{
    top:0;
  }
}

.page-content--product{
  padding-top:55px;
}

@media only screen and (max-width:768px){
  .page-content--product{
    padding-top:0;
  }

  .grid--product-images-right{
    display:flex;
    flex-wrap:wrap
  }

    .grid--product-images-right .grid__item:first-child{
      order:2;
    }
}

.modal .page-content--product{
  width:1500px;
  max-width:100%;
}

.product-single__meta{
  padding-left:45px
}

@media only screen and (max-width:768px){

.product-single__meta{
    text-align:left;
    padding-left:0;
    margin-top:15px
}
  }

.grid--product-images-right .product-single__meta{
    padding-left:0;
    padding-right:45px
}

@media only screen and (max-width:768px){

.grid--product-images-right .product-single__meta{
      padding-right:0
}
    }

.product-single__meta .social-sharing{
    margin-top:30px;
  }

.product-single__meta .rte{
    text-align:left
  }

html[dir=rtl] .product-single__meta .rte{
      text-align:right
  }

.product-single__vendor{
  text-transform:uppercase;
  letter-spacing:0.3em;
  font-size:0.8em;
  margin-bottom:7.5px;
}

.product-single__title{
  margin-bottom:10px;
  word-wrap:break-word;
}

.product-single__review-link{
  display:block
}

.product-single__review-link .spr-badge[data-rating="0.0"]{
    display:none;
  }

.product-single__review-link .spr-badge{
    margin-bottom:7px;
  }

.product-single__review-link .spr-badge-starrating{
    margin-right:8px;
  }

.product-single__review-link .spr-badge-caption{
    font-size:calc(var(--typeBaseSize) - 1px);
  }

.product-single__sku{
  margin-bottom:5px;
}

.product-single__description-full{
  margin:30px 0
}

.product-single__description-full+.collapsibles-wrapper{
    margin:0 0;
  }

@media only screen and (min-width:769px){

.product-single__description-full{
    margin:30px 95px
}

    .product-single__description-full+.collapsibles-wrapper{
      margin:0 95px;
    }
  }

.product-single__variants{
  display:none
}

.no-js .product-single__variants{
    display:block;
    margin-bottom:30px
}

.product-image-main{
  position:relative;
}

[data-button_style=angled] .product-image-main .btn:not(.product__photo-zoom){
  display:inline-block;
  top:50%;
  position:absolute;
}

.product__video-wrapper{
  position:relative;
  overflow:hidden;
  max-width:100%;
  padding-bottom:100%;
  height:auto;
  background-color:{{ settings.color_small_image_bg | default: "#eee" }};
  background-color:var(--colorSmallImageBg)
}

.product__video-wrapper iframe{
    width:100%;
    height:100%;
    transition:opacity 0.5s ease-in;
  }

.product__video-wrapper[data-video-style=muted].loaded:before{
    content:"";
    position:absolute;
    top:0;
    left:0;
    right:0;
    bottom:0;
    z-index:1;
  }

.product__video-wrapper.video-interactable:before{
    display:none;
  }

.product__video-wrapper.loaded:after{
    display:none;
  }

.product__video-wrapper.loading iframe{
    opacity:0.01;
  }

.product__video-wrapper.loaded iframe{
    opacity:1;
  }

.product__video{
  position:absolute;
  top:0;
  left:0;
  width:100%;
  height:100%;
}

.product-video-trigger{
  position:absolute;
  top:50%;
  left:50%;
  transform:translate(-50%, -50%);
}

.product__photos--beside{
  display:flex;
  width:100%;
}

.product__photos{
  direction:ltr
}

.product__photos a{
    display:block;
    max-width:100%;
  }

.product__photos img{
    display:block;
    margin:0 auto;
    max-width:100%;
    width:100%;
  }

.product__main-photos{
  position:relative;
  flex:1 1 auto
}

.product__main-photos img{
    display:none;
  }

.product__main-photos .flickity-page-dots{
    display:none;
  }

@media only screen and (max-width:768px){

.product__main-photos{
    margin-bottom:30px;
    margin-left:-17px;
    margin-right:-17px
}

    .product__main-photos .flickity-page-dots{
      display:block;
    }
  }

.product-main-slide:not(.is-selected) button,.product-main-slide:not(.is-selected) iframe,.product-main-slide:not(.is-selected) model-viewer,.product-main-slide:not(.is-selected) video{
    display:none;
  }

.product-main-slide{
  display:none;
  width:100%;
  overflow:hidden
}

.product-main-slide:first-child{
    display:block;
  }

.flickity-slider .product-main-slide{
    display:block
}

@media only screen and (max-width:768px){

.grid--product-images--partial .flickity-slider .product-main-slide{
        width:75%;
        margin-right:4px
}
    }

.product__thumbs{
  position:relative;
}

.product__thumbs--below{
  margin-top:8.5px
}

@media only screen and (min-width:769px){

.product__thumbs--below{
    margin-top:15px
}
  }

.product__thumbs--beside{
  flex:0 0 60px;
  max-width:60px;
  margin-left:8.5px
}

@media only screen and (min-width:769px){

.product__thumbs--beside{
    flex:0 0 80px;
    max-width:80px;
    margin-left:15px
}

    .product__thumbs--beside.product__thumbs-placement--left{
      order:-1;
      margin-left:0;
      margin-right:15px;
    }
  }

.product__thumbs--scroller{
  scrollbar-width:none;
  scroll-behavior:smooth;
  -ms-overflow-style:-ms-autohiding-scrollbar
}

.product__thumbs--scroller::-webkit-scrollbar{
    height:0;
    width:0;
  }

.product__thumbs--below .product__thumbs--scroller{
    overflow-x:scroll;
    white-space:nowrap
}

.product__thumbs--beside .product__thumbs--scroller{
    position:absolute;
    top:0;
    left:0;
    right:0;
    bottom:0;
    width:100%;
    overflow-y:scroll
}

.product__thumb-item{
  border:2px solid transparent
}

.product__thumb-item a.is-active,.product__thumb-item a:focus{
    outline:none
  }

.product__thumb-item a.is-active:before,.product__thumb-item a:focus:before{
      content:"";
      display:block;
      position:absolute;
      top:0;
      left:0;
      right:0;
      bottom:0;
      box-shadow:inset 0 0 0 2px {{ settings.color_body_text | default:"#1c1d1d" }};
      box-shadow:inset 0 0 0 2px var(--colorTextBody);
      z-index:1;
    }

.product__thumb-item a:active:before{
    content:none;
  }

.product__thumbs--beside .product__thumb-item{
    margin-bottom:8.5px
}

@media only screen and (min-width:769px){

.product__thumbs--beside .product__thumb-item{
      margin-bottom:15px
}
    }

.product__thumbs--beside .product__thumb-item:last-child{
      margin-bottom:0;
    }

.product__thumbs--below .product__thumb-item{
    display:inline-block;
    vertical-align:middle;
    margin-right:8.5px;
    max-width:80px
}

@media only screen and (min-width:769px){

.product__thumbs--below .product__thumb-item{
      margin-right:15px
}
    }

.product__thumbs--below .product__thumb-item:last-child{
      margin-right:0;
    }

.product__price{
  color:{{ settings.color_price | default: "#1c1d1d" }};
  color:var(--colorPrice);
  margin-right:5px;
  font-size:calc(var(--typeBaseSize) + 2px)
}

.product__price.on-sale{
    padding-right:5px;
     FONT-WEIGHT: 600;
  font-size: 29px!important;
  }

.product__unit-price{
  font-size:0.8em;
  opacity:0.8;
}

.product__unit-price--spacing{
  margin-top:10px;
}

.product__price--compare{
  padding-right:5px;
  display:inline-block;
  text-decoration:line-through;
  font-size: 17px!important;
}

.product__price-savings{
  color:{{ settings.color_savings_text | default: "#1c1d1d" }};
  color:var(--colorTextSavings);
  white-space:nowrap;
}

.product__quantity label{
    display:block;
    margin-bottom:10px;
  }

.product-form-holder--loaded{
  animation:fade-in 1s ease;
}

.add-to-cart[disabled]+.shopify-payment-button{
  display:none;
}

.product-slideshow.flickity-enabled .product-main-slide{
    display:none;
  }

.product-slideshow.flickity-enabled .flickity-viewport .product-main-slide{
    display:block;
  }

.product__photo-zoom{
  position:absolute !important;
  bottom:0;
  right:0;
  cursor:zoom-in
}

@media only screen and (max-width:768px){

.product__photo-zoom{
    padding:6px
}

.product__main-photos .product__photo-zoom{
      margin-bottom:10px;
      margin-right:10px
}

.product-slideshow .product__photo-zoom{
      opacity:0;
      transition:opacity 0.5s ease-out
}

.product-slideshow .is-selected .product__photo-zoom{
      opacity:1
}
  }

@media only screen and (min-width:769px){

.product__photo-zoom{
    opacity:0;
    width:100%;
    top:0;
    left:0;
    margin:0;
    border-radius:0
}

    .product__photo-zoom span,.product__photo-zoom svg{
      display:none;
    }
  }

.product__thumb-arrow{
  position:absolute;
  background:{{ settings.color_body_bg | default: "#fff" }};
  background:var(--colorBody);
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  transform:none;
  border-radius:0;
  padding:0;
  z-index:2
}

.product__thumb-arrow .icon{
    display:inline-block;
    width:6px;
    height:10px;
  }

.product__thumbs[data-position=below] .product__thumb-arrow{
  top:0;
  height:100%;
  width:25px
}

.product__thumbs[data-position=below] .product__thumb-arrow.product__thumb-arrow--prev{
    left:0;
    text-align:left;
  }

.product__thumbs[data-position=below] .product__thumb-arrow.product__thumb-arrow--next{
    right:0;
    text-align:right;
  }

.product__thumbs[data-position=beside] .product__thumb-arrow{
  width:100%
}

.product__thumbs[data-position=beside] .product__thumb-arrow .icon{
    margin:0 auto;
    transform:rotate(90deg);
  }

.product__thumbs[data-position=beside] .product__thumb-arrow.product__thumb-arrow--prev{
    top:0;
    left:auto;
    padding-bottom:10px;
  }

.product__thumbs[data-position=beside] .product__thumb-arrow.product__thumb-arrow--next{
    top:auto;
    bottom:0;
    right:auto;
    padding-top:10px;
  }

.product__thumb{
  position:relative;
  display:block;
  cursor:pointer;
}

.product__thumb-icon{
  position:absolute;
  top:5px;
  right:5px;
  background-color:{{ settings.color_body_text | default: "#1c1d1d" }};
  background-color:var(--colorTextBody);
  border-radius:100px;
  padding:6px;
  z-index:1;
  opacity:0;
  transition:opacity 0.5s ease;
  font-size:0
}

.aos-animate .product__thumb-icon{
    opacity:1
}

.product__thumb-icon .icon{
    fill:{{ settings.color_body_bg | default: "#fff" }};
    fill:var(--colorBody);
    width:10px;
    height:10px
  }

@media only screen and (min-width:769px){

.product__thumb-icon .icon{
      width:13px;
      height:13px
  }
    }

.product__policies{
  font-size:0.85em;
}

.shopify-payment-terms{
  margin:12px 0
}

.shopify-payment-terms:empty{
    display:none;
  }

.modal .shopify-payment-terms{
    display:none
}

.template-blog .article{
  margin-bottom:45px
}

@media only screen and (min-width:769px){

.template-blog .article{
    margin-bottom:90px
}
  }

.template-blog .article:last-child{
  margin-bottom:0
}

@media only screen and (min-width:769px){

.template-blog .article:last-child{
    margin-bottom:45px
}
  }

.article__body{
  margin-bottom:15px
}

@media only screen and (min-width:769px){

.article__body{
    margin-bottom:30px
}
  }

.article__comment{
  margin-bottom:30px
}

.article__comment:last-child{
    margin-bottom:0;
  }

.password-page__header__grid{
  display:flex;
  flex:1 1 auto;
  align-items:center;
  justify-content:space-between;
}

.password-page__logo h1{
    margin-bottom:0;
  }

.password-page__logo .logo{
    max-width:100%;
  }

.password-login{
  display:block;
  margin:0 auto;
  padding:7.5px 15px;
}

.password-form{
  margin-bottom:1em;
}

.password__lock .icon{
  position:relative;
  top:-2px;
  margin-right:4px;
  width:24px;
  height:24px;
}

.icon-shopify-logo{
  width:60px;
  height:20px;
}

@media only screen and (max-width:768px){

#LoginModal .modal__close{
    padding:20px
}
  }

#LoginModal .modal__inner{
  background:{{ settings.color_body_bg | default: "#fff" }};
  background:var(--colorBody);
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  padding:30px
}

@media only screen and (max-width:768px){

#LoginModal .modal__inner{
    margin-bottom:40vh
}
  }

.password-admin-link{
  margin:0
}

.password-admin-link a{
    border-bottom:2px solid !important;
    border-bottom-color:{{ settings.color_borders | default: "#1c1d1d" }} !important;
    border-bottom-color:var(--colorBorder) !important;
  }

.template-giftcard,.template-giftcard body{
  background:{{ settings.color_body_bg | default: "#fff" }};
  background:var(--colorBody)
}

.template-giftcard a,.template-giftcard body a{
    text-decoration:none;
  }

.template-giftcard .page-width{
  max-width:588px;
}

.giftcard-header{
  padding:60px 0;
  font-size:1em;
  text-align:center
}

.giftcard-header a{
    display:block;
    margin:0 auto;
  }

.template-giftcard .shop-url{
  display:none;
}

.giftcard__border{
  padding:1.5em;
  box-shadow:0 10px 30px rgba(0, 0, 0, 0.3);
}

.giftcard__content:after{content:"";display:table;clear:both;}

.giftcard__content{
  background-color:{{ settings.color_body_bg | default: "#fff" }};
  background-color:var(--colorBody);
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
}

.giftcard__header:after{content:"";display:table;clear:both;}

.giftcard__header{
  padding:15px;
}

.giftcard__title{
  float:left;
  margin-bottom:0;
}

.giftcard__tag{
  display:block;
  float:right;
  background-color:{{ settings.color_body_text | default: "#1c1d1d" }};
  background-color:var(--colorTextBody);
  border:1px solid transparent;
  color:{{ settings.color_body_bg | default: "#fff" }};
  color:var(--colorBody);
  padding:10px;
  border-radius:4px;
  font-size:0.75em;
  text-transform:uppercase;
  letter-spacing:.05em;
  line-height:1;
}

.giftcard__tag--active{
  background:transparent;
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  border:1px solid;
  border-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-color:var(--colorBorder);
}

.giftcard__wrap{
  position:relative;
  margin:15px 15px 30px
}

.giftcard__wrap img{
    position:relative;
    display:block;
    border-radius:10px;
    z-index:2;
  }

.giftcard__code{
  position:absolute;
  bottom:30px;
  text-align:center;
  width:100%;
  z-index:50;
}

.giftcard__code--medium{
  font-size:.875em;
}

.giftcard__code--small{
  font-size:.75em;
}

.giftcard__code__inner{
  display:inline-block;
  vertical-align:baseline;
  background-color:#fff;
  padding:.5em;
  border-radius:4px;
  max-width:450px;
  box-shadow:0 0 0 1px rgba(0, 0, 0, 0.1)
}

.giftcard__code--small .giftcard__code__inner{
    overflow:auto
}

.giftcard__code__text{
  font-weight:400;
  font-size:1.875em;
  text-transform:uppercase;
  border-radius:2px;
  border:1px dashed;
  border-color:{{ settings.color_borders | default: "#1c1d1d" }};
  border-color:var(--colorBorder);
  padding:.4em .5em;
  display:inline-block;
  vertical-align:baseline;
  color:{{ settings.color_body_text | default: "#1c1d1d" }};
  color:var(--colorTextBody);
  line-height:1
}

.disabled .giftcard__code__text{
    color:#999;
    text-decoration:line-through
}

.giftcard__amount{
  position:absolute;
  top:0;
  right:0;
  color:#fff;
  font-size:2.75em;
  line-height:1.2;
  padding:15px;
  z-index:50
}

.giftcard__amount strong{
    display:block;
    text-shadow:3px 3px 0 rgba(0, 0, 0, 0.1);
  }

.giftcard__amount--medium{
  font-size:2em;
}

.tooltip{
  display:block;
  position:absolute;
  top:-50%;
  right:50%;
  margin-top:16px;
  z-index:3;
  color:#fff;
  text-align:center;
  white-space:nowrap
}

.tooltip:before{
    content:"";
    display:block;
    position:absolute;
    left:100%;
    bottom:0;
    width:0;
    height:0;
    margin-left:-5px;
    margin-bottom:-5px;
    border-left:8px solid transparent;
    border-right:8px solid transparent;
    border-top:5px solid #333;
    border-top:5px solid rgba(51, 51, 51, 0.9);
  }

.tooltip__label{
  display:block;
  position:relative;
  right:-50%;
  border:none;
  border-radius:4px;
  background:#333;
  background:rgba(51, 51, 51, 0.9);
  min-height:14px;
  font-weight:400;
  font-size:12px;
  text-decoration:none;
  line-height:16px;
  text-shadow:none;
  padding:.5em .75em;
  margin-left:.25em
}

.tooltip__label small{
    text-transform:uppercase;
    letter-spacing:.1em;
    color:#b3b3b3;
    font-size:.875em;
  }

.giftcard__instructions{
  text-align:center;
  margin:0 15px 30px;
}

.giftcard__actions{
  position:relative;
  text-align:center;
  overflow:hidden;
  padding-bottom:1em;
}

.template-giftcard .action-link{
  position:absolute;
  left:15px;
  top:50%;
  font-size:0.875em;
  font-weight:700;
  display:block;
  padding-top:4px;
  text-transform:uppercase;
  letter-spacing:.2em;
  margin-top:-10px
}

.template-giftcard .action-link:focus,.template-giftcard .action-link:hover{
    color:{{ settings.color_body_text | default: "#1c1d1d" }};
    color:var(--colorTextBody);
  }

.template-giftcard .action-link__print{
  display:inline-block;
  vertical-align:baseline;
  width:17px;
  height:17px;
  vertical-align:middle;
  margin-right:10px;
  opacity:1;
  background-image:url(//cdn.shopify.com/s/assets/gift-card/icon-print-164daa1ae32d10d1f9b83ac21b6f2c70.png);
  background-repeat:no-repeat;
  background-position:0 0;
}

.giftcard__footer{
  text-align:center;
  padding:60px 0;
}

#QrCode img{
    padding:30px;
    border:1px solid;
    border-color:{{ settings.color_borders | default: "#1c1d1d" }};
    border-color:var(--colorBorder);
    border-radius:4px;
    margin:0 auto 30px;
  }

@media only screen and (max-width:768px){
  .giftcard{
    font-size:12px;
  }

  .giftcard-header{
    padding:30px 0;
  }

  .header-logo{
    font-size:2em;
  }

  .giftcard__border{
    padding:15px;
  }

  .giftcard__actions{
    padding:15px;
  }

  .giftcard__actions .btn{
    width:100%;
    padding-left:0;
    padding-right:0;
  }

  .template-giftcard .action-link{
    display:none;
  }
}

@media screen and (max-width:400px){
  .giftcard__amount strong{
    text-shadow:2px 2px 0 rgba(0, 0, 0, 0.1);
  }

  .giftcard__wrap:after,.giftcard__wrap:before{
    display:none;
  }

  .giftcard__code{
    font-size:.75em;
  }

  .giftcard__code--medium{
    font-size:.65em;
  }

  .giftcard__code--small{
    font-size:.55em;
  }
}

@media screen and (max-height:800px){
  .header-logo img{
    max-height:90px;
  }
}

@media print{
  @page{
    margin:0.5cm;
  }

  h2,h3,p{
    orphans:3;
    widows:3;
  }

  h2,h3{
    page-break-after:avoid;
  }

  body,html{
    background-color:#fff;
  }

  .giftcard-header{
    padding:10px 0;
  }

  .giftcard__border,.giftcard__content{
    border:0 none;
  }

  .add-to-apple-wallet,.giftcard__actions,.giftcard__wrap:after,.giftcard__wrap:before,.site-header__logo-link img:nth-child(2),.tooltip{
    display:none;
  }

  .giftcard__title{
    float:none;
    text-align:center;
  }

  .giftcard__code__text{
    color:#555;
  }

  .template-giftcard .shop-url{
    display:block;
  }

  .template-giftcard .logo{
    color:#58686f;
  }
@media only screen and (max-width: 429px){
.template-product .variant-input-wrap{

text-align: left !important;
  }
@media (max-width: 768px) {
.product-single__meta .product-block > .variant-wrapper {
    text-align: left !important;
  }
.product-single__meta .h2.product-single__title {
    text-align: left;
  }
@media (max-width:768px) {
.product-block, .product-single__meta .rte {
  text-align: left;
  }
.template-collection  .grid-product__content .grid-product__price .grid-product__price--original{
          color: #f80707 !important;
}
  }

