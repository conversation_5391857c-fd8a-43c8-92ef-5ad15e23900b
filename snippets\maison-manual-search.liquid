<!-- Search Slide-over Panel -->
<div
  x-show="showSearchPopup"
  class="search-overlay-container"
  role="dialog"
  aria-modal="true"
>
  <!-- Background overlay -->
  <div
    x-show="showSearchPopup"
    x-transition:enter="transition ease-in-out duration-300"
    x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100"
    x-transition:leave="transition ease-in-out duration-300"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"
    class="search-backdrop"
    @click="showSearchPopup = false"
  ></div>

  <!-- Slide-over panel -->
  <div class="search-panel-outer-container">
    <div
      x-show="showSearchPopup"
      x-transition:enter="transition ease-in-out duration-300"
      x-transition:enter-start="translate-x-full"
      x-transition:enter-end="translate-x-0"
      x-transition:leave="transition ease-in-out duration-300"
      x-transition:leave-start="translate-x-0"
      x-transition:leave-end="translate-x-full"
      class="search-panel-inner-container"
    >
      <div class="search-panel-content-wrapper">
        <!-- Header -->
        <div class="search-panel-header">
          <div class="search-panel-header-flex">
            <h2 class="search-panel-title">Search Manual</h2>
            <button
              type="button"
              class="search-panel-close-button"
              @click="showSearchPopup = false"
            >
              <span class="sr-only">Close panel</span>
              <svg
                class="search-panel-close-icon"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- Search input -->
          <!-- div class="search-panel-input-section">
            <div class="search-panel-input-relative-wrapper">
              <input
                type="text"
                class="search-panel-input"
                placeholder="Search for features, settings, or help..."
                x-model="searchQuery"
                @input="performSearch()"
                autofocus
              >
              <div class="search-panel-input-icon-wrapper">
                <svg class="search-panel-input-icon" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div -->
        </div>

        <!-- Panel content -->
        <div class="search-panel-body">
          <div class="search-panel-body-padding">
            <!-- Popular Searches -->
            <div x-show="!searchQuery" class="popular-searches-container" style="margin-bottom: 2rem;">
              <h3 class="popular-searches-title" style="margin-bottom: 0.75rem;">Popular Searches</h3>
              <div class="popular-searches-tags-container">
                <template x-for="tab in tabs" :key="tab.id">
                  <template x-for="searchKey in tab.searchKeys" :key="searchKey">
                    <button
                      @click="activeTab = tab.id; showSearchPopup = false"
                      class="popular-search-tag"
                      x-text="searchKey.charAt(0).toUpperCase() + searchKey.slice(1)"
                    ></button>
                  </template>
                </template>
              </div>
            </div>

            <!-- Search Results -->
            <!--
              div x-show="searchResults.length > 0" class="search-results-container">
                <h3 class="search-results-title" style="margin-bottom: 0.75rem;">Results</h3>
                <div class="search-results-list" style="display: flex; flex-direction: column; gap: 0.5rem;">
                  <template x-for="tab in tabs" :key="tab.id">
                      <template x-for="searchKey in tab.searchKeys" :key="searchKey">
                    <button
                      @click="selectedTab = tab.id; showSearchPopup = false"
                      class="search-result-button"
                    >
                      <div class="search-result-flex-container">
                        <div class="search-result-icon-container">
                          <div class="search-result-icon-wrapper">
                            <svg
                              class="search-result-icon"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke-width="1.5"
                              stroke="currentColor"
                            >
                              <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                            </svg>
                          </div>
                        </div>
                        <div class="search-result-text-container">
                          <h4 class="search-result-title" x-text="result.title"></h4>
                          <p class="search-result-description" x-text="result.description"></p>
                        </div>
                      </div>
                    </button>
                    </template>
                  </template>
                </div>
              </div
            -->

            <!-- No Results -->
            <div x-show="searchQuery && searchResults.length === 0" class="no-results-container">
              <div class="no-results-icon-wrapper">
                <svg
                  class="no-results-icon"
                  width="24px"
                  height="24px"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                </svg>
              </div>
              <p class="no-results-text"><span x-text="searchQuery"></span></p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
