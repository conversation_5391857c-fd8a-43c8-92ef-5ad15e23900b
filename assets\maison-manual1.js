function manualApp() {
  return {
    darkMode: false,
    selectedSeries: "health",
    selectedModel: "health-2",
    activeTab: tabs[0].id,
    searchQuery: "",
    showSearchResults: false,
    searchResults: [],
    showSearchPopup: false,

    models: {
      health: [
        { id: "health-2", name: "Health Smartwatch 2", badge: "Best Seller" },
        {
          id: "health-2-plus",
          name: "Health Smartwatch 2+",
          badge: "Enhanced",
        },
        { id: "health-3", name: "Health Smartwatch 3", badge: "Latest" },
      ],
      ultra: [
        { id: "ultra-1", name: "GARD PRO Ultra", badge: "Premium" },
        { id: "ultra-2-plus", name: "GARD PRO Ultra 2+", badge: "Flagship" },
      ],
    },

    // It gets set in the liquid template
    tabs: tabs,

    popularSearches: [
      "How to pair",
      "Battery life",
      "Reset watch",
      "Heart rate",
      "Update software",
    ],

    searchDatabase: [
      {
        id: 1,
        title: "How to pair your watch",
        description: "Step-by-step Bluetooth pairing guide",
        section: "getting-started",
      },
      {
        id: 2,
        title: "Maximize battery life",
        description: "Tips to extend your watch battery",
        section: "daily-use",
      },
      {
        id: 3,
        title: "Reset your watch",
        description: "Factory reset and troubleshooting",
        section: "troubleshooting",
      },
      {
        id: 4,
        title: "Heart rate monitoring",
        description: "Understanding your heart rate data",
        section: "health-fitness",
      },
      {
        id: 5,
        title: "Software updates",
        description: "Keep your watch up to date",
        section: "advanced-settings",
      },
    ],

    init() {
      this.darkMode = localStorage.getItem("darkMode") === "true";
    },

    get currentModels() {
      return this.models[this.selectedSeries];
    },

    selectSeries(series) {
      this.selectedSeries = series;
      this.selectedModel = this.currentModels[0].id;
    }, 

    selectModel(modelId) {
      this.selectedModel = modelId;
    },

    setActiveTab(tabId) {
      this.activeTab = tabId;
    },

    toggleTheme() {
      this.darkMode = !this.darkMode;
      localStorage.setItem("darkMode", this.darkMode);
    },

    performSearch() {
      if (this.searchQuery.length < 2) {
        this.searchResults = [];
        return;
      }

      const query = this.searchQuery.toLowerCase();
      this.searchResults = this.searchDatabase.filter(
        (item) =>
          item.title.toLowerCase().includes(query) ||
          item.description.toLowerCase().includes(query)
      );
    },

    navigateToResult(result) {
      this.activeTab = result.section;
      this.showSearchResults = false;
      this.searchQuery = "";
    },

  };
}
