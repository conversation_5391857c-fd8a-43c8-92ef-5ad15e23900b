<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme.js Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .text-tab-title {
            display: inline-block;
            padding: 10px 15px;
            margin: 5px;
            background: #f0f0f0;
            cursor: pointer;
            border-radius: 3px;
        }
        .text-tab-title.active {
            background: #007cba;
            color: white;
        }
        .text-tab-info {
            display: none;
            padding: 15px;
            margin-top: 10px;
            background: #f9f9f9;
            border-radius: 3px;
        }
        .text-tab-info.active {
            display: block;
        }
        .text-tab-img {
            display: none;
            width: 200px;
            height: 150px;
            background: #ddd;
            margin: 10px 0;
            border-radius: 3px;
            text-align: center;
            line-height: 150px;
        }
        .text-tab-img.active {
            display: block;
        }
        .error-log {
            background: #ffe6e6;
            border: 1px solid #ff9999;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success-log {
            background: #e6ffe6;
            border: 1px solid #99ff99;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>Theme.js Fix Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Page with Tab Elements (Should work normally)</h2>
        <div class="text-tab-title" data-block-id="tab1">Tab 1</div>
        <div class="text-tab-title" data-block-id="tab2">Tab 2</div>
        <div class="text-tab-title" data-block-id="tab3">Tab 3</div>
        
        <div class="text-tab-info" data-block-id="tab1">Content for Tab 1</div>
        <div class="text-tab-info" data-block-id="tab2">Content for Tab 2</div>
        <div class="text-tab-info" data-block-id="tab3">Content for Tab 3</div>
        
        <div class="text-tab-img" data-block-id="tab1">Image 1</div>
        <div class="text-tab-img" data-block-id="tab2">Image 2</div>
        <div class="text-tab-img" data-block-id="tab3">Image 3</div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Page without Tab Elements (Should NOT cause errors)</h2>
        <p>This section has no tab elements. The original code would cause errors here.</p>
    </div>
    
    <div id="error-log" class="error-log" style="display: none;"></div>
    <div id="success-log" class="success-log" style="display: none;"></div>
    
    <script>
        // Capture console errors
        const originalError = console.error;
        let errorCount = 0;
        
        console.error = function(...args) {
            errorCount++;
            const errorLog = document.getElementById('error-log');
            errorLog.style.display = 'block';
            errorLog.textContent += `Error ${errorCount}: ${args.join(' ')}\n`;
            originalError.apply(console, args);
        };
        
        // Test the fixed theme.js code
        document.addEventListener('DOMContentLoaded', function () {
            const titles = document.querySelectorAll('.text-tab-title');
            let currentIndex = 0;
            let autoRotateInterval;
            let isAutoRotating = true;

            if (titles.length > 0) {
                const firstTitle = titles[0];
                const firstBlockId = firstTitle.getAttribute('data-block-id');

                const firstDescription = document.querySelector(`.text-tab-info[data-block-id="${firstBlockId}"]`);
                const firstImage = document.querySelector(`.text-tab-img[data-block-id="${firstBlockId}"]`);

                if (firstDescription) {
                    firstDescription.classList.add('active');
                }
                if (firstImage) {
                    firstImage.classList.add('active');
                }
            }

            function startAutoRotation() {
                if (isAutoRotating) {
                    autoRotateInterval = setInterval(function () {
                        moveToNextTab();
                    }, 2000); // Faster for testing
                }
            }

            function stopAutoRotation() {
                clearInterval(autoRotateInterval);
                isAutoRotating = false;
            }

            function moveToNextTab() {
                // Check if titles exist before proceeding - THIS IS THE FIX
                if (titles.length === 0) {
                    return;
                }

                const allDescriptions = document.querySelectorAll('.text-tab-info');
                const allImages = document.querySelectorAll('.text-tab-img');

                allDescriptions.forEach((desc) => {
                    desc.classList.remove('active');
                });

                allImages.forEach((img) => {
                    img.classList.remove('active');
                });

                currentIndex = (currentIndex + 1) % titles.length;

                const newBlockId = titles[currentIndex].getAttribute('data-block-id');
                const newDescription = document.querySelector(`.text-tab-info[data-block-id="${newBlockId}"]`);
                const newImage = document.querySelector(`.text-tab-img[data-block-id="${newBlockId}"]`);

                if (newDescription) {
                    newDescription.classList.add('active');
                }
                if (newImage) {
                    newImage.classList.add('active');
                }
            }

            titles.forEach((title, index) => {
                title.addEventListener('click', function () {
                    if (isAutoRotating) {
                        stopAutoRotation();
                    }

                    const blockId = this.getAttribute('data-block-id');

                    const allDescriptions = document.querySelectorAll('.text-tab-info');
                    const allImages = document.querySelectorAll('.text-tab-img');

                    allDescriptions.forEach((desc) => {
                        desc.classList.remove('active');
                    });

                    allImages.forEach((img) => {
                        img.classList.remove('active');
                    });

                    const description = document.querySelector(`.text-tab-info[data-block-id="${blockId}"]`);
                    const image = document.querySelector(`.text-tab-img[data-block-id="${blockId}"]`);

                    if (description) {
                        description.classList.add('active');
                    }
                    if (image) {
                        image.classList.add('active');
                    }

                    currentIndex = index;
                });
            });
            
            // Only start auto rotation if there are titles to rotate through - THIS IS THE FIX
            if (titles.length > 0) {
                startAutoRotation();
            }
            
            // Show success message after 5 seconds if no errors
            setTimeout(() => {
                if (errorCount === 0) {
                    const successLog = document.getElementById('success-log');
                    successLog.style.display = 'block';
                    successLog.textContent = '✅ SUCCESS: No JavaScript errors detected! The fix is working correctly.';
                }
            }, 5000);
        });
    </script>
</body>
</html>
