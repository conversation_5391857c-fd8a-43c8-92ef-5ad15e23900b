{%- if section.settings.divider -%}<div class="section--divider">{%- endif -%}

<div class="page-width width_{{ section.id }} custom-{{ section.settings.custom_class }} custom-section_ontdek_meer">
  {%- if section.settings.title != blank -%}
    <div class="section-header text-{{ section.settings.align_text }}">
      <h2>{{ section.settings.title }}</h2>
    </div>
  {%- endif -%}

  <div class="grid{% unless section.blocks.size == 5 %} grid--uniform{% endunless %} grid--flush-bottom  text_column_title custom-image-slider">
    {%- liquid
      assign grid_item_width = 'medium-up--one-third'
      case section.blocks.size
        when 1
          assign grid_item_width = 'medium-up--one-half'
        when 2
          assign grid_item_width = 'medium-up--one-half'
        when 4
          assign grid_item_width = 'medium-up--one-half'
      endcase
    -%}
    {%- for block in section.blocks -%}
      {%- if section.blocks.size == 5 and forloop.index < 3 -%}
        {%- assign column_width = 'medium-up--one-half' -%}
      {%- else -%}
        {%- assign column_width = grid_item_width -%}
      {%- endif -%}
      <div
        class="grid__item {{ column_width }} text-{{ section.settings.align_text }} text_column_content_outer"
        {{ block.shopify_attributes }}
        data-aos="row-of-3"
      >
        {%- if block.settings.enable_image -%}
          <div
            class="maindiv"
            style="{% if section.settings.align_text == 'center' %}margin: 0 auto;{% endif %} max-width: {{ block.settings.image_width }}px;"
          >
            {%- if block.settings.button_link != blank -%}
              <a href="{{ block.settings.button_link }}">
            {%- endif -%}
            {%- if block.settings.image != blank -%}
              <div
                class="image-wrap text-spacing"
                style="height: 0; padding-bottom: {{ 100 | divided_by: block.settings.image.aspect_ratio }}%;"
              >
                {%- assign img_url = block.settings.image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}
                <img
                  class="lazyload"
                  data-src="{{ img_url }}"
                  data-widths="[180, 360, 540, 720, 900, 1080]"
                  data-aspectratio="{{ block.settings.image.aspect_ratio }}"
                  data-sizes="auto"
                  alt="{{ block.settings.image.alt }}"
                >
              </div>
            {%- else -%}
              <div class="image-wrap text-spacing">{{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}</div>
            {%- endif -%}
            {%- if block.settings.button_link != blank -%}
              </a>
            {%- endif -%}
          </div>
        {%- endif -%}
        <div class="text_column_content">
          {%- if block.settings.title != blank -%}
            <h3>{{ block.settings.title }}</h3>
          {%- endif -%}
          {%- if block.settings.text != blank -%}
            <div class="rte-setting text-spacing">{{ block.settings.text }}</div>
          {%- endif -%}
          {%- if block.settings.button_label != blank -%}
            <div class="text_column_btn">
              <a href="{{ block.settings.button_link }}" class="btn btn--secondary btn--small">
                {{ block.settings.button_label }}
              </a>
            </div>
          {%- endif -%}
        </div>
      </div>
    {%- endfor -%}
  </div>
</div>

{%- if section.settings.divider -%}</div>{%- endif -%}

{% if section.settings.full_width %}
  <style>
    #shopify-section-{{ section.id }} .width_{{ section.id }}{
      max-width: 100%;
    }
  </style>
{% endif %}

{% schema %}
{
  "name": "t:sections.text-columns.name",
  "class": "index-section",
  "settings": [
    {
      "type": "text",
      "id": "custom_class",
      "label": "Custom Class"
    },
    {
      "type": "text",
      "id": "title",
      "label": "t:sections.text-columns.settings.title.label"
    },
    {
      "type": "select",
      "id": "align_text",
      "label": "t:sections.text-columns.settings.align_text.label",
      "default": "center",
      "options": [
        {
          "value": "left",
          "label": "t:sections.text-columns.settings.align_text.options.left.label"
        },
        {
          "value": "center",
          "label": "t:sections.text-columns.settings.align_text.options.center.label"
        },
        {
          "value": "right",
          "label": "t:sections.text-columns.settings.align_text.options.right.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Section Full Width Desktop",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "divider",
      "label": "t:sections.text-columns.settings.divider.label",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "enable_slider",
      "label": "Enable/Disable Slider",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "text_block",
      "name": "t:sections.text-columns.blocks.column.name",
      "settings": [
        {
          "type": "checkbox",
          "id": "enable_image",
          "label": "t:sections.text-columns.blocks.column.settings.enable_image.label",
          "default": true
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.text-columns.blocks.column.settings.image.label"
        },
        {
          "type": "range",
          "id": "image_width",
          "label": "t:sections.text-columns.blocks.column.settings.image_width.label",
          "default": 650,
          "min": 60,
          "max": 650,
          "step": 10,
          "unit": "px"
        },
        {
          "type": "text",
          "id": "title",
          "label": "t:sections.text-columns.blocks.column.settings.title.label",
          "default": "Example title"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "t:sections.text-columns.blocks.column.settings.text.label",
          "default": "<p>Use this section to explain a set of product features, to link to a series of pages, or to answer common questions about your products. Add images for emphasis.</p>"
        },
        {
          "type": "text",
          "id": "button_label",
          "label": "t:sections.text-columns.blocks.column.settings.button_label.label",
          "default": "Optional button"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.text-columns.blocks.column.settings.button_link.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.text-columns.presets.text_columns_with_images.name",
      "blocks": [
        {
          "type": "text_block"
        },
        {
          "type": "text_block"
        },
        {
          "type": "text_block"
        }
      ]
    }
  ]
}
{% endschema %}
