/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "6131505d-46a0-4651-a793-438eef432c11": {
      "type": "promo-grid",
      "blocks": {
        "template--16686928003320__6131505d-46a0-4651-a793-438eef432c11-1686234104e43580ab-0": {
          "type": "advanced",
          "settings": {
            "subheading": "GARD PRO®",
            "heading": "Essential accessories",
            "textarea": "",
            "cta_text1": "",
            "cta_link1": "",
            "cta_text2": "",
            "cta_link2": "",
            "image": "shopify://shop_images/ass.webp",
            "video_url": "",
            "width": "100",
            "height": 500,
            "text_size": 100,
            "text_align": "vertical-center horizontal-left",
            "focal_point": "20% 50%",
            "color_accent": "rgba(0,0,0,0)",
            "boxed": false,
            "framed": false
          }
        }
      },
      "block_order": [
        "template--16686928003320__6131505d-46a0-4651-a793-438eef432c11-1686234104e43580ab-0"
      ],
      "settings": {
        "full_width": true,
        "gutter_size": 0,
        "space_above": false,
        "space_below": false
      }
    },
    "collection-header": {
      "type": "collection-header",
      "settings": {
        "enable": true,
        "collection_image_enable": false,
        "parallax": true,
        "parallax_direction": "left"
      }
    },
    "main-collection": {
      "type": "main-collection",
      "blocks": {
        "collection_description": {
          "type": "collection_description",
          "settings": {}
        },
        "subcollections": {
          "type": "subcollections",
          "settings": {
            "subcollections_per_row": 5
          }
        },
        "product_grid": {
          "type": "product_grid",
          "settings": {
            "enable_collection_count": true,
            "per_row": 4,
            "rows_per_page": 7,
            "mobile_flush_grid": false
          }
        }
      },
      "block_order": [
        "collection_description",
        "subcollections",
        "product_grid"
      ],
      "settings": {
        "enable_sidebar": true,
        "collapsed": true,
        "filter_style": "drawer",
        "enable_color_swatches": true,
        "enable_sort": true
      }
    }
  },
  "order": [
    "6131505d-46a0-4651-a793-438eef432c11",
    "collection-header",
    "main-collection"
  ]
}
