
    
    <style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-580622358482518611.gps.gpsil [style*="--as:"]{align-self:var(--as)}.gps-580622358482518611.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-580622358482518611.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-580622358482518611.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-580622358482518611.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-580622358482518611.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-580622358482518611.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-580622358482518611.gps.gpsil [style*="--hvr-bgi:"]:hover{background-image:var(--hvr-bgi)}.gps-580622358482518611.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-580622358482518611.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-580622358482518611.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-580622358482518611.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-580622358482518611.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-580622358482518611.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-580622358482518611.gps.gpsil [style*="--hvr-bb:"]:hover{border-bottom:var(--hvr-bb)}.gps-580622358482518611.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-580622358482518611.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-580622358482518611.gps.gpsil [style*="--hvr-bblr:"]:hover{border-bottom-left-radius:var(--hvr-bblr)}.gps-580622358482518611.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-580622358482518611.gps.gpsil [style*="--hvr-bbrr:"]:hover{border-bottom-right-radius:var(--hvr-bbrr)}.gps-580622358482518611.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-580622358482518611.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-580622358482518611.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-580622358482518611.gps.gpsil [style*="--hvr-bt:"]:hover{border-top:var(--hvr-bt)}.gps-580622358482518611.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-580622358482518611.gps.gpsil [style*="--hvr-btlr:"]:hover{border-top-left-radius:var(--hvr-btlr)}.gps-580622358482518611.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-580622358482518611.gps.gpsil [style*="--hvr-btrr:"]:hover{border-top-right-radius:var(--hvr-btrr)}.gps-580622358482518611.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-580622358482518611.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-580622358482518611.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-580622358482518611.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-580622358482518611.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-580622358482518611.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-580622358482518611.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-580622358482518611.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-580622358482518611.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-580622358482518611.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-580622358482518611.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-580622358482518611.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-580622358482518611.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-580622358482518611.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-580622358482518611.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-580622358482518611.gps.gpsil [style*="--tdt:"]{text-decoration-thickness:var(--tdt)}.gps-580622358482518611.gps.gpsil [style*="--tdl:"]{text-decoration-line:var(--tdl)}.gps-580622358482518611.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-580622358482518611.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-580622358482518611.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-580622358482518611.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-580622358482518611.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-580622358482518611.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-580622358482518611.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-580622358482518611.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-580622358482518611.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-580622358482518611.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-580622358482518611.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-580622358482518611.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-580622358482518611.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-580622358482518611.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-580622358482518611.gps.gpsil [style*="--right:"]{right:var(--right)}.gps-580622358482518611.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-580622358482518611.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-580622358482518611.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-580622358482518611.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-580622358482518611.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-580622358482518611.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-580622358482518611.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-580622358482518611.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-580622358482518611.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-580622358482518611.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-580622358482518611.gps.gpsil [style*="--gtc-tablet:"]{grid-template-columns:var(--gtc-tablet)}.gps-580622358482518611.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-580622358482518611.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-580622358482518611.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-580622358482518611.gps.gpsil [style*="--mb-tablet:"]{margin-bottom:var(--mb-tablet)}.gps-580622358482518611.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-580622358482518611.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-580622358482518611.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-580622358482518611.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-580622358482518611.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-580622358482518611.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-580622358482518611.gps.gpsil [style*="--right-tablet:"]{right:var(--right-tablet)}.gps-580622358482518611.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-580622358482518611.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-580622358482518611.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-580622358482518611.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-580622358482518611.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-580622358482518611.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-580622358482518611.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-580622358482518611.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-580622358482518611.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-580622358482518611.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-580622358482518611.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-580622358482518611.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-580622358482518611.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-580622358482518611.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-580622358482518611.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-580622358482518611.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-580622358482518611.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-580622358482518611.gps.gpsil [style*="--right-mobile:"]{right:var(--right-mobile)}.gps-580622358482518611.gps.gpsil [style*="--ta-mobile:"]{text-align:var(--ta-mobile)}.gps-580622358482518611.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-580622358482518611.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-580622358482518611.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-580622358482518611 .gp-rotate-0,.gps-580622358482518611 .gp-rotate-180,.gps-580622358482518611 .mobile\:gp-rotate-0,.gps-580622358482518611 .mobile\:gp-rotate-180,.gps-580622358482518611 .tablet\:gp-rotate-0,.gps-580622358482518611 .tablet\:gp-rotate-180{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-580622358482518611 .gp-static{position:static}.gps-580622358482518611 .\!gp-absolute{position:absolute!important}.gps-580622358482518611 .gp-relative{position:relative}.gps-580622358482518611 .gp-left-0{left:0}.gps-580622358482518611 .gp-right-0{right:0}.gps-580622358482518611 .gp-z-1{z-index:1}.gps-580622358482518611 .gp-z-2{z-index:2}.gps-580622358482518611 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-580622358482518611 .gp-my-0{margin-bottom:0;margin-top:0}.gps-580622358482518611 .gp-mb-0{margin-bottom:0}.gps-580622358482518611 .gp-block{display:block}.gps-580622358482518611 .gp-inline-block{display:inline-block}.gps-580622358482518611 .\!gp-flex{display:flex!important}.gps-580622358482518611 .gp-flex{display:flex}.gps-580622358482518611 .gp-inline-flex{display:inline-flex}.gps-580622358482518611 .gp-grid{display:grid}.gps-580622358482518611 .gp-contents{display:contents}.gps-580622358482518611 .\!gp-hidden{display:none!important}.gps-580622358482518611 .gp-hidden{display:none}.gps-580622358482518611 .gp-h-auto{height:auto}.gps-580622358482518611 .gp-h-full{height:100%}.gps-580622358482518611 .\!gp-min-h-full{min-height:100%!important}.gps-580622358482518611 .gp-w-full{width:100%}.gps-580622358482518611 .gp-max-w-full{max-width:100%}.gps-580622358482518611 .gp-flex-1{flex:1 1 0%}.gps-580622358482518611 .gp-flex-none{flex:none}.gps-580622358482518611 .gp-shrink-0{flex-shrink:0}.gps-580622358482518611 .gp-rotate-0{--tw-rotate:0deg}.gps-580622358482518611 .gp-rotate-0,.gps-580622358482518611 .gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-580622358482518611 .gp-rotate-180{--tw-rotate:180deg}.gps-580622358482518611 .gp-cursor-pointer{cursor:pointer}.gps-580622358482518611 .gp-select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.gps-580622358482518611 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-580622358482518611 .\!gp-flex-row{flex-direction:row!important}.gps-580622358482518611 .gp-flex-row{flex-direction:row}.gps-580622358482518611 .gp-flex-col{flex-direction:column}.gps-580622358482518611 .gp-flex-wrap{flex-wrap:wrap}.gps-580622358482518611 .\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-580622358482518611 .gp-items-center{align-items:center}.gps-580622358482518611 .gp-justify-center{justify-content:center}.gps-580622358482518611 .gp-justify-between{justify-content:space-between}.gps-580622358482518611 .gp-gap-2{gap:8px}.gps-580622358482518611 .gp-overflow-hidden{overflow:hidden}.gps-580622358482518611 .gp-break-words{overflow-wrap:break-word}.gps-580622358482518611 .gp-text-center{text-align:center}.gps-580622358482518611 .gp-leading-\[0\]{line-height:0}.gps-580622358482518611 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-580622358482518611 .gp-text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity))}.gps-580622358482518611 .gp-no-underline{text-decoration-line:none}.gps-580622358482518611 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-580622358482518611 .gp-duration-200{transition-duration:.2s}.gps-580622358482518611 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-580622358482518611 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-580622358482518611 .disabled\:gp-opacity-30:disabled{opacity:.3}.gps-580622358482518611 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-580622358482518611 .gp-group[data-state=loading] .group-data-\[state\=loading\]\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-580622358482518611 .tablet\:\!gp-absolute{position:absolute!important}.gps-580622358482518611 .tablet\:gp-absolute{position:absolute}.gps-580622358482518611 .tablet\:gp-left-0{left:0}.gps-580622358482518611 .tablet\:gp-right-0{right:0}.gps-580622358482518611 .tablet\:gp-z-2{z-index:2}.gps-580622358482518611 .tablet\:gp-block{display:block}.gps-580622358482518611 .tablet\:\!gp-flex{display:flex!important}.gps-580622358482518611 .tablet\:\!gp-hidden{display:none!important}.gps-580622358482518611 .tablet\:gp-hidden{display:none}.gps-580622358482518611 .tablet\:gp-h-auto{height:auto}.gps-580622358482518611 .tablet\:\!gp-min-h-full{min-height:100%!important}.gps-580622358482518611 .tablet\:gp-flex-none{flex:none}.gps-580622358482518611 .tablet\:gp-rotate-0{--tw-rotate:0deg}.gps-580622358482518611 .tablet\:gp-rotate-0,.gps-580622358482518611 .tablet\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-580622358482518611 .tablet\:gp-rotate-180{--tw-rotate:180deg}.gps-580622358482518611 .tablet\:\!gp-flex-row{flex-direction:row!important}.gps-580622358482518611 .tablet\:gp-flex-row{flex-direction:row}.gps-580622358482518611 .tablet\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-580622358482518611 .tablet\:gp-px-0{padding-left:0;padding-right:0}}@media (max-width:767px){.gps-580622358482518611 .mobile\:\!gp-absolute{position:absolute!important}.gps-580622358482518611 .mobile\:gp-absolute{position:absolute}.gps-580622358482518611 .mobile\:gp-left-0{left:0}.gps-580622358482518611 .mobile\:gp-right-0{right:0}.gps-580622358482518611 .mobile\:gp-z-2{z-index:2}.gps-580622358482518611 .mobile\:gp-block{display:block}.gps-580622358482518611 .mobile\:\!gp-flex{display:flex!important}.gps-580622358482518611 .mobile\:\!gp-hidden{display:none!important}.gps-580622358482518611 .mobile\:gp-hidden{display:none}.gps-580622358482518611 .mobile\:gp-h-auto{height:auto}.gps-580622358482518611 .mobile\:\!gp-min-h-full{min-height:100%!important}.gps-580622358482518611 .mobile\:gp-flex-none{flex:none}.gps-580622358482518611 .mobile\:gp-rotate-0{--tw-rotate:0deg}.gps-580622358482518611 .mobile\:gp-rotate-0,.gps-580622358482518611 .mobile\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-580622358482518611 .mobile\:gp-rotate-180{--tw-rotate:180deg}.gps-580622358482518611 .mobile\:\!gp-flex-row{flex-direction:row!important}.gps-580622358482518611 .mobile\:gp-flex-row{flex-direction:row}.gps-580622358482518611 .mobile\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-580622358482518611 .mobile\:gp-px-0{padding-left:0;padding-right:0}}.gps-580622358482518611 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-580622358482518611 .\[\&\>svg\]\:gp-h-full>svg{height:100%}.gps-580622358482518611 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}.gps-580622358482518611 .\[\&\>svg\]\:gp-w-full>svg{width:100%}@media (max-width:1024px){.gps-580622358482518611 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-580622358482518611 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-580622358482518611 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-580622358482518611 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}</style>
    
    
    

    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}

    
        <section
          class="gp-mx-auto gp-max-w-full [&_*]:gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:none;--pl-tablet:none;--pl-mobile:none;--pr:none;--pr-tablet:none;--pr-mobile:none"
        >
          
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"#FFFFFF","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gg0bROCedM"}' data-id="gg0bROCedM" id="gg0bROCedM" data-same-height-subgrid-container class="gg0bROCedM gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:24px;--pl:15px;--pb:24px;--pr:15px;--pt-tablet:var(--g-s-4xl);--pl-tablet:15px;--pb-tablet:var(--g-s-4xl);--pr-tablet:15px;--pt-mobile:4px;--pl-mobile:24px;--pb-mobile:var(--g-s-xxs);--pr-mobile:24px;--cg:32px;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="gcjCrDExjR gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gPssQD0UXo"}' data-id="gPssQD0UXo" id="gPssQD0UXo" data-same-height-subgrid-container class="gPssQD0UXo gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--cg:32px;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="gw9ywpW9Ff gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gjvJeOIW-3"}' data-id="gjvJeOIW-3" id="gjvJeOIW-3" data-same-height-subgrid-container class="gjvJeOIW-3 gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:39px;--mb-tablet:39px;--mb-mobile:39px;--cg:8px;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="gYRTMZeGdh gp-relative gp-flex gp-flex-col"
    >
      
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gizi5Gm3ca"
        class="gizi5Gm3ca"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:0px;--mb-mobile:0px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425976168"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-heading, heading);--weight:700;--ls:normal;--size:36px;--size-tablet:28px;--size-mobile:26px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;--tt:uppercase;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggizi5Gm3ca_text | replace: '$locationOrigin', locationOrigin }}
        </h2>
      </div>
    </div>
    
    </div><div
      data-same-height-display-contents
      style="--jc:start"
      class="gOXAikCB-g gp-relative gp-flex gp-flex-col"
    >
      
    <gp-button
      gp-data='{"btnLink":{"link":"#g071lCCNnn","selectedTab":"scrollToList","type":"scroll-to","title":"Section 9"}}'
      
      class="gp-flex gp-flex-col"
    >
      <div style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:none;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--pt:0px;--pl:0px;--pb:0px;--pr:0px;--pt-tablet:0px;--pl-tablet:0px;--pb-tablet:0px;--pr-tablet:0px;--mb-mobile:19px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--ta:right;--ta-mobile:left" >
        <style>[data-id="g55y4Ypgl9"].gp-button-base::before, [data-id="g55y4Ypgl9-interaction"].gp-button-base::before {
      
    content: "";
    height: 100%;
    width: 100%;
    position: absolute;
    pointer-events: none;
    top: 0;
    left: 0;
    border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
    
      border-bottom-left-radius: 999px;
      border-bottom-right-radius: 999px;
      border-top-left-radius: 999px;
      border-top-right-radius: 999px;
      
  
    }
  
      
  [data-id="g55y4Ypgl9"]:hover::before, [data-id="g55y4Ypgl9-interaction"]:hover::before {
    
    
  }</style>
        <a
            class="gp-trigger-button-link gp-hidden"
            href=#g071lCCNnn
            target=undefined
          ></a>
        <a
          data-id="g55y4Ypgl9" dataId="g55y4Ypgl9" data-state="idle" aria-label="<p>GEAR UP WITH ULTRA 2+ TODAY</p>"
          
          href="#g071lCCNnn" target
          style="--w:Auto;--w-tablet:Auto;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:32px;--pr:32px;--pt:12px;--pb:12px;--pl-mobile:32px;--pr-mobile:32px;--pt-mobile:12px;--pb-mobile:12px;--bblr:999px;--bbrr:999px;--btlr:999px;--btrr:999px;--hvr-bblr:999px;--hvr-bbrr:999px;--hvr-btlr:999px;--hvr-btrr:999px;--shadow:none;--bgi:;--hvr-bgi:;--hvr-bg:#1180FF;--bg:#0171E3;--c:var(--g-c-text-3, text-3);--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:600;--size:18px;--size-tablet:16px;--size-mobile:14px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--tt:uppercase"
          class="g55y4Ypgl9 gp-text-center gp-button-base gp-group gp-relative gp-inline-flex gp-max-w-full gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-200 disabled:gp-btn-disabled disabled:gp-opacity-30 gp-text-g-text-3 "
        >
        
        <div class="gp-inline-flex">
          
          
    <span
      data-gp-text
      style="--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:600;--size:18px;--size-tablet:16px;--size-mobile:14px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--tt:uppercase;--ts:none;word-break:break-word"
      class="gp-content-product-button group-active/button:!gp-text-inherit gp-relative gp-flex gp-h-full gp-items-center gp-overflow-hidden gp-break-words group-data-[state=loading]:gp-invisible [&_p]:gp-whitespace-pre-line gp-text button-text"
    >
      {{ section.settings.gg55y4Ypgl9_label }}
    </span>
        </div>
        
        </a>
      </div>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-button-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    </gp-button>
  
    </div>

      
    </gp-row>
  
  
    </div>

      
    </gp-row>
  
  
    <gp-carousel gp-data='{"id":"gKLloD-1LK","setting":{"animationMode":"ease-in","arrow":{"desktop":true,"mobile":true},"arrowBorder":{"desktop":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"}},"arrowButtonSize":{"desktop":{"height":"32px","padding":{"linked":true},"shapeLinked":true,"shapeValue":"1/1","width":"32px"}},"arrowCustom":"<svg width=\"37\" height=\"37\" viewBox=\"0 0 37 37\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"> <rect width=\"37\" height=\"37\" fill=\"white\"/> <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 19.0006C12 18.868 12.0527 18.7408 12.1464 18.6471C12.2402 18.5533 12.3674 18.5006 12.5 18.5006H24.293L21.146 15.3546C21.0521 15.2607 20.9994 15.1334 20.9994 15.0006C20.9994 14.8679 21.0521 14.7405 21.146 14.6466C21.2399 14.5527 21.3672 14.5 21.5 14.5C21.6328 14.5 21.7601 14.5527 21.854 14.6466L25.854 18.6466C25.9006 18.6931 25.9375 18.7483 25.9627 18.809C25.9879 18.8697 26.0009 18.9349 26.0009 19.0006C26.0009 19.0664 25.9879 19.1315 25.9627 19.1923C25.9375 19.253 25.9006 19.3082 25.854 19.3546L21.854 23.3546C21.7601 23.4485 21.6328 23.5013 21.5 23.5013C21.3672 23.5013 21.2399 23.4485 21.146 23.3546C21.0521 23.2607 20.9994 23.1334 20.9994 23.0006C20.9994 22.8679 21.0521 22.7405 21.146 22.6466L24.293 19.5006H12.5C12.3674 19.5006 12.2402 19.448 12.1464 19.3542C12.0527 19.2604 12 19.1332 12 19.0006Z\" fill=\"#242424\"/> </svg>","arrowGapToEachSide":"16","arrowIconSize":{"desktop":24},"autoplay":true,"autoplayTimeout":2,"childItem":["Slide 1","Slide 2","Slide 3","Slide 4","Slide 5","Slide 6","Slide 7"],"controlOverContent":{"desktop":true,"mobile":true,"tablet":true},"dot":{"desktop":true,"mobile":false,"tablet":false},"dotActiveColor":{"desktop":"#242424"},"dotColor":{"desktop":"bg-1"},"dotGapToCarousel":{"desktop":"16","mobile":16,"tablet":16},"dotSize":{"desktop":12,"mobile":12,"tablet":12},"dotStyle":{"desktop":"outside","mobile":"inside","tablet":"inside"},"enableDrag":{"desktop":true},"itemNumber":{"desktop":3,"mobile":1,"tablet":1},"loop":{"desktop":true},"navigationEnable":{"desktop":true,"mobile":true,"tablet":true},"navigationStyle":{"desktop":"inside","mobile":"inside","tablet":"inside"},"pauseOnHover":true,"roundedArrow":{"desktop":{"radiusType":"small"}},"runPreview":false,"sneakPeak":{"desktop":false,"mobile":false,"tablet":false},"sneakPeakOffsetCenter":{"desktop":50,"mobile":50,"tablet":50},"sneakPeakOffsetForward":{"desktop":50,"mobile":50,"tablet":50},"sneakPeakType":{"desktop":"forward"},"vertical":{"desktop":false}},"styles":{"align":{"desktop":"center"},"playSpeed":500,"sizeSetting":{"desktop":{"height":"auto","width":"100%"},"mobile":{"width":"100%"},"tablet":{"width":"100%"}},"spacing":{"desktop":16}}}' data-id="gKLloD-1LK" id="gp-root-carousel-gKLloD-1LK" style="--jc:center" class="gp-group/carousel gp-w-full gp-flex">
      <div
        
        class="gKLloD-1LK gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--h:auto;--h-tablet:auto;--h-mobile:auto;--w:100%;--w-tablet:100%;--w-mobile:100%"
      >
        <div class="gp-relative gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row" style="--h:auto;--h-tablet:100%;--h-mobile:100%;gap:16px">
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0 gp-carousel-arrow-gKLloD-1LK gKLloD-1LK gp-carousel-action-back gem-slider-previous gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-absolute gp-z-2 tablet:!gp-flex tablet:!gp-absolute tablet:gp-z-2 mobile:!gp-flex mobile:!gp-absolute mobile:gp-z-2"
      style="--d:flex;--d-tablet:flex;--d-mobile:flex;--left:16px;--right:initial;--top:initial;--bottom:;--left-tablet:16px;--right-tablet:initial;--top-tablet:initial;--bottom-tablet:;--left-mobile:16px;--right-mobile:initial;--top-mobile:initial;--bottom-mobile:;--w:32px;--h:32px"
    >
      <style type="text/css">
      .gp-carousel-arrow-gKLloD-1LK {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gKLloD-1LK::before {
        content: '';
        height: 100%;
        width: 100%;
        position: absolute;
        pointer-events: none;
        z-index: 10;
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
      @media only screen and (max-width: 1024px) and (min-width: 768px) {
        .gp-carousel-arrow-gKLloD-1LK {
          border-radius: var(--g-radius-small);
        }
        .gp-carousel-arrow-gKLloD-1LK::before {
          border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
          border-radius: var(--g-radius-small);
        }
      }
      @media only screen and (max-width: 768px) {
        .gp-carousel-arrow-gKLloD-1LK {
          border-radius: var(--g-radius-small);
        }
        .gp-carousel-arrow-gKLloD-1LK::before {
          border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
          border-radius: var(--g-radius-small);
        }
      }
    </style>
      
    <div
      class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
      style="--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
    >
        <svg width="37" height="37" viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect width="37" height="37" fill="white"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M12 19.0006C12 18.868 12.0527 18.7408 12.1464 18.6471C12.2402 18.5533 12.3674 18.5006 12.5 18.5006H24.293L21.146 15.3546C21.0521 15.2607 20.9994 15.1334 20.9994 15.0006C20.9994 14.8679 21.0521 14.7405 21.146 14.6466C21.2399 14.5527 21.3672 14.5 21.5 14.5C21.6328 14.5 21.7601 14.5527 21.854 14.6466L25.854 18.6466C25.9006 18.6931 25.9375 18.7483 25.9627 18.809C25.9879 18.8697 26.0009 18.9349 26.0009 19.0006C26.0009 19.0664 25.9879 19.1315 25.9627 19.1923C25.9375 19.253 25.9006 19.3082 25.854 19.3546L21.854 23.3546C21.7601 23.4485 21.6328 23.5013 21.5 23.5013C21.3672 23.5013 21.2399 23.4485 21.146 23.3546C21.0521 23.2607 20.9994 23.1334 20.9994 23.0006C20.9994 22.8679 21.0521 22.7405 21.146 22.6466L24.293 19.5006H12.5C12.3674 19.5006 12.2402 19.448 12.1464 19.3542C12.0527 19.2604 12 19.1332 12 19.0006Z" fill="#242424"/> </svg>
    </div>
  
    </button>
  
          <div id="gp-carousel-gKLloD-1LK" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none gp-carousel-slider-gKLloD-1LK mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full" style="--cg-mobile:16px;--cg-tablet:16px;--cg:16px">
            
    
    <div
      
      id=""
      class="gem-slider-item gp-w-full gem-slider-item-gKLloD-1LK gp-child-item-gKLloD-1LK gw6NcZOKjy"
      style="--minw:calc(100% / 3 - 10.666666666666666px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 3 - 10.666666666666666px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1 - 0px);--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC"
      data-index="0"
      grid-index="{{forloop.index}}"
      external-id="{{media.external_id}}"
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
    >
      <div
        class="gp-w-full gp-h-full"
        
      >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"#F6F6F6","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gYWd2cEsYe"}' data-id="gYWd2cEsYe" id="gYWd2cEsYe" data-same-height-subgrid-container class="gYWd2cEsYe gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--pt:20px;--pl:20px;--pb:20px;--pr:20px;--cg:8px;--cg-tablet:23px;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-tablet:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--h:100%;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:space-between"
      class="gVMXxO0EVK gp-relative gp-flex gp-flex-col"
    >
      
    <div
      role="presentation"
      data-id="gD_-_aWOM3"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left"
      class="gp-group/image gp-relative force-publish-1757425976207 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto gD_-_aWOM3"
    >
      <div
        
        style="border-radius:inherit;--jc:left"
        class="gp-h-full gp-w-full gp-flex pointer-events-auto"
      >
        
    <picture style="border-radius:inherit" class="gp-contents">
      
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_553400155311702965-58bdd352-25ed-4781-8feb-2da4721c3e6a.png" | file_url }}" srcset="{{ "gempages_553400155311702965-58bdd352-25ed-4781-8feb-2da4721c3e6a.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_553400155311702965-58bdd352-25ed-4781-8feb-2da4721c3e6a.png" | file_url }}" srcset="{{ "gempages_553400155311702965-58bdd352-25ed-4781-8feb-2da4721c3e6a.png" | file_url }}" />
    
      
      <img
        loading="eager" fetchpriority="high"
        src="{{ "gempages_553400155311702965-58bdd352-25ed-4781-8feb-2da4721c3e6a.png" | file_url }}" data-src="{{ "gempages_553400155311702965-58bdd352-25ed-4781-8feb-2da4721c3e6a.png" | file_url }}"
        alt="Alt Image"
        width="100%"
        style="--aspect:3/4;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--radiusType:rounded"
        class="gp-inline-block gp-w-full gp-max-w-full gp_force_load"
      />
    
    </picture>
  
      </div>
    </div>
  
    </div><div
      data-same-height-display-contents
      style="--jc:space-between"
      class="gKOgf2WcKp gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gTrVXDZnAI"}' data-id="gTrVXDZnAI" id="gTrVXDZnAI" data-same-height-subgrid-container class="gTrVXDZnAI gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:0px;--pl:8px;--pr:8px;--pt-mobile:32px;--pl-mobile:6px;--pb-mobile:var(--g-s-2xl);--pr-mobile:6px;--pl-tablet:var(--g-s-3xl);--pr-tablet:var(--g-s-3xl);--cg:32px;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="gbIS0VvMh- gp-relative gp-flex gp-flex-col"
    >
      
    
      
      
    <div data-id="gOLIgLn8ft"  style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:19px;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">
      <div class="gp-flex gp-flex-wrap" style="--cg:4px;--jc:left">
            <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="g0n6IvyAFa ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gYnUUQoBsv" class="gp-leading-[0] gYnUUQoBsv" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gYnUUQoBsv" class="icon-wrapper gp-inline-flex gp-overflow-hidden gYnUUQoBsv " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gQDGg_0Dxv ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gk2rD6ktOl" class="gp-leading-[0] gk2rD6ktOl" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gk2rD6ktOl" class="icon-wrapper gp-inline-flex gp-overflow-hidden gk2rD6ktOl " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gZ4Lnd3axT ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gXwfvddNzF" class="gp-leading-[0] gXwfvddNzF" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gXwfvddNzF" class="icon-wrapper gp-inline-flex gp-overflow-hidden gXwfvddNzF " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gvTurjhmH1 ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gwfJRHE3f5" class="gp-leading-[0] gwfJRHE3f5" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gwfJRHE3f5" class="icon-wrapper gp-inline-flex gp-overflow-hidden gwfJRHE3f5 " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gyy4Sws8W1 ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gLp68zPt8L" class="gp-leading-[0] gLp68zPt8L" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gLp68zPt8L" class="icon-wrapper gp-inline-flex gp-overflow-hidden gLp68zPt8L " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div>
          </div>
    </div>
  
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="g_9sQ3Go_C"
        class="g_9sQ3Go_C"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:var(--g-s-2xl);--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425976209"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:16px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#000000;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.gg_9sQ3Go_C_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gakXTWZ92J"
        class="gakXTWZ92J"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425976210"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--c:#000000;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggakXTWZ92J_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    </div>

      
    </gp-row>
  
  
    </div>

      
    </gp-row>
  
  
      </div>
    </div>
  
    <div
      
      id=""
      class="gem-slider-item gp-w-full gem-slider-item-gKLloD-1LK gp-child-item-gKLloD-1LK gdAcpUITE5"
      style="--minw:calc(100% / 3 - 10.666666666666666px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 3 - 10.666666666666666px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1 - 0px);--pageType:GP_STATIC"
      data-index="1"
      grid-index="{{forloop.index}}"
      external-id="{{media.external_id}}"
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
    >
      <div
        class="gp-w-full gp-h-full"
        
      >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"#F6F6F6","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gW2H9khZPA"}' data-id="gW2H9khZPA" id="gW2H9khZPA" data-same-height-subgrid-container class="gW2H9khZPA gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--pt:20px;--pl:20px;--pb:20px;--pr:20px;--cg:8px;--cg-tablet:23px;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-tablet:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--h:100%;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:space-between"
      class="gIrs6IaPbV gp-relative gp-flex gp-flex-col"
    >
      
    <div
      role="presentation"
      data-id="g3umcvefad"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left"
      class="gp-group/image gp-relative force-publish-1757425976211 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto g3umcvefad"
    >
      <div
        
        style="border-radius:inherit;--jc:left"
        class="gp-h-full gp-w-full gp-flex pointer-events-auto"
      >
        
    <picture style="border-radius:inherit" class="gp-contents">
      
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_553400155311702965-6982ad95-b285-4c71-9062-1a5d827700dc.png" | file_url }}" srcset="{{ "gempages_553400155311702965-6982ad95-b285-4c71-9062-1a5d827700dc.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_553400155311702965-6982ad95-b285-4c71-9062-1a5d827700dc.png" | file_url }}" srcset="{{ "gempages_553400155311702965-6982ad95-b285-4c71-9062-1a5d827700dc.png" | file_url }}" />
    
      
      <img
        loading="eager" fetchpriority="high"
        src="{{ "gempages_553400155311702965-6982ad95-b285-4c71-9062-1a5d827700dc.png" | file_url }}" data-src="{{ "gempages_553400155311702965-6982ad95-b285-4c71-9062-1a5d827700dc.png" | file_url }}"
        alt="Alt Image"
        width="100%"
        style="--aspect:3/4;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--radiusType:rounded"
        class="gp-inline-block gp-w-full gp-max-w-full gp_force_load"
      />
    
    </picture>
  
      </div>
    </div>
  
    </div><div
      data-same-height-display-contents
      style="--jc:space-between"
      class="g4XhP0I6Ti gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gkQ0FGsrB3"}' data-id="gkQ0FGsrB3" id="gkQ0FGsrB3" data-same-height-subgrid-container class="gkQ0FGsrB3 gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pl:8px;--pr:8px;--pt-mobile:32px;--pl-mobile:6px;--pb-mobile:var(--g-s-2xl);--pr-mobile:6px;--pl-tablet:var(--g-s-3xl);--pr-tablet:var(--g-s-3xl);--cg:32px;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="gIrrLpVJuA gp-relative gp-flex gp-flex-col"
    >
      
    
      
      
    <div data-id="gcJxCbAt6D"  style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:19px;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">
      <div class="gp-flex gp-flex-wrap" style="--cg:4px;--jc:left">
            <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gDT0s1OKzv ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gVsMikvF2i" class="gp-leading-[0] gVsMikvF2i" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gVsMikvF2i" class="icon-wrapper gp-inline-flex gp-overflow-hidden gVsMikvF2i " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="g6CXX87c3s ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gIGr4UCP1m" class="gp-leading-[0] gIGr4UCP1m" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:0px;--ta:left">
      <div data-id="gIGr4UCP1m" class="icon-wrapper gp-inline-flex gp-overflow-hidden gIGr4UCP1m " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gyzDMRexFn ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gs2GZpwRU6" class="gp-leading-[0] gs2GZpwRU6" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gs2GZpwRU6" class="icon-wrapper gp-inline-flex gp-overflow-hidden gs2GZpwRU6 " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gFodAW8YQf ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gnW0v-Ow7j" class="gp-leading-[0] gnW0v-Ow7j" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:0px;--ta:left">
      <div data-id="gnW0v-Ow7j" class="icon-wrapper gp-inline-flex gp-overflow-hidden gnW0v-Ow7j " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gcpED9BOzx ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gYrdLRYFCy" class="gp-leading-[0] gYrdLRYFCy" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gYrdLRYFCy" class="icon-wrapper gp-inline-flex gp-overflow-hidden gYrdLRYFCy " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div>
          </div>
    </div>
  
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gkPkhyuLeV"
        class="gkPkhyuLeV"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:var(--g-s-2xl);--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425976214"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#000000;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggkPkhyuLeV_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="g21DwELodL"
        class="g21DwELodL"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425976214"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--c:#242424;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.gg21DwELodL_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    </div>

      
    </gp-row>
  
  
    </div>

      
    </gp-row>
  
  
      </div>
    </div>
  
    <div
      
      id=""
      class="gem-slider-item gp-w-full gem-slider-item-gKLloD-1LK gp-child-item-gKLloD-1LK gPFQ9KRS_x"
      style="--minw:calc(100% / 3 - 10.666666666666666px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 3 - 10.666666666666666px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1 - 0px);--pageType:GP_STATIC"
      data-index="2"
      grid-index="{{forloop.index}}"
      external-id="{{media.external_id}}"
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
    >
      <div
        class="gp-w-full gp-h-full"
        
      >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"#F6F6F6","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gdjCi-ABZ-"}' data-id="gdjCi-ABZ-" id="gdjCi-ABZ-" data-same-height-subgrid-container class="gdjCi-ABZ- gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--pt:20px;--pl:20px;--pb:20px;--pr:20px;--cg:8px;--cg-tablet:23px;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-tablet:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--h:100%;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:space-between"
      class="gaCSb7YMz_ gp-relative gp-flex gp-flex-col"
    >
      
    <div
      role="presentation"
      data-id="gKMmz6NFMg"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left"
      class="gp-group/image gp-relative force-publish-1757425976215 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto gKMmz6NFMg"
    >
      <div
        
        style="border-radius:inherit;--jc:left"
        class="gp-h-full gp-w-full gp-flex pointer-events-auto"
      >
        
    <picture style="border-radius:inherit" class="gp-contents">
      
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_553400155311702965-94ded00c-4138-492d-b653-8f8ac98d2f18.png" | file_url }}" srcset="{{ "gempages_553400155311702965-94ded00c-4138-492d-b653-8f8ac98d2f18.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_553400155311702965-94ded00c-4138-492d-b653-8f8ac98d2f18.png" | file_url }}" srcset="{{ "gempages_553400155311702965-94ded00c-4138-492d-b653-8f8ac98d2f18.png" | file_url }}" />
    
      
      <img
        loading="eager" fetchpriority="high"
        src="{{ "gempages_553400155311702965-94ded00c-4138-492d-b653-8f8ac98d2f18.png" | file_url }}" data-src="{{ "gempages_553400155311702965-94ded00c-4138-492d-b653-8f8ac98d2f18.png" | file_url }}"
        alt="Alt Image"
        width="100%"
        style="--aspect:3/4;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--radiusType:rounded"
        class="gp-inline-block gp-w-full gp-max-w-full gp_force_load"
      />
    
    </picture>
  
      </div>
    </div>
  
    </div><div
      data-same-height-display-contents
      style="--jc:space-between"
      class="gTPVx08V_g gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gIzSBWrTwt"}' data-id="gIzSBWrTwt" id="gIzSBWrTwt" data-same-height-subgrid-container class="gIzSBWrTwt gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pl:8px;--pr:8px;--pt-mobile:32px;--pl-mobile:6px;--pb-mobile:var(--g-s-2xl);--pr-mobile:6px;--pl-tablet:var(--g-s-3xl);--pr-tablet:var(--g-s-3xl);--cg:32px;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="gkO-ZD8snD gp-relative gp-flex gp-flex-col"
    >
      
    
      
      
    <div data-id="gk7qPfg-rz"  style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:18px;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">
      <div class="gp-flex gp-flex-wrap" style="--cg:4px;--jc:left">
            <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gELHOdVYlb ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="g6cucm3TdW" class="gp-leading-[0] g6cucm3TdW" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="g6cucm3TdW" class="icon-wrapper gp-inline-flex gp-overflow-hidden g6cucm3TdW " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gwBai7_Mot ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gWxNp0GXQ8" class="gp-leading-[0] gWxNp0GXQ8" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gWxNp0GXQ8" class="icon-wrapper gp-inline-flex gp-overflow-hidden gWxNp0GXQ8 " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="g6SBjZW46k ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="ge86ZClLQV" class="gp-leading-[0] ge86ZClLQV" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="ge86ZClLQV" class="icon-wrapper gp-inline-flex gp-overflow-hidden ge86ZClLQV " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gh8HzmKit5 ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gsJr-rJYFn" class="gp-leading-[0] gsJr-rJYFn" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gsJr-rJYFn" class="icon-wrapper gp-inline-flex gp-overflow-hidden gsJr-rJYFn " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gkmiKpBJbe ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="g095o9ju7J" class="gp-leading-[0] g095o9ju7J" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="g095o9ju7J" class="icon-wrapper gp-inline-flex gp-overflow-hidden g095o9ju7J " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div>
          </div>
    </div>
  
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gh1IPXe1R-"
        class="gh1IPXe1R-"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:var(--g-s-2xl);--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425976218"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#000000;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggh1IPXe1R-_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gwRVEBL2TO"
        class="gwRVEBL2TO"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425976218"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--c:#242424;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggwRVEBL2TO_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    </div>

      
    </gp-row>
  
  
    </div>

      
    </gp-row>
  
  
      </div>
    </div>
  
    <div
      
      id=""
      class="gem-slider-item gp-w-full gem-slider-item-gKLloD-1LK gp-child-item-gKLloD-1LK gyyCwZ68FH"
      style="--minw:calc(100% / 3 - 10.666666666666666px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 3 - 10.666666666666666px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1 - 0px);--pageType:GP_STATIC"
      data-index="3"
      grid-index="{{forloop.index}}"
      external-id="{{media.external_id}}"
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
    >
      <div
        class="gp-w-full gp-h-full"
        
      >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"#F6F6F6","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gDPYEF6HPw"}' data-id="gDPYEF6HPw" id="gDPYEF6HPw" data-same-height-subgrid-container class="gDPYEF6HPw gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--pt:20px;--pl:20px;--pb:20px;--pr:20px;--cg:8px;--cg-tablet:23px;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-tablet:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--h:100%;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:space-between"
      class="ggSub7M9e7 gp-relative gp-flex gp-flex-col"
    >
      
    <div
      role="presentation"
      data-id="gLAlGxE4it"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left"
      class="gp-group/image gp-relative force-publish-1757425976220 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto gLAlGxE4it"
    >
      <div
        
        style="border-radius:inherit;--jc:left"
        class="gp-h-full gp-w-full gp-flex pointer-events-auto"
      >
        
    <picture style="border-radius:inherit" class="gp-contents">
      
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_553400155311702965-963e1ed1-1e0b-45f9-b4a1-058a1610865c.png" | file_url }}" srcset="{{ "gempages_553400155311702965-963e1ed1-1e0b-45f9-b4a1-058a1610865c.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_553400155311702965-963e1ed1-1e0b-45f9-b4a1-058a1610865c.png" | file_url }}" srcset="{{ "gempages_553400155311702965-963e1ed1-1e0b-45f9-b4a1-058a1610865c.png" | file_url }}" />
    
      
      <img
        loading="eager" fetchpriority="high"
        src="{{ "gempages_553400155311702965-963e1ed1-1e0b-45f9-b4a1-058a1610865c.png" | file_url }}" data-src="{{ "gempages_553400155311702965-963e1ed1-1e0b-45f9-b4a1-058a1610865c.png" | file_url }}"
        alt="Alt Image"
        width="100%"
        style="--aspect:3/4;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--radiusType:rounded"
        class="gp-inline-block gp-w-full gp-max-w-full gp_force_load"
      />
    
    </picture>
  
      </div>
    </div>
  
    </div><div
      data-same-height-display-contents
      style="--jc:space-between"
      class="g9I8HS68a0 gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gBFRMVxGeY"}' data-id="gBFRMVxGeY" id="gBFRMVxGeY" data-same-height-subgrid-container class="gBFRMVxGeY gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pl:8px;--pr:8px;--pt-mobile:32px;--pl-mobile:6px;--pb-mobile:var(--g-s-2xl);--pr-mobile:6px;--pl-tablet:var(--g-s-3xl);--pr-tablet:var(--g-s-3xl);--cg:32px;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="g8QPGC8VHN gp-relative gp-flex gp-flex-col"
    >
      
    
      
      
    <div data-id="gisnS0uVsw"  style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:19px;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">
      <div class="gp-flex gp-flex-wrap" style="--cg:4px;--jc:left">
            <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="geveaCYRjH ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="ghekuR1Ft0" class="gp-leading-[0] ghekuR1Ft0" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="ghekuR1Ft0" class="icon-wrapper gp-inline-flex gp-overflow-hidden ghekuR1Ft0 " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gDobcqiXGo ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="ggPLzOBK-D" class="gp-leading-[0] ggPLzOBK-D" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="ggPLzOBK-D" class="icon-wrapper gp-inline-flex gp-overflow-hidden ggPLzOBK-D " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gqWSOqUAv3 ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="ggtlfI466r" class="gp-leading-[0] ggtlfI466r" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="ggtlfI466r" class="icon-wrapper gp-inline-flex gp-overflow-hidden ggtlfI466r " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="g4ZrZzFD19 ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gc5jTIh2j7" class="gp-leading-[0] gc5jTIh2j7" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gc5jTIh2j7" class="icon-wrapper gp-inline-flex gp-overflow-hidden gc5jTIh2j7 " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="g0w_gNXBJk ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gXr27gIidh" class="gp-leading-[0] gXr27gIidh" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gXr27gIidh" class="icon-wrapper gp-inline-flex gp-overflow-hidden gXr27gIidh " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div>
          </div>
    </div>
  
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gAkD1qyV9z"
        class="gAkD1qyV9z"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:var(--g-s-2xl);--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425976222"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#000000;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggAkD1qyV9z_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gKQg77dk46"
        class="gKQg77dk46"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425976223"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--c:#242424;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggKQg77dk46_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    </div>

      
    </gp-row>
  
  
    </div>

      
    </gp-row>
  
  
      </div>
    </div>
  
    <div
      
      id=""
      class="gem-slider-item gp-w-full gem-slider-item-gKLloD-1LK gp-child-item-gKLloD-1LK gndzlKLWMA"
      style="--minw:calc(100% / 3 - 10.666666666666666px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 3 - 10.666666666666666px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1 - 0px);--pageType:GP_STATIC"
      data-index="4"
      grid-index="{{forloop.index}}"
      external-id="{{media.external_id}}"
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
    >
      <div
        class="gp-w-full gp-h-full"
        
      >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"#F6F6F6","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gcQfO54BhF"}' data-id="gcQfO54BhF" id="gcQfO54BhF" data-same-height-subgrid-container class="gcQfO54BhF gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--pt:20px;--pl:20px;--pb:20px;--pr:20px;--cg:8px;--cg-tablet:23px;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-tablet:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--h:100%;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:space-between"
      class="gmk-sALWpr gp-relative gp-flex gp-flex-col"
    >
      
    <div
      role="presentation"
      data-id="g3yeaf40t0"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left"
      class="gp-group/image gp-relative force-publish-1757425976224 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto g3yeaf40t0"
    >
      <div
        
        style="border-radius:inherit;--jc:left"
        class="gp-h-full gp-w-full gp-flex pointer-events-auto"
      >
        
    <picture style="border-radius:inherit" class="gp-contents">
      
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_553400155311702965-545f831d-2dc4-494c-8788-4156ef21eb55.png" | file_url }}" srcset="{{ "gempages_553400155311702965-545f831d-2dc4-494c-8788-4156ef21eb55.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_553400155311702965-545f831d-2dc4-494c-8788-4156ef21eb55.png" | file_url }}" srcset="{{ "gempages_553400155311702965-545f831d-2dc4-494c-8788-4156ef21eb55.png" | file_url }}" />
    
      
      <img
        loading="eager" fetchpriority="high"
        src="{{ "gempages_553400155311702965-545f831d-2dc4-494c-8788-4156ef21eb55.png" | file_url }}" data-src="{{ "gempages_553400155311702965-545f831d-2dc4-494c-8788-4156ef21eb55.png" | file_url }}"
        alt="Alt Image"
        width="100%"
        style="--aspect:3/4;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--radiusType:rounded"
        class="gp-inline-block gp-w-full gp-max-w-full gp_force_load"
      />
    
    </picture>
  
      </div>
    </div>
  
    </div><div
      data-same-height-display-contents
      style="--jc:space-between"
      class="govzm__gMf gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gNtHDecIdp"}' data-id="gNtHDecIdp" id="gNtHDecIdp" data-same-height-subgrid-container class="gNtHDecIdp gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pl:8px;--pr:8px;--pt-mobile:32px;--pl-mobile:6px;--pb-mobile:var(--g-s-2xl);--pr-mobile:6px;--pl-tablet:var(--g-s-3xl);--pr-tablet:var(--g-s-3xl);--cg:32px;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="gNkqEixk1X gp-relative gp-flex gp-flex-col"
    >
      
    
      
      
    <div data-id="gEUNlos-33"  style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:19px;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">
      <div class="gp-flex gp-flex-wrap" style="--cg:4px;--jc:left">
            <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="ge-JFUou_N ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gJJq5baupj" class="gp-leading-[0] gJJq5baupj" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gJJq5baupj" class="icon-wrapper gp-inline-flex gp-overflow-hidden gJJq5baupj " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="glVRDmouTc ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gbOj1OTAbH" class="gp-leading-[0] gbOj1OTAbH" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gbOj1OTAbH" class="icon-wrapper gp-inline-flex gp-overflow-hidden gbOj1OTAbH " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="g_ire05p4R ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="g_1_TwBt7i" class="gp-leading-[0] g_1_TwBt7i" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="g_1_TwBt7i" class="icon-wrapper gp-inline-flex gp-overflow-hidden g_1_TwBt7i " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="guvDHGBh51 ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gkUjn4kmHd" class="gp-leading-[0] gkUjn4kmHd" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gkUjn4kmHd" class="icon-wrapper gp-inline-flex gp-overflow-hidden gkUjn4kmHd " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gcaSrGqbph ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gYjk0w9xns" class="gp-leading-[0] gYjk0w9xns" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gYjk0w9xns" class="icon-wrapper gp-inline-flex gp-overflow-hidden gYjk0w9xns " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div>
          </div>
    </div>
  
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gsHf5I-HRi"
        class="gsHf5I-HRi"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:var(--g-s-2xl);--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425976227"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#000000;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggsHf5I-HRi_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gvbVVubWXR"
        class="gvbVVubWXR"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425976227"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--c:#242424;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggvbVVubWXR_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    </div>

      
    </gp-row>
  
  
    </div>

      
    </gp-row>
  
  
      </div>
    </div>
  
    <div
      
      id=""
      class="gem-slider-item gp-w-full gem-slider-item-gKLloD-1LK gp-child-item-gKLloD-1LK gtxD9BGju1"
      style="--minw:calc(100% / 3 - 10.666666666666666px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 3 - 10.666666666666666px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1 - 0px);--pageType:GP_STATIC"
      data-index="5"
      grid-index="{{forloop.index}}"
      external-id="{{media.external_id}}"
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
    >
      <div
        class="gp-w-full gp-h-full"
        
      >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"#F6F6F6","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gZBMcuA4M1"}' data-id="gZBMcuA4M1" id="gZBMcuA4M1" data-same-height-subgrid-container class="gZBMcuA4M1 gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--pt:20px;--pl:20px;--pb:20px;--pr:20px;--cg:8px;--cg-tablet:23px;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-tablet:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--h:100%;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:space-between"
      class="gFQLcw9lIy gp-relative gp-flex gp-flex-col"
    >
      
    <div
      role="presentation"
      data-id="gtmwIzgXpE"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left"
      class="gp-group/image gp-relative force-publish-1757425976228 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto gtmwIzgXpE"
    >
      <div
        
        style="border-radius:inherit;--jc:left"
        class="gp-h-full gp-w-full gp-flex pointer-events-auto"
      >
        
    <picture style="border-radius:inherit" class="gp-contents">
      
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_553400155311702965-f736f716-eb9c-4934-95f2-f1185d5d7b27.png" | file_url }}" srcset="{{ "gempages_553400155311702965-f736f716-eb9c-4934-95f2-f1185d5d7b27.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_553400155311702965-f736f716-eb9c-4934-95f2-f1185d5d7b27.png" | file_url }}" srcset="{{ "gempages_553400155311702965-f736f716-eb9c-4934-95f2-f1185d5d7b27.png" | file_url }}" />
    
      
      <img
        loading="eager" fetchpriority="high"
        src="{{ "gempages_553400155311702965-f736f716-eb9c-4934-95f2-f1185d5d7b27.png" | file_url }}" data-src="{{ "gempages_553400155311702965-f736f716-eb9c-4934-95f2-f1185d5d7b27.png" | file_url }}"
        alt="Alt Image"
        width="100%"
        style="--aspect:3/4;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--radiusType:rounded"
        class="gp-inline-block gp-w-full gp-max-w-full gp_force_load"
      />
    
    </picture>
  
      </div>
    </div>
  
    </div><div
      data-same-height-display-contents
      style="--jc:space-between"
      class="gQlNnMMrEw gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gOcSC4C-Kd"}' data-id="gOcSC4C-Kd" id="gOcSC4C-Kd" data-same-height-subgrid-container class="gOcSC4C-Kd gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pl:8px;--pr:8px;--pt-mobile:32px;--pl-mobile:6px;--pb-mobile:var(--g-s-2xl);--pr-mobile:6px;--pl-tablet:var(--g-s-3xl);--pr-tablet:var(--g-s-3xl);--cg:32px;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="g0EWYmWP0_ gp-relative gp-flex gp-flex-col"
    >
      
    
      
      
    <div data-id="gQEl_egQRv"  style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:19px;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">
      <div class="gp-flex gp-flex-wrap" style="--cg:4px;--jc:left">
            <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gLpwma6pUx ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gXovWO_XKL" class="gp-leading-[0] gXovWO_XKL" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gXovWO_XKL" class="icon-wrapper gp-inline-flex gp-overflow-hidden gXovWO_XKL " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="g1ufCifkcb ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="g6zEG32xK7" class="gp-leading-[0] g6zEG32xK7" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="g6zEG32xK7" class="icon-wrapper gp-inline-flex gp-overflow-hidden g6zEG32xK7 " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="g0XbgmShGJ ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gXK1DCQSs1" class="gp-leading-[0] gXK1DCQSs1" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gXK1DCQSs1" class="icon-wrapper gp-inline-flex gp-overflow-hidden gXK1DCQSs1 " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gHffDsDmcd ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="g9uSqhKAzB" class="gp-leading-[0] g9uSqhKAzB" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="g9uSqhKAzB" class="icon-wrapper gp-inline-flex gp-overflow-hidden g9uSqhKAzB " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="g8gV7jcf4s ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gL1q2igJpU" class="gp-leading-[0] gL1q2igJpU" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gL1q2igJpU" class="icon-wrapper gp-inline-flex gp-overflow-hidden gL1q2igJpU " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div>
          </div>
    </div>
  
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gRuUcKK6sG"
        class="gRuUcKK6sG"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:var(--g-s-2xl);--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425976231"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#575757;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggRuUcKK6sG_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gDCpej8HT0"
        class="gDCpej8HT0"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425976231"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--c:#242424;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggDCpej8HT0_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    </div>

      
    </gp-row>
  
  
    </div>

      
    </gp-row>
  
  
      </div>
    </div>
  
    <div
      
      id=""
      class="gem-slider-item gp-w-full gem-slider-item-gKLloD-1LK gp-child-item-gKLloD-1LK gPDE13yWKy"
      style="--minw:calc(100% / 3 - 10.666666666666666px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 3 - 10.666666666666666px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1 - 0px);--pageType:GP_STATIC"
      data-index="6"
      grid-index="{{forloop.index}}"
      external-id="{{media.external_id}}"
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
    >
      <div
        class="gp-w-full gp-h-full"
        
      >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"#F6F6F6","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gdehZW0Niz"}' data-id="gdehZW0Niz" id="gdehZW0Niz" data-same-height-subgrid-container class="gdehZW0Niz gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--pt:20px;--pl:20px;--pb:20px;--pr:20px;--cg:8px;--cg-tablet:23px;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-tablet:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--h:100%;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:space-between"
      class="gScIRUqXGW gp-relative gp-flex gp-flex-col"
    >
      
    <div
      role="presentation"
      data-id="grM68Uyzjx"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left"
      class="gp-group/image gp-relative force-publish-1757425976233 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto grM68Uyzjx"
    >
      <div
        
        style="border-radius:inherit;--jc:left"
        class="gp-h-full gp-w-full gp-flex pointer-events-auto"
      >
        
    <picture style="border-radius:inherit" class="gp-contents">
      
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_553400155311702965-0e4cca42-7061-4172-9f62-7e53f7c87007.png" | file_url }}" srcset="{{ "gempages_553400155311702965-0e4cca42-7061-4172-9f62-7e53f7c87007.png" | file_url }}" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_553400155311702965-0e4cca42-7061-4172-9f62-7e53f7c87007.png" | file_url }}" srcset="{{ "gempages_553400155311702965-0e4cca42-7061-4172-9f62-7e53f7c87007.png" | file_url }}" />
    
      
      <img
        loading="eager" fetchpriority="high"
        src="{{ "gempages_553400155311702965-0e4cca42-7061-4172-9f62-7e53f7c87007.png" | file_url }}" data-src="{{ "gempages_553400155311702965-0e4cca42-7061-4172-9f62-7e53f7c87007.png" | file_url }}"
        alt="Alt Image"
        width="100%"
        style="--aspect:3/4;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--radiusType:rounded"
        class="gp-inline-block gp-w-full gp-max-w-full gp_force_load"
      />
    
    </picture>
  
      </div>
    </div>
  
    </div><div
      data-same-height-display-contents
      style="--jc:space-between"
      class="g2k1Bu8Srw gp-relative gp-flex gp-flex-col"
    >
      
    

    

    
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"g7EhRWFc4A"}' data-id="g7EhRWFc4A" id="g7EhRWFc4A" data-same-height-subgrid-container class="g7EhRWFc4A gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pl:8px;--pr:8px;--pt-mobile:32px;--pl-mobile:6px;--pb-mobile:var(--g-s-2xl);--pr-mobile:6px;--pl-tablet:var(--g-s-3xl);--pr-tablet:var(--g-s-3xl);--cg:32px;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="gpJELOHI-z gp-relative gp-flex gp-flex-col"
    >
      
    
      
      
    <div data-id="geoK9ihpC4"  style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:19px;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">
      <div class="gp-flex gp-flex-wrap" style="--cg:4px;--jc:left">
            <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gz2tf75Tir ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gfrlDU6NsX" class="gp-leading-[0] gfrlDU6NsX" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gfrlDU6NsX" class="icon-wrapper gp-inline-flex gp-overflow-hidden gfrlDU6NsX " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="g6N7AleScz ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="g1mDQ9jM9_" class="gp-leading-[0] g1mDQ9jM9_" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="g1mDQ9jM9_" class="icon-wrapper gp-inline-flex gp-overflow-hidden g1mDQ9jM9_ " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gC8vLJSZPr ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gzg4DrfKfp" class="gp-leading-[0] gzg4DrfKfp" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gzg4DrfKfp" class="icon-wrapper gp-inline-flex gp-overflow-hidden gzg4DrfKfp " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gWAfioSxBE ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gJsSdQy04K" class="gp-leading-[0] gJsSdQy04K" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gJsSdQy04K" class="icon-wrapper gp-inline-flex gp-overflow-hidden gJsSdQy04K " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px" class="gnVxtFqYhx ">
      
    <div  class="gp-flex gp-w-full gp-flex-col gp-items-center">
      
    <div id="gmuIE1I3RW" class="gp-leading-[0] gmuIE1I3RW" style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ta:left">
      <div data-id="gmuIE1I3RW" class="icon-wrapper gp-inline-flex gp-overflow-hidden gmuIE1I3RW " style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent">
      
        <div >
          <span style="--c:#FAAD14;--t:rotate(0deg);--w:18px;--h:18px;--minw:18px;--height-desktop:18px;--height-tablet:18px;--height-mobile:18px" class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] "><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
        </div>
      </div>
    </div>
    
    </div>
  
      </div>
          </div>
    </div>
  
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gmgUac7OYG"
        class="gmgUac7OYG"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--mb:var(--g-s-2xl);--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425976235"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#000000;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggmgUac7OYG_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="ge7Sa0rrKZ"
        class="ge7Sa0rrKZ"
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425976235"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class=" gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:400;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--c:#000000;word-break:break-word;overflow:hidden;--tdl:"
        >
          {{ section.settings.gge7Sa0rrKZ_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    </div>

      
    </gp-row>
  
  
    </div>

      
    </gp-row>
  
  
      </div>
    </div>
  
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0 gp-carousel-arrow-gKLloD-1LK gKLloD-1LK gp-carousel-action-next gem-slider-next gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-absolute gp-z-2 tablet:!gp-flex tablet:!gp-absolute tablet:gp-z-2 mobile:!gp-flex mobile:!gp-absolute mobile:gp-z-2"
      style="--d:flex;--d-tablet:flex;--d-mobile:flex;--right:16px;--left:initial;--top:;--bottom:initial;--right-tablet:16px;--left-tablet:initial;--top-tablet:;--bottom-tablet:initial;--right-mobile:16px;--left-mobile:initial;--top-mobile:;--bottom-mobile:initial;--w:32px;--h:32px"
    >
      <style type="text/css">
      .gp-carousel-arrow-gKLloD-1LK {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gKLloD-1LK::before {
        content: '';
        height: 100%;
        width: 100%;
        position: absolute;
        pointer-events: none;
        z-index: 10;
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
      @media only screen and (max-width: 1024px) and (min-width: 768px) {
        .gp-carousel-arrow-gKLloD-1LK {
          border-radius: var(--g-radius-small);
        }
        .gp-carousel-arrow-gKLloD-1LK::before {
          border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
          border-radius: var(--g-radius-small);
        }
      }
      @media only screen and (max-width: 768px) {
        .gp-carousel-arrow-gKLloD-1LK {
          border-radius: var(--g-radius-small);
        }
        .gp-carousel-arrow-gKLloD-1LK::before {
          border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
          border-radius: var(--g-radius-small);
        }
      }
    </style>
      
    <div
      class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
      style="--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
    >
        <svg width="37" height="37" viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect width="37" height="37" fill="white"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M12 19.0006C12 18.868 12.0527 18.7408 12.1464 18.6471C12.2402 18.5533 12.3674 18.5006 12.5 18.5006H24.293L21.146 15.3546C21.0521 15.2607 20.9994 15.1334 20.9994 15.0006C20.9994 14.8679 21.0521 14.7405 21.146 14.6466C21.2399 14.5527 21.3672 14.5 21.5 14.5C21.6328 14.5 21.7601 14.5527 21.854 14.6466L25.854 18.6466C25.9006 18.6931 25.9375 18.7483 25.9627 18.809C25.9879 18.8697 26.0009 18.9349 26.0009 19.0006C26.0009 19.0664 25.9879 19.1315 25.9627 19.1923C25.9375 19.253 25.9006 19.3082 25.854 19.3546L21.854 23.3546C21.7601 23.4485 21.6328 23.5013 21.5 23.5013C21.3672 23.5013 21.2399 23.4485 21.146 23.3546C21.0521 23.2607 20.9994 23.1334 20.9994 23.0006C20.9994 22.8679 21.0521 22.7405 21.146 22.6466L24.293 19.5006H12.5C12.3674 19.5006 12.2402 19.448 12.1464 19.3542C12.0527 19.2604 12 19.1332 12 19.0006Z" fill="#242424"/> </svg>
    </div>
  
    </button>
  
        </div>
        
    
        <div class="gp-carousel-dot-container gp-carousel-dot-container-gKLloD-1LK gp-z-2 gp-flex gp-flex-1 gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-static gp-flex-row gp-left-0 gp-right-0 tablet:gp-absolute tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-absolute mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0" style="--mt:16px;--bottom-tablet:16px;--bottom-mobile:16px;--d:flex;--d-tablet:none;--d-mobile:none">
          <!-- item render by js -->
        </div>
      
  
      </div>
    </gp-carousel>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://assets.gemcommerce.com/assets-v2/gp-carousel-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    </div>

      
    </gp-row>
  
        </section>
      
  
    <style {% if section.settings.section_preload == "false" %} class="gps-link" type="text/disabled-css" {% endif %}>@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWubEbVmUiAw.woff) format('woff');
}
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYaalmUiAw.woff) format('woff');
}
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmUiAw.woff) format('woff');
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBHMdazQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBHMdazQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBHMdazQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style>
    
{% schema %}
  {
    
    "name": "Section 7",
    "tag": "section",
    "class": "gps-580622358482518611 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=578718746651132539&sectionId=580622358482518611)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggizi5Gm3ca_text","label":"ggizi5Gm3ca_text","default":"What Our&nbsp;<br>Adventurers Say"},{"type":"html","id":"gg55y4Ypgl9_label","label":"gg55y4Ypgl9_label","default":"<p>GEAR UP WITH ULTRA 2+ TODAY</p>"},{"type":"html","id":"gg_9sQ3Go_C_text","label":"gg_9sQ3Go_C_text","default":"<p><span style=\"background-color:rgba(0,0,0,0);font-size:18px;\">\"</span><span style=\"background-color:rgba(0,0,0,0);color:rgb(0,0,0);font-size:18px;\">Great watch, extremely happy with the purchase. Easy to use and a good range of applications and the battery last much longer than my Apple Watch Series 6.\"</span></p>"},{"type":"html","id":"ggakXTWZ92J_text","label":"ggakXTWZ92J_text","default":"<p><span style=\"color:#242424;\"><strong>Kenneth N.</strong></span> <span style=\"color:#B4B4B4;font-size:13px;\">/ Happy Customer</span></p>"},{"type":"html","id":"ggkPkhyuLeV_text","label":"ggkPkhyuLeV_text","default":"<p><span style=\"background-color:rgba(0, 0, 0, 0);font-size:18px;\">\"</span><span style=\"background-color:rgba(0, 0, 0, 0);color:rgb(0,0,0);font-size:18px;\">Fantastic watch,amazing battery life,very reliable,perfect for active life style!\"</span></p>"},{"type":"html","id":"gg21DwELodL_text","label":"gg21DwELodL_text","default":"<p><span style=\"color:#242424;\"><strong>Igor G.</strong></span> <span style=\"color:#B4B4B4;font-size:13px;\">/ Happy Customer</span></p>"},{"type":"html","id":"ggh1IPXe1R-_text","label":"ggh1IPXe1R-_text","default":"<p><span style=\"background-color:rgba(0, 0, 0, 0);font-size:18px;\">\"</span><span style=\"background-color:rgba(0, 0, 0, 0);color:rgb(0,0,0);font-size:18px;\">I just got it this week and so far i’m loving it. Can’t wait to use it on my half marathon and obstacle races coming up soon.\"</span></p>"},{"type":"html","id":"ggwRVEBL2TO_text","label":"ggwRVEBL2TO_text","default":"<p><span style=\"color:#242424;\"><strong>Franco C.</strong></span> <span style=\"color:#B4B4B4;font-size:13px;\">/ Happy Customer</span></p>"},{"type":"html","id":"ggAkD1qyV9z_text","label":"ggAkD1qyV9z_text","default":"<p><span style=\"background-color:rgba(0, 0, 0, 0);font-size:18px;\">\"</span><span style=\"background-color:rgba(0, 0, 0, 0);color:rgb(0,0,0);font-size:18px;\">These watches are great. Easy to order, easy to set up. They function well.\"</span></p>"},{"type":"html","id":"ggKQg77dk46_text","label":"ggKQg77dk46_text","default":"<p><span style=\"color:#242424;\"><strong>Cameron S.</strong></span> <span style=\"color:#B4B4B4;font-size:13px;\">/ Happy Customer</span></p>"},{"type":"html","id":"ggsHf5I-HRi_text","label":"ggsHf5I-HRi_text","default":"<p><span style=\"background-color:rgba(0, 0, 0, 0);font-size:18px;\">\"</span><span style=\"background-color:rgba(0, 0, 0, 0);color:rgb(0,0,0);font-size:18px;\">Good quality smart watch. Watch band a little small but has plenty of flexibility. A speedy delivery time. Plenty of apps for the user. Overall a great watch. Thank you Gard Pro ultra 2+\"</span></p>"},{"type":"html","id":"ggvbVVubWXR_text","label":"ggvbVVubWXR_text","default":"<p><span style=\"color:#242424;\"><strong>Akosita P.</strong></span> <span style=\"color:#B4B4B4;font-size:13px;\">/ Happy Customer</span></p>"},{"type":"html","id":"ggRuUcKK6sG_text","label":"ggRuUcKK6sG_text","default":"<p><span style=\"color:#000000;\">\"I'm only starting to use it but I am very pleased\"</span></p>"},{"type":"html","id":"ggDCpej8HT0_text","label":"ggDCpej8HT0_text","default":"<p><span style=\"color:#242424;\"><strong>Darren C</strong></span> <span style=\"color:#B4B4B4;font-size:13px;\">/ Happy Customer</span></p>"},{"type":"html","id":"ggmgUac7OYG_text","label":"ggmgUac7OYG_text","default":"<p><span style=\"background-color:rgba(0, 0, 0, 0);font-size:18px;\">\"</span><span style=\"background-color:rgba(0, 0, 0, 0);color:rgb(0,0,0);font-size:18px;\">Great watch and a fraction of the price of comparable watches.\"</span></p>"},{"type":"html","id":"gge7Sa0rrKZ_text","label":"gge7Sa0rrKZ_text","default":"<p><span style=\"color:#242424;\"><strong>Tony G.</strong></span> <span style=\"color:#B4B4B4;font-size:13px;\">/ Happy Customer</span></p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
  