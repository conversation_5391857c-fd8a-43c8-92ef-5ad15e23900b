/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "advanced-content": {
      "name": "Contenido personalizado",
      "settings": {
        "full_width": {
          "label": "Ancho de página completo"
        },
        "space_around": {
          "label": "Añadir espacio por encima y por debajo"
        }
      },
      "blocks": {
        "html": {
          "name": "HTML",
          "settings": {
            "code": {
              "label": "HTML",
              "info": "Compatible con Liquid"
            },
            "width": {
              "label": "Ancho"
            },
            "alignment": {
              "label": "Alineación vertical",
              "info": "Se alinea cuando está junto a otro contenido personalizado",
              "options": {
                "top-middle": {
                  "label": "Arriba"
                },
                "center": {
                  "label": "Centrada"
                },
                "bottom-middle": {
                  "label": "Abajo"
                }
              }
            }
          }
        },
        "image": {
          "name": "Imagen",
          "settings": {
            "image": {
              "label": "Imagen"
            },
            "link": {
              "label": "Enlace"
            },
            "width": {
              "label": "Ancho"
            },
            "alignment": {
              "label": "Alineación vertical",
              "info": "Se alinea cuando está junto a otro contenido personalizado",
              "options": {
                "top-middle": {
                  "label": "Arriba"
                },
                "center": {
                  "label": "Centrada"
                },
                "bottom-middle": {
                  "label": "Abajo"
                }
              }
            }
          }
        }
      },
      "presets": {
        "custom_content": {
          "name": "Contenido personalizado"
        }
      }
    },
    "apps": {
      "name": "Aplicaciones",
      "settings": {
        "full_width": {
          "label": "Ancho de página completo"
        },
        "space_around": {
          "label": "Añadir espacio por encima y por debajo"
        }
      },
      "presets": {
        "apps": {
          "name": "Aplicaciones"
        }
      }
    },
    "article-template": {
      "name": "Páginas del artículo",
      "settings": {
        "image_hero": {
          "label": "Utilizar la imagen destacada como hero de ancho completo",
          "info": "(si se establece la imagen del artículo)"
        },
        "blog_show_tags": {
          "label": "Mostrar etiquetas"
        },
        "blog_show_date": {
          "label": "Mostrar fecha"
        },
        "blog_show_comments": {
          "label": "Mostrar el recuento de comentarios"
        },
        "blog_show_author": {
          "label": "Mostrar autor"
        },
        "social_sharing_blog": {
          "label": "Mostrar botones para compartir en redes sociales"
        }
      }
    },
    "background-image-text": {
      "name": "Imagen grande con caja de texto",
      "settings": {
        "subtitle": {
          "label": "Subtítulo"
        },
        "title": {
          "label": "Encabezado"
        },
        "text": {
          "label": "Texto"
        },
        "button_label": {
          "label": "Etiqueta de botón"
        },
        "button_link": {
          "label": "Enlace de botón"
        },
        "image": {
          "label": "Imagen"
        },
        "focal_point": {
          "label": "Punto de enfoque de la imagen",
          "info": "Utilizar para mantener a la vista el sujeto de su foto.",
          "options": {
            "20_0": {
              "label": "Arriba a la izquierda"
            },
            "top": {
              "label": "Arriba"
            },
            "80_0": {
              "label": "Arriba a la derecha"
            },
            "20_50": {
              "label": "Izquierda"
            },
            "center": {
              "label": "Centrada"
            },
            "80_50": {
              "label": "Derecha"
            },
            "20_100": {
              "label": "Abajo a la izquierda"
            },
            "bottom": {
              "label": "Abajo"
            },
            "80_100": {
              "label": "Abajo a la derecha"
            }
          }
        },
        "layout": {
          "label": "Diseño",
          "options": {
            "left": {
              "label": "Texto a la izquierda"
            },
            "right": {
              "label": "Texto a la derecha"
            }
          }
        },
        "height": {
          "label": "Altura de la sección"
        },
        "framed": {
          "label": "Añadir marco"
        },
        "parallax_direction": {
          "label": "dirección de paralaje",
          "options": {
            "top": {
              "label": "Vertical"
            },
            "left": {
              "label": "Horizontal"
            }
          }
        },
        "parallax": {
          "label": "Habilitar el paralaje"
        }
      },
      "presets": {
        "large_image_with_text_box": {
          "name": "Imagen grande con caja de texto"
        }
      }
    },
    "background-video-text": {
      "name": "Vídeo largo con caja de texto",
      "settings": {
        "subtitle": {
          "label": "Subtítulo"
        },
        "title": {
          "label": "Encabezado"
        },
        "text": {
          "label": "Texto"
        },
        "button_label": {
          "label": "Etiqueta de botón"
        },
        "button_link": {
          "label": "Enlace de botón",
          "info": "Los enlaces a los vídeos de YouTube se abrirán en un reproductor de vídeo"
        },
        "video_url": {
          "label": "Enlace al vídeo de fondo",
          "info": "Es compatible con YouTube, .MP4 y Vimeo. No todas las funciones son compatibles con Vimeo. [Saber más](https://archetypethemes.co/blogs/impulse/how-do-i-add-background-videos)"
        },
        "color_border": {
          "label": "Color del vídeo",
          "info": "Utilizar para la zona fronteriza móvil"
        },
        "layout": {
          "label": "Diseño",
          "options": {
            "left": {
              "label": "Texto a la izquierda"
            },
            "right": {
              "label": "Texto a la derecha"
            }
          }
        },
        "height": {
          "label": "Altura de la sección"
        }
      },
      "presets": {
        "large_video_with_text_box": {
          "name": "Vídeo largo con caja de texto"
        }
      }
    },
    "blog-posts": {
      "name": "Artículos de blog",
      "settings": {
        "title": {
          "label": "Encabezado"
        },
        "blog": {
          "label": "Blog"
        },
        "post_limit": {
          "label": "Publicaciones"
        },
        "blog_show_tags": {
          "label": "Mostrar etiquetas"
        },
        "blog_show_date": {
          "label": "Mostrar fecha"
        },
        "blog_show_comments": {
          "label": "Mostrar el recuento de comentarios"
        },
        "blog_show_author": {
          "label": "Mostrar autor"
        },
        "view_all": {
          "label": "Mostrar el botón \" ver todo\""
        },
        "blog_image_size": {
          "label": "Tamaño de la imagen",
          "options": {
            "natural": {
              "label": "Natural"
            },
            "square": {
              "label": "Cuadrado (1:1)"
            },
            "landscape": {
              "label": "Paisaje (4:3)"
            },
            "portrait": {
              "label": "Retrato (2:3)"
            },
            "wide": {
              "label": "Ancho (16:9)"
            }
          }
        },
        "divider": {
          "label": "Mostrar el divisor de sección"
        }
      },
      "presets": {
        "blog_posts": {
          "name": "Artículos de blog"
        }
      }
    },
    "blog-template": {
      "name": "Páginas del blog",
      "settings": {
        "blog_show_tag_filter": {
          "label": "Mostrar filtro de etiquetas"
        },
        "blog_show_rss": {
          "label": "Mostrar enlace RSS"
        },
        "blog_show_tags": {
          "label": "Mostrar etiquetas"
        },
        "blog_show_date": {
          "label": "Mostrar fecha"
        },
        "blog_show_comments": {
          "label": "Mostrar el recuento de comentarios"
        },
        "blog_show_author": {
          "label": "Mostrar autor"
        },
        "blog_show_excerpt": {
          "label": "Mostrar extracto"
        },
        "blog_image_size": {
          "label": "Tamaño de la imagen",
          "options": {
            "natural": {
              "label": "Natural"
            },
            "square": {
              "label": "Cuadrado (1:1)"
            },
            "landscape": {
              "label": "Paisaje (4:3)"
            },
            "portrait": {
              "label": "Retrato (2:3)"
            },
            "wide": {
              "label": "Ancho (16:9)"
            }
          }
        }
      }
    },
    "collection-header": {
      "name": "Cabeza de la colección",
      "settings": {
        "enable": {
          "label": "Habilitar cabecera"
        },
        "collection_image_enable": {
          "label": "Mostrar imagen de la colección"
        },
        "parallax_direction": {
          "label": "dirección de paralaje",
          "options": {
            "top": {
              "label": "Vertical"
            },
            "left": {
              "label": "Horizontal"
            }
          }
        },
        "parallax": {
          "label": "Imagen de paralaje"
        }
      }
    },
    "collection-return": {
      "name": "Volver a la colección"
    },
    "contact-form": {
      "name": "Formulario de contacto",
      "settings": {
        "content": "Todos los envíos se realizan a la dirección de correo electrónico del cliente de su tienda.  [Saber más](https://help.shopify.com/en/manual/using-themes/change-the-layout/add-contact-form#view-contact-form-submissions).",
        "title": {
          "label": "Título"
        },
        "text": {
          "label": "Texto"
        },
        "show_phone": {
          "label": "Mostrar número de teléfono"
        },
        "narrow_column": {
          "label": "Columna estrecha"
        }
      },
      "presets": {
        "contact_form": {
          "name": "Formulario de contacto"
        }
      }
    },
    "faq": {
      "name": "Preguntas frecuentes",
      "settings": {
        "title": {
          "label": "Encabezado"
        }
      },
      "blocks": {
        "rich_text": {
          "name": "Texto enriquecido",
          "settings": {
            "title": {
              "label": "Título"
            },
            "text": {
              "label": "Texto"
            },
            "align_text": {
              "label": "Alineación de texto",
              "options": {
                "left": {
                  "label": "Izquierda"
                },
                "center": {
                  "label": "Centrado"
                },
                "right": {
                  "label": "Derecha"
                }
              }
            }
          }
        },
        "question": {
          "name": "Pregunta",
          "settings": {
            "title": {
              "label": "Pregunta"
            },
            "text": {
              "label": "Texto"
            }
          }
        }
      },
      "presets": {
        "faq": {
          "name": "Preguntas frecuentes"
        }
      }
    },
    "featured-collection": {
      "name": "Colección destacada",
      "settings": {
        "title": {
          "label": "Encabezado"
        },
        "home_featured_products": {
          "label": "Colección"
        },
        "per_row": {
          "label": "Productos por fila"
        },
        "rows": {
          "label": "Filas de productos"
        },
        "mobile_scrollable": {
          "label": "Activar uso de banda magnética en el móvil"
        },
        "view_all": {
          "label": "Mostrar el enlace 'ver todo'"
        },
        "divider": {
          "label": "Mostrar el divisor de sección"
        }
      },
      "presets": {
        "featured_collection": {
          "name": "Colección destacada"
        }
      }
    },
    "featured-collections": {
      "name": "Lista de colecciones",
      "settings": {
        "title": {
          "label": "Encabezado"
        },
        "divider": {
          "label": "Mostrar el divisor de sección"
        },
        "per_row": {
          "label": "Colecciones por fila"
        },
        "enable_gutter": {
          "label": "Añadir espacio"
        }
      },
      "presets": {
        "collection_list": {
          "name": "Lista de colecciones"
        }
      },
      "blocks": {
        "collection": {
          "name": "Colección",
          "settings": {
            "collection": {
              "label": "Colección"
            },
            "title": {
              "label": "Título"
            },
            "focal_point": {
              "label": "Punto focal",
              "info": "Utilizar para mantener a la vista el sujeto de su foto.",
              "options": {
                "20_0": {
                  "label": "Arriba a la izquierda"
                },
                "top_center": {
                  "label": "Arriba en el centro"
                },
                "80_0": {
                  "label": "Arriba a la derecha"
                },
                "20_50": {
                  "label": "Izquierda"
                },
                "center_center": {
                  "label": "Centrado"
                },
                "80_50": {
                  "label": "Derecha"
                },
                "20_100": {
                  "label": "Abajo a la izquierda"
                },
                "bottom_center": {
                  "label": "Abajo en el centro"
                },
                "80_100": {
                  "label": "Abajo a la derecha"
                }
              }
            }
          }
        }
      }
    },
    "featured-product": {
      "name": "Producto destacado",
      "settings": {
        "featured_product": {
          "label": "Producto"
        },
        "divider": {
          "label": "Mostrar el divisor de sección"
        },
        "sku_enable": {
          "label": "Mostrar SKU"
        },
        "header_media": "Multimedia",
        "content": "Más información sobre [tipos de medios de comunicación](https://help.shopify.com/en/manual/products/product-media)",
        "image_position": {
          "label": "Posición",
          "options": {
            "left": {
              "label": "Izquierda"
            },
            "right": {
              "label": "Derecha"
            }
          }
        },
        "image_size": {
          "label": "Tamaño",
          "options": {
            "small": {
              "label": "Pequeña"
            },
            "medium": {
              "label": "Mediana"
            },
            "large": {
              "label": "Grande"
            }
          }
        },
        "product_zoom_enable": {
          "label": "Habilitar zoom en la imagen"
        },
        "thumbnail_position": {
          "label": "Posición de la miniatura",
          "options": {
            "beside": {
              "label": "Junto a multimedia"
            },
            "below": {
              "label": "Medios de comunicación de abajo"
            }
          }
        },
        "thumbnail_arrows": {
          "label": "Mostrar las flechas de las miniaturas"
        },
        "mobile_layout": {
          "label": "Diseño para móviles",
          "options": {
            "partial": {
              "label": "75 % de ancho"
            },
            "full": {
              "label": "Ancho completo"
            }
          }
        },
        "enable_video_looping": {
          "label": "Activar la reproducción de video en bucle"
        },
        "product_video_style": {
          "label": "Estilo de vídeo",
          "options": {
            "muted": {
              "label": "Vídeo sin sonido"
            },
            "unmuted": {
              "label": "Vídeo con sonido"
            }
          },
          "info": "El vídeo con sonido no se reproducirá de forma automática"
        }
      },
      "presets": {
        "featured_product": {
          "name": "Producto destacado"
        }
      }
    },
    "featured-video": {
      "name": "Video",
      "settings": {
        "title": {
          "label": "Encabezado"
        },
        "video_url": {
          "label": "Enlace de vídeo"
        },
        "divider": {
          "label": "Mostrar el divisor de sección"
        }
      },
      "presets": {
        "video": {
          "name": "Video"
        }
      }
    },
    "footer-promotions": {
      "name": "Promociones a pie de página",
      "settings": {
        "hide_homepage": {
          "label": "No mostrar en la página de inicio"
        },
        "image_size": {
          "label": "Tamaño de la imagen",
          "options": {
            "natural": {
              "label": "Natural"
            },
            "square": {
              "label": "Cuadrado (1:1)"
            },
            "landscape": {
              "label": "Paisaje (4:3)"
            },
            "portrait": {
              "label": "Retrato (2:3)"
            },
            "wide": {
              "label": "Ancho (16:9)"
            }
          }
        }
      },
      "blocks": {
        "column": {
          "name": "Columna",
          "settings": {
            "enable_image": {
              "label": "Mostrar imagen"
            },
            "image": {
              "label": "Imagen"
            },
            "title": {
              "label": "Encabezado"
            },
            "text": {
              "label": "Texto"
            },
            "button_label": {
              "label": "Etiqueta de botón"
            },
            "button_link": {
              "label": "Enlace"
            }
          }
        }
      }
    },
    "footer": {
      "name": "Pie de página",
      "settings": {
        "header_language_selector": "Para agregar un idioma, ve a tu [configuración de idiomas.](/admin/settings/languages)",
        "show_locale_selector": {
          "label": "Mostrar el selector de idioma"
        },
        "header_currency_selector": "Para añadir una moneda, vaya a su [configuración de moneda](/admin/settings/payments)",
        "show_currency_selector": {
          "label": "Mostrar el selector de moneda"
        },
        "show_currency_flags": {
          "label": "Mostrar banderas de moneda"
        },
        "header_additional_footer_content": "Contenido adicional a pie de página",
        "show_payment_icons": {
          "label": "Mostrar íconos de pago"
        },
        "show_copyright": {
          "label": "Mostrar derechos de autor"
        },
        "copyright_text": {
          "label": "Texto adicional sobre derechos de autor"
        }
      },
      "blocks": {
        "logo": {
          "name": "Logotipo",
          "settings": {
            "logo": {
              "label": "Imagen del logo"
            },
            "desktop_logo_height": {
              "label": "Altura del logotipo"
            },
            "container_width": {
              "label": "Ancho de columna"
            }
          }
        },
        "navigation": {
          "name": "Navegación",
          "settings": {
            "show_footer_title": {
              "label": "Mostrar título"
            },
            "menu": {
              "label": "Seleccionar un menú",
              "info": "Este menú no muestra los elementos desplegables"
            },
            "container_width": {
              "label": "Ancho de columna"
            }
          }
        },
        "newsletter_and_social": {
          "name": "Boletín informativo y social",
          "settings": {
            "show_footer_title": {
              "label": "Mostrar título"
            },
            "content": "Los clientes que se registren tendrán una cuenta creada para ellos en Shopify. [Ver clientes](/admin/customers).",
            "title": {
              "label": "Encabezado"
            },
            "text": {
              "label": "Texto",
              "info": "Opcional"
            },
            "container_width": {
              "label": "Ancho de columna"
            }
          }
        },
        "custom_text": {
          "name": "Texto personalizado",
          "settings": {
            "show_footer_title": {
              "label": "Mostrar título"
            },
            "title": {
              "label": "Encabezado"
            },
            "image": {
              "label": "Imagen"
            },
            "text": {
              "label": "Texto"
            },
            "container_width": {
              "label": "Ancho de columna"
            }
          }
        }
      }
    },
    "giftcard-header": {
      "name": "Encabezado",
      "settings": {
        "logo": {
          "label": "Logotipo"
        },
        "desktop_logo_width": {
          "label": "Ancho del logotipo de escritorio"
        },
        "mobile_logo_width": {
          "label": "Anchura del logotipo en el móvil",
          "info": "Establecido como ancho máximo, puede aparecer más pequeño"
        }
      }
    },
    "header": {
      "name": "Encabezado",
      "settings": {
        "main_menu_link_list": {
          "label": "Navegación"
        },
        "mega_menu_images": {
          "label": "Mostrar imágenes del mega menú",
          "info": "[Cómo crear un megamenú](https://archetypethemes.co/blogs/impulse/how-do-i-create-a-mega-menu)"
        },
        "main_menu_alignment": {
          "label": "Diseño de la cabecera",
          "options": {
            "left": {
              "label": "Logotipo a la izquierda, menú a la izquierda"
            },
            "left-center": {
              "label": "Logotipo a la izquierda, menú al centro"
            },
            "left-drawer": {
              "label": "Logotipo a la izquierda, menú lateral"
            },
            "center-left": {
              "label": "Logotipo en el centro, menú izquierda"
            },
            "center-split": {
              "label": "Logotipo en el centro, menú dividido"
            },
            "center": {
              "label": "Logotipo en el centro, menú abajo"
            },
            "center-drawer": {
              "label": "Centro del logotipo, bandeja de menús"
            }
          }
        },
        "header_style": {
          "label": "Estilo de cabecera",
          "options": {
            "normal": {
              "label": "Normal"
            },
            "sticky": {
              "label": "Adhesivo"
            }
          }
        },
        "sticky_index": {
          "label": "Superposición de la cabecera sobre la página de inicio"
        },
        "sticky_collection": {
          "label": "Cabecera superpuesta sobre la colección",
          "info": "(si la imagen de la colección está activada)"
        },
        "header_announcement_bar": "Barra de anuncios",
        "announcement_compact": {
          "label": "Utilizar el estilo compacto"
        },
        "announcement_above_header": {
          "label": "Mostrar siempre por encima de la cabecera"
        },
        "header_toolbar": "Barra de herramientas",
        "toolbar_menu": {
          "label": "Navegación",
          "info": "Este menú no muestra los elementos desplegables"
        },
        "toolbar_social": {
          "label": "Mostrar iconos sociales"
        },
        "header_language_selector": "Para agregar un idioma, ve a tu [configuración de idiomas.](/admin/settings/languages)",
        "show_locale_selector": {
          "label": "Mostrar el selector de idioma"
        },
        "header_currency_selector": "Para añadir una moneda, vaya a su [configuración de moneda](/admin/settings/payments)",
        "show_currency_selector": {
          "label": "Mostrar el selector de moneda"
        },
        "show_currency_flags": {
          "label": "Mostrar banderas de moneda"
        }
      },
      "blocks": {
        "logo": {
          "name": "Logotipo",
          "settings": {
            "logo": {
              "label": "Logotipo"
            },
            "logo-inverted": {
              "label": "Logotipo blanco",
              "info": "Utilizar cuando está encima de una imagen"
            },
            "desktop_logo_width": {
              "label": "Ancho del logotipo de escritorio"
            },
            "mobile_logo_width": {
              "label": "Anchura del logotipo en el móvil",
              "info": "Establecido como ancho máximo, puede aparecer más pequeño"
            }
          }
        },
        "announcement": {
          "name": "Anuncio",
          "settings": {
            "text": {
              "label": "Encabezado"
            },
            "link_text": {
              "label": "Texto"
            },
            "link": {
              "label": "Enlace"
            }
          }
        }
      }
    },
    "hero-video": {
      "name": "Video hero",
      "settings": {
        "title": {
          "label": "Encabezado"
        },
        "title_size": {
          "label": "Tamaño del texto de la cabecera"
        },
        "subheading": {
          "label": "Subtítulo"
        },
        "link_text": {
          "label": "Texto del botón"
        },
        "link": {
          "label": "Enlace de botón",
          "info": "Los enlaces a los vídeos de YouTube se abrirán en un reproductor de vídeo"
        },
        "color_accent": {
          "label": "Botones"
        },
        "text_align": {
          "label": "Alineación de texto",
          "options": {
            "vertical-center_horizontal-left": {
              "label": "Centro izquierda"
            },
            "vertical-center_horizontal-center": {
              "label": "Centrado"
            },
            "vertical-center_horizontal-right": {
              "label": "Centro derecha"
            },
            "vertical-bottom_horizontal-left": {
              "label": "Abajo a la izquierda"
            },
            "vertical-bottom_horizontal-center": {
              "label": "Abajo en el centro"
            },
            "vertical-bottom_horizontal-right": {
              "label": "Abajo a la derecha"
            }
          }
        },
        "video_url": {
          "label": "Enlace al vídeo de fondo",
          "info": "Es compatible con YouTube, .MP4 y Vimeo. No todas las funciones son compatibles con Vimeo. [Saber más](https://archetypethemes.co/blogs/impulse/how-do-i-add-background-videos)"
        },
        "overlay_opacity": {
          "label": "Protección del texto",
          "info": "Oscurece su imagen para asegurar que su texto sea legible"
        },
        "section_height": {
          "label": "Altura del escritorio",
          "options": {
            "450px": {
              "label": "450 px"
            },
            "550px": {
              "label": "550 px"
            },
            "650px": {
              "label": "650 px"
            },
            "750px": {
              "label": "750 px"
            },
            "100vh": {
              "label": "Pantalla completa"
            }
          }
        },
        "mobile_height": {
          "label": "Altura móvil",
          "options": {
            "auto": {
              "label": "Auto"
            },
            "250px": {
              "label": "250 px"
            },
            "300px": {
              "label": "300 px"
            },
            "400px": {
              "label": "400 px"
            },
            "500px": {
              "label": "500 px"
            },
            "100vh": {
              "label": "Pantalla completa"
            }
          }
        }
      },
      "presets": {
        "video_hero": {
          "name": "Video hero"
        }
      }
    },
    "image-comparison": {
      "name": "Comparación de imágenes",
      "settings": {
        "heading": {
          "label": "Título"
        },
        "heading_size": {
          "label": "Tamaño del encabezado",
          "options": {
            "large": {
              "label": "Larga"
            },
            "medium": {
              "label": "Medio"
            },
            "small": {
              "label": "Pequeña"
            }
          }
        },
        "heading_position": {
          "label": "Posición de rumbo",
          "options": {
            "left": {
              "label": "Izquierda"
            },
            "center": {
              "label": "Centro"
            },
            "right": {
              "label": "Derecha"
            }
          }
        },
        "fullwidth": {
          "label": "ancho de página completo"
        },
        "slider_style": {
          "label": "Estilo deslizante",
          "options": {
            "classic": {
              "label": "Clásica"
            },
            "minimal": {
              "label": "Mínima"
            }
          }
        },
        "height": {
          "label": "Altura"
        },
        "header_colors": "Colores",
        "color": {
          "label": "Botón"
        }
      },
      "blocks": {
        "image": {
          "name": "Imagen",
          "settings": {
            "image": {
              "label": "Imagen"
            }
          }
        }
      }
    },
    "list-collections-template": {
      "name": "Página de lista de colecciones",
      "settings": {
        "title_enable": {
          "label": "Mostrar título"
        },
        "content": "Todas sus colecciones aparecen en la lista por defecto. Para personalizar su lista, elegir \"seleccionado\" y añadir colecciones.",
        "display_type": {
          "label": "Seleccionar colecciones para mostrar",
          "options": {
            "all": {
              "label": "Todo"
            },
            "selected": {
              "label": "Seleccionado"
            }
          }
        },
        "sort": {
          "label": "Ordenar colecciones por:",
          "info": "La clasificación sólo se aplica cuando se selecciona \"todo\".",
          "options": {
            "products_high": {
              "label": "Recuento de productos, de mayor a menor"
            },
            "products_low": {
              "label": "Recuento de productos, de menor a mayor"
            },
            "alphabetical": {
              "label": "Alfabéticamente, A-Z"
            },
            "alphabetical_reversed": {
              "label": "Alfabéticamente, Z-A"
            },
            "date": {
              "label": "Fecha: antigua a reciente"
            },
            "date_reversed": {
              "label": "Fecha: reciente a antigua"
            }
          }
        },
        "grid": {
          "label": "Colecciones por fila"
        }
      },
      "blocks": {
        "collection": {
          "name": "Colección",
          "settings": {
            "collection": {
              "label": "Colección"
            }
          }
        }
      }
    },
    "hotspots": {
      "name": "Puntos de acceso de imagen",
      "settings": {
        "title": {
          "label": "Título"
        },
        "heading_size": {
          "label": "Tamaño del encabezado",
          "options": {
            "large": {
              "label": "Larga"
            },
            "medium": {
              "label": "Medio"
            },
            "small": {
              "label": "Pequeña"
            }
          }
        },
        "heading_position": {
          "label": "Posición de rumbo",
          "options": {
            "left": {
              "label": "Izquierda"
            },
            "center": {
              "label": "Centro"
            },
            "right": {
              "label": "Derecha"
            }
          }
        },
        "image": {
          "label": "Imagen",
          "info": "Se recomienda una proporción cuadrada para una experiencia óptima en el móvil"
        },
        "indent_image": {
          "label": "Ancho de página completo"
        },
        "image_position": {
          "label": "Posición de la imagen",
          "options": {
            "left": {
              "label": "Izquierda"
            },
            "right": {
              "label": "Derecha"
            }
          }
        },
        "hotspot_style": {
          "label": "Estilo de icono de punto caliente",
          "options": {
            "dot": {
              "label": "Punto"
            },
            "plus": {
              "label": "Más"
            },
            "bag": {
              "label": "Bolsa"
            },
            "tag": {
              "label": "Etiqueta"
            }
          }
        },
        "hotspot_color": {
          "label": "Color del punto de acceso"
        }
      },
      "blocks": {
        "product": {
          "name": "punto de acceso del producto",
          "settings": {
            "featured_product": {
              "label": "Producto"
            },
            "vertical": {
              "label": "Posición vertical"
            },
            "horizontal": {
              "label": "Posición horizontal"
            }
          }
        },
        "paragraph": {
          "name": "punto de acceso de párrafo",
          "settings": {
            "subheading": {
              "label": "Subtítulo"
            },
            "heading": {
              "label": "Título"
            },
            "content": {
              "label": "Contenido"
            },
            "button_text": {
              "label": "Botón de texto"
            },
            "button_link": {
              "label": "Enlace de botón"
            },
            "vertical": {
              "label": "Posición vertical"
            },
            "horizontal": {
              "label": "Posición horizontal"
            }
          }
        }
      }
    },
    "logo-list": {
      "name": "Lista de logotipos",
      "settings": {
        "title": {
          "label": "Encabezado"
        },
        "logo_opacity": {
          "label": "Opacidad del logotipo"
        },
        "divider": {
          "label": "Mostrar el divisor de sección"
        }
      },
      "blocks": {
        "logo": {
          "name": "Logotipo",
          "settings": {
            "image": {
              "label": "Imagen"
            },
            "link": {
              "label": "Enlace",
              "info": "Opcional"
            }
          }
        }
      },
      "presets": {
        "logo_list": {
          "name": "Lista de logotipos"
        }
      }
    },
    "main-404": {
      "name": "página 404"
    },
    "main-cart": {
      "name": "Página de la cesta"
    },
    "main-collection": {
      "name": "Cuadrícula de productos",
      "settings": {
        "header_filtering_and_sorting": "Filtrado y ordenado",
        "enable_sidebar": {
          "label": "Activar el filtro",
          "info": "Permitir a sus clientes filtrar las colecciones y los resultados de la búsqueda por disponibilidad de productos, precio, color y mucho más. [Personalizar filtros](/admin/menus)"
        },
        "collapsed": {
          "label": "Minimizar los filtros"
        },
        "filter_style": {
          "label": "Estilo del filtro",
          "options": {
            "sidebar": {
              "label": "Barra lateral"
            },
            "drawer": {
              "label": "Cajón"
            }
          }
        },
        "enable_color_swatches": {
          "label": "Activar las muestras de color",
          "info": "[Ver instrucciones de configuración](https://archetypethemes.co/blogs/impulse/how-do-i-set-up-color-swatches)"
        },
        "enable_sort": {
          "label": "Mostrar opciones de clasificación"
        }
      },
      "blocks": {
        "collection_description": {
          "name": "Descripción de la colección"
        },
        "products": {
          "name": "Productos",
          "settings": {
            "enable_collection_count": {
              "label": "Activar el recuento de la colección"
            },
            "per_row": {
              "label": "Productos por fila"
            },
            "rows_per_page": {
              "label": "Filas por página"
            },
            "mobile_flush_grid": {
              "label": "Rejilla al ras en el móvil"
            }
          }
        },
        "subcollections": {
          "name": "Subcolecciones",
          "settings": {
            "content": "Los enlaces a las colecciones de su menú aparecerán aquí. [Más información](https://archetypethemes.co/blogs/impulse/how-do-i-create-subcollections)",
            "subcollections_per_row": {
              "label": "Subcolecciones por fila"
            }
          }
        }
      }
    },
    "main-page-full-width": {
      "name": "Página (ancho completo)"
    },
    "main-page": {
      "name": "Página"
    },
    "main-product": {
      "name": "Producto",
      "settings": {
        "sku_enable": {
          "label": "Mostrar SKU"
        },
        "header_media": "Multimedia",
        "content": "Más información sobre [tipos de medios de comunicación](https://help.shopify.com/en/manual/products/product-media)",
        "image_position": {
          "label": "Posición",
          "options": {
            "left": {
              "label": "Izquierda"
            },
            "right": {
              "label": "Derecha"
            }
          }
        },
        "image_size": {
          "label": "Tamaño",
          "options": {
            "small": {
              "label": "Pequeña"
            },
            "medium": {
              "label": "Mediana"
            },
            "large": {
              "label": "Grande"
            }
          }
        },
        "product_zoom_enable": {
          "label": "Habilitar zoom en la imagen"
        },
        "thumbnail_position": {
          "label": "Posición de la miniatura",
          "options": {
            "beside": {
              "label": "Junto a multimedia"
            },
            "below": {
              "label": "Medios de comunicación de abajo"
            }
          }
        },
        "thumbnail_height": {
          "label": "Altura de la miniatura",
          "info": "Solo se aplica cuando la posición de la miniatura se establece en 'Junto a multimedia'.",
          "options": {
            "fixed": {
              "label": "Fijo"
            },
            "flexible": {
              "label": "Flexible"
            }
          }
        },
        "thumbnail_arrows": {
          "label": "Mostrar las flechas de las miniaturas"
        },
        "mobile_layout": {
          "label": "Diseño para móviles",
          "options": {
            "partial": {
              "label": "75 % de ancho"
            },
            "full": {
              "label": "Ancho completo"
            }
          }
        },
        "enable_video_looping": {
          "label": "Activar la reproducción de video en bucle"
        },
        "product_video_style": {
          "label": "Estilo de vídeo",
          "options": {
            "muted": {
              "label": "Vídeo sin sonido"
            },
            "unmuted": {
              "label": "Vídeo con sonido"
            }
          },
          "info": "El vídeo con sonido no se reproducirá de forma automática"
        }
      }
    },
    "main-search": {
      "name": "Búsqueda",
      "settings": {
        "header_filtering_and_sorting": "Filtrado y ordenado",
        "enable_sidebar": {
          "label": "Activar el filtro",
          "info": "Permitir a sus clientes filtrar las colecciones y los resultados de la búsqueda por disponibilidad de productos, precio, color y mucho más. [Personalizar filtros](/admin/menus)"
        },
        "collapsed": {
          "label": "Minimizar los filtros"
        },
        "filter_style": {
          "label": "Estilo del filtro",
          "options": {
            "sidebar": {
              "label": "Barra lateral"
            },
            "drawer": {
              "label": "Cajón"
            }
          }
        },
        "enable_color_swatches": {
          "label": "Activar las muestras de color",
          "info": "[Ver instrucciones de configuración](https://archetypethemes.co/blogs/impulse/how-do-i-set-up-color-swatches)"
        },
        "per_row": {
          "label": "Productos por fila"
        },
        "rows_per_page": {
          "label": "Filas por página"
        },
        "mobile_flush_grid": {
          "label": "Rejilla al ras en el móvil"
        }
      }
    },
    "map": {
      "name": "Mapa",
      "settings": {
        "map_title": {
          "label": "Encabezado"
        },
        "address": {
          "label": "Dirección y horario"
        },
        "map_address": {
          "label": "Dirección del mapa",
          "info": "Google Maps encontrará la ubicación exacta"
        },
        "api_key": {
          "label": "Clave API de Google Maps",
          "info": "Tendrá que [registrar una clave API de Google Maps](https://help.shopify.com/manual/using-themes/troubleshooting/map-section-api-key) para visualizar el mapa"
        },
        "show_button": {
          "label": "Mostrar el botón \"Obtener direcciones\""
        },
        "background_image": {
          "label": "Imagen",
          "info": "Utilizar en lugar de una clave API"
        },
        "background_image_position": {
          "label": "Punto de enfoque de la imagen",
          "options": {
            "top_left": {
              "label": "Arriba a la izquierda"
            },
            "top_center": {
              "label": "Arriba en el centro"
            },
            "top_right": {
              "label": "Arriba a la derecha"
            },
            "center_left": {
              "label": "Centrado a la izquierda"
            },
            "center_center": {
              "label": "Centrado en el medio"
            },
            "center_right": {
              "label": "Centrado a la derecha"
            },
            "bottom_left": {
              "label": "Abajo a la izquierda"
            },
            "bottom_center": {
              "label": "Abajo en el centro"
            },
            "bottom_right": {
              "label": "Abajo a la derecha"
            }
          },
          "info": "Utilizar para mantener a la vista el sujeto de su foto."
        }
      },
      "presets": {
        "map": {
          "name": "Mapa"
        }
      }
    },
    "newsletter-popup": {
      "name": "Ventana emergente",
      "settings": {
        "mode": {
          "label": "Habilitar ventana emergente",
          "info": "Aparece en el editor de temas cuando está deshabilitado."
        },
        "disable_for_account_holders": {
          "label": "Desactivar para titulares de cuentas",
          "info": "No se mostrará a los clientes que hayan creado una cuenta en tu tienda."
        },
        "popup_seconds": {
          "label": "Retraso",
          "info": "El retraso está desactivado en el editor de temas para la visibilidad"
        },
        "popup_days": {
          "label": "Frecuencia",
          "info": "Número de días antes de que vuelva a aparecer una ventana emergente descartada"
        },
        "header_content": "Contenido",
        "popup_title": {
          "label": "Encabezado"
        },
        "popup_image": {
          "label": "Imagen",
          "info": "Non appare su mobile per soddisfare le [linee guida interstiziali] di Google (https://webmasters.googleblog.com/2016/08/helping-users-easily-access-content-on.html) per migliorare il SEO"
        },
        "image_position": {
          "label": "Posición de la imagen",
          "options": {
            "left": {
              "label": "Izquierda"
            },
            "right": {
              "label": "Derecha"
            }
          }
        },
        "popup_text": {
          "label": "Texto"
        },
        "close_text": {
          "label": "Texto del botón de cerrar"
        },
        "header_newsletter": "Boletín de noticias",
        "content": "Cada registro creará un cliente en su tienda. [Ver clientes](/admin/customers?query=&accepts_marketing=1).",
        "enable_newsletter": {
          "label": "Activar el boletín de noticias"
        },
        "header_button": "Botón",
        "button_label": {
          "label": "Etiqueta de botón"
        },
        "button_link": {
          "label": "Enlace de botón"
        },
        "enable_button": {
          "label": "Botón de activación"
        }
      },
      "blocks": {
        "header": {
          "name": "Recordatorio adhesivo",
          "settings": {
            "text": {
              "label": "Etiqueta recordatoria",
              "info": "Aparece cuando se cierra la ventana emergente del boletín informativo.",
              "default": "Consigue un 10 % de descuento"
            }
          }
        }
      }
    },
    "newsletter": {
      "name": "Suscriptor de correo electrónico",
      "blocks": {
        "title": {
          "name": "Título",
          "settings": {
            "title": {
              "label": "Encabezado"
            }
          }
        },
        "text": {
          "name": "Texto",
          "settings": {
            "text": {
              "label": "Subtítulo"
            }
          }
        },
        "form": {
          "name": "Formulario"
        },
        "share_buttons": {
          "name": "Compartir botones"
        }
      },
      "settings": {
        "content": "Los clientes que se suscriban se les añadirá su dirección de correo electrónico a la [lista de clientes] que aceptan comunicaciones de marketing (/admin/customers?query=&accepts_marketing=1).",
        "color_background": {
          "label": "Historial"
        },
        "color_text": {
          "label": "Texto"
        }
      },
      "presets": {
        "email_signup": {
          "name": "Suscriptor de correo electrónico"
        }
      }
    },
    "password-header": {
      "name": "Encabezado",
      "settings": {
        "overlay_header": {
          "label": "Cabecera superpuesta"
        },
        "logo": {
          "label": "Imagen del logo"
        },
        "desktop_logo_height": {
          "label": "Altura del logotipo de escritorio"
        },
        "mobile_logo_height": {
          "label": "Altura del logotipo móvil"
        }
      }
    },
    "product-full-width": {
      "name": "Detalles de ancho completo",
      "settings": {
        "content": "Para las líneas de productos con descripciones largas, le recomendamos que coloque su descripción y sus fichas dentro de esta sección.",
        "max_width": {
          "label": "Optimizar la legibilidad",
          "info": "Aplica un ancho máximo"
        }
      },
      "blocks": {
        "description": {
          "name": "Descripción",
          "settings": {
            "is_tab": {
              "label": "Mostrar como pestaña"
            }
          }
        },
        "text": {
          "name": "Texto",
          "settings": {
            "text": {
              "label": "Texto"
            }
          }
        },
        "tab": {
          "name": "Pestaña",
          "settings": {
            "title": {
              "label": "Encabezado"
            },
            "content": {
              "label": "Contenido de la pestaña"
            },
            "page": {
              "label": "Pestaña de contenido de la página"
            }
          }
        },
        "share_on_social": {
          "name": "Compartir en las redes sociales",
          "settings": {
            "content": "Elegir a qué plataformas compartir en los ajustes globales del tema"
          }
        },
        "separator": {
          "name": "Separador"
        },
        "contact_form": {
          "name": "Formulario de contacto",
          "settings": {
            "content": "Todos los envíos se realizan a la dirección de correo electrónico del cliente de su tienda.  [Saber más](https://help.shopify.com/en/manual/using-themes/change-the-layout/add-contact-form#view-contact-form-submissions).",
            "title": {
              "label": "Encabezado"
            },
            "phone": {
              "label": "Añadir campo de número de teléfono"
            }
          }
        },
        "html": {
          "name": "HTML",
          "settings": {
            "code": {
              "label": "HTML",
              "info": "Compatible con Liquid"
            }
          }
        }
      }
    },
    "product-recommendations": {
      "name": "Recomendaciones de productos",
      "settings": {
        "show_product_recommendations": {
          "label": "Mostrar recomendaciones dinámicas",
          "info": "Las recomendaciones dinámicas cambian y mejoran con el tiempo. [Más información](https://help.shopify.com/en/themes/development/recommended-products)"
        },
        "product_recommendations_heading": {
          "label": "Encabezado"
        },
        "related_count": {
          "label": "Número de productos relacionados"
        }
      }
    },
    "promo-grid": {
      "name": "Red de promoción",
      "settings": {
        "full_width": {
          "label": "Ancho de página completo"
        },
        "gutter_size": {
          "label": "Espaciado"
        },
        "space_above": {
          "label": "Añadir el espacio superior"
        },
        "space_below": {
          "label": "Añadir el espacio inferior"
        }
      },
      "presets": {
        "promotional_grid": {
          "name": "Red de promoción"
        }
      },
      "blocks": {
        "advanced": {
          "name": "Avanzado",
          "settings": {
            "subheading": {
              "label": "Subtítulo"
            },
            "heading": {
              "label": "Encabezado"
            },
            "textarea": {
              "label": "Texto"
            },
            "cta_text1": {
              "label": "Texto del botón nº 1"
            },
            "cta_link1": {
              "label": "Enlace del botón nº 1"
            },
            "cta_text2": {
              "label": "Texto del botón nº 2"
            },
            "cta_link2": {
              "label": "Enlace del botón nº 2"
            },
            "image": {
              "label": "Imagen"
            },
            "video_url": {
              "label": "URL del vídeo"
            },
            "header_layout": "Diseño",
            "width": {
              "label": "Ancho"
            },
            "height": {
              "label": "Altura"
            },
            "text_size": {
              "label": "Tamaño del texto"
            },
            "header_alignment": "Alineación",
            "text_align": {
              "label": "Alineación de texto",
              "options": {
                "vertical-top_horizontal-left": {
                  "label": "Arriba a la izquierda"
                },
                "vertical-top_horizontal-center": {
                  "label": "Arriba en el centro"
                },
                "vertical-top_horizontal-right": {
                  "label": "Arriba a la derecha"
                },
                "vertical-center_horizontal-left": {
                  "label": "Centro izquierda"
                },
                "vertical-center_horizontal-center": {
                  "label": "Centrado"
                },
                "vertical-center_horizontal-right": {
                  "label": "Centro derecha"
                },
                "vertical-bottom_horizontal-left": {
                  "label": "Abajo a la izquierda"
                },
                "vertical-bottom_horizontal-center": {
                  "label": "Abajo en el centro"
                },
                "vertical-bottom_horizontal-right": {
                  "label": "Abajo a la derecha"
                }
              }
            },
            "focal_point": {
              "label": "Punto de enfoque de la imagen",
              "options": {
                "20_0": {
                  "label": "Arriba a la izquierda"
                },
                "top": {
                  "label": "Arriba en el centro"
                },
                "80_0": {
                  "label": "Arriba a la derecha"
                },
                "20_50": {
                  "label": "Centro izquierda"
                },
                "center": {
                  "label": "Centrado"
                },
                "80_50": {
                  "label": "Centro derecha"
                },
                "20_100": {
                  "label": "Abajo a la izquierda"
                },
                "bottom": {
                  "label": "Abajo en el centro"
                },
                "80_100": {
                  "label": "Abajo a la derecha"
                }
              }
            },
            "header_design": "Diseño",
            "color_accent": {
              "label": "Botones"
            },
            "boxed": {
              "label": "Añadir casilla"
            },
            "framed": {
              "label": "Añadir marco"
            }
          }
        },
        "banner": {
          "name": "Bandera",
          "settings": {
            "heading": {
              "label": "Encabezado"
            },
            "text": {
              "label": "Texto"
            },
            "link": {
              "label": "Enlace"
            },
            "label": {
              "label": "Vincular etiqueta"
            },
            "image": {
              "label": "Imagen"
            },
            "header_design": "Diseño",
            "color_tint": {
              "label": "Tinte"
            },
            "color_tint_opacity": {
              "label": "Cantidad"
            },
            "framed": {
              "label": "Añadir marco"
            }
          }
        },
        "image": {
          "name": "Imagen",
          "settings": {
            "image": {
              "label": "Imagen"
            },
            "link": {
              "label": "Enlace"
            },
            "width": {
              "label": "Ancho"
            }
          }
        },
        "product": {
          "name": "Producto",
          "settings": {
            "product": {
              "label": "Producto"
            },
            "subheading": {
              "label": "Subtítulo"
            },
            "heading": {
              "label": "Encabezado"
            },
            "textarea": {
              "label": "Texto"
            },
            "link_label": {
              "label": "Texto del botón"
            },
            "label": {
              "label": "Etiqueta"
            },
            "enable_price": {
              "label": "Mostrar precio"
            },
            "width": {
              "label": "Ancho"
            },
            "text_size": {
              "label": "Tamaño del texto"
            },
            "header_design": "Diseño",
            "color_tint": {
              "label": "Tinte"
            },
            "color_tint_opacity": {
              "label": "Cantidad"
            },
            "framed": {
              "label": "Añadir marco"
            }
          }
        },
        "sale_collection": {
          "name": "Colección de venta",
          "settings": {
            "sale_collection": {
              "label": "Colección de venta"
            },
            "top_text": {
              "label": "Texto superior"
            },
            "middle_text": {
              "label": "Texto central"
            },
            "bottom_text": {
              "label": "Texto inferior"
            },
            "header_layout": "Diseño",
            "width": {
              "label": "Ancho"
            },
            "header_design": "Diseño",
            "color_tint": {
              "label": "Tinte"
            },
            "color_tint_opacity": {
              "label": "Cantidad"
            },
            "boxed": {
              "label": "Añadir casilla"
            },
            "framed": {
              "label": "Añadir marco"
            }
          }
        },
        "simple": {
          "name": "Simple",
          "settings": {
            "link": {
              "label": "Enlace"
            },
            "text": {
              "label": "Texto"
            },
            "image": {
              "label": "Imagen"
            },
            "header_layout": "Diseño",
            "width": {
              "label": "Ancho"
            },
            "height": {
              "label": "Altura"
            },
            "header_design": "Diseño",
            "color_tint": {
              "label": "Tinte"
            },
            "color_tint_opacity": {
              "label": "Cantidad"
            },
            "framed": {
              "label": "Añadir marco"
            }
          }
        }
      }
    },
    "recently-viewed": {
      "name": "Vistos recientemente",
      "settings": {
        "content": "Los productos vistos recientemente solo son visibles cuando se navega fuera del editor",
        "recent_count": {
          "label": "Número de productos recientes"
        }
      }
    },
    "rich-text": {
      "name": "Texto enriquecido",
      "settings": {
        "align_text": {
          "label": "Alineación de texto",
          "options": {
            "left": {
              "label": "Izquierda"
            },
            "center": {
              "label": "Centrado"
            },
            "right": {
              "label": "Derecha"
            }
          }
        },
        "narrow_column": {
          "label": "Columna estrecha"
        },
        "divider": {
          "label": "Mostrar el divisor de sección"
        }
      },
      "blocks": {
        "heading": {
          "name": "Encabezado",
          "settings": {
            "title": {
              "label": "Encabezado"
            }
          }
        },
        "text": {
          "name": "Texto",
          "settings": {
            "enlarge_text": {
              "label": "Ampliar el texto"
            },
            "text": {
              "label": "Texto"
            }
          }
        },
        "button": {
          "name": "Botón",
          "settings": {
            "link": {
              "label": "Enlace de botón"
            },
            "link_text": {
              "label": "Texto del botón"
            }
          }
        },
        "page": {
          "name": "Página",
          "settings": {
            "page_text": {
              "label": "Página"
            }
          }
        }
      },
      "presets": {
        "rich_text": {
          "name": "Texto enriquecido"
        }
      }
    },
    "slideshow": {
      "name": "Hero (presentación de diapositivas opcional)",
      "settings": {
        "section_height": {
          "label": "Altura del escritorio",
          "options": {
            "natural": {
              "label": "Natural"
            },
            "450px": {
              "label": "450 px"
            },
            "550px": {
              "label": "550 px"
            },
            "650px": {
              "label": "650 px"
            },
            "750px": {
              "label": "750 px"
            },
            "100vh": {
              "label": "Pantalla completa"
            }
          }
        },
        "mobile_height": {
          "label": "Altura móvil",
          "options": {
            "auto": {
              "label": "Auto"
            },
            "250px": {
              "label": "250 px"
            },
            "300px": {
              "label": "300 px"
            },
            "400px": {
              "label": "400 px"
            },
            "500px": {
              "label": "500 px"
            },
            "100vh": {
              "label": "Pantalla completa"
            }
          }
        },
        "parallax_direction": {
          "label": "dirección de paralaje",
          "options": {
            "top": {
              "label": "Vertical"
            },
            "left": {
              "label": "Horizontal"
            }
          }
        },
        "parallax": {
          "label": "Habilitar el paralaje"
        },
        "style": {
          "label": "Estilo de navegación con diapositivas",
          "options": {
            "minimal": {
              "label": "Mínimo"
            },
            "arrows": {
              "label": "Flechas"
            },
            "bars": {
              "label": "Barras"
            },
            "dots": {
              "label": "Puntos"
            }
          }
        },
        "autoplay": {
          "label": "Diapositivas de cambio automático"
        },
        "autoplay_speed": {
          "label": "Cambiar las imágenes cada"
        }
      },
      "blocks": {
        "slide": {
          "name": "Diapositiva",
          "settings": {
            "top_subheading": {
              "label": "Subtítulo"
            },
            "title": {
              "label": "Encabezado"
            },
            "title_size": {
              "label": "Tamaño del texto de la cabecera"
            },
            "subheading": {
              "label": "Texto"
            },
            "link": {
              "label": "Enlace de diapositivas"
            },
            "link_text": {
              "label": "Texto del enlace de la diapositiva"
            },
            "link_2": {
              "label": "Enlace de la diapositiva 2"
            },
            "link_text_2": {
              "label": "Texto de enlace de la diapositiva 2"
            },
            "color_accent": {
              "label": "Botones"
            },
            "text_align": {
              "label": "Alineación de texto",
              "options": {
                "vertical-center_horizontal-left": {
                  "label": "Centro izquierda"
                },
                "vertical-center_horizontal-center": {
                  "label": "Centrado"
                },
                "vertical-center_horizontal-right": {
                  "label": "Centro derecha"
                },
                "vertical-bottom_horizontal-left": {
                  "label": "Abajo a la izquierda"
                },
                "vertical-bottom_horizontal-center": {
                  "label": "Abajo en el centro"
                },
                "vertical-bottom_horizontal-right": {
                  "label": "Abajo a la derecha"
                }
              }
            },
            "image": {
              "label": "Imagen"
            },
            "image_mobile": {
              "label": "Imagen móvil"
            },
            "overlay_opacity": {
              "label": "Protección del texto",
              "info": "Oscurece su imagen para asegurar que su texto sea legible"
            },
            "focal_point": {
              "label": "Punto de enfoque de la imagen",
              "info": "Utilizar para mantener a la vista el sujeto de su foto.",
              "options": {
                "20_0": {
                  "label": "Arriba a la izquierda"
                },
                "top_center": {
                  "label": "Arriba en el centro"
                },
                "80_0": {
                  "label": "Arriba a la derecha"
                },
                "20_50": {
                  "label": "Izquierda"
                },
                "center_center": {
                  "label": "Centrado"
                },
                "80_50": {
                  "label": "Derecha"
                },
                "20_100": {
                  "label": "Abajo a la izquierda"
                },
                "bottom_center": {
                  "label": "Abajo en el centro"
                },
                "80_100": {
                  "label": "Abajo a la derecha"
                }
              }
            }
          }
        }
      },
      "presets": {
        "hero_optional_slideshow": {
          "name": "Hero (presentación de diapositivas opcional)"
        }
      }
    },
    "testimonials": {
      "name": "Opiniones",
      "settings": {
        "title": {
          "label": "Encabezado"
        },
        "align_text": {
          "label": "Alineación de texto",
          "options": {
            "left": {
              "label": "Izquierda"
            },
            "center": {
              "label": "Centrado"
            },
            "right": {
              "label": "Derecha"
            }
          }
        },
        "round_images": {
          "label": "Imágenes circulares",
          "info": "Necesita imágenes cuadradas"
        },
        "color_background": {
          "label": "Historial"
        },
        "color_text": {
          "label": "Texto"
        }
      },
      "blocks": {
        "testimonial": {
          "name": "Opinión",
          "settings": {
            "icon": {
              "label": "Ícono",
              "options": {
                "none": {
                  "label": "Ninguno"
                },
                "quote": {
                  "label": "Presupuesto"
                },
                "5-stars": {
                  "label": "5 estrellas"
                },
                "4-stars": {
                  "label": "4 estrellas"
                },
                "3-stars": {
                  "label": "3 estrellas"
                },
                "2-stars": {
                  "label": "2 estrellas"
                },
                "1-star": {
                  "label": "1 estrella"
                }
              }
            },
            "testimonial": {
              "label": "Texto"
            },
            "image": {
              "label": "Imagen del autor"
            },
            "author": {
              "label": "Autor"
            },
            "author_info": {
              "label": "Información del autor"
            }
          }
        }
      },
      "presets": {
        "testimonials": {
          "name": "Opiniones"
        }
      }
    },
    "text-and-image": {
      "name": "Imagen con texto",
      "settings": {
        "image": {
          "label": "Imagen"
        },
        "image2": {
          "label": "Imagen 2"
        },
        "image_width": {
          "label": "Ancho de imagen"
        },
        "subtitle": {
          "label": "Subtítulo"
        },
        "title": {
          "label": "Encabezado"
        },
        "text": {
          "label": "Texto"
        },
        "button_label": {
          "label": "Etiqueta de botón"
        },
        "button_link": {
          "label": "Enlace de botón"
        },
        "button_style": {
          "label": "Estilo botón",
          "options": {
            "primary": {
              "label": "Primario"
            },
            "secondary": {
              "label": "Secundario"
            }
          }
        },
        "align_text": {
          "label": "Alineación de texto",
          "options": {
            "left": {
              "label": "Izquierda"
            },
            "center": {
              "label": "Centrado"
            },
            "right": {
              "label": "Derecha"
            }
          }
        },
        "layout": {
          "label": "Diseño",
          "options": {
            "left": {
              "label": "Imagen a la izquierda"
            },
            "right": {
              "label": "Imagen de la derecha"
            }
          }
        },
        "divider": {
          "label": "Mostrar el divisor de sección"
        }
      },
      "presets": {
        "image_with_text": {
          "name": "Imagen con texto"
        }
      }
    },
    "text-columns": {
      "name": "Columnas de texto con imágenes",
      "settings": {
        "title": {
          "label": "Encabezado"
        },
        "align_text": {
          "label": "Alineación",
          "options": {
            "left": {
              "label": "Izquierda"
            },
            "center": {
              "label": "Centrado"
            },
            "right": {
              "label": "Derecha"
            }
          }
        },
        "divider": {
          "label": "Mostrar el divisor de sección"
        }
      },
      "blocks": {
        "column": {
          "name": "Columna",
          "settings": {
            "enable_image": {
              "label": "Mostrar imagen"
            },
            "image": {
              "label": "Imagen"
            },
            "image_width": {
              "label": "Ancho de imagen"
            },
            "title": {
              "label": "Encabezado"
            },
            "text": {
              "label": "Texto"
            },
            "button_label": {
              "label": "Etiqueta de botón"
            },
            "button_link": {
              "label": "Enlace"
            }
          }
        }
      },
      "presets": {
        "text_columns_with_images": {
          "name": "Columnas de texto con imágenes"
        }
      }
    },
    "age-verification-popup": {
      "name": "Ventana emergente de verificación de edad",
      "settings": {
        "enable_age_verification_popup": {
          "label": "Mostrar ventana emergente de verificación de edad"
        },
        "enable_test_mode": {
          "label": "Habilitar modo de prueba",
          "info": "Obliga a que la verificación se muestre en cada actualización y solo debe usarse para editar la ventana emergente. Asegúrese de que el 'Modo de prueba' esté deshabilitado al iniciar su tienda."
        },
        "header_background_image": "Imagen de fondo",
        "image": {
          "label": "Imagen",
          "info": "2000 x 800px recomendada"
        },
        "blur_image": {
          "label": "Desenfocar la imagen"
        },
        "header_age_verification_question": "Pregunta de verificación de edad",
        "heading": {
          "label": "Encabezado"
        },
        "text": {
          "label": "Pregunta de verificación de edad"
        },
        "decline_button_label": {
          "label": "Texto del botón Rechazar"
        },
        "approve_button_label": {
          "label": "Aprobar el texto del botón"
        },
        "header_declined": "Declined",
        "content": "Este contenido se mostrará si el usuario no cumple con los requisitos de verificación.",
        "decline_heading": {
          "label": "Encabezado"
        },
        "decline_text": {
          "label": "Texto"
        },
        "return_button_label": {
          "label": "Texto del botón de retorno"
        }
      }
    },
    "countdown": {
      "name": "Cuenta regresiva",
      "settings": {
        "layout": {
          "label": "Disposición de la sección",
          "options": {
            "banner": {
              "label": "Bandera"
            },
            "hero": {
              "label": "Hero"
            }
          }
        },
        "full_width": {
          "label": "Habilitar ancho completo"
        },
        "header_colors": "Colores",
        "text_color": {
          "label": "Color de texto"
        },
        "background_color": {
          "label": "Color de fondo",
          "info": "Se utiliza cuando no se selecciona ninguna imagen de fondo."
        },
        "header_background_image": "Imagen de fondo",
        "background_image": {
          "label": "Imagen de fondo"
        },
        "overlay_color": {
          "label": "Cubrir"
        },
        "overlay_opacity": {
          "label": "Opacidad superpuesta"
        },
        "mobile_image": {
          "label": "Imagen móvil"
        },
        "focal_point": {
          "label": "Punto de enfoque de la imagen",
          "options": {
            "20_0": {
              "label": "Arriba a la izquierda"
            },
            "top": {
              "label": "Arriba"
            },
            "80_0": {
              "label": "Arriba a la derecha"
            },
            "20_50": {
              "label": "Izquierda"
            },
            "center": {
              "label": "Centrada"
            },
            "80_50": {
              "label": "Derecha"
            },
            "20_100": {
              "label": "Abajo a la izquierda"
            },
            "bottom": {
              "label": "Abajo"
            },
            "80_100": {
              "label": "Abajo a la derecha"
            }
          }
        },
        "mobile_image_focal_point": {
          "label": "Punto de enfoque de imagen móvil",
          "options": {
            "20_0": {
              "label": "Arriba a la izquierda"
            },
            "top": {
              "label": "Arriba"
            },
            "80_0": {
              "label": "Arriba a la derecha"
            },
            "20_50": {
              "label": "Izquierda"
            },
            "center": {
              "label": "Centrada"
            },
            "80_50": {
              "label": "Derecha"
            },
            "20_100": {
              "label": "Abajo a la izquierda"
            },
            "bottom": {
              "label": "Abajo"
            },
            "80_100": {
              "label": "Abajo a la derecha"
            }
          }
        }
      },
      "blocks": {
        "timer": {
          "name": "Temporizador",
          "settings": {
            "year": {
              "label": "Año"
            },
            "month": {
              "label": "Mes",
              "options": {
                "01": {
                  "label": "Enero"
                },
                "02": {
                  "label": "Febrero"
                },
                "03": {
                  "label": "Marzo"
                },
                "04": {
                  "label": "Abril"
                },
                "05": {
                  "label": "Mayo"
                },
                "06": {
                  "label": "Junio"
                },
                "07": {
                  "label": "Julio"
                },
                "08": {
                  "label": "Agosto"
                },
                "09": {
                  "label": "Septiembre"
                },
                "10": {
                  "label": "Octubre"
                },
                "11": {
                  "label": "Octubre"
                },
                "12": {
                  "label": "Diciembre"
                }
              }
            },
            "day": {
              "label": "Día"
            },
            "hour": {
              "label": "Hora",
              "options": {
                "00": {
                  "label": "00:00"
                },
                "01": {
                  "label": "01:00"
                },
                "02": {
                  "label": "02:00"
                },
                "03": {
                  "label": "03:00"
                },
                "04": {
                  "label": "04:00"
                },
                "05": {
                  "label": "05:00"
                },
                "06": {
                  "label": "06:00"
                },
                "07": {
                  "label": "07:00"
                },
                "08": {
                  "label": "08:00"
                },
                "09": {
                  "label": "09:00"
                },
                "10": {
                  "label": "10:00"
                },
                "11": {
                  "label": "11:00"
                },
                "12": {
                  "label": "12:00"
                },
                "13": {
                  "label": "13:00"
                },
                "14": {
                  "label": "14:00"
                },
                "15": {
                  "label": "15:00"
                },
                "16": {
                  "label": "16:00"
                },
                "17": {
                  "label": "17:00"
                },
                "18": {
                  "label": "18:00"
                },
                "19": {
                  "label": "19:00"
                },
                "20": {
                  "label": "20:00"
                },
                "21": {
                  "label": "21:00"
                },
                "22": {
                  "label": "22:00"
                },
                "23": {
                  "label": "23:00"
                }
              }
            },
            "minute": {
              "label": "Minuto"
            },
            "hide_timer": {
              "label": "Ocultar el temporizador en completo"
            },
            "text": {
              "label": "Mensaje de temporizador completo"
            }
          }
        },
        "content": {
          "name": "Contenido",
          "settings": {
            "heading": {
              "label": "Encabezado"
            },
            "heading_size": {
              "label": "Tamaño del encabezado",
              "options": {
                "small": {
                  "label": "Pequeña"
                },
                "medium": {
                  "label": "Mediana"
                },
                "large": {
                  "label": "Grande"
                }
              }
            },
            "text": {
              "label": "Texto"
            },
            "content_alignment": {
              "label": "Alineación de contenido",
              "options": {
                "left": {
                  "label": "Izquierda"
                },
                "center": {
                  "label": "Centrado"
                },
                "right": {
                  "label": "Derecha"
                }
              }
            }
          }
        },
        "button": {
          "name": "Botón",
          "settings": {
            "button_link": {
              "label": "Enlace de botón"
            },
            "button": {
              "label": "Etiqueta de botón"
            },
            "button_style": {
              "label": "Estilo botón",
              "options": {
                "secondary": {
                  "label": "Contorno"
                },
                "solid": {
                  "label": "Sólido"
                }
              }
            }
          }
        }
      },
      "presets": {
        "countdown": {
          "name": "Cuenta regresiva"
        }
      }
    }
  },
  "settings_schema": {
    "colors": {
      "name": "Colores",
      "settings": {
        "header_general": "General",
        "color_body_bg": {
          "label": "Historial"
        },
        "color_body_text": {
          "label": "Texto"
        },
        "color_price": {
          "label": "Precio"
        },
        "color_savings_text": {
          "label": "Guardar precio"
        },
        "color_borders": {
          "label": "Líneas y bordes"
        },
        "color_button": {
          "label": "Botones"
        },
        "color_button_text": {
          "label": "Texto del botón"
        },
        "color_sale_tag": {
          "label": "Etiquetas de venta"
        },
        "color_sale_tag_text": {
          "label": "Texto de la etiqueta de venta"
        },
        "color_cart_dot": {
          "label": "Punto de la cesta"
        },
        "color_small_image_bg": {
          "label": "Fondo de la imagen"
        },
        "color_large_image_bg": {
          "label": "Fondo de la sección de imágenes"
        },
        "header_header": "Encabezado",
        "color_header": {
          "label": "Historial"
        },
        "color_header_text": {
          "label": "Texto"
        },
        "color_announcement": {
          "label": "Barra de anuncios"
        },
        "color_announcement_text": {
          "label": "Texto de la barra de anuncio"
        },
        "header_footer": "Pie de página",
        "color_footer": {
          "label": "Historial"
        },
        "color_footer_text": {
          "label": "Texto"
        },
        "header_menu_and_cart_drawers": "Menú y cesta de la compra",
        "color_drawer_background": {
          "label": "Historial"
        },
        "color_drawer_text": {
          "label": "Texto"
        },
        "color_drawer_border": {
          "label": "Líneas y bordes"
        },
        "color_drawer_button": {
          "label": "Botones"
        },
        "color_drawer_button_text": {
          "label": "Texto del botón"
        },
        "color_modal_overlays": {
          "label": "Superposiciones"
        },
        "header_image_treatment": "Tratamiento de la imagen",
        "content": "Se utiliza en presentaciones de diapositivas, héroes de vídeo, rejilla de promoción y cabeceras de colecciones",
        "color_image_text": {
          "label": "Texto"
        },
        "color_image_overlay": {
          "label": "Superposición"
        },
        "color_image_overlay_opacity": {
          "label": "Opacidad de la superposición"
        },
        "color_image_overlay_text_shadow": {
          "label": "Cantidad de sombra de texto"
        }
      }
    },
    "typography": {
      "name": "Tipografía",
      "settings": {
        "header_headings": "Títulos",
        "type_header_font_family": {
          "label": "Fuente"
        },
        "type_header_spacing": {
          "label": "Espacio entre letras"
        },
        "type_header_base_size": {
          "label": "Tamaño de la base"
        },
        "type_header_line_height": {
          "label": "Altura de línea"
        },
        "type_header_capitalize": {
          "label": "Escribir en mayúsculas"
        },
        "type_headers_align_text": {
          "label": "Cabezales centrales"
        },
        "header_body_text": "Texto del cuerpo",
        "type_base_font_family": {
          "label": "Fuente"
        },
        "type_base_spacing": {
          "label": "Espacio entre letras"
        },
        "type_base_size": {
          "label": "Tamaño de la base"
        },
        "type_base_line_height": {
          "label": "Altura de línea"
        },
        "type_body_align_text": {
          "label": "Centrar el texto"
        },
        "header_extras": "Extras",
        "type_navigation_style": {
          "label": "Fuente de navegación",
          "options": {
            "body": {
              "label": "Cuerpo"
            },
            "heading": {
              "label": "Encabezado"
            }
          }
        },
        "type_navigation_size": {
          "label": "Tamaño de la navegación"
        },
        "type_navigation_capitalize": {
          "label": "Navegar con mayúsculas"
        },
        "type_product_style": {
          "label": "Fuente de la cesta de productos",
          "options": {
            "body": {
              "label": "Cuerpo"
            },
            "heading": {
              "label": "Encabezado"
            }
          }
        },
        "type_product_capitalize": {
          "label": "Poner en mayúsculas la cesta de productos"
        },
        "type_collection_font": {
          "label": "Fuente de los mosaicos de la colección",
          "options": {
            "body": {
              "label": "Cuerpo"
            },
            "heading": {
              "label": "Encabezado"
            }
          }
        },
        "type_collection_size": {
          "label": "Tamaño de los mosaicos de la colección"
        },
        "header_buttons": "Botones",
        "button_style": {
          "label": "Estilo",
          "options": {
            "square": {
              "label": "Cuadrado"
            },
            "round-slight": {
              "label": "Ligeramente redondo"
            },
            "round": {
              "label": "Vuelta "
            },
            "angled": {
              "label": "Angular"
            }
          }
        },
        "header_icons": "Íconos",
        "icon_weight": {
          "label": "Peso",
          "options": {
            "2px": {
              "label": "Ultraligero"
            },
            "3px": {
              "label": "Ligero"
            },
            "4px": {
              "label": "Regular"
            },
            "5px": {
              "label": "Seminegrita"
            },
            "6px": {
              "label": "Negrita"
            },
            "7px": {
              "label": "En negrita"
            }
          }
        },
        "icon_linecaps": {
          "label": "Bordes",
          "options": {
            "miter": {
              "label": "Nítido"
            },
            "round": {
              "label": "Vuelta "
            }
          }
        }
      }
    },
    "products": {
      "name": "Productos",
      "settings": {
        "product_save_amount": {
          "label": "Mostrar la cantidad guardada"
        },
        "product_save_type": {
          "label": "Estilo de visualización del ahorro",
          "options": {
            "dollar": {
              "label": "Dólar"
            },
            "percent": {
              "label": "Porcentaje"
            }
          }
        },
        "vendor_enable": {
          "label": "Mostrar proveedor"
        }
      }
    },
    "product_tiles": {
      "name": "Mosaicos de productos",
      "settings": {
        "quick_shop_enable": {
          "label": "Habilitar la función de compra rápida"
        },
        "quick_shop_text": {
          "label": "Texto del botón de compra rápida"
        },
        "product_grid_image_size": {
          "label": "Forzar el tamaño de la imagen",
          "options": {
            "natural": {
              "label": "Natural"
            },
            "square": {
              "label": "Cuadrado (1:1)"
            },
            "landscape": {
              "label": "Paisaje (4:3)"
            },
            "portrait": {
              "label": "Retrato (2:3)"
            }
          }
        },
        "product_grid_image_fill": {
          "label": "Ampliar la imagen para llenar el espacio",
          "info": "No tiene efecto cuando el tamaño de la imagen de la cesta se ajusta a \"Natural\"."
        },
        "product_hover_image": {
          "label": "Pasar el ratón para ver la segunda imagen"
        },
        "header_color_swatches": "Muestras de color",
        "enable_swatches": {
          "label": "Activar las muestras de color"
        },
        "swatch_style": {
          "label": "Estilo de la muestra",
          "options": {
            "round": {
              "label": "Vuelta "
            },
            "square": {
              "label": "Cuadrado"
            }
          }
        },
        "header_product_reviews": "Reseñas de productos",
        "content": "Añada reseñas habilitando la configuración de abajo e instalando la [aplicación de reseñas de productos de Shopify](https://apps.shopify.com/product-reviews) y siguiendo nuestra [guía de configuración](https://archetypethemes.co/blogs/support/installing-shopifys-product-reviews-app)",
        "enable_product_reviews": {
          "label": "Activar revisiones de productos"
        }
      }
    },
    "collection_tiles": {
      "name": "Mosaicos de la colección",
      "settings": {
        "header_collection_tiles": "Mosaicos de la colección",
        "collection_grid_style": {
          "label": "Estilo",
          "options": {
            "overlaid": {
              "label": "Superposición"
            },
            "overlaid-box": {
              "label": "Superposición de la caja"
            },
            "below": {
              "label": "Abajo"
            }
          }
        },
        "collection_grid_shape": {
          "label": "Forma",
          "options": {
            "square": {
              "label": "Cuadrado (1:1)"
            },
            "landscape": {
              "label": "Paisaje (4:3)"
            },
            "portrait": {
              "label": "Retrato (2:3)"
            }
          }
        },
        "collection_grid_image": {
          "label": "Imagen",
          "options": {
            "product": {
              "label": "Primer producto"
            },
            "collection": {
              "label": "Imagen de la colección"
            }
          }
        },
        "collection_grid_text_align": {
          "label": "Alineación de texto",
          "options": {
            "top-left": {
              "label": "Arriba a la izquierda"
            },
            "top-center": {
              "label": "Arriba en el centro"
            },
            "top-right": {
              "label": "Arriba a la derecha"
            },
            "left": {
              "label": "Izquierda"
            },
            "center": {
              "label": "Centrado"
            },
            "right": {
              "label": "Derecha"
            },
            "bottom-left": {
              "label": "Abajo a la izquierda"
            },
            "bottom-center": {
              "label": "Abajo en el centro"
            },
            "bottom-right": {
              "label": "Abajo a la derecha"
            }
          }
        },
        "collection_grid_tint": {
          "label": "Tinte"
        },
        "collection_grid_opacity": {
          "label": "Opacidad del matiz"
        },
        "collection_grid_gutter": {
          "label": "Añadir espacio"
        }
      }
    },
    "cart": {
      "name": "Carrito",
      "settings": {
        "header_cart": "Carrito",
        "cart_type": {
          "label": "Tipo de carrito",
          "options": {
            "page": {
              "label": "Página"
            },
            "drawer": {
              "label": "Cajón"
            }
          }
        },
        "cart_icon": {
          "label": "Icono del carrito",
          "options": {
            "bag": {
              "label": "Bolsa"
            },
            "bag-minimal": {
              "label": "Bolsa mínima"
            },
            "cart": {
              "label": "Carrito"
            }
          }
        },
        "cart_additional_buttons": {
          "label": "Activar botones de pago adicionales",
          "info": "Los botones pueden aparecer en la página del carrito o en la página de pago, pero no en ambas."
        },
        "cart_notes_enable": {
          "label": "Activar las notas de pedido"
        },
        "cart_terms_conditions_enable": {
          "label": "Activar la casilla de términos y condiciones"
        },
        "cart_terms_conditions_page": {
          "label": "Página de términos y condiciones"
        }
      }
    },
    "social_media": {
      "name": "Redes sociales",
      "settings": {
        "header_accounts": "Cuentas",
        "social_facebook_link": {
          "label": "Facebook",
          "info": "https://www.facebook.com/shopify"
        },
        "social_twitter_link": {
          "label": "Twitter",
          "info": "https://twitter.com/shopify"
        },
        "social_pinterest_link": {
          "label": "Pinterest",
          "info": "https://www.pinterest.com/shopify"
        },
        "social_instagram_link": {
          "label": "Instagram",
          "info": "https://instagram.com/shopify"
        },
        "social_snapchat_link": {
          "label": "Snapchat",
          "info": "https://www.snapchat.com/add/shopify"
        },
        "social_tiktok_link": {
          "label": "TikTok",
          "info": "https://www.tiktok.com/@shopify"
        },
        "social_tumblr_link": {
          "label": "Tumblr",
          "info": "http://shopify.tumblr.com"
        },
        "social_linkedin_link": {
          "label": "LinkedIn",
          "info": "https://www.linkedin.com/in/shopify"
        },
        "social_youtube_link": {
          "label": "YouTube",
          "info": "https://www.youtube.com/user/shopify"
        },
        "social_vimeo_link": {
          "label": "Vimeo",
          "info": "https://vimeo.com/shopify"
        },
        "header_sharing_options": "Opciones para compartir",
        "share_facebook": {
          "label": "Compartir en Facebook"
        },
        "share_twitter": {
          "label": "Compartir en Twitter"
        },
        "share_pinterest": {
          "label": "Guardar en Pinterest"
        }
      }
    },
    "favicon": {
      "name": "Favicon",
      "settings": {
        "favicon": {
          "label": "Imagen de favicon",
          "info": "Se reducirá a 32 x 32px"
        }
      }
    },
    "search": {
      "name": "Búsqueda",
      "settings": {
        "search_enable": {
          "label": "Activar la búsqueda"
        },
        "search_type": {
          "label": "Resultados de búsqueda",
          "options": {
            "product": {
              "label": "Solo productos"
            },
            "product_page": {
              "label": "Productos y páginas"
            },
            "product_article": {
              "label": "Productos y artículos"
            },
            "product_article_page": {
              "label": "Productos, artículos y páginas"
            },
            "product_article_page_collection": {
              "label": "Todo el contenido"
            }
          }
        },
        "header_predictive_search": "Búsqueda predictiva",
        "predictive_search_enabled": {
          "label": "Activar la búsqueda predictiva",
          "info": "Resultados de la búsqueda en directo. No está disponible en todos los idiomas. [Más información](https://help.shopify.com/en/themes/development/search/predictive-search#general-requirements-and-limitations)"
        },
        "predictive_search_show_vendor": {
          "label": "Mostrar proveedor"
        },
        "predictive_search_show_price": {
          "label": "Mostrar precio"
        },
        "predictive_image_size": {
          "label": "Relación de aspecto de la imagen del producto",
          "options": {
            "square": {
              "label": "Cuadrado (1:1)"
            },
            "landscape": {
              "label": "Paisaje (4:3)"
            },
            "portrait": {
              "label": "Retrato (2:3)"
            }
          }
        }
      }
    },
    "extras": {
      "name": "Extras",
      "settings": {
        "show_breadcrumbs": {
          "label": "Mostrar las migas de pan"
        },
        "show_breadcrumbs_collection_link": {
          "label": "Mostrar la página de colecciones en la lista de migas de pan"
        },
        "text_direction": {
          "label": "Dirección del texto",
          "options": {
            "ltr": {
              "label": "De izquierda a derecha"
            },
            "rtl": {
              "label": "De derecha a izquierda"
            }
          }
        },
        "disable_animations": {
          "label": "Desactivar las animaciones del zoom"
        }
      }
    }
  },
  "locales": {
    "general": {
      "404": {
        "title": "404 página no encontrada",
        "subtext_html": "<p>La página que buscaba no existe. </p><p><a href='{{ url }}'>Continuar comprando</a></p>"
      },
      "accessibility": {
        "skip_to_content": "Ir directamente al contenido",
        "close_modal": "Cerrar (esc)",
        "close": "Cerrar",
        "learn_more": "Más información"
      },
      "meta": {
        "tags": "Etiquetado \"{{ tags }}\"",
        "page": "Página {{ page }}"
      },
      "pagination": {
        "previous": "Anterior",
        "next": "Siguiente"
      },
      "password_page": {
        "login_form_heading": "Entrar en la tienda con la contraseña",
        "login_form_password_label": "Contraseña",
        "login_form_password_placeholder": "Tu contraseña",
        "login_form_submit": "Ingresar",
        "signup_form_email_label": "Correo electrónico",
        "signup_form_success": "Le enviaremos un correo electrónico antes de la apertura.",
        "admin_link_html": "¿Propietario de la tienda? <a href=\"/admin\" class=\"text-link\">Entrar aquí</a>",
        "password_link": "Contraseña",
        "powered_by_shopify_html": "Esta tienda contará con tecnología Shopify {{ shopify }}"
      },
      "breadcrumbs": {
        "home": "Inicio",
        "home_link_title": "Volver a la página principal"
      },
      "social": {
        "share_on_facebook": "Compartir",
        "share_on_twitter": "Tweet",
        "share_on_pinterest": "Fijar",
        "alt_text": {
          "share_on_facebook": "Compartir en Facebook",
          "share_on_twitter": "Compartir en Twitter",
          "share_on_pinterest": "Guardar en Pinterest"
        }
      },
      "newsletter_form": {
        "newsletter_email": "Introduzca su correo electrónico",
        "newsletter_confirmation": "Gracias por suscribirte",
        "submit": "Suscribirse"
      },
      "search": {
        "view_more": "Ver más",
        "collections": "Colecciones:",
        "pages": "Páginas:",
        "articles": "Artículos:",
        "no_results_html": "Su búsqueda de \"{{ terms }}\" no ha dado ningún resultado.",
        "results_for_html": "Su búsqueda de \"{{ terms }}\" revela lo siguiente:",
        "title": "Búsqueda",
        "placeholder": "Buscar en nuestra tienda",
        "submit": "Búsqueda",
        "result_count": {
          "one": "{{ count }} resultado",
          "other": "{{ count }} resultados"
        }
      },
      "drawers": {
        "navigation": "Navegación del sitio",
        "close_menu": "Cerrar el menú",
        "expand_submenu": "Ampliar el submenú",
        "collapse_submenu": "Contraer el submenú"
      },
      "currency": {
        "dropdown_label": "Moneda"
      },
      "language": {
        "dropdown_label": "Idioma"
      }
    },
    "sections": {
      "map": {
        "get_directions": "Obtener direcciones",
        "address_error": "Error al buscar esa dirección",
        "address_no_results": "No hay resultados para esa dirección",
        "address_query_limit_html": "Ha superado el límite de uso de la API de Google. Considere la posibilidad de actualizar a un <a href=\"https://developers.google.com/maps/premium/usage-limits\">Plan Premium</a>.",
        "auth_error_html": "Ha habido un problema al autentificar su cuenta de Google Maps. Cree y habilite los permisos de <a href=\"https://developers.google.com/maps/documentation/javascript/get-api-key\">Apriencia de JavaScript</a> y <a href=\"https://developers.google.com/maps/documentation/geocoding/get-api-key\">Apriencia de geocodificación</a> de su app."
      },
      "slideshow": {
        "play_slideshow": "Reproducir la presentación",
        "pause_slideshow": "Pausar la presentación"
      }
    },
    "blogs": {
      "article": {
        "view_all": "Ver todo",
        "tags": "Etiquetas",
        "read_more": "Seguir leyendo",
        "back_to_blog": "Volver a {{ title }}"
      },
      "comments": {
        "title": "Deja un comentario",
        "name": "Nombre",
        "email": "Correo electrónico",
        "message": "Mensaje",
        "post": "Publicar comentario",
        "moderated": "Tener en cuenta que los comentarios deben ser aprobados antes de ser publicados",
        "success_moderated": "Tu comentario se publicó correctamente. Lo publicaremos en breve, cuando se modere nuestro blog.",
        "success": "Tu comentario se publicó correctamente. ¡Gracias!",
        "with_count": {
          "one": "{{ count }} comentario",
          "other": "{{ count }} comentarios"
        }
      }
    },
    "cart": {
      "general": {
        "title": "Carrito",
        "remove": "Eliminar",
        "note": "Nota de pedido",
        "subtotal": "Subtotal",
        "discounts": "Descuentos",
        "shipping_at_checkout": "Los gastos de envío, los impuestos y los códigos de descuento se calculan en el momento de la compra.",
        "update": "Carrito de actualización",
        "checkout": "Pagar pedido",
        "empty": "Su carrito está actualmente vacío.  ",
        "continue_browsing_html": "<a href='{{ url }}'>Continuar comprando</a>",
        "close_cart": "Cerrar carrito",
        "reduce_quantity": "Reducir la cantidad de artículos en uno",
        "increase_quantity": "Aumentar la cantidad de artículos en uno",
        "terms": "Estoy de acuerdo con los términos y condiciones",
        "terms_html": "Estoy de acuerdo con los <a href='{{ url }}' target='_blank'>términos y condiciones</a>",
        "terms_confirm": "Debe estar de acuerdo con los términos y condiciones de venta para comprobarlo"
      },
      "label": {
        "price": "Precio",
        "quantity": "Cantidad",
        "total": "Total"
      }
    },
    "collections": {
      "general": {
        "catalog_title": "Catálogo",
        "all_of_collection": "Ver todo",
        "view_all_products_html": "Ver los {{ count }} productos<br>",
        "see_more": "Mostrar más",
        "see_less": "Mostrar menos",
        "no_matches": "Lo sentimos, no hay productos en esta colección.",
        "items_with_count": {
          "one": "{{ count }} producto",
          "other": "{{ count }} productos"
        }
      },
      "sorting": {
        "title": "Ordenar"
      },
      "filters": {
        "title_tags": "Filtrar",
        "all_tags": "Todos los productos",
        "categories_title": "Categorías"
      }
    },
    "contact": {
      "form": {
        "name": "Nombre",
        "email": "Correo electrónico",
        "phone": "Número de teléfono",
        "message": "Mensaje",
        "send": "Enviar",
        "post_success": "Gracias por contactarnos. Te responderemos lo antes posible."
      }
    },
    "customer": {
      "account": {
        "title": "Mi cuenta",
        "details": "Detalles de la cuenta",
        "view_addresses": "Ver direcciones",
        "return": "Volver a la cuenta"
      },
      "activate_account": {
        "title": "Activar cuenta",
        "subtext": "Crea tu contraseña para activar tu cuenta.",
        "password": "Contraseña",
        "password_confirm": "Confirmar contraseña",
        "submit": "Activar cuenta",
        "cancel": "Rechazar invitación"
      },
      "addresses": {
        "title": "Direcciones",
        "default": "Predeterminada",
        "add_new": "Agregar dirección",
        "edit_address": "Editar dirección",
        "first_name": "Nombre",
        "last_name": "Apellido",
        "company": "Empresa",
        "address1": "Dirección1",
        "address2": "Dirección2",
        "city": "Ciudad",
        "country": "País",
        "province": "Provincia",
        "zip": "Código postal",
        "phone": "Teléfono",
        "set_default": "Establecer como dirección predeterminada",
        "add": "Agregar dirección",
        "update": "Actualizar dirección",
        "cancel": "Cancelar",
        "edit": "Editar",
        "delete": "Eliminar",
        "delete_confirm": "¿Está seguro/a de que deseas eliminar esta dirección?"
      },
      "login": {
        "title": "Inicio de sesión",
        "email": "Correo electrónico",
        "password": "Contraseña",
        "forgot_password": "¿Ha olvidado la contraseña?",
        "sign_in": "Registrar",
        "cancel": "Volver a la tienda",
        "guest_title": "Continuar como invitado",
        "guest_continue": "Continuar"
      },
      "orders": {
        "title": "Historial de pedidos",
        "order_number": "Pedido",
        "date": "Fecha",
        "payment_status": "Estado del pago",
        "fulfillment_status": "Estado de preparación del pedido",
        "total": "Total",
        "none": "Aún no has realizado ningún pedido."
      },
      "order": {
        "title": "Pedido {{ name }}",
        "date_html": "Realizado el {{ date }}",
        "cancelled_html": "Pedido cancelado el {{ date }}",
        "cancelled_reason": "Motivo: {{ reason }}",
        "billing_address": "Dirección de facturación",
        "payment_status": "Estado del pago",
        "shipping_address": "Dirección de envío",
        "fulfillment_status": "Estado de preparación del pedido",
        "discount": "Descuento",
        "shipping": "Envío",
        "tax": "Impuesto",
        "product": "Producto",
        "sku": "SKU",
        "price": "Precio",
        "quantity": "Cantidad",
        "total": "Total",
        "fulfilled_at_html": "Preparado el {{ date }}",
        "subtotal": "Subtotal"
      },
      "recover_password": {
        "title": "Restablecer tu contraseña",
        "email": "Correo electrónico",
        "submit": "Enviar",
        "cancel": "Cancelar",
        "subtext": "Le enviaremos un correo electrónico para restablecer su contraseña.",
        "success": "Te hemos enviado un correo electrónico con un enlace para actualizar tu contraseña."
      },
      "reset_password": {
        "title": "Restablecer contraseña de cuenta",
        "subtext": "Ingresa una nueva contraseña para {{ email }}",
        "password": "Contraseña",
        "password_confirm": "Confirmar contraseña",
        "submit": "Restablecer contraseña"
      },
      "register": {
        "title": "Crear una cuenta",
        "first_name": "Nombre",
        "last_name": "Apellido",
        "email": "Correo electrónico",
        "password": "Contraseña",
        "submit": "Crear",
        "cancel": "Volver a la tienda"
      }
    },
    "home_page": {
      "onboarding": {
        "product_title": "Ejemplo de producto",
        "product_description": "Esta zona se utiliza para describir los detalles de su producto. Informe a los clientes sobre el aspecto, el tacto y el estilo de su producto. Añada detalles sobre el color, los materiales utilizados, el tamaño y el lugar de fabricación.",
        "collection_title": "Ejemplo de colección",
        "no_content": "Esta sección no incluye actualmente ningún contenido. Añada contenido a esta sección utilizando la barra lateral."
      }
    },
    "layout": {
      "cart": {
        "title": "Carrito"
      },
      "customer": {
        "account": "Cuenta",
        "log_out": "Cerrar sesión",
        "log_in": "Iniciar sesión",
        "create_account": "Crear cuenta"
      },
      "footer": {
        "social_platform": "{{ name }} en {{ platform}}"
      }
    },
    "products": {
      "general": {
        "color_swatch_trigger": "Color",
        "size_trigger": "Tamaño",
        "size_chart": "Cuadro de tallas",
        "save_html": "Guardar {{ saved_amount }}",
        "collection_return": "Volver a {{ collection }}",
        "next_product": "Siguiente: {{ title }}",
        "sale": "Oferta",
        "sale_price": "Precio de oferta",
        "regular_price": "Precio habitual",
        "from_text_html": "de {{ price }}",
        "recent_products": "Vistos recientemente",
        "reviews": "Reseñas"
      },
      "product": {
        "description": "Descripción",
        "in_stock_label": "En stock, listo para enviar",
        "stock_label": {
          "one": "Pocas existencias, {{ count }} artículo restante",
          "other": "Pocas existencias, {{ count }} artículos restantes"
        },
        "sold_out": "Agotado",
        "unavailable": "No disponible",
        "quantity": "Cantidad",
        "add_to_cart": "Agregar al carrito",
        "preorder": "Pedir por adelantado",
        "include_taxes": "Impuesto incluido.",
        "shipping_policy_html": "<a href='{{ link }}'>Envío</a> calculado en la caja.",
        "will_not_ship_until": "Listo para enviar {{ date }}",
        "will_be_in_stock_after": "De nuevo en existencias {{ date }}",
        "waiting_for_stock": "Inventario en marcha",
        "view_in_space": "Ver en tu espacio",
        "view_in_space_label": "Ver en tu espacio, carga el artículo en la ventana de realidad aumentada"
      }
    },
    "store_availability": {
      "general": {
        "view_store_info": "Ver información de la tienda",
        "check_other_stores": "Verificar disponibilidad en otras tiendas",
        "pick_up_available": "Retiro disponible",
        "pick_up_currently_unavailable": "Recogida actualmente no disponible",
        "pick_up_available_at_html": "Recogida disponible en <strong>{ location_name }}</strong>",
        "pick_up_unavailable_at_html": "Recogida actualmente no disponible en <strong>{ location_name }}</strong>"
      }
    },
    "gift_cards": {
      "issued": {
        "title_html": "¡Aquí está tu tarjeta de regalo de {{ value }} para {{ shop }}!",
        "subtext": "¡Aquí tiene su tarjeta de regalo!",
        "disabled": "Desactivado",
        "expired": "Expirado el {{ expiry }}",
        "active": "Expira el {{ expiry }}",
        "redeem": "Utilizar este código en la caja para canjear su tarjeta regalo",
        "shop_link": "Empezar a comprar",
        "print": "Imprimir",
        "add_to_apple_wallet": "Agregar a Apple Wallet"
      }
    },
    "date_formats": {
      "month_day_year": "%b %d, %Y"
    }
  },
  "product_block": {
    "price": {
      "name": "Precio"
    },
    "quantity_selector": {
      "name": "Selector de cantidad"
    },
    "size_chart": {
      "name": "Tabla de tamaños",
      "settings": {
        "page": {
          "label": "Página de la tabla de medidas"
        }
      }
    },
    "variant_picker": {
      "name": "Selector de variante",
      "settings": {
        "variant_labels": {
          "label": "Mostrar etiquetas de variantes"
        },
        "picker_type": {
          "label": "Tipo",
          "options": {
            "button": {
              "label": "Botones"
            },
            "dropdown": {
              "label": "Desplegable"
            }
          }
        },
        "color_swatches": {
          "label": "Activar las muestras de color",
          "info": "Es necesario que el tipo esté configurado como 'Botones'. [Aprende a configurar las muestras](https://archetypethemes.co/blogs/impulse/how-do-i-set-up-color-swatches)"
        },
        "product_dynamic_variants_enable": {
          "label": "Habilitar opciones de productos dinámicos"
        }
      }
    },
    "description": {
      "name": "Descripción",
      "settings": {
        "is_tab": {
          "label": "Mostrar como pestaña"
        }
      }
    },
    "buy_buttons": {
      "name": "Botones de compras",
      "settings": {
        "show_dynamic_checkout": {
          "label": "Mostrar botón de pago dinámico",
          "info": "Permite a los clientes pagar directamente con un método de pago conocido. [Más información](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"
        },
        "surface_pickup_enable": {
          "label": "Habilita la función de disponibilidad de recogida",
          "info": "Más información sobre cómo establecer esta función [aquí](https://help.shopify.com/en/manual/shipping/setting-up-and-managing-your-shipping/local-methods/local-pickup)"
        }
      }
    },
    "inventory_status": {
      "name": "Estado del inventario",
      "settings": {
        "inventory_threshold": {
          "label": "Umbral de inventario bajo"
        },
        "inventory_transfers_enable": {
          "label": "Mostrar aviso de transferencia de inventario",
          "info": "Más información sobre cómo crear transferencias de inventario [aquí](https://help.shopify.com/en/manual/products/inventory/transfers/create-transfer)"
        }
      }
    },
    "sales_point": {
      "name": "Puntos de venta",
      "settings": {
        "icon": {
          "label": "Ícono",
          "options": {
            "checkmark": {
              "label": "Marca de verificación"
            },
            "gift": {
              "label": "Regalo"
            },
            "globe": {
              "label": "Globo"
            },
            "heart": {
              "label": "Corazón"
            },
            "leaf": {
              "label": "Hoja"
            },
            "lock": {
              "label": "Candado"
            },
            "package": {
              "label": "Paquete"
            },
            "phone": {
              "label": "Teléfono"
            },
            "ribbon": {
              "label": "Cinta"
            },
            "shield": {
              "label": "Escudo"
            },
            "tag": {
              "label": "Etiqueta"
            },
            "truck": {
              "label": "Camión"
            }
          }
        },
        "text": {
          "label": "Texto"
        }
      }
    },
    "text": {
      "name": "Texto",
      "settings": {
        "text": {
          "label": "Texto"
        }
      }
    },
    "trust_badge": {
      "name": "Insignia de confianza",
      "settings": {
        "trust_image": {
          "label": "Imagen"
        }
      }
    },
    "tab": {
      "name": "Pestaña",
      "settings": {
        "title": {
          "label": "Encabezado"
        },
        "content": {
          "label": "Contenido de la pestaña"
        },
        "page": {
          "label": "Pestaña de contenido de la página"
        }
      }
    },
    "share_on_social": {
      "name": "Compartir en las redes sociales",
      "settings": {
        "content": "Elegir a qué plataformas compartir en los ajustes globales del tema"
      }
    },
    "separator": {
      "name": "Separador"
    },
    "contact_form": {
      "name": "Formulario de contacto",
      "settings": {
        "content": "Todos los envíos se realizan a la dirección de correo electrónico del cliente de su tienda.  [Saber más](https://help.shopify.com/en/manual/using-themes/change-the-layout/add-contact-form#view-contact-form-submissions).",
        "title": {
          "label": "Encabezado"
        },
        "phone": {
          "label": "Añadir campo de número de teléfono"
        }
      }
    },
    "html": {
      "name": "HTML",
      "settings": {
        "code": {
          "label": "HTML",
          "info": "Compatible con Liquid"
        }
      }
    }
  }
}
