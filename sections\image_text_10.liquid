<section class="image_text_10"  style="background:{{section.settings.bg_color}}; {% if section.settings.bg_image != blank %}background-image: url({{ section.settings.bg_image | img_url: 'master' }}); background-size: cover; background-repeat: no-repeat; background-position: center;{% endif %};">
  <div class="page_wrapper">
    <div class="top_heading_wrap">
     {% if section.settings.sub_heading != blank %}
        <h4 class="sub_heading">{{ section.settings.sub_heading }}</h4>
        {% endif %}
        {% if section.settings.heading != blank %}
        <h2 class="heading">{{ section.settings.heading }}</h2>
        {% endif %}
    </div>
    <div class="wrapper">
      <div class="text_block">
        
        {% if section.settings.content != blank %}
        <div class="content">{{ section.settings.content }}</div>
        {% endif %}
      </div>
      <div class="image_block">
        {% if section.settings.image != blank %}
        <img src="{{ section.settings.image |img_url :'master' }}">
        {% endif %}
      </div>
      
    </div>
  </div>
</section>

<style>

  section.image_text_10 {
      padding: 50px 0 50px 150px;
}
section.image_text_10 .wrapper .text_block {
     width: 100%;
    padding-top: 50px;
}
section.image_text_10 h2.heading {
    font-size: 74px;
    font-family: Helvetica-Bold;
    margin: 0;
  color:#2a2b2b;
      text-transform: none;
      width: 100%;
    max-width: 850px;

}
  .image_text_10 h4.sub_heading {
    font-family: Helvetica-Bold;
    margin: 0;
    text-transform: none;
    font-size: 25px;
    letter-spacing: 0;
    color: #ff832d;
    font-weight:700;
}
section.image_text_10 .wrapper .text_block .content p {
    margin: 0;
    font-size: 25px;
    font-family: Helvetica-Bold;
    color: #676666;
    font-weight:700;
}
  section.image_text_10 .wrapper .image_block {
    width: 100%;
    font-size: 0;
}
  section.image_text_10 .wrapper .image_block img{
    width: 100%;
        box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.3);
    }
  .image_text_10 .wrapper {
    grid-template-columns: 720px 1fr;
    display: grid
;
    align-items: start;
    gap: 40px;
}
  section.image_text_10 .wrapper .text_block .content {
    width: 100%;
    /* max-width: 530px; */
}
  @media only screen and (min-width: 2600px) {
section.image_text_10 h2.heading {
    font-size: 134px;
      max-width: 1650px;
}
 .image_text_10 h4.sub_heading {
    font-size: 50px;
 }
    section.image_text_10 .wrapper .text_block .content p {

    font-size: 50px;
    }
    .image_text_10 .wrapper {
    grid-template-columns: 1224px 1fr;
    }
  }
  @media only screen and (max-width: 1600px) {
  section.image_text_10 {
    padding: 25px 0 25px 60px;
}
    
  }
  @media only screen and (max-width: 1280px) {
  section.image_text_10 h2.heading {
    font-size: 48px;
    max-width: 760px;
  }
    section.image_text_10 .wrapper .text_block .content p {
      font-size: 20px;
    }
    .image_text_10 h4.sub_heading {

    font-size: 20px;
    }
    .image_text_10 .wrapper {
    grid-template-columns: 540px 1fr;
    }
  }
  @media only screen and (max-width: 840px) {
  .image_text_10 .wrapper {
    grid-template-columns: 1fr;

}
        section.image_text_10 {
        padding: 30px 0 ;
    }
    section.image_text_10 .wrapper .text_block {
    padding-top: 20px;
}
    section.image_text_10 .wrapper .text_block {
    padding: 20px 20px 0;
}
    .image_text_10 .top_heading_wrap {
    padding: 0 20px;
}
  }
  @media only screen and (max-width: 480px) {
  section.image_text_10 h2.heading {
        font-size: 38px;
    }
    section.image_text_10 .wrapper .text_block {
    gap: 20px;
    }
      
        section.image_text_10 .wrapper .text_block .content p {
     
        font-size: 18px;
    }
        .image_text_10 h4.sub_heading {
        font-size: 18px;
    }
  }
</style>




  

{% schema %}
{
  "name": "Image Text 10",
  "settings": [
    {
      "type": "color",
      "id": "bg_color",
      "default": "transparent",
      "label": "Backgroud Color"
    },
       {
      "type": "image_picker",
      "id": "bg_image",
      "label": "Background Image"
    },
    
      {
          "type": "text",
          "id": "sub_heading",
          "label": "Sub Heading"
        },
      {
          "type": "text",
          "id": "heading",
          "label": "Heading"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "content"
        },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    }
    
    
    
    
  ],

  "presets": [
    {
      "name": "Image Text 10",
      "blocks": []
    }
  ]
}
{% endschema %}

