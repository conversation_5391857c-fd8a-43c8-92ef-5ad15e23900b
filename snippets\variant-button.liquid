{%- assign swatch_file_extension = 'png' -%}

{%- capture size_chart_title -%}
  {{ 'products.general.size_chart' | t }} <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-size-chart" viewBox="0 0 64 64"><defs><style>.a{fill:none;stroke:#000;stroke-width:2px}</style></defs><path class="a" d="M22.39 33.53c-7.46 0-13.5-3.9-13.5-8.72s6-8.72 13.5-8.72 13.5 3.9 13.5 8.72a12 12 0 0 1-.22 1.73"/><ellipse cx="22.39" cy="24.81" rx="3.28" ry="2.12"/><path class="a" d="M8.89 24.81V38.5c0 7.9 6.4 9.41 14.3 9.41h31.92V33.53H22.39M46.78 33.53v7.44M38.65 33.53v7.44M30.52 33.53v7.44M22.39 33.53v7.44"/></svg>
{%- endcapture -%}

{%- liquid
  assign is_size = false
  assign size_trigger = 'products.general.size_trigger' | t | downcase
  assign downcased_option = option.name | downcase

  if downcased_option contains size_trigger
    assign is_size = true
  endif
-%}

<div class="variant-wrapper js" data-type="button">
  <label
    class="variant__label !tw-text-[15px] max-md:!tw-text-[13px] !tw-text-[#000000] !tw-tracking-[1px] !tw-mb-[15px] !tw-font-normal !tw-font-dm-sans tw-uppercase !tw-leading-[1] {% if option.name == 'Default' or option.name == 'Title' %} hidden-label{% endif %}{% unless variant_labels %} hidden-label{% endunless %}"
    for="ProductSelect-{{ section_id }}-{{ product.id }}-option-{{ forloop.index0 }}"
  >
    {{ option.name }}
    {%- if connect_to_sizechart and is_size -%}
      <span class="variant__label-info">
        &mdash;
        {%- render 'tool-tip-trigger',
          title: size_chart_title,
          content: section.blocks[sizechart_index].settings.size_chart.content,
          context: 'size-chart'
        -%}
      </span>
    {%- endif -%}
    {%- if is_color -%}
      <span class="variant__label-info">
        &mdash;
        <span
          data-variant-color-label
          data-index="{{ forloop.index0 }}"
          data-option-index="{{ color_option_index }}"
        >
          {{ option.selected_value }}
        </span>
      </span>
    {% elsif template.suffix == 'oddit' %}
      <span class="oddit-template variant__label-info !tw-text-[#828282] !tw-uppercase">
        <span
          data-variant-color-label
          data-index="{{ forloop.index0 }}"
          data-option-index="{{ color_option_index }}"
        >
          {{ option.selected_value }}
        </span>
      </span>
    {%- endif -%}
  </label>
  {%- assign option_index = forloop.index -%}
  <fieldset
    class="variant-input-wrap tw-flex tw-flex-wrap tw-gap-[10px] max-md:tw-gap-[8px] !tw-m-0"
    name="{{ option.name }}"
    data-index="option{{ option_index }}"
    data-handle="{{ option.name | handleize }}"
    id="ProductSelect-{{ section_id }}-{{ product.id }}-option-{{ forloop.index0 }}"
  >
    <legend class="hide">{{ option.name }}</legend>
    {%- for value in option.values -%}
      {%- liquid
        for variant in product.variants
          if variant.title contains value
            assign current_variant = variant
          endif
        endfor

        assign product_available = true
        if product.options.size == 1
          assign product_available = product.variants[forloop.index0].available
        endif

        assign downcased_option_name = option.name | downcase
      -%}
      <div
        class="variant-input tw-w-[86px] max-md:tw-w-[58px] {% if downcased_option_name == 'color' %}tw-h-[86px] max-md:tw-h-[58px] tw-flex{% else %}tw-h-[40px]{% endif %}"
        data-index="option{{ option_index }}"
        data-value="{{ value | escape }}"
      >
        <input
          type="radio"
          form="{{ form_id }}"
          {% if option.selected_value == value %}
            checked="checked"
          {% endif %}
          value="{{ value | escape }}"
          data-index="option{{ option_index }}"
          name="{{ option.name }}"
          data-variant-input
          class="{% unless product_available %} disabled{% endunless %}{% if is_color %} variant__input--color-swatch{% endif %}"
          {% if is_color %}
            data-color-name="{{ value | escape }}"
          {% endif %}
          {% if is_color %}
            data-color-index="{{ color_option_index }}"
          {% endif %}
          id="ProductSelect-{{ section_id }}-{{ product.id }}-option-{{ option.name | handleize }}-{{ value | url_encode }}"
        >
        {%- if is_color -%}
          {%- liquid
            assign color_file_name = value | handle | append: '.' | append: swatch_file_extension
            assign color_image = color_file_name | file_img_url: '50x50' | prepend: 'https:' | split: '?' | first
            assign color_swatch_fallback = value | split: ' ' | last | handle
          -%}
          <label
            for="ProductSelect-{{ section_id }}-{{ product.id }}-option-{{ option.name | handleize }}-{{ value | url_encode }}"
            class="variant__button-label color-swatch color-swatch--{{ value | handle }}{% unless product_available %} disabled{% endunless %}"
            style="background-color: {{ color_swatch_fallback }};{% if images[color_file_name] != blank %}  background-image: url({{ color_image }});{% endif %}"
          >
            {{ value | escape }}
          </label>
        {%- else -%}
          <label
            for="ProductSelect-{{ section_id }}-{{ product.id }}-option-{{ option.name | handleize }}-{{ value | url_encode }}"
            class="variant__button-label !tw-m-0 tw-rounded-[10px] !tw-shadow-[0_0_0_1px_#E5E7EB] !tw-flex tw-justify-center tw-items-center tw-h-full !tw-bg-white !tw-p-0 {% unless product_available %} disabled{% endunless %}"
          >
            {% if template.suffix == 'oddit'
              and current_variant.featured_image != blank
              and downcased_option_name == 'color'
            %}
              <img
                src="{{ current_variant.featured_image | img_url: 'master' }}"
                alt="{{ current_variant.featured_image.alt }}"
                class="tw-object-contain tw-scale-[1.8]"
              >
            {% else %}
              {{- value | escape -}}
            {%- endif -%}
          </label>
        {%- endif -%}
      </div>
    {%- endfor -%}
  </fieldset>
</div>
