/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-product",
      "blocks": {
        "price": {
          "type": "price",
          "settings": {}
        },
        "separator": {
          "type": "separator",
          "settings": {}
        },
        "variant_picker": {
          "type": "variant_picker",
          "settings": {
            "variant_labels": true,
            "picker_type": "button",
            "product_dynamic_variants_enable": true,
            "color_swatches": false
          }
        },
        "sales_point": {
          "type": "sales_point",
          "settings": {
            "icon": "globe",
            "text": "Free worldwide shipping"
          }
        },
        "inventory_status": {
          "type": "inventory_status",
          "settings": {
            "inventory_threshold": 10,
            "inventory_transfers_enable": true
          }
        },
        "buy_buttons": {
          "type": "buy_buttons",
          "settings": {
            "show_dynamic_checkout": true,
            "surface_pickup_enable": true
          }
        },
        "description": {
          "type": "description",
          "settings": {
            "is_tab": true
          }
        },
        "tab": {
          "type": "tab",
          "settings": {
            "title": "Shipping information",
            "content": "<p>Use collapsible tabs for more detailed information that will help customers make a purchasing decision.</p><p>Ex: Shipping and return policies, size guides, and other common questions.</p>",
            "page": ""
          }
        },
        "contact": {
          "type": "contact",
          "settings": {
            "title": "Ask a question",
            "phone": false
          }
        },
        "share": {
          "type": "share",
          "settings": {}
        }
      },
      "block_order": [
        "price",
        "separator",
        "variant_picker",
        "sales_point",
        "inventory_status",
        "buy_buttons",
        "description",
        "tab",
        "contact",
        "share"
      ],
      "settings": {
        "sku_enable": false,
        "image_position": "left",
        "image_size": "medium",
        "product_zoom_enable": true,
        "thumbnail_position": "beside",
        "thumbnail_height": "flexible",
        "thumbnail_arrows": false,
        "mobile_layout": "partial",
        "enable_video_looping": true,
        "product_video_style": "muted"
      }
    },
    "sub": {
      "type": "product-full-width",
      "disabled": true,
      "settings": {
        "max_width": true
      }
    },
    "product-recommendations": {
      "type": "product-recommendations",
      "settings": {
        "show_product_recommendations": true,
        "product_recommendations_heading": "You may also like",
        "related_count": 5
      }
    },
    "recently-viewed": {
      "type": "recently-viewed",
      "settings": {
        "recent_count": 5
      }
    },
    "collection-return": {
      "type": "collection-return",
      "settings": {}
    },
    "slideshow": {
      "type": "slideshow",
      "blocks": {
        "slideshow-0": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "Two line\ntitle slide.",
            "title_size": 80,
            "subheading": "And optional subtext",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        },
        "slideshow-1": {
          "type": "image",
          "settings": {
            "top_subheading": "",
            "title": "Two line\ntitle slide.",
            "title_size": 80,
            "subheading": "And optional subtext",
            "link": "",
            "link_text": "",
            "link_2": "",
            "link_text_2": "",
            "color_accent": "rgba(0,0,0,0)",
            "text_align": "vertical-center horizontal-center",
            "parallax_direction": "top",
            "overlay_opacity": 0,
            "focal_point": "center center"
          }
        }
      },
      "block_order": [
        "slideshow-0",
        "slideshow-1"
      ],
      "settings": {
        "section_height": "550px",
        "mobile_height": "auto",
        "parallax": false,
        "style": "bars",
        "autoplay": true,
        "autoplay_speed": 7
      }
    },
    "text-and-image": {
      "type": "text-and-image",
      "settings": {
        "image_width": 700,
        "subtitle": "",
        "title": "Image with text",
        "text": "<p>Pair large text with an image to tell a story, explain a detail about your product, or describe a new promotion.</p>",
        "button_label": "",
        "button_link": "",
        "button_style": "primary",
        "align_text": "left",
        "layout": "right",
        "divider": false
      }
    },
    "text-and-image2": {
      "type": "text-and-image",
      "settings": {
        "image_width": 700,
        "subtitle": "",
        "title": "Image with text",
        "text": "<p>Pair large text with an image to tell a story, explain a detail about your product, or describe a new promotion.</p>",
        "button_label": "",
        "button_link": "",
        "button_style": "primary",
        "align_text": "left",
        "layout": "left",
        "divider": false
      }
    },
    "testimonials": {
      "type": "testimonials",
      "blocks": {
        "testimonials-0": {
          "type": "testimonial",
          "settings": {
            "icon": "5-stars",
            "testimonial": "<p>Add customer reviews and testimonials to showcase your store’s happy customers.</p>",
            "author": "Author's name",
            "author_info": "Los Angeles, CA"
          }
        },
        "testimonials-1": {
          "type": "testimonial",
          "settings": {
            "icon": "5-stars",
            "testimonial": "<p>Add customer reviews and testimonials to showcase your store’s happy customers.</p>",
            "author": "Author's name",
            "author_info": "Los Angeles, CA"
          }
        },
        "testimonials-2": {
          "type": "testimonial",
          "settings": {
            "icon": "5-stars",
            "testimonial": "<p>Add customer reviews and testimonials to showcase your store’s happy customers.</p>",
            "author": "Author's name",
            "author_info": "Los Angeles, CA"
          }
        }
      },
      "block_order": [
        "testimonials-0",
        "testimonials-1",
        "testimonials-2"
      ],
      "settings": {
        "title": "Testimonials",
        "align_text": "center",
        "round_images": true,
        "color_background": "#f9f9f9",
        "color_text": "#1c1d1d"
      }
    },
    "faq": {
      "type": "faq",
      "blocks": {
        "faq-0": {
          "type": "question",
          "settings": {
            "title": "Frequently asked question",
            "text": "<p>Use this text to answer questions in as much detail as possible for your customers.</p>"
          }
        },
        "faq-1": {
          "type": "question",
          "settings": {
            "title": "Frequently asked question",
            "text": "<p>Use this text to answer questions in as much detail as possible for your customers.</p>"
          }
        },
        "faq-2": {
          "type": "question",
          "settings": {
            "title": "Frequently asked question",
            "text": "<p>Use this text to answer questions in as much detail as possible for your customers.</p>"
          }
        }
      },
      "block_order": [
        "faq-0",
        "faq-1",
        "faq-2"
      ],
      "settings": {
        "title": "FAQs"
      }
    },
    "hero-video": {
      "type": "hero-video",
      "settings": {
        "title": "Bring your\nbrand to life.",
        "title_size": 80,
        "subheading": "Seamless hero videos",
        "link_text": "Optional button",
        "link": "",
        "color_accent": "rgba(0,0,0,0)",
        "text_align": "vertical-center horizontal-center",
        "video_url": "https://www.youtube.com/watch?v=_9VUPq3SxOc",
        "overlay_opacity": 0,
        "section_height": "650px",
        "mobile_height": "auto"
      }
    },
    "alireviews-section-1672312882": {
      "type": "apps",
      "settings": {
        "full_width": false,
        "space_around": true
      }
    }
  },
  "order": [
    "main",
    "sub",
    "product-recommendations",
    "recently-viewed",
    "collection-return",
    "slideshow",
    "text-and-image",
    "text-and-image2",
    "testimonials",
    "faq",
    "hero-video",
    "alireviews-section-1672312882"
  ]
}
