
    
    <style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-580761018800538195.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-580761018800538195.gps.gpsil [style*="--as:"]{align-self:var(--as)}.gps-580761018800538195.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-580761018800538195.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-580761018800538195.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-580761018800538195.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-580761018800538195.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-580761018800538195.gps.gpsil [style*="--hvr-bgc:"]:hover{background-color:var(--hvr-bgc)}.gps-580761018800538195.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-580761018800538195.gps.gpsil [style*="--hvr-bgi:"]:hover{background-image:var(--hvr-bgi)}.gps-580761018800538195.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-580761018800538195.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-580761018800538195.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-580761018800538195.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-580761018800538195.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-580761018800538195.gps.gpsil [style*="--focus-b:"]:focus{border:var(--focus-b)}.gps-580761018800538195.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-580761018800538195.gps.gpsil [style*="--hvr-bb:"]:hover{border-bottom:var(--hvr-bb)}.gps-580761018800538195.gps.gpsil [style*="--focus-bb:"]:focus{border-bottom:var(--focus-bb)}.gps-580761018800538195.gps.gpsil [style*="--blw:"]{border-left-width:var(--blw)}.gps-580761018800538195.gps.gpsil [style*="--hvr-blw:"]:hover{border-left-width:var(--hvr-blw)}.gps-580761018800538195.gps.gpsil [style*="--brw:"]{border-right-width:var(--brw)}.gps-580761018800538195.gps.gpsil [style*="--hvr-brw:"]:hover{border-right-width:var(--hvr-brw)}.gps-580761018800538195.gps.gpsil [style*="--blc:"]{border-left-color:var(--blc)}.gps-580761018800538195.gps.gpsil [style*="--brc:"]{border-right-color:var(--brc)}.gps-580761018800538195.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-580761018800538195.gps.gpsil [style*="--hvr-bc:"]:hover{border-color:var(--hvr-bc)}.gps-580761018800538195.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-580761018800538195.gps.gpsil [style*="--hvr-bblr:"]:hover{border-bottom-left-radius:var(--hvr-bblr)}.gps-580761018800538195.gps.gpsil [style*="--focus-bblr:"]:focus{border-bottom-left-radius:var(--focus-bblr)}.gps-580761018800538195.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-580761018800538195.gps.gpsil [style*="--hvr-bbrr:"]:hover{border-bottom-right-radius:var(--hvr-bbrr)}.gps-580761018800538195.gps.gpsil [style*="--focus-bbrr:"]:focus{border-bottom-right-radius:var(--focus-bbrr)}.gps-580761018800538195.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-580761018800538195.gps.gpsil [style*="--hvr-bl:"]:hover{border-left:var(--hvr-bl)}.gps-580761018800538195.gps.gpsil [style*="--br:"]{border-right:var(--br)}.gps-580761018800538195.gps.gpsil [style*="--hvr-br:"]:hover{border-right:var(--hvr-br)}.gps-580761018800538195.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-580761018800538195.gps.gpsil [style*="--hvr-bs:"]:hover{border-style:var(--hvr-bs)}.gps-580761018800538195.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-580761018800538195.gps.gpsil [style*="--hvr-bt:"]:hover{border-top:var(--hvr-bt)}.gps-580761018800538195.gps.gpsil [style*="--focus-bt:"]:focus{border-top:var(--focus-bt)}.gps-580761018800538195.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-580761018800538195.gps.gpsil [style*="--hvr-btlr:"]:hover{border-top-left-radius:var(--hvr-btlr)}.gps-580761018800538195.gps.gpsil [style*="--focus-btlr:"]:focus{border-top-left-radius:var(--focus-btlr)}.gps-580761018800538195.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-580761018800538195.gps.gpsil [style*="--hvr-btrr:"]:hover{border-top-right-radius:var(--hvr-btrr)}.gps-580761018800538195.gps.gpsil [style*="--focus-btrr:"]:focus{border-top-right-radius:var(--focus-btrr)}.gps-580761018800538195.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-580761018800538195.gps.gpsil [style*="--hvr-bw:"]:hover{border-width:var(--hvr-bw)}.gps-580761018800538195.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-580761018800538195.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-580761018800538195.gps.gpsil [style*="--hvr-shadow:"]:hover{box-shadow:var(--hvr-shadow)}.gps-580761018800538195.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-580761018800538195.gps.gpsil [style*="--hvr-c:"]:hover{color:var(--hvr-c)}.gps-580761018800538195.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-580761018800538195.gps.gpsil [style*="--fd:"]{flex-direction:var(--fd)}.gps-580761018800538195.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-580761018800538195.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-580761018800538195.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-580761018800538195.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-580761018800538195.gps.gpsil [style*="--gg:"]{grid-gap:var(--gg)}.gps-580761018800538195.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-580761018800538195.gps.gpsil [style*="--gtr:"]{grid-template-rows:var(--gtr)}.gps-580761018800538195.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-580761018800538195.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-580761018800538195.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-580761018800538195.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-580761018800538195.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-580761018800538195.gps.gpsil [style*="--tdt:"]{text-decoration-thickness:var(--tdt)}.gps-580761018800538195.gps.gpsil [style*="--tdc:"]{text-decoration-color:var(--tdc)}.gps-580761018800538195.gps.gpsil [style*="--tdl:"]{text-decoration-line:var(--tdl)}.gps-580761018800538195.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-580761018800538195.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-580761018800538195.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-580761018800538195.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-580761018800538195.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-580761018800538195.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-580761018800538195.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-580761018800538195.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-580761018800538195.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-580761018800538195.gps.gpsil [style*="--of:"]{overflow:var(--of)}.gps-580761018800538195.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-580761018800538195.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-580761018800538195.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-580761018800538195.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-580761018800538195.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-580761018800538195.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-580761018800538195.gps.gpsil [style*="--pos:"]{position:var(--pos)}.gps-580761018800538195.gps.gpsil [style*="--right:"]{right:var(--right)}.gps-580761018800538195.gps.gpsil [style*="--rg:"]{row-gap:var(--rg)}.gps-580761018800538195.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-580761018800538195.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-580761018800538195.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-580761018800538195.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-580761018800538195.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-580761018800538195.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-580761018800538195.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-580761018800538195.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-580761018800538195.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-580761018800538195.gps.gpsil [style*="--hvr-shadow-tablet:"]:hover{box-shadow:var(--hvr-shadow-tablet)}.gps-580761018800538195.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-580761018800538195.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-580761018800538195.gps.gpsil [style*="--gg-tablet:"]{grid-gap:var(--gg-tablet)}.gps-580761018800538195.gps.gpsil [style*="--gtc-tablet:"]{grid-template-columns:var(--gtc-tablet)}.gps-580761018800538195.gps.gpsil [style*="--gtr-tablet:"]{grid-template-rows:var(--gtr-tablet)}.gps-580761018800538195.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-580761018800538195.gps.gpsil [style*="--jc-tablet:"]{justify-content:var(--jc-tablet)}.gps-580761018800538195.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-580761018800538195.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-580761018800538195.gps.gpsil [style*="--mb-tablet:"]{margin-bottom:var(--mb-tablet)}.gps-580761018800538195.gps.gpsil [style*="--mr-tablet:"]{margin-right:var(--mr-tablet)}.gps-580761018800538195.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-580761018800538195.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-580761018800538195.gps.gpsil [style*="--op-tablet:"]{opacity:var(--op-tablet)}.gps-580761018800538195.gps.gpsil [style*="--o-tablet:"]{order:var(--o-tablet)}.gps-580761018800538195.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-580761018800538195.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-580761018800538195.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-580761018800538195.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-580761018800538195.gps.gpsil [style*="--pos-tablet:"]{position:var(--pos-tablet)}.gps-580761018800538195.gps.gpsil [style*="--right-tablet:"]{right:var(--right-tablet)}.gps-580761018800538195.gps.gpsil [style*="--rg-tablet:"]{row-gap:var(--rg-tablet)}.gps-580761018800538195.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-580761018800538195.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-580761018800538195.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-580761018800538195.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-580761018800538195.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-580761018800538195.gps.gpsil [style*="--hvr-shadow-mobile:"]:hover{box-shadow:var(--hvr-shadow-mobile)}.gps-580761018800538195.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-580761018800538195.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-580761018800538195.gps.gpsil [style*="--gg-mobile:"]{grid-gap:var(--gg-mobile)}.gps-580761018800538195.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-580761018800538195.gps.gpsil [style*="--gtr-mobile:"]{grid-template-rows:var(--gtr-mobile)}.gps-580761018800538195.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-580761018800538195.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-580761018800538195.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-580761018800538195.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-580761018800538195.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-580761018800538195.gps.gpsil [style*="--mr-mobile:"]{margin-right:var(--mr-mobile)}.gps-580761018800538195.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-580761018800538195.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-580761018800538195.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-580761018800538195.gps.gpsil [style*="--op-mobile:"]{opacity:var(--op-mobile)}.gps-580761018800538195.gps.gpsil [style*="--o-mobile:"]{order:var(--o-mobile)}.gps-580761018800538195.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-580761018800538195.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-580761018800538195.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-580761018800538195.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-580761018800538195.gps.gpsil [style*="--pos-mobile:"]{position:var(--pos-mobile)}.gps-580761018800538195.gps.gpsil [style*="--right-mobile:"]{right:var(--right-mobile)}.gps-580761018800538195.gps.gpsil [style*="--rg-mobile:"]{row-gap:var(--rg-mobile)}.gps-580761018800538195.gps.gpsil [style*="--ta-mobile:"]{text-align:var(--ta-mobile)}.gps-580761018800538195.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-580761018800538195.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-580761018800538195.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-580761018800538195 .-gp-rotate-90,.gps-580761018800538195 .-gp-translate-x-1\/2,.gps-580761018800538195 .-gp-translate-y-1\/2,.gps-580761018800538195 .gp-rotate-0,.gps-580761018800538195 .gp-rotate-180,.gps-580761018800538195 .gp-rotate-90,.gps-580761018800538195 .gp-rotate-\[-90deg\],.gps-580761018800538195 .gp-translate-x-\[-50\%\],.gps-580761018800538195 .gp-translate-y-0,.gps-580761018800538195 .mobile\:gp-rotate-0,.gps-580761018800538195 .mobile\:gp-rotate-180,.gps-580761018800538195 .mobile\:gp-rotate-90,.gps-580761018800538195 .mobile\:gp-rotate-\[-90deg\],.gps-580761018800538195 .tablet\:gp-rotate-0,.gps-580761018800538195 .tablet\:gp-rotate-180,.gps-580761018800538195 .tablet\:gp-rotate-90,.gps-580761018800538195 .tablet\:gp-rotate-\[-90deg\]{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-580761018800538195 .focus-visible\:gp-shadow-none,.gps-580761018800538195 .gp-shadow-none{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-580761018800538195 .gp-g-subheading-2{font-family:var(--g-sh2-ff);font-size:var(--g-sh2-size);font-style:var(--g-sh2-fs);font-weight:var(--g-sh2-weight);letter-spacing:var(--g-sh2-ls);line-height:var(--g-sh2-lh)}.gps-580761018800538195 .gp-sr-only{clip:rect(0,0,0,0);border-width:0;height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;white-space:nowrap;width:1px}.gps-580761018800538195 .gp-pointer-events-none{pointer-events:none}.gps-580761018800538195 .gp-invisible{visibility:hidden}.gps-580761018800538195 .gp-static{position:static}.gps-580761018800538195 .gp-absolute{position:absolute}.gps-580761018800538195 .gp-relative{position:relative}.gps-580761018800538195 .gp-inset-0{inset:0}.gps-580761018800538195 .gp-bottom-0{bottom:0}.gps-580761018800538195 .gp-bottom-\[-4px\]{bottom:-4px}.gps-580761018800538195 .gp-bottom-\[calc\(100\%\+20px\)\]{bottom:calc(100% + 20px)}.gps-580761018800538195 .gp-left-0{left:0}.gps-580761018800538195 .gp-left-1\/2,.gps-580761018800538195 .gp-left-\[50\%\]{left:50%}.gps-580761018800538195 .gp-right-0{right:0}.gps-580761018800538195 .gp-top-0{top:0}.gps-580761018800538195 .gp-top-1\/2{top:50%}.gps-580761018800538195 .gp-z-0{z-index:0}.gps-580761018800538195 .gp-z-1{z-index:1}.gps-580761018800538195 .gp-z-10{z-index:10}.gps-580761018800538195 .gp-z-2{z-index:2}.gps-580761018800538195 .gp-z-\[90\]{z-index:90}.gps-580761018800538195 .\!gp-m-0{margin:0!important}.gps-580761018800538195 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-580761018800538195 .gp-my-0{margin-bottom:0;margin-top:0}.gps-580761018800538195 .\!gp-ml-0{margin-left:0!important}.gps-580761018800538195 .gp-mb-0{margin-bottom:0}.gps-580761018800538195 .gp-mb-\[-10px\]{margin-bottom:-10px}.gps-580761018800538195 .gp-ml-2{margin-left:8px}.gps-580761018800538195 .gp-line-clamp-2{-webkit-box-orient:vertical;-webkit-line-clamp:2;display:-webkit-box;overflow:hidden}.gps-580761018800538195 .gp-block{display:block}.gps-580761018800538195 .\!gp-flex{display:flex!important}.gps-580761018800538195 .gp-flex{display:flex}.gps-580761018800538195 .gp-inline-flex{display:inline-flex}.gps-580761018800538195 .gp-grid{display:grid}.gps-580761018800538195 .\!gp-hidden{display:none!important}.gps-580761018800538195 .gp-hidden{display:none}.gps-580761018800538195 .gp-aspect-\[56\/32\]{aspect-ratio:56/32}.gps-580761018800538195 .gp-aspect-square{aspect-ratio:1/1}.gps-580761018800538195 .gp-h-0{height:0}.gps-580761018800538195 .gp-h-12{height:48px}.gps-580761018800538195 .gp-h-5{height:20px}.gps-580761018800538195 .gp-h-6{height:24px}.gps-580761018800538195 .gp-h-auto{height:auto}.gps-580761018800538195 .gp-h-full{height:100%}.gps-580761018800538195 .gp-max-h-full{max-height:100%}.gps-580761018800538195 .\!gp-min-h-0{min-height:0!important}.gps-580761018800538195 .\!gp-min-h-full{min-height:100%!important}.gps-580761018800538195 .gp-min-h-0{min-height:0}.gps-580761018800538195 .gp-w-14{width:56px}.gps-580761018800538195 .gp-w-5{width:20px}.gps-580761018800538195 .gp-w-6{width:24px}.gps-580761018800538195 .gp-w-auto{width:auto}.gps-580761018800538195 .gp-w-fit{width:-moz-fit-content;width:fit-content}.gps-580761018800538195 .gp-w-full{width:100%}.gps-580761018800538195 .gp-w-max{width:-moz-max-content;width:max-content}.gps-580761018800538195 .\!gp-min-w-full{min-width:100%!important}.gps-580761018800538195 .gp-min-w-\[45px\]{min-width:45px}.gps-580761018800538195 .gp-min-w-fit{min-width:-moz-fit-content;min-width:fit-content}.gps-580761018800538195 .\!gp-max-w-\[150px\]{max-width:150px!important}.gps-580761018800538195 .\!gp-max-w-full{max-width:100%!important}.gps-580761018800538195 .gp-max-w-full{max-width:100%}.gps-580761018800538195 .gp-flex-1{flex:1 1 0%}.gps-580761018800538195 .gp-shrink-0{flex-shrink:0}.gps-580761018800538195 .gp-shrink-\[99999\]{flex-shrink:99999}.gps-580761018800538195 .-gp-translate-x-1\/2{--tw-translate-x:-50%}.gps-580761018800538195 .-gp-translate-x-1\/2,.gps-580761018800538195 .-gp-translate-y-1\/2{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-580761018800538195 .-gp-translate-y-1\/2{--tw-translate-y:-50%}.gps-580761018800538195 .gp-translate-x-\[-50\%\]{--tw-translate-x:-50%}.gps-580761018800538195 .gp-translate-x-\[-50\%\],.gps-580761018800538195 .gp-translate-y-0{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-580761018800538195 .gp-translate-y-0{--tw-translate-y:0px}.gps-580761018800538195 .-gp-rotate-90{--tw-rotate:-90deg}.gps-580761018800538195 .-gp-rotate-90,.gps-580761018800538195 .gp-rotate-0{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-580761018800538195 .gp-rotate-0{--tw-rotate:0deg}.gps-580761018800538195 .gp-rotate-180{--tw-rotate:180deg}.gps-580761018800538195 .gp-rotate-180,.gps-580761018800538195 .gp-rotate-90{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-580761018800538195 .gp-rotate-90{--tw-rotate:90deg}.gps-580761018800538195 .gp-rotate-\[-90deg\]{--tw-rotate:-90deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-580761018800538195 .\!gp-cursor-not-allowed{cursor:not-allowed!important}.gps-580761018800538195 .gp-cursor-default{cursor:default}.gps-580761018800538195 .gp-cursor-pointer{cursor:pointer}.gps-580761018800538195 .gp-select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.gps-580761018800538195 .gp-appearance-none{-webkit-appearance:none;-moz-appearance:none;appearance:none}.gps-580761018800538195 .gp-grid-rows-\[0fr\]{grid-template-rows:0fr}.gps-580761018800538195 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-580761018800538195 .\!gp-flex-row{flex-direction:row!important}.gps-580761018800538195 .gp-flex-row{flex-direction:row}.gps-580761018800538195 .\!gp-flex-col{flex-direction:column!important}.gps-580761018800538195 .gp-flex-col{flex-direction:column}.gps-580761018800538195 .\!gp-flex-wrap{flex-wrap:wrap!important}.gps-580761018800538195 .gp-flex-wrap{flex-wrap:wrap}.gps-580761018800538195 .\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-580761018800538195 .gp-items-start{align-items:flex-start}.gps-580761018800538195 .gp-items-center{align-items:center}.gps-580761018800538195 .gp-justify-start{justify-content:flex-start}.gps-580761018800538195 .gp-justify-center{justify-content:center}.gps-580761018800538195 .gp-justify-between{justify-content:space-between}.gps-580761018800538195 .gp-gap-2{gap:8px}.gps-580761018800538195 .gp-gap-\[16px\]{gap:16px}.gps-580761018800538195 .gp-overflow-hidden{overflow:hidden}.gps-580761018800538195 .gp-overflow-clip{overflow:clip}.gps-580761018800538195 .gp-truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.gps-580761018800538195 .gp-text-ellipsis{text-overflow:ellipsis}.gps-580761018800538195 .gp-break-words{overflow-wrap:break-word}.gps-580761018800538195 .gp-break-all{word-break:break-all}.gps-580761018800538195 .\!gp-rounded-none{border-radius:0!important}.gps-580761018800538195 .gp-rounded{border-radius:4px}.gps-580761018800538195 .gp-rounded-\[8px\]{border-radius:8px}.gps-580761018800538195 .gp-rounded-none{border-radius:0}.gps-580761018800538195 .gp-border-g-line-2{border-color:var(--g-c-line-2)}.gps-580761018800538195 .gp-bg-\[\#333333\]{--tw-bg-opacity:1;background-color:rgb(51 51 51/var(--tw-bg-opacity))}.gps-580761018800538195 .gp-bg-black\/50{background-color:rgba(0,0,0,.5)}.gps-580761018800538195 .gp-bg-black\/80{background-color:rgba(0,0,0,.8)}.gps-580761018800538195 .gp-bg-g-bg-3{background-color:var(--g-c-bg-3)}.gps-580761018800538195 .gp-bg-transparent{background-color:transparent}.gps-580761018800538195 .gp-bg-auto{background-size:auto}.gps-580761018800538195 .gp-object-cover{-o-object-fit:cover;object-fit:cover}.gps-580761018800538195 .gp-p-\[4px\]{padding:4px}.gps-580761018800538195 .gp-px-0{padding-left:0;padding-right:0}.gps-580761018800538195 .gp-px-1{padding-left:4px;padding-right:4px}.gps-580761018800538195 .gp-px-\[8px\]{padding-left:8px;padding-right:8px}.gps-580761018800538195 .gp-py-\[4px\]{padding-bottom:4px;padding-top:4px}.gps-580761018800538195 .\!gp-pb-0{padding-bottom:0!important}.gps-580761018800538195 .gp-pb-1{padding-bottom:4px}.gps-580761018800538195 .gp-pl-4{padding-left:16px}.gps-580761018800538195 .gp-pr-1{padding-right:4px}.gps-580761018800538195 .gp-pr-6{padding-right:24px}.gps-580761018800538195 .gp-text-center{text-align:center}.gps-580761018800538195 .gp-text-\[12px\]{font-size:12px}.gps-580761018800538195 .gp-leading-\[0\]{line-height:0}.gps-580761018800538195 .gp-text-\[\#F9F9F9\]{--tw-text-opacity:1;color:rgb(249 249 249/var(--tw-text-opacity))}.gps-580761018800538195 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-580761018800538195 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-580761018800538195 .gp-text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity))}.gps-580761018800538195 .gp-text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity))}.gps-580761018800538195 .gp-no-underline{text-decoration-line:none}.gps-580761018800538195 .gp-opacity-0{opacity:0}.gps-580761018800538195 .gp-opacity-20{opacity:.2}.gps-580761018800538195 .gp-opacity-25{opacity:.25}.gps-580761018800538195 .gp-opacity-30{opacity:.3}.gps-580761018800538195 .gp-opacity-75{opacity:.75}.gps-580761018800538195 .gp-shadow-none{--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-580761018800538195 .gp-outline-none{outline:2px solid transparent;outline-offset:2px}.gps-580761018800538195 .gp-outline-1{outline-width:1px}.gps-580761018800538195 .-gp-outline-offset-1{outline-offset:-1px}.gps-580761018800538195 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-580761018800538195 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-580761018800538195 .gp-transition-opacity{transition-duration:.15s;transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-580761018800538195 .gp-duration-100{transition-duration:.1s}.gps-580761018800538195 .gp-duration-150{transition-duration:.15s}.gps-580761018800538195 .gp-duration-200{transition-duration:.2s}.gps-580761018800538195 .gp-duration-500{transition-duration:.5s}.gps-580761018800538195 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-580761018800538195 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-580761018800538195 .after\:gp-absolute:after{content:var(--tw-content);position:absolute}.gps-580761018800538195 .after\:gp-bottom-\[-10px\]:after{bottom:-10px;content:var(--tw-content)}.gps-580761018800538195 .after\:gp-left-0:after{content:var(--tw-content);left:0}.gps-580761018800538195 .after\:gp-w-full:after{content:var(--tw-content);width:100%}.gps-580761018800538195 .after\:gp-p-\[7px\]:after{content:var(--tw-content);padding:7px}.gps-580761018800538195 .after\:gp-content-\[\'\'\]:after{--tw-content:"";content:var(--tw-content)}@media (hover:hover) and (pointer:fine){.gps-580761018800538195 .hover\:gp-border-g-line-3:hover{border-color:var(--g-c-line-3)}.gps-580761018800538195 .hover\:gp-bg-\[\#ef0800\]:hover{--tw-bg-opacity:1;background-color:rgb(239 8 0/var(--tw-bg-opacity))}.gps-580761018800538195 .hover\:gp-bg-g-bg-3:hover{background-color:var(--g-c-bg-3)}.gps-580761018800538195 .hover\:gp-text-black:hover{--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity))}.gps-580761018800538195 .hover\:gp-text-g-text-2:hover{color:var(--g-c-text-2)}}.gps-580761018800538195 .focus-visible\:gp-shadow-none:focus-visible{--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-580761018800538195 .focus-visible\:gp-outline-none:focus-visible{outline:2px solid transparent;outline-offset:2px}.gps-580761018800538195 .active\:gp-bg-g-bg-3:active{background-color:var(--g-c-bg-3)}.gps-580761018800538195 .active\:gp-text-g-text-2:active{color:var(--g-c-text-2)}.gps-580761018800538195 .disabled\:gp-cursor-not-allowed:disabled{cursor:not-allowed}.gps-580761018800538195 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-580761018800538195 .gp-group:hover .group-hover\:gp-visible{visibility:visible}.gps-580761018800538195 .gp-group:hover .group-hover\:gp-opacity-100{opacity:1}.gps-580761018800538195 .gp-group:hover .group-hover\:\[color\:var\(--icon-expand-hover-color\)\]{color:var(--icon-expand-hover-color)}.gps-580761018800538195 .gp-group:hover .group-hover\:\[color\:var\(--text-hover-color\)\!important\]{color:var(--text-hover-color)!important}}.gps-580761018800538195 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-580761018800538195 .data-\[disabled\=\'disabled\'\]\:gp-pointer-events-none[data-disabled=disabled]{pointer-events:none}.gps-580761018800538195 .data-\[hidden\=\'false\'\]\:gp-flex[data-hidden=false]{display:flex}.gps-580761018800538195 .data-\[hidden\=true\]\:gp-hidden[data-hidden=true],.gps-580761018800538195 .data-\[only-image\=true\]\:gp-hidden[data-only-image=true]{display:none}.gps-580761018800538195 .data-\[disabled\=\'disabled\'\]\:\!gp-cursor-not-allowed[data-disabled=disabled]{cursor:not-allowed!important}.gps-580761018800538195 .data-\[disabled\=true\]\:gp-opacity-60[data-disabled=true]{opacity:.6}.gps-580761018800538195 .data-\[outline\=active\]\:gp-outline[data-outline=active]{outline-style:solid}.gps-580761018800538195 .data-\[outline\=deactive\]\:after\:\!gp-border-transparent[data-outline=deactive]:after{border-color:transparent!important;content:var(--tw-content)}.gps-580761018800538195 .gp-group[data-state=loading] .group-data-\[state\=loading\]\:gp-visible{visibility:visible}.gps-580761018800538195 .gp-group[data-state=loading] .group-data-\[state\=loading\]\:gp-invisible{visibility:hidden}@keyframes gp-spin{to{transform:rotate(1turn)}}.gps-580761018800538195 .gp-group[data-state=loading] .group-data-\[state\=loading\]\:gp-animate-spin{animation:gp-spin 1s linear infinite}@media (max-width:1024px){.gps-580761018800538195 .tablet\:gp-static{position:static}.gps-580761018800538195 .tablet\:gp-left-0{left:0}.gps-580761018800538195 .tablet\:gp-right-0{right:0}.gps-580761018800538195 .tablet\:gp-ml-2{margin-left:8px}.gps-580761018800538195 .tablet\:gp-block{display:block}.gps-580761018800538195 .tablet\:\!gp-flex{display:flex!important}.gps-580761018800538195 .tablet\:gp-flex{display:flex}.gps-580761018800538195 .tablet\:\!gp-hidden{display:none!important}.gps-580761018800538195 .tablet\:gp-hidden{display:none}.gps-580761018800538195 .tablet\:gp-h-full{height:100%}.gps-580761018800538195 .tablet\:\!gp-min-h-0{min-height:0!important}.gps-580761018800538195 .tablet\:\!gp-min-h-full{min-height:100%!important}.gps-580761018800538195 .tablet\:gp-w-fit{width:-moz-fit-content;width:fit-content}.gps-580761018800538195 .tablet\:gp-flex-1{flex:1 1 0%}.gps-580761018800538195 .tablet\:gp-rotate-0{--tw-rotate:0deg}.gps-580761018800538195 .tablet\:gp-rotate-0,.gps-580761018800538195 .tablet\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-580761018800538195 .tablet\:gp-rotate-180{--tw-rotate:180deg}.gps-580761018800538195 .tablet\:gp-rotate-90{--tw-rotate:90deg}.gps-580761018800538195 .tablet\:gp-rotate-90,.gps-580761018800538195 .tablet\:gp-rotate-\[-90deg\]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-580761018800538195 .tablet\:gp-rotate-\[-90deg\]{--tw-rotate:-90deg}.gps-580761018800538195 .tablet\:\!gp-flex-row{flex-direction:row!important}.gps-580761018800538195 .tablet\:gp-flex-row{flex-direction:row}.gps-580761018800538195 .tablet\:\!gp-flex-col{flex-direction:column!important}.gps-580761018800538195 .tablet\:gp-flex-col{flex-direction:column}.gps-580761018800538195 .tablet\:\!gp-flex-wrap{flex-wrap:wrap!important}.gps-580761018800538195 .tablet\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-580761018800538195 .tablet\:gp-px-0{padding-left:0;padding-right:0}}@media (max-width:767px){.gps-580761018800538195 .mobile\:gp-static{position:static}.gps-580761018800538195 .mobile\:gp-left-0{left:0}.gps-580761018800538195 .mobile\:gp-right-0{right:0}.gps-580761018800538195 .mobile\:gp-ml-2{margin-left:8px}.gps-580761018800538195 .mobile\:gp-block{display:block}.gps-580761018800538195 .mobile\:\!gp-flex{display:flex!important}.gps-580761018800538195 .mobile\:gp-flex{display:flex}.gps-580761018800538195 .mobile\:\!gp-hidden{display:none!important}.gps-580761018800538195 .mobile\:gp-hidden{display:none}.gps-580761018800538195 .mobile\:gp-h-full{height:100%}.gps-580761018800538195 .mobile\:\!gp-min-h-0{min-height:0!important}.gps-580761018800538195 .mobile\:\!gp-min-h-full{min-height:100%!important}.gps-580761018800538195 .mobile\:gp-w-fit{width:-moz-fit-content;width:fit-content}.gps-580761018800538195 .mobile\:gp-flex-1{flex:1 1 0%}.gps-580761018800538195 .mobile\:gp-rotate-0{--tw-rotate:0deg}.gps-580761018800538195 .mobile\:gp-rotate-0,.gps-580761018800538195 .mobile\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-580761018800538195 .mobile\:gp-rotate-180{--tw-rotate:180deg}.gps-580761018800538195 .mobile\:gp-rotate-90{--tw-rotate:90deg}.gps-580761018800538195 .mobile\:gp-rotate-90,.gps-580761018800538195 .mobile\:gp-rotate-\[-90deg\]{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-580761018800538195 .mobile\:gp-rotate-\[-90deg\]{--tw-rotate:-90deg}.gps-580761018800538195 .mobile\:\!gp-flex-row{flex-direction:row!important}.gps-580761018800538195 .mobile\:gp-flex-row{flex-direction:row}.gps-580761018800538195 .mobile\:\!gp-flex-col{flex-direction:column!important}.gps-580761018800538195 .mobile\:gp-flex-col{flex-direction:column}.gps-580761018800538195 .mobile\:\!gp-flex-wrap{flex-wrap:wrap!important}.gps-580761018800538195 .mobile\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-580761018800538195 .mobile\:gp-px-0{padding-left:0;padding-right:0}}.gps-580761018800538195 .\[\&\>\*\]\:gp-h-full>*{height:100%}.gps-580761018800538195 .\[\&\>\*\]\:gp-w-full>*{width:100%}.gps-580761018800538195 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-580761018800538195 .\[\&\>svg\]\:\!gp-h-\[var\(--height-iconCollapseSize\)\]>svg{height:var(--height-iconCollapseSize)!important}.gps-580761018800538195 .\[\&\>svg\]\:gp-h-full>svg{height:100%}.gps-580761018800538195 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}.gps-580761018800538195 .\[\&\>svg\]\:\!gp-w-full>svg{width:100%!important}.gps-580761018800538195 .\[\&\>svg\]\:gp-w-full>svg{width:100%}@media (max-width:1024px){.gps-580761018800538195 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-580761018800538195 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-580761018800538195 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-580761018800538195 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}</style>
    
    
    

    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}

    
        <section
          class="gp-mx-auto gp-max-w-full [&_*]:gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
          style="--w:calc(var(--g-ct-w, 1200px) + 2 * var(--g-ct-p, 15px));--w-tablet:100%;--w-mobile:100%;--pl:none;--pl-tablet:none;--pl-mobile:none;--pr:none;--pr-tablet:none;--pr-mobile:none"
        >
          
    <gp-row gp-data='{"background":{"desktop":{"attachment":"scroll","color":"#ffffff","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"g071lCCNnn"}' data-id="g071lCCNnn" id="g071lCCNnn" data-same-height-subgrid-container class="g071lCCNnn gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--blockPadding:base;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--op-mobile:100%;--op-tablet:100%;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:80px;--pl:60px;--pb:48px;--pr:60px;--pt-mobile:28px;--pl-mobile:15px;--pb-mobile:28px;--pr-mobile:15px;--pt-tablet:32px;--pl-tablet:15px;--pb-tablet:32px;--pr-tablet:15px;--cg:32px;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:#ffffff;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="gVnN8OSgFm gp-relative gp-flex gp-flex-col"
    >
      {% render 'gp-section-580761018800538195-0', product: product, variant: variant, product_form_id: product_form_id, productSelectedVariant: productSelectedVariant, form: form  %}
    </div>

      
    </gp-row>
  
        </section>
      
  
    <style {% if section.settings.section_preload == "false" %} class="gps-link" type="text/disabled-css" {% endif %}>@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWubEbVmUiAw.woff) format('woff');
}
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYaalmUiAw.woff) format('woff');
}
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmUiAw.woff) format('woff');
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBHMdazQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 600;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBHMdazQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBHMdazTgWw.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v49/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBHMdazQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style>
    
{% schema %}
  {
    
    "name": "Section 9",
    "tag": "section",
    "class": "gps-580761018800538195 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=578718746651132539&sectionId=580761018800538195)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"gg2J3SUlfsO_text","label":"gg2J3SUlfsO_text","default":"<p>(2,701)</p>"},{"type":"html","id":"gg6uB5P1M9O_text","label":"gg6uB5P1M9O_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(29,29,31);font-size:16px;\">Unsurpassed in toughness, ready for the toughest work and sports challenges. Sturdier than all competitors and for only half the price. Building on the Ultra 1 but better in every way.</span></p>"},{"type":"html","id":"gg3BeoVNY2T_label","label":"gg3BeoVNY2T_label","default":"Add to cart"},{"type":"html","id":"gg3BeoVNY2T_outOfStockLabel","label":"gg3BeoVNY2T_outOfStockLabel","default":"Out of stock"},{"type":"html","id":"gg3BeoVNY2T_unavailableLabel","label":"gg3BeoVNY2T_unavailableLabel","default":"Unavailable"},{"type":"html","id":"gguhQK-nPW6_childItem_0","label":"gguhQK-nPW6_childItem_0","default":"<p>SHIPPING</p>"},{"type":"html","id":"gguhQK-nPW6_childItem_1","label":"gguhQK-nPW6_childItem_1","default":"<p>30 DAY RISK-FREE TRIAL</p>"},{"type":"html","id":"ggtYWeYhAq0_text","label":"ggtYWeYhAq0_text","default":"<p><strong>UNITED STATES:</strong> 3 - 6 Business days delivery<br><br><strong>THE REST OF THE WORLD:</strong> 3 - 6 Business days delivery<br>&nbsp;</p><p>Orders placed before 9 p.m. will be shipped the same day.</p><p><strong>SHIPPING</strong><br>Gard Pro orders are shipped with DHL, FedEx, and UPS. When your order is shipped, you will receive a Track &amp; Trace number by e-mail.<br>&nbsp;</p><p><strong>RETURNS</strong><br>We offer a 30-day reflection period on every order to return or exchange if necessary; for more information, check our entire policy.</p>"},{"type":"html","id":"ggtAAny52mR_text","label":"ggtAAny52mR_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:#000000;font-size:14.72px;\">Not sure it’s right for you? Try it for 30 days, completely risk-free. If it’s not exactly what you wanted, return or exchange it—no hassle, no pressure. We’re confident you’ll love it.</span></p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
  