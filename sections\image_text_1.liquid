<section class="image_text_1"  style="background:{{section.settings.bg_color}}; {% if section.settings.bg_image != blank %}background-image: url({{ section.settings.bg_image | img_url: 'master' }}); background-size: cover; background-repeat: no-repeat; background-position: center;{% endif %};">
  <div class="page_wrapper">
    <div class="wrapper">
      <div class="text_block">
        {% if section.settings.heading != blank %}
        <h2 class="heading">{{ section.settings.heading }}</h2>
        {% endif %}
        {% if section.settings.content != blank %}
        <div class="content">{{ section.settings.content }}</div>
        {% endif %}
      </div>
      <div class="image_block">
        {% if section.settings.image != blank %}
        <img src="{{ section.settings.image |img_url :'master' }}">
        {% endif %}
      </div>
      
    </div>
  </div>
</section>

<style>

  section.image_text_1 {
    padding: 50px 150px;
}
section.image_text_1 .wrapper .text_block {
    width: 100%;
    display: flex
;
    flex-direction: column;
    gap: 30px;
      max-width: 770px;
}
section.image_text_1 .wrapper .text_block h2.heading {
    font-size: 74px;
    font-family: Helvetica-Bold;
    margin: 0;
  color:#2a2b2b;
      text-transform: none;

}
section.image_text_1 .wrapper .text_block .content p {
    margin: 0;
    font-size: 25px;
    font-family: Helvetica-Bold;
    color: #676666;
  font-weight:700;
}
  section.image_text_1 .wrapper .image_block {
    width: 100%;
    font-size: 0;
}
  section.image_text_1 .wrapper .image_block img{
    width: 100%;
    }
  .image_text_1 .wrapper {
    grid-template-columns: 1.5fr 1fr;
    display: grid;
        align-items: center;
    gap:100px;
}
  section.image_text_1 .wrapper .text_block .content {
    width: 100%;
    /* max-width: 530px; */
}
  @media only screen and (min-width: 2600px) {
section.image_text_1 .wrapper .text_block h2.heading {
    font-size: 134px;
}
    section.image_text_1 .wrapper .text_block {
    gap: 50px;
              max-width: 1296px;
}
    section.image_text_1 .wrapper .text_block .content p {

    font-size: 50px;
    }
  }
  @media only screen and (max-width: 1600px) {
  section.image_text_1 {
    padding: 25px 60px;
}
    
  }
  @media only screen and (max-width: 1280px) {
  section.image_text_1 .wrapper .text_block h2.heading {
    font-size: 48px;
  }
    section.image_text_1 .wrapper .text_block .content p {
      font-size: 20px;
    }
  }
  @media only screen and (max-width: 840px) {
  .image_text_1 .wrapper {
    grid-template-columns: 1fr;
    gap: 50px;
}
        section.image_text_1 {
        padding: 30px 20px;
    }
  }
  @media only screen and (max-width: 480px) {
  section.image_text_1 .wrapper .text_block h2.heading {
        font-size: 38px;
    }
    section.image_text_1 .wrapper .text_block {
    gap: 20px;
    }
      
        section.image_text_1 .wrapper .text_block .content p {
     
        font-size: 18px;
    }
  }
</style>




  

{% schema %}
{
  "name": "Image Text 1",
  "settings": [
    {
      "type": "color",
      "id": "bg_color",
      "default": "transparent",
      "label": "Backgroud Color"
    },
       {
      "type": "image_picker",
      "id": "bg_image",
      "label": "Background Image"
    },
      {
          "type": "text",
          "id": "heading",
          "label": "Heading"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "content"
        },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    }
    
    
    
    
  ],

  "presets": [
    {
      "name": "Image Text 1",
      "blocks": []
    }
  ]
}
{% endschema %}

