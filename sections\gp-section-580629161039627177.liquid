
    
    <style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-580629161039627177.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-580629161039627177.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-580629161039627177.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-580629161039627177.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-580629161039627177.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-580629161039627177.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-580629161039627177.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-580629161039627177.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-580629161039627177.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-580629161039627177.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-580629161039627177.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-580629161039627177.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-580629161039627177.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-580629161039627177.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-580629161039627177.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-580629161039627177.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-580629161039627177.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-580629161039627177.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-580629161039627177.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-580629161039627177.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-580629161039627177.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-580629161039627177.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-580629161039627177.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-580629161039627177.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-580629161039627177.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-580629161039627177.gps.gpsil [style*="--tdt:"]{text-decoration-thickness:var(--tdt)}.gps-580629161039627177.gps.gpsil [style*="--tdl:"]{text-decoration-line:var(--tdl)}.gps-580629161039627177.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-580629161039627177.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-580629161039627177.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-580629161039627177.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-580629161039627177.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-580629161039627177.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-580629161039627177.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-580629161039627177.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-580629161039627177.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-580629161039627177.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-580629161039627177.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-580629161039627177.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-580629161039627177.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-580629161039627177.gps.gpsil [style*="--rg:"]{row-gap:var(--rg)}.gps-580629161039627177.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-580629161039627177.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-580629161039627177.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-580629161039627177.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-580629161039627177.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-580629161039627177.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-580629161039627177.gps.gpsil [style*="--ml-tablet:"]{margin-left:var(--ml-tablet)}.gps-580629161039627177.gps.gpsil [style*="--mr-tablet:"]{margin-right:var(--mr-tablet)}.gps-580629161039627177.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-580629161039627177.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-580629161039627177.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-580629161039627177.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-580629161039627177.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-580629161039627177.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-580629161039627177.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-580629161039627177.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-580629161039627177.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-580629161039627177.gps.gpsil [style*="--ml-mobile:"]{margin-left:var(--ml-mobile)}.gps-580629161039627177.gps.gpsil [style*="--mr-mobile:"]{margin-right:var(--mr-mobile)}.gps-580629161039627177.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-580629161039627177.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-580629161039627177.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-580629161039627177.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-580629161039627177.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-580629161039627177.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-580629161039627177.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-580629161039627177.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-580629161039627177 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-580629161039627177 .\!gp-relative{position:relative!important}.gps-580629161039627177 .gp-relative{position:relative}.gps-580629161039627177 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-580629161039627177 .gp-mb-0{margin-bottom:0}.gps-580629161039627177 .gp-flex{display:flex}.gps-580629161039627177 .gp-inline-flex{display:inline-flex}.gps-580629161039627177 .gp-grid{display:grid}.gps-580629161039627177 .\!gp-hidden{display:none!important}.gps-580629161039627177 .gp-hidden{display:none}.gps-580629161039627177 .gp-h-full{height:100%}.gps-580629161039627177 .gp-w-\[0\.01px\]{width:.01px}.gps-580629161039627177 .gp-w-full{width:100%}.gps-580629161039627177 .gp-max-w-full{max-width:100%}.gps-580629161039627177 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-580629161039627177 .gp-flex-col{flex-direction:column}.gps-580629161039627177 .gp-items-center{align-items:center}.gps-580629161039627177 .gp-overflow-hidden{overflow:hidden}.gps-580629161039627177 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-580629161039627177 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-580629161039627177 .gp-duration-200{transition-duration:.2s}.gps-580629161039627177 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-580629161039627177 .tablet\:\!gp-hidden{display:none!important}.gps-580629161039627177 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-580629161039627177 .mobile\:\!gp-hidden{display:none!important}.gps-580629161039627177 .mobile\:gp-hidden{display:none}}.gps-580629161039627177 .\[\&\>svg\]\:gp-h-full>svg{height:100%}.gps-580629161039627177 .\[\&\>svg\]\:gp-w-full>svg{width:100%}.gps-580629161039627177 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}</style>
    
    
    

    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}

    
        <section
          class="gp-mx-auto gp-max-w-full [&_*]:gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
          style="--w:100%;--w-tablet:calc(var(--g-ct-w, 100%) + 2 * var(--g-ct-p, 15px));--w-mobile:calc(var(--g-ct-w, 100%) + 2 * var(--g-ct-p, 15px));--pl:none;--pl-tablet:none;--pl-mobile:none;--pr:none;--pr-tablet:none;--pr-mobile:none"
        >
          
    <gp-row gp-data='{"background":{"desktop":{"type":"color","color":"transparent","image":{"src":"","width":0,"height":0},"size":"cover","position":{"x":50,"y":50},"repeat":"no-repeat","attachment":"scroll"}},"uid":"g-9-VG--S3"}' data-id="g-9-VG--S3" id="g-9-VG--S3" data-same-height-subgrid-container class="g-9-VG--S3 gp-relative gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]" style="--blockPadding:base;--pt:var(--g-s-2xl);--pl:0px;--pb:var(--g-s-2xl);--pr:0px;--pt-tablet:var(--g-s-2xl);--pl-tablet:0px;--pb-tablet:var(--g-s-2xl);--pr-tablet:0px;--pt-mobile:var(--g-s-2xl);--pl-mobile:0px;--pb-mobile:var(--g-s-2xl);--pr-mobile:0px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-3, line-3);--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--op:100%;--cg:16px;--rg:0px;--gtc:minmax(0, 12fr);--w:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--pc:start">
      

      <div
      data-same-height-display-contents
      style="--jc:start"
      class="gYbqB5PWQ4 gp-relative gp-flex gp-flex-col"
    >
      
  <script src="https://assets.gemcommerce.com/assets-v2/gp-marquee-v7-5.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  <gp-marquee data-id="gwNkgdobfK" gp-data='{"setting":{"activeItem":"0","childItem":["Item 1","Item 2","Item 3","Item 4"],"direction":"left","hasItemShadow":false,"hoverItem":"0","iconSeparatorSvg":"<svg height=\"20px\" width=\"20px\" data-name=\"dot-outline-filled\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 256 256\" fill=\"currentColor\" data-id=\"508817731477569896\">\n              <path fill=\"currentColor\" strokelinecap=\"round\" strokelinejoin=\"round\" d=\"M156,128a28,28,0,1,1-28-28A28,28,0,0,1,156,128Z\"></path></svg>","isPreview":false,"isShowIconSeparator":true,"itemWidthType":{"desktop":"FIT_CONTENT","mobile":"FIT_CONTENT","tablet":"FIT_CONTENT"},"speed":1,"stopOnHover":true,"uid":"gwNkgdobfK"},"styles":{"align":{"desktop":"center"},"backgroundColor":{"desktop":"#201912"},"iconSeparatorColor":"#0171E3","iconSeparatorSize":{"desktop":"50"},"itemBackgroundColor":{"desktop":"transparent"},"itemBorderStyle":{"border":"none","borderType":"none","borderWidth":"1px","color":"#000000","isCustom":false,"width":"1px 1px 1px 1px"},"itemCorner":{"radiusType":"none"},"itemMaxWidth":{"desktop":"300px","mobile":"150px","tablet":"300px"},"itemSpacing":{"desktop":"6px"},"sizeSetting":{"desktop":{"height":"Auto","shapeLinked":false,"width":"100%"}}}}'
   class="gwNkgdobfK">
   <div class="gp-flex gp-w-full gp-relative" style="--jc:center">
      <div class="!gp-relative" style="--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--w:100%;--w-tablet:100%;--w-mobile:100%;--bs:none;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-3, line-3);--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:16px;--pl:0px;--pb:16px;--pr:0px;--bg:#201912">
         <div class="gem-marquee gp-overflow-hidden gp-flex gp-items-center gp-w-full gp-h-full gwNkgdobfK">
            <div class="gp-overflow-hidden gp-w-full gp-h-full">
               
  <div style="--pause-on-hover:paused;--pause-on-click:paused;--width:100%;--transform:none;min-width:100%" class="rfm-marquee-container">
    <div class="rfm-marquee" style="--play:running;--direction:normal;--delay:0s;--iteration-count:infinite;--min-width:auto;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto">
      <div class="rfm-initial-child-container">
        
        <div style="--transform:none;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto" class="rfm-child">
          
               <div class="gp-flex gp-items-center gp-marque-child-item gem-child-marquee-item gp-w-full">
                  
                  <div class="gp-inline-flex gp-items-center gp-relative gp-transition-all"
                     style="--pr:0px;--pr-tablet:0px;--pr-mobile:0px;text-wrap:wrap;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto;--maxw:unset;--maxw-tablet:unset;--maxw-mobile:unset">
                     <div
      class="gp-relative gp-overflow-hidden gem-marquee-item  gMXbNra9BO gem-marquee-item-gMXbNra9BO"
      style="--bg:transparent;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%);--b:none;--bw:1px 1px 1px 1px;--bc:#000000"
    >
      <div className="first-block gp-w-[0.01px]"></div>
      
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="glIKvhM2oA"
        class="glIKvhM2oA"
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-3, line-3);--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:8px;--pb:8px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425937236"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class="gp-g-paragraph-1  gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--b:none;--bc:#121212;--bw:1px 1px 1px 1px;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--weight:bold;--c:#FFFFFF;--tt:none;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden;--tdl:"
        >
          {{ section.settings.gglIKvhM2oA_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    </div>
                     <span style="--c:#0171E3;--ml:6px;--ml-tablet:6px;--ml-mobile:6px;--mr:6px;--mr-tablet:6px;--mr-mobile:6px;--h:50px;--h-tablet:50px;--h-mobile:50px;--w:50px;--w-tablet:50px;--w-mobile:50px;--minw:50px;--minw-tablet:50px;--minw-mobile:50px"
                        class="gp-inline-flex gp-items-center  [&>svg]:gp-h-full [&>svg]:gp-w-full">
                        <svg height="20px" width="20px" data-name="dot-outline-filled" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" fill="currentColor" data-id="508817731477569896">
              <path fill="currentColor" strokelinecap="round" strokelinejoin="round" d="M156,128a28,28,0,1,1-28-28A28,28,0,0,1,156,128Z"></path></svg>
                     </span>
                  </div>
                  
                  <div class="gp-inline-flex gp-items-center gp-relative gp-transition-all"
                     style="--pr:0px;--pr-tablet:0px;--pr-mobile:0px;text-wrap:wrap;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto;--maxw:unset;--maxw-tablet:unset;--maxw-mobile:unset">
                     <div
      class="gp-relative gp-overflow-hidden gem-marquee-item  gJcaMUFwf9 gem-marquee-item-gJcaMUFwf9"
      style="--bg:transparent;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%);--b:none;--bw:1px 1px 1px 1px;--bc:#000000"
    >
      <div className="first-block gp-w-[0.01px]"></div>
      
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gcPOerbqI0"
        class="gcPOerbqI0"
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-3, line-3);--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:8px;--pb:8px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425937237"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class="gp-g-paragraph-1  gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--b:none;--bc:#121212;--bw:1px 1px 1px 1px;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--weight:bold;--c:#FFFFFF;--tt:none;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggcPOerbqI0_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    </div>
                     <span style="--c:#0171E3;--ml:6px;--ml-tablet:6px;--ml-mobile:6px;--mr:6px;--mr-tablet:6px;--mr-mobile:6px;--h:50px;--h-tablet:50px;--h-mobile:50px;--w:50px;--w-tablet:50px;--w-mobile:50px;--minw:50px;--minw-tablet:50px;--minw-mobile:50px"
                        class="gp-inline-flex gp-items-center  [&>svg]:gp-h-full [&>svg]:gp-w-full">
                        <svg height="20px" width="20px" data-name="dot-outline-filled" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" fill="currentColor" data-id="508817731477569896">
              <path fill="currentColor" strokelinecap="round" strokelinejoin="round" d="M156,128a28,28,0,1,1-28-28A28,28,0,0,1,156,128Z"></path></svg>
                     </span>
                  </div>
                  
                  <div class="gp-inline-flex gp-items-center gp-relative gp-transition-all"
                     style="--pr:0px;--pr-tablet:0px;--pr-mobile:0px;text-wrap:wrap;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto;--maxw:unset;--maxw-tablet:unset;--maxw-mobile:unset">
                     <div
      class="gp-relative gp-overflow-hidden gem-marquee-item  giah9LpCX2 gem-marquee-item-giah9LpCX2"
      style="--bg:transparent;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%);--b:none;--bw:1px 1px 1px 1px;--bc:#000000"
    >
      <div className="first-block gp-w-[0.01px]"></div>
      
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gKapNWUYaA"
        class="gKapNWUYaA"
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-3, line-3);--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:8px;--pb:8px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425937239"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class="gp-g-paragraph-1  gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--b:none;--bc:#121212;--bw:1px 1px 1px 1px;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--weight:bold;--c:#FFFFFF;--tt:none;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggKapNWUYaA_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    </div>
                     <span style="--c:#0171E3;--ml:6px;--ml-tablet:6px;--ml-mobile:6px;--mr:6px;--mr-tablet:6px;--mr-mobile:6px;--h:50px;--h-tablet:50px;--h-mobile:50px;--w:50px;--w-tablet:50px;--w-mobile:50px;--minw:50px;--minw-tablet:50px;--minw-mobile:50px"
                        class="gp-inline-flex gp-items-center  [&>svg]:gp-h-full [&>svg]:gp-w-full">
                        <svg height="20px" width="20px" data-name="dot-outline-filled" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" fill="currentColor" data-id="508817731477569896">
              <path fill="currentColor" strokelinecap="round" strokelinejoin="round" d="M156,128a28,28,0,1,1-28-28A28,28,0,0,1,156,128Z"></path></svg>
                     </span>
                  </div>
                  
                  <div class="gp-inline-flex gp-items-center gp-relative gp-transition-all"
                     style="--pr:0px;--pr-tablet:0px;--pr-mobile:0px;text-wrap:wrap;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto;--maxw:unset;--maxw-tablet:unset;--maxw-mobile:unset">
                     <div
      class="gp-relative gp-overflow-hidden gem-marquee-item  gfSK-dnuDV gem-marquee-item-gfSK-dnuDV"
      style="--bg:transparent;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%);--b:none;--bw:1px 1px 1px 1px;--bc:#000000"
    >
      <div className="first-block gp-w-[0.01px]"></div>
      
    {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
    <div data-id="gj4W8biHa-"
        class="gj4W8biHa-"
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-3, line-3);--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pageType:GP_STATIC;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--pt:8px;--pb:8px;--dynamic-source-color:#F4F4F4"
        data-test-force-publish="1757425937240"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          data-gp-text
          class="gp-g-paragraph-1  gp-text-instant gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--tdt:auto;--b:none;--bc:#121212;--bw:1px 1px 1px 1px;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--weight:bold;--c:#FFFFFF;--tt:none;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden;--tdl:"
        >
          {{ section.settings.ggj4W8biHa-_text | replace: '$locationOrigin', locationOrigin }}
        </div>
      </div>
    </div>
    
    </div>
                     <span style="--c:#0171E3;--ml:6px;--ml-tablet:6px;--ml-mobile:6px;--mr:6px;--mr-tablet:6px;--mr-mobile:6px;--h:50px;--h-tablet:50px;--h-mobile:50px;--w:50px;--w-tablet:50px;--w-mobile:50px;--minw:50px;--minw-tablet:50px;--minw-mobile:50px"
                        class="gp-inline-flex gp-items-center  [&>svg]:gp-h-full [&>svg]:gp-w-full">
                        <svg height="20px" width="20px" data-name="dot-outline-filled" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" fill="currentColor" data-id="508817731477569896">
              <path fill="currentColor" strokelinecap="round" strokelinejoin="round" d="M156,128a28,28,0,1,1-28-28A28,28,0,0,1,156,128Z"></path></svg>
                     </span>
                  </div>
                  
               </div>
               
        </div>
        
      </div>
    </div>
    <div class="rfm-marquee placeholder-marquee" style="--play:running;--direction:normal;--delay:0s;--iteration-count:infinite;--min-width:auto;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto"></div>
  </div>
  
            </div>
         </div>
      </div>
   </div>
</gp-marquee>

    </div>

      
    </gp-row>
  
        </section>
      
  
    
    
{% schema %}
  {
    
    "name": "Section 8",
    "tag": "section",
    "class": "gps-580629161039627177 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=578718746651132539&sectionId=580629161039627177)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"gglIKvhM2oA_text","label":"gglIKvhM2oA_text","default":"FREE SHIPPING"},{"type":"html","id":"ggcPOerbqI0_text","label":"ggcPOerbqI0_text","default":"30 DAYS MONEY BACK GUARANTEE"},{"type":"html","id":"ggKapNWUYaA_text","label":"ggKapNWUYaA_text","default":"LIMITED TIME 50% OFF SALE"},{"type":"html","id":"ggj4W8biHa-_text","label":"ggj4W8biHa-_text","default":"<p>2 YEAR WARRANTY</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
  