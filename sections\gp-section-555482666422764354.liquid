

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-555482666422764354.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-555482666422764354.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-555482666422764354.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-555482666422764354.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-555482666422764354.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-555482666422764354.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-555482666422764354.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-555482666422764354.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-555482666422764354.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-555482666422764354.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-555482666422764354.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-555482666422764354.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-555482666422764354.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-555482666422764354.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-555482666422764354.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-555482666422764354.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-555482666422764354.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-555482666422764354.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-555482666422764354.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-555482666422764354.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-555482666422764354.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-555482666422764354.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-555482666422764354.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-555482666422764354.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-555482666422764354.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-555482666422764354.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-555482666422764354.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-555482666422764354.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-555482666422764354.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-555482666422764354.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-555482666422764354.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-555482666422764354.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-555482666422764354.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-555482666422764354.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-555482666422764354.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-555482666422764354.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-555482666422764354.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-555482666422764354.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-555482666422764354.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-555482666422764354.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-555482666422764354.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-555482666422764354.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-555482666422764354.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-555482666422764354.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-555482666422764354.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-555482666422764354.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-555482666422764354.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-555482666422764354.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-555482666422764354.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-555482666422764354.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-555482666422764354.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-555482666422764354.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-555482666422764354.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-555482666422764354.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-555482666422764354.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-555482666422764354.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-555482666422764354.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-555482666422764354.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-555482666422764354.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-555482666422764354.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-555482666422764354.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-555482666422764354.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-555482666422764354.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-555482666422764354.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-555482666422764354.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-555482666422764354.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-555482666422764354.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-555482666422764354.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-555482666422764354.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-555482666422764354.gps.gpsil [style*="--o-mobile:"]{order:var(--o-mobile)}.gps-555482666422764354.gps.gpsil [style*="--pc-mobile:"]{place-content:var(--pc-mobile)}.gps-555482666422764354.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-555482666422764354.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-555482666422764354.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-555482666422764354.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-555482666422764354.gps.gpsil [style*="--ta-mobile:"]{text-align:var(--ta-mobile)}.gps-555482666422764354.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-555482666422764354.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-555482666422764354 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-555482666422764354 .gp-relative{position:relative}.gps-555482666422764354 .gp-z-1{z-index:1}.gps-555482666422764354 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-555482666422764354 .gp-mb-0{margin-bottom:0}.gps-555482666422764354 .gp-flex{display:flex}.gps-555482666422764354 .gp-inline-flex{display:inline-flex}.gps-555482666422764354 .gp-grid{display:grid}.gps-555482666422764354 .gp-contents{display:contents}.gps-555482666422764354 .\!gp-hidden{display:none!important}.gps-555482666422764354 .gp-hidden{display:none}.gps-555482666422764354 .gp-h-auto{height:auto}.gps-555482666422764354 .gp-h-full{height:100%}.gps-555482666422764354 .gp-w-full{width:100%}.gps-555482666422764354 .gp-max-w-full{max-width:100%}.gps-555482666422764354 .gp-flex-none{flex:none}.gps-555482666422764354 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-555482666422764354 .gp-flex-col{flex-direction:column}.gps-555482666422764354 .gp-items-center{align-items:center}.gps-555482666422764354 .gp-justify-center{justify-content:center}.gps-555482666422764354 .gp-gap-y-0{row-gap:0}.gps-555482666422764354 .gp-overflow-hidden{overflow:hidden}.gps-555482666422764354 .gp-break-words{overflow-wrap:break-word}.gps-555482666422764354 .gp-rounded-none{border-radius:0}.gps-555482666422764354 .gp-text-center{text-align:center}.gps-555482666422764354 .gp-leading-\[0\]{line-height:0}.gps-555482666422764354 .gp-text-g-line-3{color:var(--g-c-line-3)}.gps-555482666422764354 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-555482666422764354 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-555482666422764354 .gp-no-underline{text-decoration-line:none}.gps-555482666422764354 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555482666422764354 .gp-duration-200{transition-duration:.2s}.gps-555482666422764354 .gp-duration-300{transition-duration:.3s}.gps-555482666422764354 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555482666422764354 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-555482666422764354 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-555482666422764354 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-555482666422764354 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-555482666422764354 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-555482666422764354 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-555482666422764354 .tablet\:\!gp-hidden{display:none!important}.gps-555482666422764354 .tablet\:gp-hidden{display:none}.gps-555482666422764354 .tablet\:gp-h-auto{height:auto}.gps-555482666422764354 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-555482666422764354 .mobile\:\!gp-hidden{display:none!important}.gps-555482666422764354 .mobile\:gp-hidden{display:none}.gps-555482666422764354 .mobile\:gp-h-auto{height:auto}.gps-555482666422764354 .mobile\:gp-flex-none{flex:none}}.gps-555482666422764354 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-555482666422764354 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}@media (max-width:1024px){.gps-555482666422764354 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-555482666422764354 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-555482666422764354 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-555482666422764354 .\[\&_p\]\:gp-inline p{display:inline}.gps-555482666422764354 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-555482666422764354 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-555482666422764354 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gfTEr2h6wm" data-id="gfTEr2h6wm"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-4xl);--pl:15px;--pb:var(--g-s-4xl);--pr:15px;--pt-mobile:12px;--pl-mobile:24px;--pb-mobile:2px;--pr-mobile:24px;--pt-tablet:var(--g-s-4xl);--pl-tablet:24px;--pb-tablet:var(--g-s-4xl);--pr-tablet:24px;--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gfTEr2h6wm gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gIfaoPL29N gp-relative gp-flex gp-flex-col"
    >
      
    {%- assign gpBkProduct = product -%}
    
        {%- liquid
          if request.page_type == 'product'
            if 'static' == 'static'
              if '14924340658558' == 'latest'
                paginate collections.all.products by 100000
                  assign product = collections.all.products | sort: 'created_at' | reverse | first
                endpaginate
              else
                assign product = all_products['gard-pro-health-smartwatch-3-1']
                assign productId = '14924340658558' | times: 1
                if product == empty or product == null
                  paginate collections.all.products by 100000
                    for item in collections.all.products
                      if item.id == productId
                        assign product = item
                      endif
                    endfor
                  endpaginate
                endif
              endif
            endif
          else
            if '14924340658558' == 'latest'
              paginate collections.all.products by 100000
                assign product = collections.all.products | sort: 'created_at'| reverse | first
              endpaginate
            else
              assign product = all_products['gard-pro-health-smartwatch-3-1']
              assign productId = '14924340658558' | times: 1
              if product == empty or product == null
                paginate collections.all.products by 100000
                  for item in collections.all.products
                    if item.id == productId
                      assign product = item
                    endif
                  endfor
                endpaginate
              endif
            endif
          endif
        -%}
      

    {%-if product != empty and product != null -%}
      {%- assign initVariantId =  -%}
      {%- assign product_form_id = 'product-form-' | append: "gQsB8S07P0" -%}
      {%- assign variant = product.selected_or_first_available_variant -%}
      {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%-if productSelectedVariant == empty or productSelectedVariant == null -%}
        {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      {%-if variant == empty or variant == null -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      <gp-product data-uid="gQsB8S07P0" data-id="gQsB8S07P0"   gp-context='{"productId": {{ product.id }}, "preSelectedOptionIds": [], "isSyncProduct": "", "variantSelected": {{ variant | json | escape }}, "inventoryQuantity": {{ variant.inventory_quantity }}, "quantity": 1, "formId": "{{ product_form_id }}" }'
        gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }}}'>
        <product-form class="product-form">
          {%- form 'product', product, id: product_form_id, class: 'form contents', data-type: 'add-to-cart-form', autocomplete: 'off'  -%}
            <input type="hidden" name="id" value="{{ variant.id }}" />
            <input type="hidden" name="quantity" value="{{ quantity }}" />
            <button type="submit" onclick="return false;" style="display:none;"></button>
            
       
      
    <div
      id="gQsB8S07P0" data-id="gQsB8S07P0-row"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:30px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%"
        class="gQsB8S07P0 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gW60qXc7Jq gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gLXVk9Dqdq" data-id="gLXVk9Dqdq"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-3xl);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gLXVk9Dqdq gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="ggnSUc0nS6 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gUm-k8fY2d">
    <div
      parentTag="Col"
        class="gUm-k8fY2d "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:600px;--w-tablet:600px;--w-mobile:600px;--ta:center;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:40px;--size-tablet:30px;--size-mobile:22px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggUm-k8fY2d_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gDWaDGfaLt">
    <div
      parentTag="Col"
        class="gDWaDGfaLt "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:20px"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:600px;--w-tablet:600px;--w-mobile:600px;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#424242;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:19px;--size-tablet:18px;--size-mobile:16px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggDWaDGfaLt_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gMUp_hdtDK" data-id="gMUp_hdtDK"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:62px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gMUp_hdtDK gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:space-between;--o:1;--o-mobile:1"
      class="gO8X68EyBF gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gg3W-RJMwN"
    role="presentation"
    class="gp-group/image gg3W-RJMwN gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb-mobile:39px;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/d50776ee31ae713f63969245a4c9631e_768x.png?v=1722521955" />
      <source media="(max-width: 1024px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/d50776ee31ae713f63969245a4c9631e_1024x.png?v=1722521955" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/d50776ee31ae713f63969245a4c9631e.png?v=1722521955"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--aspect-tablet:auto;--aspect-mobile:auto;--objf:cover;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--bblr:12px;--bbrr:12px;--btlr:12px;--btrr:12px;--radiusType:custom;--shadow:none"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:space-between;--o:0;--o-mobile:0"
      class="gORZciShLC gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="glgsByaI6w" data-id="glgsByaI6w"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-3xl);--pt:0px;--pl:0px;--pb:0px;--pr:0px;--cg:var(--g-s-xl);--pc:start;--pc-mobile:center;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:rgba(251, 251, 251, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="glgsByaI6w gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gWbi3J8q6- gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] g-X3fvMHe8"
    >
      <div 
      data-id="g-X3fvMHe8"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:40px;--w-tablet:40px;--w-mobile:40px;--h:40px;--h-tablet:40px;--h-mobile:40px;--minw:40px;--minw-tablet:40px;--minw-mobile:40px;--height-desktop:40px;--height-tablet:40px;--height-mobile:40px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817656352801128">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M206,128a77.92,77.92,0,0,0-32.53-63.31L167.1,29.5A14,14,0,0,0,153.32,18H102.68A14,14,0,0,0,88.9,29.5L82.53,64.69a77.87,77.87,0,0,0,0,126.62L88.9,226.5A14,14,0,0,0,102.68,238h50.64a14,14,0,0,0,13.78-11.5l6.37-35.19A77.92,77.92,0,0,0,206,128ZM100.71,31.64a2,2,0,0,1,2-1.64h50.64a2,2,0,0,1,2,1.64l4.56,25.19a77.68,77.68,0,0,0-63.7,0Zm54.58,192.72a2,2,0,0,1-2,1.64H102.68a2,2,0,0,1-2-1.64l-4.56-25.19a77.68,77.68,0,0,0,63.7,0ZM128,194a66,66,0,1,1,66-66A66.08,66.08,0,0,1,128,194Zm46-66a6,6,0,0,1-6,6H128a6,6,0,0,1-6-6V88a6,6,0,0,1,12,0v34h34A6,6,0,0,1,174,128Z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gipFikpnCU gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmWEK0w9WE">
    <div
      parentTag="Col"
        class="gmWEK0w9WE "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s)"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:16px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggmWEK0w9WE_text | replace: '$locationOrigin', locationOrigin }}</h3>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g4cPPWsuDc">
    <div
      parentTag="Col"
        class="g4cPPWsuDc "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#000000;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg4cPPWsuDc_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gNLiqGFwm0" data-id="gNLiqGFwm0"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-3xl);--pt:0px;--pl:0px;--pb:0px;--pr:0px;--cg:var(--g-s-xl);--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:rgba(251, 251, 251, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gNLiqGFwm0 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="glonttr-lA gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] g7LcRDP4fp"
    >
      <div 
      data-id="g7LcRDP4fp"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:40px;--w-tablet:40px;--w-mobile:40px;--h:40px;--h-tablet:40px;--h-mobile:40px;--minw:40px;--minw-tablet:40px;--minw-mobile:40px;--height-desktop:40px;--height-tablet:40px;--height-mobile:40px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817609073033576">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M248,122H238V88a14,14,0,0,0-14-14H206V64a14,14,0,0,0-14-14H168a14,14,0,0,0-14,14v58H102V64A14,14,0,0,0,88,50H64A14,14,0,0,0,50,64V74H32A14,14,0,0,0,18,88v34H8a6,6,0,0,0,0,12H18v34a14,14,0,0,0,14,14H50v10a14,14,0,0,0,14,14H88a14,14,0,0,0,14-14V134h52v58a14,14,0,0,0,14,14h24a14,14,0,0,0,14-14V182h18a14,14,0,0,0,14-14V134h10a6,6,0,0,0,0-12ZM32,170a2,2,0,0,1-2-2V88a2,2,0,0,1,2-2H50v84Zm58,22a2,2,0,0,1-2,2H64a2,2,0,0,1-2-2V64a2,2,0,0,1,2-2H88a2,2,0,0,1,2,2Zm104,0a2,2,0,0,1-2,2H168a2,2,0,0,1-2-2V64a2,2,0,0,1,2-2h24a2,2,0,0,1,2,2Zm32-24a2,2,0,0,1-2,2H206V86h18a2,2,0,0,1,2,2Z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g-u_-xWHpp gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g9UhO9V965">
    <div
      parentTag="Col"
        class="g9UhO9V965 "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s)"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:16px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg9UhO9V965_text | replace: '$locationOrigin', locationOrigin }}</h3>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g0xOpI3tgU">
    <div
      parentTag="Col"
        class="g0xOpI3tgU "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#000000;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg0xOpI3tgU_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="gv6PgTfswO" data-id="gv6PgTfswO"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-3xl);--pt:0px;--pl:0px;--pb:0px;--pr:0px;--cg:var(--g-s-xl);--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:rgba(251, 251, 251, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gv6PgTfswO gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g2LT1qEDI9 gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gBbdTf2nbj"
    >
      <div 
      data-id="gBbdTf2nbj"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:40px;--w-tablet:40px;--w-mobile:40px;--h:40px;--h-tablet:40px;--h-mobile:40px;--minw:40px;--minw-tablet:40px;--minw-mobile:40px;--height-desktop:40px;--height-tablet:40px;--height-mobile:40px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817619858555240">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M95.31,101.2a57.37,57.37,0,0,1,0,53.6,6,6,0,0,1-10.62-5.6,44.75,44.75,0,0,0,0-42.4,6,6,0,1,1,10.62-5.6Zm47.86-34.49a6,6,0,0,0-2.46,8.12,112.67,112.67,0,0,1,0,106.34,6,6,0,1,0,10.58,5.66,124.65,124.65,0,0,0,0-117.66A6,6,0,0,0,143.17,66.71Zm-28,16a6,6,0,0,0-2.48,8.12,79,79,0,0,1,0,74.36,6,6,0,0,0,10.6,5.64,91,91,0,0,0,0-85.64A6,6,0,0,0,115.18,82.7ZM230,128A102,102,0,1,1,128,26,102.12,102.12,0,0,1,230,128Zm-12,0a90,90,0,1,0-90,90A90.1,90.1,0,0,0,218,128Z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gtU0xyBOIu gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gHbnAM0hPp">
    <div
      parentTag="Col"
        class="gHbnAM0hPp "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s)"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:16px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggHbnAM0hPp_text | replace: '$locationOrigin', locationOrigin }}</h3>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gahQM535Wg">
    <div
      parentTag="Col"
        class="gahQM535Wg "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggahQM535Wg_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      parentTag="Col" id="g56XOFG4-s" data-id="g56XOFG4-s"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-3xl);--pt:0px;--pl:0px;--pb:0px;--pr:0px;--mb-mobile:var(--g-s-2xl);--cg:var(--g-s-xl);--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:rgba(251, 251, 251, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g56XOFG4-s gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gQHK8C2vT2 gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gOwZzDZ1r6"
    >
      <div 
      data-id="gOwZzDZ1r6"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:40px;--w-tablet:40px;--w-mobile:40px;--h:40px;--h-tablet:40px;--h-mobile:40px;--minw:40px;--minw-tablet:40px;--minw-mobile:40px;--height-desktop:40px;--height-tablet:40px;--height-mobile:40px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817609073688936">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M200,58H32A22,22,0,0,0,10,80v96a22,22,0,0,0,22,22H200a22,22,0,0,0,22-22V80A22,22,0,0,0,200,58Zm10,118a10,10,0,0,1-10,10H32a10,10,0,0,1-10-10V80A10,10,0,0,1,32,70H200a10,10,0,0,1,10,10Zm44-80v64a6,6,0,0,1-12,0V96a6,6,0,0,1,12,0ZM137.1,124.85a6,6,0,0,1,.27,5.83l-16,32a6,6,0,1,1-10.74-5.36L122.29,134H100a6,6,0,0,1-5.37-8.68l16-32a6,6,0,0,1,10.74,5.36L109.71,122H132A6,6,0,0,1,137.1,124.85Z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gVUUT7eHE_ gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gIzdrOduS7">
    <div
      parentTag="Col"
        class="gIzdrOduS7 "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s)"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text-g-text-2 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:19px;--size-tablet:19px;--size-mobile:16px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggIzdrOduS7_text | replace: '$locationOrigin', locationOrigin }}</h3>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g0ViVxzTM3">
    <div
      parentTag="Col"
        class="g0ViVxzTM3 "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg0ViVxzTM3_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:0px;--mb-mobile:11px;--ta:left"
    
  >
    <style>
    .gqJkMZhoOX.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      
    }

    .gqJkMZhoOX:hover::before {
      
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .gqJkMZhoOX:hover .gp-button-icon {
      color: undefined;
    }

     .gqJkMZhoOX .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gqJkMZhoOX:hover .gp-button-price {
      color: undefined;
    }

    .gqJkMZhoOX .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gqJkMZhoOX .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gqJkMZhoOX:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#g5UWRTEtPs" target="_self" data-id="gqJkMZhoOX" aria-label="<p>SHOP THE FEMALE COLLECTION</p>"
      
      data-state="idle"
      class="gqJkMZhoOX gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center"
      style="--hvr-bg:#1180ff;--bg:#096de3;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--w:100%;--w-tablet:Auto;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:32px;--pr:32px;--pt:12px;--pb:12px;--pl-tablet:32px;--pr-tablet:32px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:32px;--pr-mobile:32px;--pt-mobile:12px;--pb-mobile:12px;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--fs:normal;--weight:600;--ls:1px;--lh-tablet:150%"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggqJkMZhoOX_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
    </div>
   
    
    </div>
    </div>
   
    
          {%- endform -%}
        </product-form>
      </gp-product>
      {%- assign product = gpBkProduct -%}
    {% else %}
      <div class="gp-text-center">{{ "gempages.Product.product_not_found" | t }}</div>
    {%- endif -%}
     <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 4",
    "tag": "section",
    "class": "gps-555482666422764354 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555482666321838914&sectionId=555482666422764354)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggUm-k8fY2d_text","label":"ggUm-k8fY2d_text","default":"SEAMLESS PERFORMANCE FOR EVERY ADVENTURE"},{"type":"html","id":"ggDWaDGfaLt_text","label":"ggDWaDGfaLt_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:18px;\">Designed for durability and smart connectivity, our waterproof wristwatches for ladies keep you going, whether you're hitting the gym or exploring the outdoors.</span></p>"},{"type":"html","id":"ggmWEK0w9WE_text","label":"ggmWEK0w9WE_text","default":"<strong>Advanced Health Tracking</strong>"},{"type":"html","id":"gg4cPPWsuDc_text","label":"gg4cPPWsuDc_text","default":"<p>Monitors sleep, mood, stress, heart rate, and more with 36% improved accuracy.</p>"},{"type":"html","id":"gg9UhO9V965_text","label":"gg9UhO9V965_text","default":"<strong>Extensive Fitness Features</strong>"},{"type":"html","id":"gg0xOpI3tgU_text","label":"gg0xOpI3tgU_text","default":"<p>Offers 110+ sports modes, pedometer, and calorie tracking.</p>"},{"type":"html","id":"ggHbnAM0hPp_text","label":"ggHbnAM0hPp_text","default":"<strong>Smart Connectivity and Productivity Tools</strong>"},{"type":"html","id":"ggahQM535Wg_text","label":"ggahQM535Wg_text","default":"<p><span style=\"color:#212121;\">Includes calling, NFC, notifications, and tools like alarms.</span></p>"},{"type":"html","id":"ggIzdrOduS7_text","label":"ggIzdrOduS7_text","default":"<strong>Long-Lasting Battery</strong>"},{"type":"html","id":"gg0ViVxzTM3_text","label":"gg0ViVxzTM3_text","default":"<p><span style=\"color:#000000;\">Designed to keep running for an extended period, so you can rely on it for days without worrying about constantly recharging.</span></p>"},{"type":"html","id":"ggqJkMZhoOX_label","label":"ggqJkMZhoOX_label","default":"<p>SHOP THE FEMALE COLLECTION</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
