<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    {{ 'maison-manual.css' | asset_url | stylesheet_tag }}
    <title>{{ page_title | escape }}</title>
    {% if page_description %}
      <meta name="description" content="{{ page_description | escape }}">
    {% endif %}
    {{ content_for_header }}
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
      [id*='__main'] {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        min-height: 100%;
        overflow: scroll;
      }

      {% comment %} .product-selector {
        display: none;
      } {% endcomment %}
    </style>
  </head>
  <body x-data="manualApp()" x-init="init()" :data-theme="darkMode ? 'dark' : 'light'">
    {{ content_for_layout }}
  </body>
</html>
