<div
      label="Block" tag="Col" type="component"
      style="--jc:start;--d-mobile:none"
      class="g_k3BAT_lT gp-relative gp-flex gp-flex-col"
    >
      
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gcQ_VBi6Aq gp-relative gp-flex gp-flex-col"
    >
      
    {%- assign gpBkProduct = product -%}
    
        {%- liquid
          if request.page_type == 'product'
            if 'static' == 'static'
              if '14922687119742' == 'latest'
                paginate collections.all.products by 100000
                  assign product = collections.all.products | sort: 'created_at' | reverse | first
                endpaginate
              else
                assign product = all_products['gard-pro-ultra-2-plus2']
                assign productId = '14922687119742' | times: 1
                if product == empty or product == null
                  paginate collections.all.products by 100000
                    for item in collections.all.products
                      if item.id == productId
                        assign product = item
                      endif
                    endfor
                  endpaginate
                endif
              endif
            endif
          else
            if '14922687119742' == 'latest'
              paginate collections.all.products by 100000
                assign product = collections.all.products | sort: 'created_at'| reverse | first
              endpaginate
            else
              assign product = all_products['gard-pro-ultra-2-plus2']
              assign productId = '14922687119742' | times: 1
              if product == empty or product == null
                paginate collections.all.products by 100000
                  for item in collections.all.products
                    if item.id == productId
                      assign product = item
                    endif
                  endfor
                endpaginate
              endif
            endif
          endif
        -%}
      

    {%-if product != empty and product != null -%}
      {%- assign initVariantId = 54999689429374 -%}
      {%- assign product_form_id = 'product-form-' | append: "gtK0apkthr" -%}
      {%- assign variant = product.variants | where: 'id', initVariantId | first -%}
      {%- assign productSelectedVariant = product.variants | where: 'id', initVariantId | first -%}
      {%-if productSelectedVariant == empty or productSelectedVariant == null -%}
        {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      {%-if variant == empty or variant == null -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      <gp-product data-uid="gtK0apkthr" data-id="gtK0apkthr"   gp-context='{"productId": {{ product.id }}, "preSelectedOptionIds": [9242196902270], "isSyncProduct": "true", "variantSelected": {{ variant | json | escape }}, "inventoryQuantity": {{ variant.inventory_quantity }}, "quantity": 1, "formId": "{{ product_form_id }}" }'
        gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }}}'>
        <product-form class="product-form">
          {%- form 'product', product, id: product_form_id, class: 'form contents', data-type: 'add-to-cart-form', autocomplete: 'off'  -%}
            <input type="hidden" name="id" value="{{ variant.id }}" />
            <input type="hidden" name="quantity" value="{{ quantity }}" />
            <button type="submit" onclick="return false;" style="display:none;"></button>
            
       
      
    <div
      id="gtK0apkthr" data-id="gtK0apkthr-row"
        style="--bs:solid;--bw:0px 0px 1px 0px;--bc:#E0E0E0;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pb:32px;--mb-mobile:21px;--cg:30px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%"
        class="gtK0apkthr gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gJG0a3pIOC gp-relative gp-flex gp-flex-col"
    >
      
  {% assign featured_image = product.featured_image %}
  {%- if variant != null and variant.featured_image != null -%}
  {% assign featured_image = variant.featured_image %}
  {%- endif -%}
    <gp-product-images-v2
      gp-data='
    {
      "id":"g_EcRmwrDz",
      "pageContext": {"pageType":"GP_STATIC","sectionName":"","isPreviewing":false,"isOptimizePlan":false,"isTranslateWithLocale":false,"isLazyLoadSection":true,"isLazyLoadImage":false,"hasCollectionHandle":true,"numberOfProductOfProductList":0,"pageBackground":"","fontType":"google","primaryLocale":"","enableLazyLoadImage":false},
      "setting":{"arrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","arrowIconColor":"#000000","arrowNavBorder":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"},"arrowNavRadius":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"borderActive":{"border":"solid","borderType":"none","borderWidth":"1px","color":"#000000","isCustom":"false","width":"1px 1px 1px 1px"},"clickOpenLightBox":{"desktop":false},"dragToScroll":true,"ftArrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","ftArrowIconColor":"#000000","ftArrowNavBorder":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"},"ftArrowNavRadius":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"ftClickOpenLightBox":{"desktop":"product-link"},"ftDotActiveColor":{"desktop":"line-3"},"ftDotColor":{"desktop":"bg-1"},"ftDotGapToCarousel":{"desktop":16},"ftDotSize":{"desktop":12},"ftDotStyle":{"desktop":"none"},"ftDragToScroll":true,"ftLoop":{"desktop":false},"ftNavigationPosition":{"desktop":"none"},"ftPauseOnHover":true,"ftSpeed":1,"galleryHoverEffect":"none","galleryZoom":150,"galleryZoomType":"default","hoverEffect":"none","loop":{"desktop":true},"navigationPosition":{"desktop":"inside"},"otherImage":0,"pauseOnHover":true,"preDisplay":"1st-available-variant","preload":true,"qualityPercent":{"desktop":100},"qualityType":{"desktop":"finest"},"speed":1,"type":{"desktop":"slider"},"typeDisplay":"all-images","zoom":150,"zoomType":"default"},
      "styles":{"align":{"desktop":"flex-start"},"corner":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"ftCorner":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"ftLayout":{"desktop":"cover"},"ftShape":{"desktop":{"gap":"","height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":""}},"itemSpacing":{"desktop":"var(--g-s-s)","mobile":"var(--g-s-s)"},"layout":{"desktop":"cover"},"position":{"desktop":"only-feature","mobile":"only-feature"},"ratioLayout":{"desktop":[2,10]},"ratioLayoutRight":{"desktop":[10,2]},"shape":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"shapeFor1Col":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"shapeFor2Col":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"50%"}},"shapeForBottom":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"}},"shapeForFtOnly":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"spacing":{"desktop":"var(--g-s-s)","mobile":"var(--g-s-s)"}}, "productUrl":{{product.url | json | escape}}, "product":{{product | json | escape}}, "collectionUrl": {{ collection.url | json | escape }}, "collection": {{ collection | json | escape}}
    }
  '
      section-id="{{section.id}}"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:24px;--mb-mobile:7px"
      data-id="g_EcRmwrDz"
      class="g_EcRmwrDz gp-overflow-hidden"
    >
      <div
        class="{%- if product.media.size > 1 -%} gp-grid gp-w-full !gp-m-0 gp-relative {%- else -%} gp-w-full !gp-m-0 gp-relative {%- endif -%}"
        {%- if product.media.size > 1 -%}
        style="--gtc:minmax(0, 12fr);--gtc-tablet:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--gg:var(--g-s-s);--gg-mobile:var(--g-s-s)"
        {%- else -%}
        style="--gtc:minmax(0, 12fr);--gg:var(--g-s-s);--gg-mobile:var(--g-s-s)"
        {%- endif -%}
      >
        
    {% capture featureImageOnlyOne %}
      
        {% assign featureMedia = variant.featured_media %}
        {% unless featureMedia %}
        {% assign featureMedia = product.featured_media %}
        {% endunless %}
        {% if product.media.size > 1 %}
          
          {% assign featureMedia = variant.featured_media %}
          {% unless featureMedia %}
          {% assign featureMedia = product.featured_media %}
          {% endunless %}
        
        {% endif %}
        {% assign largestRatio = 0 %}
        {% assign height = featureMedia.height | times: 1.0 %}
        {% assign width = featureMedia.width | times: 1.0 %}
        {% assign ratio = height | divided_by: width %}
        {% if ratio > largestRatio %}
          {% assign largestRatio = ratio %}
        {% endif %}
        {% assign productImageWidth = 0 %}
        {% case featureMedia.media_type %}
          {% when 'image' %}
            {% assign productImageWidth = featureMedia.width %}
          {% else %}
            {% assign productImageWidth = featureMedia.preview_image.width %}
        {% endcase %}
        {% if featureMedia == null %}
        {% assign productImageWidth = 1600 %}
        {% endif %}
        <div 
          class='gp-feature-image-carousel gp-feature-image-only' 
          style="--o:0;--o-tablet:0;--o-mobile:0;--d:flex;--d-mobile:flex;--d-tablet:flex;--jc:flex-start"
        >
          <div 
            class="gp-relative"
            style="--w:100%;--w-tablet:100%;--w-mobile:100%"
          >
            
      
    
            <div 
              type="gp-feature-image-only"
              product-id="{{product.id}}"
              product-media="{{product.media.size}}"
              class="gp-image-item gp-ft-image-item gp-group gp-z-0 gp-flex !gp-max-w-full !gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-featured-image-wrapper"
              style="--h:auto;--h-tablet:auto;--h-mobile:auto;width:{{productImageWidth}}px"
            >
              <div 
                class="gp-w-full  {% if featureMedia == null or featureMedia.media_type == 'image' %} {{ 'gp-h-0' }} {% else %} {{ 'gp-h-full !gp-pb-0' }} {% endif %} gp-relative" 
                style="--pb: calc((1/1)*100%);--pb-tablet: calc((1/1)*100%);--pb-mobile: calc((1/1)*100%); {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %} {{ 'display: flex; align-items: center; justify-content: center' }} {% endif %}"
              >
                
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity {{shouldHidden}}"
      src="{{src | image_url}}" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}"
      fetchpriority="high"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset="{{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w"
      sizes="(max-width: 768px) 50vw, 100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
      
      
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{featureMedia.alt}}"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{featureMedia.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
      {% when 'model' %}
        
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%"
      
      
      
      
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}"
      alt="{{featureMedia.preview_image.alt}}"
      camera-controls="true"
      poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
  <img
      
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none"
      src width="2237" height="1678" alt="No Image"
      fetchpriority="high"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset=""
      sizes="(max-width: 768px) 50vw, 100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
    {% endcase %}
    
              </div>
            </div>
          </div>
        </div>
      
    {% endcapture %}
    {% if product.media.size > 1 %}
      
      
    <gp-carousel data-id="gp-carousel-g_EcRmwrDz" type="gp-feature-image-carousel" product-id="{{product.id}}" product-media="{{product.media.size}}" id="gp-root-carousel-ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}-{{section.id}}" class="
          gp-px-0 tablet:gp-px-0 mobile:gp-px-0
          gp-flex-1 gp-w-full gp-feature-image-carousel
          gp-group/carousel gp-flex" gp-data='{"id":"ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}-{{section.id}}","setting":{"loop":{"desktop":false},"slidesToShow":{"desktop":1},"dotStyle":{"desktop":"none","tablet":"none","mobile":"none"},"dotSize":{"desktop":12},"dotGapToCarousel":{"desktop":16},"dotColor":{"desktop":"bg-1"},"dotActiveColor":{"desktop":"line-3"},"controlOverContent":{"desktop":false,"tablet":false,"mobile":false},"enableDrag":{"desktop":true,"tablet":true,"mobile":true},"arrowCustom":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","arrowCustomColor":"#000000","arrowBorder":{"desktop":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"}},"roundedArrow":{"desktop":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"}},"sneakPeakType":{"desktop":"center"},"arrowGapToEachSide":"16","navigationStyle":{"desktop":"none"},"arrowButtonSize":{"desktop":{"width":"24px","height":"24px"}}},"styles":{"sizeSetting":{"desktop":{"width":"100%","height":"auto"},"tablet":{"width":"100%","height":"auto"},"mobile":{"width":"100%","height":"auto"}},"align":{"desktop":"flex-start","tablet":"flex-start","mobile":"flex-start"}},"isHiddenArrowWhenDisabled":true}' style="--jc:flex-start;--o:0;--o-tablet:0;--o-mobile:0;--d:flex;--d-mobile:flex;--d-tablet:flex">
      <div class="gp-hidden gp-aspect-square gp-w-[12px] gp-rounded-full gp-shadow-md"></div>
      <div
        
        class="gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block gp-carousel-g_EcRmwrDz gp-featured-image-wrapper"
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      >
        <div
          class="gp-h-full gp-relative gp-mx-auto gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row"
          style="--h:100%;--h-tablet:100%;--h-mobile:100%;gap:16px"
        >
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}} gp-carousel-action-back gem-slider-previous ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}-{{section.id}} gp-carousel-arrow-gp-carousel-g_EcRmwrDz gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--left:initial;--top:initial;--right:initial;--bottom:;--left-tablet:initial;--top-tablet:initial;--right-tablet:initial;--bottom-tablet:;--left-mobile:initial;--top-mobile:initial;--right-mobile:initial;--bottom-mobile:;--d:none;--d-tablet:none;--d-mobile:none;background-color:;--w:24px;--h:24px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
  style="--c:#000000;--w:undefinedpx;--w-tablet:undefinedpx;--w-mobile:undefinedpx;--h:undefinedpx;--h-tablet:undefinedpx;--h-mobile:undefinedpx"
  >
    <svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z" fill="currentColor"/>
    </svg>
    </div>
      <style>
    .ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g_EcRmwrDz {
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    .ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g_EcRmwrDz::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g_EcRmwrDz {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g_EcRmwrDz::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
    @media only screen and (max-width: 768px) {
      .ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g_EcRmwrDz {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g_EcRmwrDz::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
  </style>
    </button>
  
          <div id="gp-carousel-ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}-{{section.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full"
          style="--cg-mobile:undefinedpx;--cg-tablet:undefinedpx;--cg:undefinedpx">
          
          {%- if product.media.size > 0 -%}
            
      
    
            {% assign largestRatio = 0 %}
            {% for featureMedia in product.media %}
              {% assign height = featureMedia.height | times: 1.0 %}
              {% assign width = featureMedia.width | times: 1.0 %}
              {% assign ratio = height | divided_by: width %}
              {% if ratio > largestRatio %}
                {% assign largestRatio = ratio %}
              {% endif %}
            {% endfor %}
            {%- for featureMedia in product.media -%}
              {%- if featureMedia.media_type == 'image' -%}
                {%- for image in product.images -%}
                  {% if image.src == featureMedia.src %}
                    {% assign imageID = image.id %}
                    {% break %}
                  {% endif%}
                {% endfor %}
              {%- else -%}
                {% assign imageID = '' %}
              {%- endif -%}
              
    {% assign productImageWidth = 0 %}
    {% case featureMedia.media_type %}
      {% when 'image' %}
        {% assign productImageWidth = featureMedia.width %}
      {% else %}
        {% assign productImageWidth = featureMedia.preview_image.width %}
    {% endcase %}
    
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      id="{{section.id}}-{{imageID}}"
      style="width:{{productImageWidth}}px;--minw:calc(100% / 4 - 33px);--minw-tablet:calc(100% / 4 - 33px);--minw-mobile:calc(100% / 4 - 33px);--maxw:calc(100% / 4 - 33px);--maxw-tablet:calc(100% / 4 - 33px);--maxw-mobile:calc(100% / 4 - 33px);outline-color:var(--g-c-brand, brand);--h:auto;--h-tablet:auto;--h-mobile:auto;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}} gp-child-item-undefined gp-group gp-z-0 gp-flex !gp-min-w-full !gp-max-w-full gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-outline-1 -gp-outline-offset-1 gp-image-item gp-ft-image-item data-[outline=active]:gp-outline undefined"
      data-index=""
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      
      <div 
        class="gp-w-full  {% if featureMedia == null or featureMedia.media_type == 'image'  %} {{ 'gp-h-0' }} {% else %} {{ 'gp-h-full !gp-pb-0' }} {% endif %} gp-relative" 
        style="--pb: calc((1/1)*100%);--pb-tablet: calc((1/1)*100%);--pb-mobile: calc((1/1)*100%); {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %} {{ 'display: flex; align-items: center; justify-content: center' }} {% endif %}"
      >
        
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity {{shouldHidden}}"
      src="{{src | image_url}}" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}"
      fetchpriority="high"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset="{{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w"
      sizes="(max-width: 768px) 50vw, 100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
      
      
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{featureMedia.alt}}"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{featureMedia.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
      {% when 'model' %}
        
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%"
      
      
      
      
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}"
      alt="{{featureMedia.preview_image.alt}}"
      camera-controls="true"
      poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
  <img
      
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none"
      src width="2237" height="1678" alt="No Image"
      fetchpriority="high"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset=""
      sizes="(max-width: 768px) 50vw, 100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover"
    />
  
    {% endcase %}
    
      </div>
      
      </div>
    </div>
  
            {% endfor %}
          {%- else -%}
            
  <img
      id="noImageError"
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full featured-image-only !gp-rounded-none"
      src="{{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}" width="480" height="480" alt="no image"
      fetchpriority="high"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset=""
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:cover;height:100%"
    />
  
          {%- endif -%}
        
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}} gp-carousel-action-next gem-slider-next ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}-{{section.id}} gp-carousel-arrow-gp-carousel-g_EcRmwrDz gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--left:initial;--top:;--right:initial;--bottom:initial;--left-tablet:initial;--top-tablet:;--right-tablet:initial;--bottom-tablet:initial;--left-mobile:initial;--top-mobile:;--right-mobile:initial;--bottom-mobile:initial;--d:none;--d-tablet:none;--d-mobile:none;background-color:;--w:24px;--h:24px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
  style="--c:#000000;--w:undefinedpx;--w-tablet:undefinedpx;--w-mobile:undefinedpx;--h:undefinedpx;--h-tablet:undefinedpx;--h-mobile:undefinedpx"
  >
    <svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z" fill="currentColor"/>
    </svg>
    </div>
      <style>
    .ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g_EcRmwrDz {
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    .ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g_EcRmwrDz::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g_EcRmwrDz {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g_EcRmwrDz::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
    @media only screen and (max-width: 768px) {
      .ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g_EcRmwrDz {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-g_EcRmwrDz::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
  </style>
    </button>
  
        </div>
        <div
    class="gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-carousel-dot-container gp-carousel-dot-container-ft-gp-carousel-g_EcRmwrDz-{{section.id}}-{{product.id}}-{{section.id}} gp-static gp-flex-row gp-left-0 gp-right-0 tablet:gp-static tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-static mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0"
    style="--bottom:16px;--bottom-tablet:16px;--bottom-mobile:16px;--d:none;--d-tablet:none;--d-mobile:none"
 ></div>
      </div>
    </gp-carousel>
    {% if product.media.size > 1 %} <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-carousel-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script> {% endif %}

  
    
      
    {% else %}
      {{ featureImageOnlyOne }}
    {% endif %}
  
         
      </div>
    </gp-product-images-v2>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-images-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="grRfw4d_jd gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gcA8eI01hz" data-id="gcA8eI01hz"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pl:0px;--pr:0px;--pl-mobile:7px;--pr-mobile:7px;--cg:8px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gcA8eI01hz gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gGZPnbYJNY gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:24px;--mb-mobile:21px" class="gEnrG_XvLO ">
      
    {%- unless product -%}
      <p>{{ "gempages.ProductTitle.product_not_found" | t }}</p>
    {%- else -%}
      
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gEnrG_XvLO">
    <div
      
        class="gEnrG_XvLO "
        
      >
      <div  >
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-product-title gp-text-g-text-2"
          style="--w:100%;--ts:none;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:var(--g-c-text-2, text-2);--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-heading, heading);--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--weight:bold;word-break:break-word;overflow:hidden"
        >{{ product.title }}</h3>
      </div>
    </div>
    </gp-text>
    
      
    {%- endunless -%}
  
      </div>
      <gp-product-price
        data-id="gIPRsxNRuX"
        class="gIPRsxNRuX gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden"
        gp-data='{"priceType":"regular","uid":"gIPRsxNRuX","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
        
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:9px"
        data-hidden="false"
      >
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-price-gIPRsxNRuX"
        class=" "
        
      >
      <div  >
        <div
          type="regular"
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-price gp-money gp-decoration-g-text-1 gp-text-g-text-2"
          style="--w:100%;--tdc:text-1;--tdt:1;--ta:center;--c:var(--g-c-text-2, text-2);--fs:normal;--ff:var(--g-font-heading, heading);--weight:600;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:18px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >
     {% if variant.price %} 
       {{ variant.price | money}}
     {% endif %}
   </div>
      </div>
    </div>
    </gp-text>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-price.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
   {%- liquid
      assign inventory_quantity = variant.inventory_quantity | default: 0
    -%}
    <gp-product-button
      class="gp-product-button"
      gp-data-wrapper="true"
      gp-label-out-of-stock="{{section.settings.ggPJfKj9oQy_outOfStockLabel}}"
      gp-label-unavailable="{{section.settings.ggPJfKj9oQy_unavailableLabel}}"
      gp-data='{"setting":{"actionEffect":"open-cart-drawer","errorMessage":"Cannot add product to cart","successMessage":"Add product to cart successfully","enableSuccessMessage":true,"enableErrorMessage":true,"label":"Add to cart","outOfStockLabel":"Out of stock","errorType":"built-in","customURL":{"link":"/cart","target":"_self"}},"styles":{"errorTypo":{"attrs":{"color":"error"},"type":"paragraph-2"},"successTypo":{"attrs":{"color":"success"},"type":"paragraph-2"}},"disabled":"{{variant.available}}","variantID":"{{variant.id}}","totalVariant":"{{product.variants.size}}"}' 
       gp-href="{{ request.origin }}{{ routes.root_url | split: '/' | join: '/' }}/cart"
       data-variant-selection-required-message="{{section.settings.ggPJfKj9oQy_variantSelectionRequiredMessage}}"
    >
        
  <gp-button >
  <div
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:center"
    class="{% unless variant.available %} !gp-hidden {% endunless %}"
  >
    <style>
    .gPJfKj9oQy.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 3px;
      border-bottom-right-radius: 3px;
      border-top-left-radius: 3px;
      border-top-right-radius: 3px;
      
    }

    .gPJfKj9oQy:hover::before {
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
    }

    .gPJfKj9oQy:hover .gp-button-icon {
      color: undefined;
    }

     .gPJfKj9oQy .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gPJfKj9oQy:hover .gp-button-price {
      color: undefined;
    }

    .gPJfKj9oQy .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gPJfKj9oQy .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gPJfKj9oQy:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="submit" data-id="gPJfKj9oQy" aria-label="Add to cart"
      name="add" gp-data-hidden="{% unless variant.available %}true{% endunless %}"
      data-state="idle"
      class="gPJfKj9oQy gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-atc tcustomizer-submit-button gp-g-paragraph-1"
      style="--hvr-bg:#1180ff;--bg:#0474F7;--bblr:3px;--bbrr:3px;--btlr:3px;--btrr:3px;--shadow:none;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:8px;--pb:8px;--c:var(--g-c-text-3, text-3);--size:15px;--size-tablet:16px;--size-mobile:14px;--weight:bold;--tt:uppercase"
    >
        <svg
          class="gp-invisible gp-absolute gp-h-5 gp-w-5 group-data-[state=loading]/button:gp-animate-spin group-data-[state=loading]/button:gp-visible"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="gp-opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="gp-opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:15px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggPJfKj9oQy_label }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

        
  <gp-button >
  <div
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:center"
    class="{% if variant.available %} !gp-hidden {% endif %}"
  >
    <style>
    .gPJfKj9oQy-sold-out.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 3px;
      border-bottom-right-radius: 3px;
      border-top-left-radius: 3px;
      border-top-right-radius: 3px;
      
    }

    .gPJfKj9oQy-sold-out:hover::before {
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
    }

    .gPJfKj9oQy-sold-out:hover .gp-button-icon {
      color: undefined;
    }

     .gPJfKj9oQy-sold-out .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gPJfKj9oQy-sold-out:hover .gp-button-price {
      color: undefined;
    }

    .gPJfKj9oQy-sold-out .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gPJfKj9oQy-sold-out .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gPJfKj9oQy-sold-out:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="button" data-id="gPJfKj9oQy" aria-label="{{section.settings.ggPJfKj9oQy_outOfStockLabel}}"
      gp-data-hidden="{% if variant.available %}true{% endif %}"
      data-state="idle"
      class="gPJfKj9oQy-sold-out gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-sold-out btn-disabled gp-opacity-30 gp-cursor-default gp-g-paragraph-1"
      style="--hvr-bg:#1180ff;--bg:#0474F7;--bblr:3px;--bbrr:3px;--btlr:3px;--btrr:3px;--shadow:none;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:8px;--pb:8px;--c:var(--g-c-text-3, text-3);--size:15px;--size-tablet:16px;--size-mobile:14px;--weight:bold;--tt:uppercase"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:15px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{section.settings.ggPJfKj9oQy_outOfStockLabel}}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

    </gp-product-button>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-button.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
   
    
    </div>
    </div>
   
    
          {%- endform -%}
        </product-form>
      </gp-product>
      {%- assign product = gpBkProduct -%}
    {% else %}
      <div class="gp-text-center">{{ "gempages.Product.product_not_found" | t }}</div>
    {%- endif -%}
     <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="geNdBVecfe gp-relative gp-flex gp-flex-col"
    >
      
    {%- assign gpBkProduct = product -%}
    
        {%- liquid
          if request.page_type == 'product'
            if 'static' == 'static'
              if '8397462372681' == 'latest'
                paginate collections.all.products by 100000
                  assign product = collections.all.products | sort: 'created_at' | reverse | first
                endpaginate
              else
                assign product = all_products['gard-pro-ultra']
                assign productId = '8397462372681' | times: 1
                if product == empty or product == null
                  paginate collections.all.products by 100000
                    for item in collections.all.products
                      if item.id == productId
                        assign product = item
                      endif
                    endfor
                  endpaginate
                endif
              endif
            endif
          else
            if '8397462372681' == 'latest'
              paginate collections.all.products by 100000
                assign product = collections.all.products | sort: 'created_at'| reverse | first
              endpaginate
            else
              assign product = all_products['gard-pro-ultra']
              assign productId = '8397462372681' | times: 1
              if product == empty or product == null
                paginate collections.all.products by 100000
                  for item in collections.all.products
                    if item.id == productId
                      assign product = item
                    endif
                  endfor
                endpaginate
              endif
            endif
          endif
        -%}
      

    {%-if product != empty and product != null -%}
      {%- assign initVariantId = 46701739213129 -%}
      {%- assign product_form_id = 'product-form-' | append: "gGd3T5HU89" -%}
      {%- assign variant = product.variants | where: 'id', initVariantId | first -%}
      {%- assign productSelectedVariant = product.variants | where: 'id', initVariantId | first -%}
      {%-if productSelectedVariant == empty or productSelectedVariant == null -%}
        {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      {%-if variant == empty or variant == null -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      <gp-product data-uid="gGd3T5HU89" data-id="gGd3T5HU89"   gp-context='{"productId": {{ product.id }}, "preSelectedOptionIds": [145701470537], "isSyncProduct": "true", "variantSelected": {{ variant | json | escape }}, "inventoryQuantity": {{ variant.inventory_quantity }}, "quantity": 1, "formId": "{{ product_form_id }}" }'
        gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }}}'>
        <product-form class="product-form">
          {%- form 'product', product, id: product_form_id, class: 'form contents', data-type: 'add-to-cart-form', autocomplete: 'off'  -%}
            <input type="hidden" name="id" value="{{ variant.id }}" />
            <input type="hidden" name="quantity" value="{{ quantity }}" />
            <button type="submit" onclick="return false;" style="display:none;"></button>
            
       
      
    <div
      id="gGd3T5HU89" data-id="gGd3T5HU89-row"
        style="--bs:solid;--bw:0px 0px 1px 0px;--bc:#E0E0E0;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pb:32px;--mb-mobile:21px;--cg:30px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%"
        class="gGd3T5HU89 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g81BLcm3yF gp-relative gp-flex gp-flex-col"
    >
      
  {% assign featured_image = product.featured_image %}
  {%- if variant != null and variant.featured_image != null -%}
  {% assign featured_image = variant.featured_image %}
  {%- endif -%}
    <gp-product-images-v2
      gp-data='
    {
      "id":"gxy0lZGh-X",
      "pageContext": {"pageType":"GP_STATIC","sectionName":"","isPreviewing":false,"isOptimizePlan":false,"isTranslateWithLocale":false,"isLazyLoadSection":true,"isLazyLoadImage":false,"hasCollectionHandle":true,"numberOfProductOfProductList":0,"pageBackground":"","fontType":"google","primaryLocale":"","enableLazyLoadImage":false},
      "setting":{"arrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","arrowIconColor":"#000000","arrowNavBorder":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"},"arrowNavRadius":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"borderActive":{"border":"solid","borderType":"none","borderWidth":"1px","color":"#000000","isCustom":"false","width":"1px 1px 1px 1px"},"clickOpenLightBox":{"desktop":false},"dragToScroll":true,"ftArrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","ftArrowIconColor":"#000000","ftArrowNavBorder":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"},"ftArrowNavRadius":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"ftClickOpenLightBox":{"desktop":"product-link"},"ftDotActiveColor":{"desktop":"line-3"},"ftDotColor":{"desktop":"bg-1"},"ftDotGapToCarousel":{"desktop":16},"ftDotSize":{"desktop":12},"ftDotStyle":{"desktop":"none"},"ftDragToScroll":true,"ftLoop":{"desktop":false},"ftNavigationPosition":{"desktop":"none"},"ftPauseOnHover":true,"ftSpeed":1,"galleryHoverEffect":"none","galleryZoom":150,"galleryZoomType":"default","hoverEffect":"none","loop":{"desktop":true},"navigationPosition":{"desktop":"inside"},"otherImage":0,"pauseOnHover":true,"preDisplay":"1st-available-variant","preload":true,"qualityPercent":{"desktop":100},"qualityType":{"desktop":"finest"},"speed":1,"type":{"desktop":"slider"},"typeDisplay":"all-images","zoom":150,"zoomType":"default"},
      "styles":{"align":{"desktop":"flex-start"},"corner":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"ftCorner":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"ftLayout":{"desktop":"contain"},"ftShape":{"desktop":{"gap":"","height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":""}},"itemSpacing":{"desktop":"var(--g-s-s)","mobile":"var(--g-s-s)"},"layout":{"desktop":"cover"},"position":{"desktop":"only-feature","mobile":"only-feature"},"ratioLayout":{"desktop":[2,10]},"ratioLayoutRight":{"desktop":[10,2]},"shape":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"shapeFor1Col":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"shapeFor2Col":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"50%"}},"shapeForBottom":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"}},"shapeForFtOnly":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"spacing":{"desktop":"var(--g-s-s)","mobile":"var(--g-s-s)"}}, "productUrl":{{product.url | json | escape}}, "product":{{product | json | escape}}, "collectionUrl": {{ collection.url | json | escape }}, "collection": {{ collection | json | escape}}
    }
  '
      section-id="{{section.id}}"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:24px;--mb-mobile:7px"
      data-id="gxy0lZGh-X"
      class="gxy0lZGh-X gp-overflow-hidden"
    >
      <div
        class="{%- if product.media.size > 1 -%} gp-grid gp-w-full !gp-m-0 gp-relative {%- else -%} gp-w-full !gp-m-0 gp-relative {%- endif -%}"
        {%- if product.media.size > 1 -%}
        style="--gtc:minmax(0, 12fr);--gtc-tablet:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--gg:var(--g-s-s);--gg-mobile:var(--g-s-s)"
        {%- else -%}
        style="--gtc:minmax(0, 12fr);--gg:var(--g-s-s);--gg-mobile:var(--g-s-s)"
        {%- endif -%}
      >
        
    {% capture featureImageOnlyOne %}
      
        {% assign featureMedia = variant.featured_media %}
        {% unless featureMedia %}
        {% assign featureMedia = product.featured_media %}
        {% endunless %}
        {% if product.media.size > 1 %}
          
          {% assign featureMedia = variant.featured_media %}
          {% unless featureMedia %}
          {% assign featureMedia = product.featured_media %}
          {% endunless %}
        
        {% endif %}
        {% assign largestRatio = 0 %}
        {% assign height = featureMedia.height | times: 1.0 %}
        {% assign width = featureMedia.width | times: 1.0 %}
        {% assign ratio = height | divided_by: width %}
        {% if ratio > largestRatio %}
          {% assign largestRatio = ratio %}
        {% endif %}
        {% assign productImageWidth = 0 %}
        {% case featureMedia.media_type %}
          {% when 'image' %}
            {% assign productImageWidth = featureMedia.width %}
          {% else %}
            {% assign productImageWidth = featureMedia.preview_image.width %}
        {% endcase %}
        {% if featureMedia == null %}
        {% assign productImageWidth = 1600 %}
        {% endif %}
        <div 
          class='gp-feature-image-carousel gp-feature-image-only' 
          style="--o:0;--o-tablet:0;--o-mobile:0;--d:flex;--d-mobile:flex;--d-tablet:flex;--jc:flex-start"
        >
          <div 
            class="gp-relative"
            style="--w:100%;--w-tablet:100%;--w-mobile:100%"
          >
            
      
    
            <div 
              type="gp-feature-image-only"
              product-id="{{product.id}}"
              product-media="{{product.media.size}}"
              class="gp-image-item gp-ft-image-item gp-group gp-z-0 gp-flex !gp-max-w-full !gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-featured-image-wrapper"
              style="--h:auto;--h-tablet:auto;--h-mobile:auto;width:{{productImageWidth}}px"
            >
              <div 
                class="gp-w-full  {% if featureMedia == null or featureMedia.media_type == 'image' %} {{ 'gp-h-0' }} {% else %} {{ 'gp-h-full !gp-pb-0' }} {% endif %} gp-relative" 
                style="--pb: calc((1/1)*100%);--pb-tablet: calc((1/1)*100%);--pb-mobile: calc((1/1)*100%); {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %} {{ 'display: flex; align-items: center; justify-content: center' }} {% endif %}"
              >
                
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity {{shouldHidden}}"
      src="{{src | image_url}}" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}"
      fetchpriority="high"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset="{{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w"
      sizes="(max-width: 768px) 50vw, 100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:contain"
    />
  
      
      
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{featureMedia.alt}}"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{featureMedia.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
      {% when 'model' %}
        
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%"
      
      
      
      
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}"
      alt="{{featureMedia.preview_image.alt}}"
      camera-controls="true"
      poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
  <img
      
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none"
      src width="2237" height="1678" alt="No Image"
      fetchpriority="high"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset=""
      sizes="(max-width: 768px) 50vw, 100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:contain"
    />
  
    {% endcase %}
    
              </div>
            </div>
          </div>
        </div>
      
    {% endcapture %}
    {% if product.media.size > 1 %}
      
      
    <gp-carousel data-id="gp-carousel-gxy0lZGh-X" type="gp-feature-image-carousel" product-id="{{product.id}}" product-media="{{product.media.size}}" id="gp-root-carousel-ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}-{{section.id}}" class="
          gp-px-0 tablet:gp-px-0 mobile:gp-px-0
          gp-flex-1 gp-w-full gp-feature-image-carousel
          gp-group/carousel gp-flex" gp-data='{"id":"ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}-{{section.id}}","setting":{"loop":{"desktop":false},"slidesToShow":{"desktop":1},"dotStyle":{"desktop":"none","tablet":"none","mobile":"none"},"dotSize":{"desktop":12},"dotGapToCarousel":{"desktop":16},"dotColor":{"desktop":"bg-1"},"dotActiveColor":{"desktop":"line-3"},"controlOverContent":{"desktop":false,"tablet":false,"mobile":false},"enableDrag":{"desktop":true,"tablet":true,"mobile":true},"arrowCustom":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","arrowCustomColor":"#000000","arrowBorder":{"desktop":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"}},"roundedArrow":{"desktop":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"}},"sneakPeakType":{"desktop":"center"},"arrowGapToEachSide":"16","navigationStyle":{"desktop":"none"},"arrowButtonSize":{"desktop":{"width":"24px","height":"24px"}}},"styles":{"sizeSetting":{"desktop":{"width":"100%","height":"auto"},"tablet":{"width":"100%","height":"auto"},"mobile":{"width":"100%","height":"auto"}},"align":{"desktop":"flex-start","tablet":"flex-start","mobile":"flex-start"}},"isHiddenArrowWhenDisabled":true}' style="--jc:flex-start;--o:0;--o-tablet:0;--o-mobile:0;--d:flex;--d-mobile:flex;--d-tablet:flex">
      <div class="gp-hidden gp-aspect-square gp-w-[12px] gp-rounded-full gp-shadow-md"></div>
      <div
        
        class="gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block gp-carousel-gxy0lZGh-X gp-featured-image-wrapper"
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      >
        <div
          class="gp-h-full gp-relative gp-mx-auto gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row"
          style="--h:100%;--h-tablet:100%;--h-mobile:100%;gap:16px"
        >
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}} gp-carousel-action-back gem-slider-previous ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}-{{section.id}} gp-carousel-arrow-gp-carousel-gxy0lZGh-X gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--left:initial;--top:initial;--right:initial;--bottom:;--left-tablet:initial;--top-tablet:initial;--right-tablet:initial;--bottom-tablet:;--left-mobile:initial;--top-mobile:initial;--right-mobile:initial;--bottom-mobile:;--d:none;--d-tablet:none;--d-mobile:none;background-color:;--w:24px;--h:24px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
  style="--c:#000000;--w:undefinedpx;--w-tablet:undefinedpx;--w-mobile:undefinedpx;--h:undefinedpx;--h-tablet:undefinedpx;--h-mobile:undefinedpx"
  >
    <svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z" fill="currentColor"/>
    </svg>
    </div>
      <style>
    .ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gxy0lZGh-X {
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    .ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gxy0lZGh-X::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gxy0lZGh-X {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gxy0lZGh-X::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
    @media only screen and (max-width: 768px) {
      .ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gxy0lZGh-X {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gxy0lZGh-X::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
  </style>
    </button>
  
          <div id="gp-carousel-ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}-{{section.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full"
          style="--cg-mobile:undefinedpx;--cg-tablet:undefinedpx;--cg:undefinedpx">
          
          {%- if product.media.size > 0 -%}
            
      
    
            {% assign largestRatio = 0 %}
            {% for featureMedia in product.media %}
              {% assign height = featureMedia.height | times: 1.0 %}
              {% assign width = featureMedia.width | times: 1.0 %}
              {% assign ratio = height | divided_by: width %}
              {% if ratio > largestRatio %}
                {% assign largestRatio = ratio %}
              {% endif %}
            {% endfor %}
            {%- for featureMedia in product.media -%}
              {%- if featureMedia.media_type == 'image' -%}
                {%- for image in product.images -%}
                  {% if image.src == featureMedia.src %}
                    {% assign imageID = image.id %}
                    {% break %}
                  {% endif%}
                {% endfor %}
              {%- else -%}
                {% assign imageID = '' %}
              {%- endif -%}
              
    {% assign productImageWidth = 0 %}
    {% case featureMedia.media_type %}
      {% when 'image' %}
        {% assign productImageWidth = featureMedia.width %}
      {% else %}
        {% assign productImageWidth = featureMedia.preview_image.width %}
    {% endcase %}
    
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      id="{{section.id}}-{{imageID}}"
      style="width:{{productImageWidth}}px;--minw:calc(100% / 4 - 33px);--minw-tablet:calc(100% / 4 - 33px);--minw-mobile:calc(100% / 4 - 33px);--maxw:calc(100% / 4 - 33px);--maxw-tablet:calc(100% / 4 - 33px);--maxw-mobile:calc(100% / 4 - 33px);outline-color:var(--g-c-brand, brand);--h:auto;--h-tablet:auto;--h-mobile:auto;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}} gp-child-item-undefined gp-group gp-z-0 gp-flex !gp-min-w-full !gp-max-w-full gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-outline-1 -gp-outline-offset-1 gp-image-item gp-ft-image-item data-[outline=active]:gp-outline undefined"
      data-index=""
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      
      <div 
        class="gp-w-full  {% if featureMedia == null or featureMedia.media_type == 'image'  %} {{ 'gp-h-0' }} {% else %} {{ 'gp-h-full !gp-pb-0' }} {% endif %} gp-relative" 
        style="--pb: calc((1/1)*100%);--pb-tablet: calc((1/1)*100%);--pb-mobile: calc((1/1)*100%); {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %} {{ 'display: flex; align-items: center; justify-content: center' }} {% endif %}"
      >
        
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity {{shouldHidden}}"
      src="{{src | image_url}}" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}"
      fetchpriority="high"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset="{{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w"
      sizes="(max-width: 768px) 50vw, 100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:contain"
    />
  
      
      
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{featureMedia.alt}}"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{featureMedia.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
      {% when 'model' %}
        
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%"
      
      
      
      
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}"
      alt="{{featureMedia.preview_image.alt}}"
      camera-controls="true"
      poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
  <img
      
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none"
      src width="2237" height="1678" alt="No Image"
      fetchpriority="high"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset=""
      sizes="(max-width: 768px) 50vw, 100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:contain"
    />
  
    {% endcase %}
    
      </div>
      
      </div>
    </div>
  
            {% endfor %}
          {%- else -%}
            
  <img
      id="noImageError"
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full featured-image-only !gp-rounded-none"
      src="{{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}" width="480" height="480" alt="no image"
      fetchpriority="high"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset=""
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:contain;height:100%"
    />
  
          {%- endif -%}
        
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}} gp-carousel-action-next gem-slider-next ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}-{{section.id}} gp-carousel-arrow-gp-carousel-gxy0lZGh-X gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--left:initial;--top:;--right:initial;--bottom:initial;--left-tablet:initial;--top-tablet:;--right-tablet:initial;--bottom-tablet:initial;--left-mobile:initial;--top-mobile:;--right-mobile:initial;--bottom-mobile:initial;--d:none;--d-tablet:none;--d-mobile:none;background-color:;--w:24px;--h:24px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
  style="--c:#000000;--w:undefinedpx;--w-tablet:undefinedpx;--w-mobile:undefinedpx;--h:undefinedpx;--h-tablet:undefinedpx;--h-mobile:undefinedpx"
  >
    <svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z" fill="currentColor"/>
    </svg>
    </div>
      <style>
    .ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gxy0lZGh-X {
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    .ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gxy0lZGh-X::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gxy0lZGh-X {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gxy0lZGh-X::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
    @media only screen and (max-width: 768px) {
      .ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gxy0lZGh-X {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gxy0lZGh-X::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
  </style>
    </button>
  
        </div>
        <div
    class="gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-carousel-dot-container gp-carousel-dot-container-ft-gp-carousel-gxy0lZGh-X-{{section.id}}-{{product.id}}-{{section.id}} gp-static gp-flex-row gp-left-0 gp-right-0 tablet:gp-static tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-static mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0"
    style="--bottom:16px;--bottom-tablet:16px;--bottom-mobile:16px;--d:none;--d-tablet:none;--d-mobile:none"
 ></div>
      </div>
    </gp-carousel>
    {% if product.media.size > 1 %} <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-carousel-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script> {% endif %}

  
    
      
    {% else %}
      {{ featureImageOnlyOne }}
    {% endif %}
  
         
      </div>
    </gp-product-images-v2>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-images-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gApel9rJzU gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gYm1UGmCZQ" data-id="gYm1UGmCZQ"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pl:0px;--pr:0px;--pl-mobile:7px;--pr-mobile:7px;--cg:8px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gYm1UGmCZQ gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g_B7Rz3nuZ gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:24px;--mb-mobile:21px" class="gZaGGwrusE ">
      
    {%- unless product -%}
      <p>{{ "gempages.ProductTitle.product_not_found" | t }}</p>
    {%- else -%}
      
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gZaGGwrusE">
    <div
      
        class="gZaGGwrusE "
        
      >
      <div  >
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-product-title gp-text-g-text-2"
          style="--w:100%;--ts:none;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:var(--g-c-text-2, text-2);--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-heading, heading);--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--weight:bold;word-break:break-word;overflow:hidden"
        >{{ product.title }}</h3>
      </div>
    </div>
    </gp-text>
    
      
    {%- endunless -%}
  
      </div>
      <gp-product-price
        data-id="gumrJgHAvS"
        class="gumrJgHAvS gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden"
        gp-data='{"priceType":"regular","uid":"gumrJgHAvS","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
        
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:9px"
        data-hidden="false"
      >
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-price-gumrJgHAvS"
        class=" "
        
      >
      <div  >
        <div
          type="regular"
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-price gp-money gp-decoration-g-text-1 gp-text-g-text-2"
          style="--w:100%;--tdc:text-1;--tdt:1;--ta:center;--c:var(--g-c-text-2, text-2);--fs:normal;--ff:var(--g-font-heading, heading);--weight:600;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:18px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >
     {% if variant.price %} 
       {{ variant.price | money}}
     {% endif %}
   </div>
      </div>
    </div>
    </gp-text>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-price.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
   {%- liquid
      assign inventory_quantity = variant.inventory_quantity | default: 0
    -%}
    <gp-product-button
      class="gp-product-button"
      gp-data-wrapper="true"
      gp-label-out-of-stock="{{section.settings.ggz9XZKrdEc_outOfStockLabel}}"
      gp-label-unavailable="{{section.settings.ggz9XZKrdEc_unavailableLabel}}"
      gp-data='{"setting":{"actionEffect":"open-cart-drawer","errorMessage":"Cannot add product to cart","successMessage":"Add product to cart successfully","enableSuccessMessage":true,"enableErrorMessage":true,"label":"Add to cart","outOfStockLabel":"Out of stock","errorType":"built-in","customURL":{"link":"/cart","target":"_self"}},"styles":{"errorTypo":{"attrs":{"color":"error"},"type":"paragraph-2"},"successTypo":{"attrs":{"color":"success"},"type":"paragraph-2"}},"disabled":"{{variant.available}}","variantID":"{{variant.id}}","totalVariant":"{{product.variants.size}}"}' 
       gp-href="{{ request.origin }}{{ routes.root_url | split: '/' | join: '/' }}/cart"
       data-variant-selection-required-message="{{section.settings.ggz9XZKrdEc_variantSelectionRequiredMessage}}"
    >
        
  <gp-button >
  <div
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:center"
    class="{% unless variant.available %} !gp-hidden {% endunless %}"
  >
    <style>
    .gz9XZKrdEc.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 3px;
      border-bottom-right-radius: 3px;
      border-top-left-radius: 3px;
      border-top-right-radius: 3px;
      
    }

    .gz9XZKrdEc:hover::before {
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
    }

    .gz9XZKrdEc:hover .gp-button-icon {
      color: undefined;
    }

     .gz9XZKrdEc .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gz9XZKrdEc:hover .gp-button-price {
      color: undefined;
    }

    .gz9XZKrdEc .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gz9XZKrdEc .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gz9XZKrdEc:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="submit" data-id="gz9XZKrdEc" aria-label="Add to cart"
      name="add" gp-data-hidden="{% unless variant.available %}true{% endunless %}"
      data-state="idle"
      class="gz9XZKrdEc gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-atc tcustomizer-submit-button gp-g-paragraph-1"
      style="--hvr-bg:#1180ff;--bg:#0474F7;--bblr:3px;--bbrr:3px;--btlr:3px;--btrr:3px;--shadow:none;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:8px;--pb:8px;--c:var(--g-c-text-3, text-3);--size:15px;--size-tablet:16px;--size-mobile:14px;--weight:bold;--tt:uppercase"
    >
        <svg
          class="gp-invisible gp-absolute gp-h-5 gp-w-5 group-data-[state=loading]/button:gp-animate-spin group-data-[state=loading]/button:gp-visible"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="gp-opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="gp-opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:15px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggz9XZKrdEc_label }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

        
  <gp-button >
  <div
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:center"
    class="{% if variant.available %} !gp-hidden {% endif %}"
  >
    <style>
    .gz9XZKrdEc-sold-out.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 3px;
      border-bottom-right-radius: 3px;
      border-top-left-radius: 3px;
      border-top-right-radius: 3px;
      
    }

    .gz9XZKrdEc-sold-out:hover::before {
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
    }

    .gz9XZKrdEc-sold-out:hover .gp-button-icon {
      color: undefined;
    }

     .gz9XZKrdEc-sold-out .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gz9XZKrdEc-sold-out:hover .gp-button-price {
      color: undefined;
    }

    .gz9XZKrdEc-sold-out .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gz9XZKrdEc-sold-out .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gz9XZKrdEc-sold-out:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="button" data-id="gz9XZKrdEc" aria-label="{{section.settings.ggz9XZKrdEc_outOfStockLabel}}"
      gp-data-hidden="{% if variant.available %}true{% endif %}"
      data-state="idle"
      class="gz9XZKrdEc-sold-out gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-sold-out btn-disabled gp-opacity-30 gp-cursor-default gp-g-paragraph-1"
      style="--hvr-bg:#1180ff;--bg:#0474F7;--bblr:3px;--bbrr:3px;--btlr:3px;--btrr:3px;--shadow:none;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:8px;--pb:8px;--c:var(--g-c-text-3, text-3);--size:15px;--size-tablet:16px;--size-mobile:14px;--weight:bold;--tt:uppercase"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:15px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{section.settings.ggz9XZKrdEc_outOfStockLabel}}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

    </gp-product-button>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-button.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
   
    
    </div>
    </div>
   
    
          {%- endform -%}
        </product-form>
      </gp-product>
      {%- assign product = gpBkProduct -%}
    {% else %}
      <div class="gp-text-center">{{ "gempages.Product.product_not_found" | t }}</div>
    {%- endif -%}
     <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="ggpfClc9CM gp-relative gp-flex gp-flex-col"
    >
      
    {%- assign gpBkProduct = product -%}
    
        {%- liquid
          if request.page_type == 'product'
            if 'static' == 'static'
              if '14924340658558' == 'latest'
                paginate collections.all.products by 100000
                  assign product = collections.all.products | sort: 'created_at' | reverse | first
                endpaginate
              else
                assign product = all_products['gard-pro-health-smartwatch-3-1']
                assign productId = '14924340658558' | times: 1
                if product == empty or product == null
                  paginate collections.all.products by 100000
                    for item in collections.all.products
                      if item.id == productId
                        assign product = item
                      endif
                    endfor
                  endpaginate
                endif
              endif
            endif
          else
            if '14924340658558' == 'latest'
              paginate collections.all.products by 100000
                assign product = collections.all.products | sort: 'created_at'| reverse | first
              endpaginate
            else
              assign product = all_products['gard-pro-health-smartwatch-3-1']
              assign productId = '14924340658558' | times: 1
              if product == empty or product == null
                paginate collections.all.products by 100000
                  for item in collections.all.products
                    if item.id == productId
                      assign product = item
                    endif
                  endfor
                endpaginate
              endif
            endif
          endif
        -%}
      

    {%-if product != empty and product != null -%}
      {%- assign initVariantId = 55006243586430 -%}
      {%- assign product_form_id = 'product-form-' | append: "gVomrTdsGN" -%}
      {%- assign variant = product.variants | where: 'id', initVariantId | first -%}
      {%- assign productSelectedVariant = product.variants | where: 'id', initVariantId | first -%}
      {%-if productSelectedVariant == empty or productSelectedVariant == null -%}
        {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      {%-if variant == empty or variant == null -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      <gp-product data-uid="gVomrTdsGN" data-id="gVomrTdsGN"   gp-context='{"productId": {{ product.id }}, "preSelectedOptionIds": [9247452201342,9557196833150], "isSyncProduct": "true", "variantSelected": {{ variant | json | escape }}, "inventoryQuantity": {{ variant.inventory_quantity }}, "quantity": 1, "formId": "{{ product_form_id }}" }'
        gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }}}'>
        <product-form class="product-form">
          {%- form 'product', product, id: product_form_id, class: 'form contents', data-type: 'add-to-cart-form', autocomplete: 'off'  -%}
            <input type="hidden" name="id" value="{{ variant.id }}" />
            <input type="hidden" name="quantity" value="{{ quantity }}" />
            <button type="submit" onclick="return false;" style="display:none;"></button>
            
       
      
    <div
      id="gVomrTdsGN" data-id="gVomrTdsGN-row"
        style="--bs:solid;--bw:0px 0px 1px 0px;--bc:#E0E0E0;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pb:32px;--mb-mobile:21px;--cg:30px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%"
        class="gVomrTdsGN gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g1Df8shZ-F gp-relative gp-flex gp-flex-col"
    >
      
  {% assign featured_image = product.featured_image %}
  {%- if variant != null and variant.featured_image != null -%}
  {% assign featured_image = variant.featured_image %}
  {%- endif -%}
    <gp-product-images-v2
      gp-data='
    {
      "id":"gFHCVQhV5P",
      "pageContext": {"pageType":"GP_STATIC","sectionName":"","isPreviewing":false,"isOptimizePlan":false,"isTranslateWithLocale":false,"isLazyLoadSection":true,"isLazyLoadImage":false,"hasCollectionHandle":true,"numberOfProductOfProductList":0,"pageBackground":"","fontType":"google","primaryLocale":"","enableLazyLoadImage":false},
      "setting":{"arrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","arrowIconColor":"#000000","arrowNavBorder":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"},"arrowNavRadius":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"borderActive":{"border":"solid","borderType":"none","borderWidth":"1px","color":"#000000","isCustom":"false","width":"1px 1px 1px 1px"},"clickOpenLightBox":{"desktop":false},"dragToScroll":true,"ftArrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","ftArrowIconColor":"#000000","ftArrowNavBorder":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"},"ftArrowNavRadius":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"ftClickOpenLightBox":{"desktop":"product-link"},"ftDotActiveColor":{"desktop":"line-3"},"ftDotColor":{"desktop":"bg-1"},"ftDotGapToCarousel":{"desktop":16},"ftDotSize":{"desktop":12},"ftDotStyle":{"desktop":"none"},"ftDragToScroll":true,"ftLoop":{"desktop":false},"ftNavigationPosition":{"desktop":"none"},"ftPauseOnHover":true,"ftSpeed":1,"galleryHoverEffect":"none","galleryZoom":150,"galleryZoomType":"default","hoverEffect":"none","loop":{"desktop":true},"navigationPosition":{"desktop":"inside"},"otherImage":0,"pauseOnHover":true,"preDisplay":"1st-available-variant","preload":true,"qualityPercent":{"desktop":100},"qualityType":{"desktop":"finest"},"speed":1,"type":{"desktop":"slider"},"typeDisplay":"all-images","zoom":150,"zoomType":"default"},
      "styles":{"align":{"desktop":"flex-start"},"corner":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"ftCorner":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"},"ftLayout":{"desktop":"contain"},"ftShape":{"desktop":{"gap":"","height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":""}},"itemSpacing":{"desktop":"var(--g-s-s)","mobile":"var(--g-s-s)"},"layout":{"desktop":"cover"},"position":{"desktop":"only-feature","mobile":"only-feature"},"ratioLayout":{"desktop":[2,10]},"ratioLayoutRight":{"desktop":[10,2]},"shape":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"shapeFor1Col":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"shapeFor2Col":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"50%"}},"shapeForBottom":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"}},"shapeForFtOnly":{"desktop":{"shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"}},"spacing":{"desktop":"var(--g-s-s)","mobile":"var(--g-s-s)"}}, "productUrl":{{product.url | json | escape}}, "product":{{product | json | escape}}, "collectionUrl": {{ collection.url | json | escape }}, "collection": {{ collection | json | escape}}
    }
  '
      section-id="{{section.id}}"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:24px;--mb-mobile:7px"
      data-id="gFHCVQhV5P"
      class="gFHCVQhV5P gp-overflow-hidden"
    >
      <div
        class="{%- if product.media.size > 1 -%} gp-grid gp-w-full !gp-m-0 gp-relative {%- else -%} gp-w-full !gp-m-0 gp-relative {%- endif -%}"
        {%- if product.media.size > 1 -%}
        style="--gtc:minmax(0, 12fr);--gtc-tablet:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--gg:var(--g-s-s);--gg-mobile:var(--g-s-s)"
        {%- else -%}
        style="--gtc:minmax(0, 12fr);--gg:var(--g-s-s);--gg-mobile:var(--g-s-s)"
        {%- endif -%}
      >
        
    {% capture featureImageOnlyOne %}
      
        {% assign featureMedia = variant.featured_media %}
        {% unless featureMedia %}
        {% assign featureMedia = product.featured_media %}
        {% endunless %}
        {% if product.media.size > 1 %}
          
          {% assign featureMedia = variant.featured_media %}
          {% unless featureMedia %}
          {% assign featureMedia = product.featured_media %}
          {% endunless %}
        
        {% endif %}
        {% assign largestRatio = 0 %}
        {% assign height = featureMedia.height | times: 1.0 %}
        {% assign width = featureMedia.width | times: 1.0 %}
        {% assign ratio = height | divided_by: width %}
        {% if ratio > largestRatio %}
          {% assign largestRatio = ratio %}
        {% endif %}
        {% assign productImageWidth = 0 %}
        {% case featureMedia.media_type %}
          {% when 'image' %}
            {% assign productImageWidth = featureMedia.width %}
          {% else %}
            {% assign productImageWidth = featureMedia.preview_image.width %}
        {% endcase %}
        {% if featureMedia == null %}
        {% assign productImageWidth = 1600 %}
        {% endif %}
        <div 
          class='gp-feature-image-carousel gp-feature-image-only' 
          style="--o:0;--o-tablet:0;--o-mobile:0;--d:flex;--d-mobile:flex;--d-tablet:flex;--jc:flex-start"
        >
          <div 
            class="gp-relative"
            style="--w:100%;--w-tablet:100%;--w-mobile:100%"
          >
            
      
    
            <div 
              type="gp-feature-image-only"
              product-id="{{product.id}}"
              product-media="{{product.media.size}}"
              class="gp-image-item gp-ft-image-item gp-group gp-z-0 gp-flex !gp-max-w-full !gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-featured-image-wrapper"
              style="--h:auto;--h-tablet:auto;--h-mobile:auto;width:{{productImageWidth}}px"
            >
              <div 
                class="gp-w-full  {% if featureMedia == null or featureMedia.media_type == 'image' %} {{ 'gp-h-0' }} {% else %} {{ 'gp-h-full !gp-pb-0' }} {% endif %} gp-relative" 
                style="--pb: calc((1/1)*100%);--pb-tablet: calc((1/1)*100%);--pb-mobile: calc((1/1)*100%); {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %} {{ 'display: flex; align-items: center; justify-content: center' }} {% endif %}"
              >
                
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity {{shouldHidden}}"
      src="{{src | image_url}}" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}"
      fetchpriority="high"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset="{{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w"
      sizes="(max-width: 768px) 50vw, 100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:contain"
    />
  
      
      
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{featureMedia.alt}}"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{featureMedia.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
      {% when 'model' %}
        
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%"
      
      
      
      
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}"
      alt="{{featureMedia.preview_image.alt}}"
      camera-controls="true"
      poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
  <img
      
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none"
      src width="2237" height="1678" alt="No Image"
      fetchpriority="high"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset=""
      sizes="(max-width: 768px) 50vw, 100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:contain"
    />
  
    {% endcase %}
    
              </div>
            </div>
          </div>
        </div>
      
    {% endcapture %}
    {% if product.media.size > 1 %}
      
      
    <gp-carousel data-id="gp-carousel-gFHCVQhV5P" type="gp-feature-image-carousel" product-id="{{product.id}}" product-media="{{product.media.size}}" id="gp-root-carousel-ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}-{{section.id}}" class="
          gp-px-0 tablet:gp-px-0 mobile:gp-px-0
          gp-flex-1 gp-w-full gp-feature-image-carousel
          gp-group/carousel gp-flex" gp-data='{"id":"ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}-{{section.id}}","setting":{"loop":{"desktop":false},"slidesToShow":{"desktop":1},"dotStyle":{"desktop":"none","tablet":"none","mobile":"none"},"dotSize":{"desktop":12},"dotGapToCarousel":{"desktop":16},"dotColor":{"desktop":"bg-1"},"dotActiveColor":{"desktop":"line-3"},"controlOverContent":{"desktop":false,"tablet":false,"mobile":false},"enableDrag":{"desktop":true,"tablet":true,"mobile":true},"arrowCustom":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","arrowCustomColor":"#000000","arrowBorder":{"desktop":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"}},"roundedArrow":{"desktop":{"bblr":"0px","bbrr":"0px","btlr":"0px","btrr":"0px","radiusType":"none"}},"sneakPeakType":{"desktop":"center"},"arrowGapToEachSide":"16","navigationStyle":{"desktop":"none"},"arrowButtonSize":{"desktop":{"width":"24px","height":"24px"}}},"styles":{"sizeSetting":{"desktop":{"width":"100%","height":"auto"},"tablet":{"width":"100%","height":"auto"},"mobile":{"width":"100%","height":"auto"}},"align":{"desktop":"flex-start","tablet":"flex-start","mobile":"flex-start"}},"isHiddenArrowWhenDisabled":true}' style="--jc:flex-start;--o:0;--o-tablet:0;--o-mobile:0;--d:flex;--d-mobile:flex;--d-tablet:flex">
      <div class="gp-hidden gp-aspect-square gp-w-[12px] gp-rounded-full gp-shadow-md"></div>
      <div
        
        class="gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block gp-carousel-gFHCVQhV5P gp-featured-image-wrapper"
        style="--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      >
        <div
          class="gp-h-full gp-relative gp-mx-auto gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row"
          style="--h:100%;--h-tablet:100%;--h-mobile:100%;gap:16px"
        >
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}} gp-carousel-action-back gem-slider-previous ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}-{{section.id}} gp-carousel-arrow-gp-carousel-gFHCVQhV5P gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--left:initial;--top:initial;--right:initial;--bottom:;--left-tablet:initial;--top-tablet:initial;--right-tablet:initial;--bottom-tablet:;--left-mobile:initial;--top-mobile:initial;--right-mobile:initial;--bottom-mobile:;--d:none;--d-tablet:none;--d-mobile:none;background-color:;--w:24px;--h:24px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
  style="--c:#000000;--w:undefinedpx;--w-tablet:undefinedpx;--w-mobile:undefinedpx;--h:undefinedpx;--h-tablet:undefinedpx;--h-mobile:undefinedpx"
  >
    <svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z" fill="currentColor"/>
    </svg>
    </div>
      <style>
    .ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gFHCVQhV5P {
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    .ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gFHCVQhV5P::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gFHCVQhV5P {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gFHCVQhV5P::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
    @media only screen and (max-width: 768px) {
      .ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gFHCVQhV5P {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gFHCVQhV5P::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
  </style>
    </button>
  
          <div id="gp-carousel-ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}-{{section.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full"
          style="--cg-mobile:undefinedpx;--cg-tablet:undefinedpx;--cg:undefinedpx">
          
          {%- if product.media.size > 0 -%}
            
      
    
            {% assign largestRatio = 0 %}
            {% for featureMedia in product.media %}
              {% assign height = featureMedia.height | times: 1.0 %}
              {% assign width = featureMedia.width | times: 1.0 %}
              {% assign ratio = height | divided_by: width %}
              {% if ratio > largestRatio %}
                {% assign largestRatio = ratio %}
              {% endif %}
            {% endfor %}
            {%- for featureMedia in product.media -%}
              {%- if featureMedia.media_type == 'image' -%}
                {%- for image in product.images -%}
                  {% if image.src == featureMedia.src %}
                    {% assign imageID = image.id %}
                    {% break %}
                  {% endif%}
                {% endfor %}
              {%- else -%}
                {% assign imageID = '' %}
              {%- endif -%}
              
    {% assign productImageWidth = 0 %}
    {% case featureMedia.media_type %}
      {% when 'image' %}
        {% assign productImageWidth = featureMedia.width %}
      {% else %}
        {% assign productImageWidth = featureMedia.preview_image.width %}
    {% endcase %}
    
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      id="{{section.id}}-{{imageID}}"
      style="width:{{productImageWidth}}px;--minw:calc(100% / 4 - 33px);--minw-tablet:calc(100% / 4 - 33px);--minw-mobile:calc(100% / 4 - 33px);--maxw:calc(100% / 4 - 33px);--maxw-tablet:calc(100% / 4 - 33px);--maxw-mobile:calc(100% / 4 - 33px);outline-color:var(--g-c-brand, brand);--h:auto;--h-tablet:auto;--h-mobile:auto;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}} gp-child-item-undefined gp-group gp-z-0 gp-flex !gp-min-w-full !gp-max-w-full gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-outline-1 -gp-outline-offset-1 gp-image-item gp-ft-image-item data-[outline=active]:gp-outline undefined"
      data-index=""
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      
      <div 
        class="gp-w-full  {% if featureMedia == null or featureMedia.media_type == 'image'  %} {{ 'gp-h-0' }} {% else %} {{ 'gp-h-full !gp-pb-0' }} {% endif %} gp-relative" 
        style="--pb: calc((1/1)*100%);--pb-tablet: calc((1/1)*100%);--pb-mobile: calc((1/1)*100%); {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %} {{ 'display: flex; align-items: center; justify-content: center' }} {% endif %}"
      >
        
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity {{shouldHidden}}"
      src="{{src | image_url}}" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}"
      fetchpriority="high"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset="{{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w"
      sizes="(max-width: 768px) 50vw, 100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:contain"
    />
  
      
      
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{featureMedia.alt}}"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{featureMedia.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
      style="width:100%;max-height:100%"
      class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
    >
      <img
        id="video-thumbnail"
        src=""
        class="gp-w-full gp-h-full gp-object-cover"
        alt="Video Thumbnail"
      ></img>
      <button
        type="button"
        class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
        aria-label="Play"
      >
        <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
          />
        </svg>
      </button>
    </div>
    </gp-lite-html5-embed>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
      {% when 'model' %}
        
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%"
      
      
      
      
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}"
      alt="{{featureMedia.preview_image.alt}}"
      camera-controls="true"
      poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
  <img
      
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none"
      src width="2237" height="1678" alt="No Image"
      fetchpriority="high"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset=""
      sizes="(max-width: 768px) 50vw, 100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:contain"
    />
  
    {% endcase %}
    
      </div>
      
      </div>
    </div>
  
            {% endfor %}
          {%- else -%}
            
  <img
      id="noImageError"
      
      draggable="false"
      isNotLazyload="true"
      class="gp-w-full featured-image-only !gp-rounded-none"
      src="{{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}" width="480" height="480" alt="no image"
      fetchpriority="high"
      quality-type={"desktop":"finest"}
      quality-percent={"desktop":100}
      data-sizes="auto"
      srcset=""
      sizes="100vw"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1;--objf:contain;height:100%"
    />
  
          {%- endif -%}
        
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}} gp-carousel-action-next gem-slider-next ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}-{{section.id}} gp-carousel-arrow-gp-carousel-gFHCVQhV5P gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--left:initial;--top:;--right:initial;--bottom:initial;--left-tablet:initial;--top-tablet:;--right-tablet:initial;--bottom-tablet:initial;--left-mobile:initial;--top-mobile:;--right-mobile:initial;--bottom-mobile:initial;--d:none;--d-tablet:none;--d-mobile:none;background-color:;--w:24px;--h:24px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
  style="--c:#000000;--w:undefinedpx;--w-tablet:undefinedpx;--w-mobile:undefinedpx;--h:undefinedpx;--h-tablet:undefinedpx;--h-mobile:undefinedpx"
  >
    <svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z" fill="currentColor"/>
    </svg>
    </div>
      <style>
    .ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gFHCVQhV5P {
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    .ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gFHCVQhV5P::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gFHCVQhV5P {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gFHCVQhV5P::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
    @media only screen and (max-width: 768px) {
      .ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gFHCVQhV5P {
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
      .ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}.gp-carousel-arrow-gp-carousel-gFHCVQhV5P::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
      }
    }
  </style>
    </button>
  
        </div>
        <div
    class="gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-carousel-dot-container gp-carousel-dot-container-ft-gp-carousel-gFHCVQhV5P-{{section.id}}-{{product.id}}-{{section.id}} gp-static gp-flex-row gp-left-0 gp-right-0 tablet:gp-static tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-static mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0"
    style="--bottom:16px;--bottom-tablet:16px;--bottom-mobile:16px;--d:none;--d-tablet:none;--d-mobile:none"
 ></div>
      </div>
    </gp-carousel>
    {% if product.media.size > 1 %} <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-carousel-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script> {% endif %}

  
    
      
    {% else %}
      {{ featureImageOnlyOne }}
    {% endif %}
  
         
      </div>
    </gp-product-images-v2>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-images-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gkAEKcQdqi gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gYgyN-Bziw" data-id="gYgyN-Bziw"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pl:0px;--pr:0px;--pl-mobile:7px;--pr-mobile:7px;--cg:8px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gYgyN-Bziw gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g-9Xjedjxk gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:24px;--mb-mobile:21px" class="gr2DPriLfZ ">
      
    {%- unless product -%}
      <p>{{ "gempages.ProductTitle.product_not_found" | t }}</p>
    {%- else -%}
      
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gr2DPriLfZ">
    <div
      
        class="gr2DPriLfZ "
        
      >
      <div  >
        <h3
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-product-title gp-text-g-text-2"
          style="--w:100%;--ts:none;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:var(--g-c-text-2, text-2);--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-heading, heading);--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--weight:bold;word-break:break-word;overflow:hidden"
        >{{ product.title }}</h3>
      </div>
    </div>
    </gp-text>
    
      
    {%- endunless -%}
  
      </div>
      <gp-product-price
        data-id="gvtKIPA90P"
        class="gvtKIPA90P gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden"
        gp-data='{"priceType":"regular","uid":"gvtKIPA90P","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
        
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:9px"
        data-hidden="false"
      >
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-price-gvtKIPA90P"
        class=" "
        
      >
      <div  >
        <div
          type="regular"
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-price gp-money gp-decoration-g-text-1 gp-text-g-text-2"
          style="--w:100%;--tdc:text-1;--tdt:1;--ta:center;--c:var(--g-c-text-2, text-2);--fs:normal;--ff:var(--g-font-heading, heading);--weight:600;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:18px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;overflow:hidden"
        >
     {% if variant.price %} 
       {{ variant.price | money}}
     {% endif %}
   </div>
      </div>
    </div>
    </gp-text>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-price.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
   {%- liquid
      assign inventory_quantity = variant.inventory_quantity | default: 0
    -%}
    <gp-product-button
      class="gp-product-button"
      gp-data-wrapper="true"
      gp-label-out-of-stock="{{section.settings.ggOnNI2ntG6_outOfStockLabel}}"
      gp-label-unavailable="{{section.settings.ggOnNI2ntG6_unavailableLabel}}"
      gp-data='{"setting":{"actionEffect":"open-cart-drawer","errorMessage":"Cannot add product to cart","successMessage":"Add product to cart successfully","enableSuccessMessage":true,"enableErrorMessage":true,"label":"Add to cart","outOfStockLabel":"Out of stock","errorType":"built-in","customURL":{"link":"/cart","target":"_self"}},"styles":{"errorTypo":{"attrs":{"color":"error"},"type":"paragraph-2"},"successTypo":{"attrs":{"color":"success"},"type":"paragraph-2"}},"disabled":"{{variant.available}}","variantID":"{{variant.id}}","totalVariant":"{{product.variants.size}}"}' 
       gp-href="{{ request.origin }}{{ routes.root_url | split: '/' | join: '/' }}/cart"
       data-variant-selection-required-message="{{section.settings.ggOnNI2ntG6_variantSelectionRequiredMessage}}"
    >
        
  <gp-button >
  <div
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px;--ta:center"
    class="{% unless variant.available %} !gp-hidden {% endunless %}"
  >
    <style>
    .gOnNI2ntG6.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 3px;
      border-bottom-right-radius: 3px;
      border-top-left-radius: 3px;
      border-top-right-radius: 3px;
      
    }

    .gOnNI2ntG6:hover::before {
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
    }

    .gOnNI2ntG6:hover .gp-button-icon {
      color: undefined;
    }

     .gOnNI2ntG6 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gOnNI2ntG6:hover .gp-button-price {
      color: undefined;
    }

    .gOnNI2ntG6 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gOnNI2ntG6 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gOnNI2ntG6:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="submit" data-id="gOnNI2ntG6" aria-label="Add to cart"
      name="add" gp-data-hidden="{% unless variant.available %}true{% endunless %}"
      data-state="idle"
      class="gOnNI2ntG6 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-atc tcustomizer-submit-button gp-g-paragraph-1"
      style="--hvr-bg:#1180ff;--bg:#0474F7;--bblr:3px;--bbrr:3px;--btlr:3px;--btrr:3px;--shadow:none;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:8px;--pb:8px;--c:var(--g-c-text-3, text-3);--size:15px;--size-tablet:16px;--size-mobile:14px;--weight:bold;--tt:uppercase"
    >
        <svg
          class="gp-invisible gp-absolute gp-h-5 gp-w-5 group-data-[state=loading]/button:gp-animate-spin group-data-[state=loading]/button:gp-visible"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="gp-opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="gp-opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:15px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggOnNI2ntG6_label }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

        
  <gp-button >
  <div
    style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px;--ta:center"
    class="{% if variant.available %} !gp-hidden {% endif %}"
  >
    <style>
    .gOnNI2ntG6-sold-out.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
      border-bottom-left-radius: 3px;
      border-bottom-right-radius: 3px;
      border-top-left-radius: 3px;
      border-top-right-radius: 3px;
      
    }

    .gOnNI2ntG6-sold-out:hover::before {
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      
    }

    .gOnNI2ntG6-sold-out:hover .gp-button-icon {
      color: undefined;
    }

     .gOnNI2ntG6-sold-out .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gOnNI2ntG6-sold-out:hover .gp-button-price {
      color: undefined;
    }

    .gOnNI2ntG6-sold-out .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gOnNI2ntG6-sold-out .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gOnNI2ntG6-sold-out:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="button" data-id="gOnNI2ntG6" aria-label="{{section.settings.ggOnNI2ntG6_outOfStockLabel}}"
      gp-data-hidden="{% if variant.available %}true{% endif %}"
      data-state="idle"
      class="gOnNI2ntG6-sold-out gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-sold-out btn-disabled gp-opacity-30 gp-cursor-default gp-g-paragraph-1"
      style="--hvr-bg:#1180ff;--bg:#0474F7;--bblr:3px;--bbrr:3px;--btlr:3px;--btrr:3px;--shadow:none;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:8px;--pb:8px;--c:var(--g-c-text-3, text-3);--size:15px;--size-tablet:16px;--size-mobile:14px;--weight:bold;--tt:uppercase"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:15px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{section.settings.ggOnNI2ntG6_outOfStockLabel}}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

    </gp-product-button>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-button.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
   
    
    </div>
    </div>
   
    
          {%- endform -%}
        </product-form>
      </gp-product>
      {%- assign product = gpBkProduct -%}
    {% else %}
      <div class="gp-text-center">{{ "gempages.Product.product_not_found" | t }}</div>
    {%- endif -%}
     <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>