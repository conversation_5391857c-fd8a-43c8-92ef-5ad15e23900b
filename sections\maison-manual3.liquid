{% schema %}
{
  "name": "Manual 3",
  "presets": [
    {
      "name": "Manual 3"
    }
  ],
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "STRAPS & ACCESSORIES"
    }
  ],
  "blocks": [
    {
      "type": "depth1",
      "name": "Main tab",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Compatible straps per model"
        }
      ]
    },
    {
      "type": "content1",
      "name": "Content 1",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Compatible straps per model"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "HORLOGEBANDEN VOOR DE HEALTH SMARTWATCH 3"
        }
      ]
    },
    {
      "type": "content2",
      "name": "Content 2",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Compatible straps per model"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "<PERSON>ORLOGEBANDEN VOOR DE HEALTH SMARTWATCH 3"
        }
      ]
    },
    {
      "type": "content3",
      "name": "Content 3",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Compatible straps per model"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "HORLOGEBANDEN VOOR DE HEALTH SMARTWATCH 3"
        }
      ]
    },
    {
      "type": "content4",
      "name": "Content 4",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Compatible straps per model"
        },
        {
          "type": "textarea",
          "id": "content",
          "label": "Content",
          "default": "HORLOGEBANDEN VOOR DE HEALTH SMARTWATCH 3"
        }
      ]
    },
    {
      "type": "content5",
      "name": "Content 5",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Compatible straps per model"
        },
        {
          "type": "text",
          "id": "headers",
          "label": "Headers",
          "default": "Bandtype:Materiaal:Kenmerken:Formaat"
        },
        {
          "type": "textarea",
          "id": "rows",
          "label": "Rows",
          "default": "Siliconen lusband:Siliconen:Zweetbestendig, waterdicht, sportief en flexibel:41 mm / 48 mm"
        }
      ]
    },
    {
      "type": "content6",
      "name": "Content 6",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Compatible straps per model"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "HORLOGEBANDEN VOOR DE HEALTH SMARTWATCH 3"
        },
        {
          "type": "textarea",
          "id": "items",
          "label": "Items",
          "default": "Item 1\nItem 2\nItem 3"
        },
        {
          "type": "text",
          "id": "subtitle",
          "label": "Subtitle",
          "default": "HORLOGEBANDEN VOOR DE HEALTH SMARTWATCH 3"
        }
      ]
    },
    {
      "type": "content7",
      "name": "Content 7",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Compatible straps per model"
        },
        {
          "type": "richtext",
          "id": "richtext",
          "label": "Richtext",
          "default": "<p>HORLOGEBANDEN VOOR DE HEALTH SMARTWATCH 3</p>"
        }
      ]
    },
    {
      "type": "content8",
      "name": "Content 8",
      "settings": [
        {
          "type": "text",
          "id": "match",
          "label": "Match",
          "default": "Compatible straps per model"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "VIDEO-INSTRUCTIE BESCHIKBAAR"
        },
        {
          "type": "textarea",
          "id": "content",
          "label": "Content",
          "default": "HORLOGEBANDEN VOOR DE HEALTH SMARTWATCH 3"
        }
      ]
    }
  ]
}
{% endschema %}

<script>
  {% assign depth1 = section.blocks | where: "type", "depth1" %}
  const tabs = [
    {% for block in depth1 %}
      {
        id: "{{ block.settings.title | handleize }}",
        name: "{{ block.settings.title | strip }}"
      },
    {% endfor %}
  ]
</script>

<div>
  <div class="container">
    <div class="header">{{ section.settings.title }}</div>

    <div class="tabs">
      <template x-for="tab in tabs" :key="tab.id">
        <div class="tab" :id="tab.id" @click="activeTab = tab.id" x-text="tab.name"></div>
      </template>
    </div>

    <!-- Compatible straps per model -->
    <div id="compatible" class="tab-content active">
      {% for block in section.blocks %}
        {% if block.settings.match == blank %}
          {% continue %}
        {% endif %}

        <div x-show="activeTab === '{{ block.settings.match | handleize }}'">
          {% case block.type %}
            {% when 'content1' %}
              <h1>{{ block.settings.title }}</h1>
            {% when 'content2' %}
              <h2>{{ block.settings.title }}</h2>
            {% when 'content3' %}
              <h3>{{ block.settings.title }}</h3>
            {% when 'content4' %}
              <p>{{ block.settings.content | newline_to_br | replace: '[', '<strong>' | replace: ']', '</strong>' }}</p>
            {% when 'content5' %}
              <table>
                <thead>
                  <tr>
                    {% assign headers = block.settings.headers | split: ':' %}
                    {% for header in headers %}
                      <th>{{ header }}</th>
                    {% endfor %}
                  </tr>
                </thead>
                <tbody>
                  {% assign rows = block.settings.rows | newline_to_br | split: '<br />' | compact %}
                  {% for row in rows %}
                    {% assign row_parts = row | split: ':' %}
                    <tr>
                      {% for part in row_parts %}
                        <td>{{ part }}</td>
                      {% endfor %}
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            {% when 'content6' %}
              <div class="warning">
                <strong>{{ block.settings.title }}</strong>
                <ul>
                  {% assign items = block.settings.items | newline_to_br | split: '<br />' | compact %}
                  {% for item in items %}
                    <li>{{ item }}</li>
                  {% endfor %}
                </ul>
                <p>{{ block.settings.subtitle }}</p>
              </div>

            {% when 'content7' %}
              {{ block.settings.richtext }}
            {% when 'content8' %}
              <div class="video-section">
                <h3>{{ block.settings.title }}</h3>
                <p>
                  {{ block.settings.content | newline_to_br | replace: '[', '<strong>' | replace: ']', '</strong>' }}
                </p>
              </div>
          {% endcase %}
        </div>
      {% endfor %}
    </div>
  </div>
</div>

<script src="{{ 'maison-manual3.js' | asset_url }}"></script>
{{ 'maison-manual3.css' | asset_url | stylesheet_tag }}
