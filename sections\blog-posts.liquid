{%- assign blog = blogs[section.settings.blog] -%}

{%- if section.settings.divider -%}<div class="section--divider">{%- endif -%}

<div class="page-width">

  {%- if section.settings.title != blank -%}
    <header class="section-header{% if section.settings.view_all %}{% unless settings.type_headers_align_text %} section-header--with-link{% endunless %}{% endif %}">
      <h2 class="section-header__title">
        {{ section.settings.title | escape }}
      </h2>
      {%- if section.settings.view_all -%}
        <a href="{{ blog.url }}"  class="btn btn--secondary btn--small section-header__link">{{ 'blogs.article.view_all' | t }}</a>
      {%- endif -%}
    </header>
  {%- endif -%}

  {%- unless blog.empty? or blog.articles.size == 0 -%}

    <div class="grid grid--uniform">
      {%- for article in blog.articles limit: section.settings.post_limit -%}
        {%- render 'article-grid-item',
          blog: blog,
          article: article,
          grid_item_width: 'medium-up--one-third',
          per_row: '3',
          image_size: section.settings.blog_image_size
        -%}
      {%- endfor -%}
    </div>

  {%- else -%}

    <div class="grid grid--uniform">
      {%- for i in (1..section.settings.post_limit) -%}
        <div class="grid__item medium-up--one-third" data-aos="row-of-3">
          <div class="grid">
            <div class="grid__item small--one-third">
              <a href="#" class="article__grid-image">
                {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
              </a>
            </div>
            <div class="grid__item small--two-thirds">
              <div class="article__grid-meta">
                {%- if section.settings.blog_show_date or article.tags.size > 0 or articles.comments_count > 0 -%}
                  <div class="article__date">
                    {%- if section.settings.blog_show_comments and article.comments_count > 0 -%}
                      <a href="#">3 comments</a> &middot;
                    {%- endif -%}
                    {%- if section.settings.blog_show_date -%}
                      Jul 17, {{ 'now' | date: "%Y" }}
                    {%- endif -%}
                  </div>
                {%- endif -%}

                <a href="#" class="article__title">Example blog post</a>

                {%- if section.settings.blog_show_author -%}
                  <div class="article__author">by Archetype Themes</div>
                {%- endif -%}
              </div>
            </div>
          </div>
        </div>
      {%- endfor -%}
    </div>

  {%- endunless -%}

</div>

{%- if section.settings.divider -%}</div>{%- endif -%}

{% schema %}
{
  "name": "t:sections.blog-posts.name",
  "class": "index-section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:sections.blog-posts.settings.title.label",
      "default": "Blog posts"
    },
    {
      "id": "blog",
      "type": "blog",
      "label": "t:sections.blog-posts.settings.blog.label"
    },
    {
      "type": "range",
      "id": "post_limit",
      "label": "t:sections.blog-posts.settings.post_limit.label",
      "default": 3,
      "min": 3,
      "max": 12,
      "step": 3
    },
    {
      "type": "checkbox",
      "id": "blog_show_tags",
      "label": "t:sections.blog-posts.settings.blog_show_tags.label"
    },
    {
      "type": "checkbox",
      "id": "blog_show_date",
      "label": "t:sections.blog-posts.settings.blog_show_date.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "blog_show_comments",
      "label": "t:sections.blog-posts.settings.blog_show_comments.label"
    },
    {
      "type": "checkbox",
      "id": "blog_show_author",
      "label": "t:sections.blog-posts.settings.blog_show_author.label"
    },
    {
      "type": "checkbox",
      "id": "view_all",
      "label": "t:sections.blog-posts.settings.view_all.label",
      "default": true
    },
    {
      "type": "select",
      "id": "blog_image_size",
      "label": "t:sections.blog-posts.settings.blog_image_size.label",
      "default": "wide",
      "options": [
        {
          "value": "natural",
          "label": "t:sections.blog-posts.settings.blog_image_size.options.natural.label"
        },
        {
          "value": "square",
          "label": "t:sections.blog-posts.settings.blog_image_size.options.square.label"
        },
        {
          "value": "landscape",
          "label": "t:sections.blog-posts.settings.blog_image_size.options.landscape.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.blog-posts.settings.blog_image_size.options.portrait.label"
        },
        {
          "value": "wide",
          "label": "t:sections.blog-posts.settings.blog_image_size.options.wide.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "divider",
      "label": "t:sections.blog-posts.settings.divider.label",
      "default": false
    }
  ],
  "presets": [
    {
      "name": "t:sections.blog-posts.presets.blog_posts.name",
      "settings": {
        "blog": "News",
        "post_limit": 3
      }
    }
  ]
}
{% endschema %}
