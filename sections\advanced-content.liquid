{%- if section.settings.space_around -%}
  <div class="index-section">
{%- endif -%}
{%- unless section.settings.full_width -%}
  <div class="page-width">
{%- endunless -%}
<div class="custom-content">
  {%- for block in section.blocks -%}
    {%- liquid
      case block.settings.width
        when '25%'
          assign block_width = 'small--one-whole one-quarter'
        when '33%'
          assign block_width = 'small--one-whole one-third'
        when '50%'
          assign block_width = 'small--one-whole one-half'
        when '66%'
          assign block_width = 'small--one-whole two-thirds'
        when '75%'
          assign block_width = 'small--one-whole three-quarters'
        when '100%'
          assign block_width = 'one-whole'
      endcase
    -%}
    <div
      class="custom__item {{ block_width }}{% if block.settings.alignment %} align--{{ block.settings.alignment }}{% endif %}"
      {{ block.shopify_attributes }}
    >
      <div class="custom__item-inner custom__item-inner--{{ block.type }}{% if block.settings.image == blank and block.type == 'image' %} custom__item-inner--placeholder-image{% endif %}">
        {%- case block.type -%}
          {%- when 'liquid' -%}
            {%- if block.settings.code != blank -%}
              <div class="rte custom_class">
                {{ block.settings.code }}
              </div>
            {%- else -%}
              <div class="rte">
                {{ 'home_page.onboarding.no_content' | t }}
              </div>
            {%- endif -%}
          {%- when 'image' -%}
            <div class="aos-animate">
              {%- if block.settings.link != blank -%}
                <a href="{{ block.settings.link }}">
              {%- endif -%}
              {%- if block.settings.image != blank -%}
                <div
                  class="image-wrap"
                  style="height: 0; padding-bottom: {{ 100 | divided_by: block.settings.image.aspect_ratio }}%;"
                >
                  {%- assign img_url = block.settings.image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}
                  <img
                    class="lazyload"
                    data-src="{{ img_url }}"
                    data-widths="[360, 540, 720, 900, 1080, 1600]"
                    data-aspectratio="{{ block.settings.image.aspect_ratio }}"
                    data-sizes="auto"
                    alt="{{ block.settings.image.alt }}"
                  >
                  <noscript>
                    <img
                      class="lazyloaded"
                      src="{{ block.settings.image | img_url: '400x' }}"
                      alt="{{ block.settings.image.alt }}"
                    >
                  </noscript>
                </div>
              {%- else -%}
                <div class="image-wrap">
                  {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg' }}
                </div>
              {%- endif -%}
              {%- if block.settings.link != blank -%}
                </a>
              {%- endif -%}
            </div>
        {%- endcase -%}
      </div>
    </div>
  {%- endfor -%}
  {%- if section.blocks.size == 0 -%}
    <div class="page-width">
      <div class="grid">
        <div class="grid__item">
          <div class="rte">
            {{ 'home_page.onboarding.no_content' | t }}
          </div>
        </div>
      </div>
    </div>
  {%- endif -%}
</div>
{%- unless section.settings.full_width -%}
  </div>
{%- endunless -%}
{%- if section.settings.space_around -%}
  </div>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.advanced-content.name",
  "max_blocks": 3,
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.advanced-content.settings.full_width.label",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "space_around",
      "label": "t:sections.advanced-content.settings.space_around.label",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "liquid",
      "name": "t:sections.advanced-content.blocks.html.name",
      "settings": [
        {
          "type": "liquid",
          "id": "code",
          "label": "t:sections.advanced-content.blocks.html.settings.code.label",
          "default": "<h2>Custom content</h2><p>Use this advanced section to build your own layouts or to add custom HTML, Liquid, or scripts.</p>",
          "info": "t:sections.advanced-content.blocks.html.settings.code.info"
        },
        {
          "type": "select",
          "id": "width",
          "label": "t:sections.advanced-content.blocks.html.settings.width.label",
          "default": "100%",
          "options": [
            {
              "value": "25%",
              "label": "25%"
            },
            {
              "value": "33%",
              "label": "33%"
            },
            {
              "value": "50%",
              "label": "50%"
            },
            {
              "value": "66%",
              "label": "66%"
            },
            {
              "value": "75%",
              "label": "75%"
            },
            {
              "value": "100%",
              "label": "100%"
            }
          ]
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "t:sections.advanced-content.blocks.html.settings.alignment.label",
          "default": "center",
          "info": "t:sections.advanced-content.blocks.html.settings.alignment.info",
          "options": [
            {
              "value": "top-middle",
              "label": "t:sections.advanced-content.blocks.html.settings.alignment.options.top-middle.label"
            },
            {
              "value": "center",
              "label": "t:sections.advanced-content.blocks.html.settings.alignment.options.center.label"
            },
            {
              "value": "bottom-middle",
              "label": "t:sections.advanced-content.blocks.html.settings.alignment.options.bottom-middle.label"
            }
          ]
        }
      ]
    },
    {
      "type": "image",
      "name": "t:sections.advanced-content.blocks.image.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.advanced-content.blocks.image.settings.image.label"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.advanced-content.blocks.image.settings.link.label"
        },
        {
          "type": "select",
          "id": "width",
          "label": "t:sections.advanced-content.blocks.image.settings.width.label",
          "default": "100%",
          "options": [
            {
              "value": "25%",
              "label": "25%"
            },
            {
              "value": "33%",
              "label": "33%"
            },
            {
              "value": "50%",
              "label": "50%"
            },
            {
              "value": "66%",
              "label": "66%"
            },
            {
              "value": "75%",
              "label": "75%"
            },
            {
              "value": "100%",
              "label": "100%"
            }
          ]
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "t:sections.advanced-content.blocks.image.settings.alignment.label",
          "default": "center",
          "info": "t:sections.advanced-content.blocks.image.settings.alignment.info",
          "options": [
            {
              "value": "top-middle",
              "label": "t:sections.advanced-content.blocks.image.settings.alignment.options.top-middle.label"
            },
            {
              "value": "center",
              "label": "t:sections.advanced-content.blocks.image.settings.alignment.options.center.label"
            },
            {
              "value": "bottom-middle",
              "label": "t:sections.advanced-content.blocks.image.settings.alignment.options.bottom-middle.label"
            }
          ]
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.advanced-content.presets.custom_content.name",
      "blocks": [
        {
          "type": "image",
          "settings": {
            "width": "50%"
          }
        },
        {
          "type": "liquid",
          "settings": {
            "width": "50%"
          }
        }
      ]
    }
  ]
}
{% endschema %}
