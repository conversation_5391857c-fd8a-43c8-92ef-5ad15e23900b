/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "general": {
    "404": {
      "title": "404 - Page non trouvée",
      "subtext_html": "Cette page n'est pas disponible. <a href='{{ url }}'>Retourner au magasinage</a>"
    },
    "accessibility": {
      "skip_to_content": "Passer au contenu",
      "close_modal": "Fermer (Esc)",
      "close": "Fermer",
      "learn_more": "Apprendre plus"
    },
    "meta": {
      "tags": "Mots clés \"{{ tags }}\"",
      "page": "Page {{ page }}"
    },
    "pagination": {
      "previous": "Précédent",
      "next": "Suivant"
    },
    "password_page": {
      "login_form_heading": "Accéder à la boutique à l'aide d'un mot de passe:",
      "login_form_password_label": "Mot de passe",
      "login_form_password_placeholder": "Votre mot de passe",
      "login_form_submit": "Entrer",
      "signup_form_email_label": "Courriel",
      "signup_form_success": "Nous vous ferons parvenir un courriel juste avant l'ouverture!",
      "admin_link_html": "Êtes-vous le propriétaire de la boutique? <a href=\"/admin\" class=\"text-link\">Connectez-vous ici</a>.",
      "password_link": "Mot de passe",
      "powered_by_shopify_html": "Cette boutique sera propulsée par {{ shopify }}"
    },
    "breadcrumbs": {
      "home": "Accueil",
      "home_link_title": "Retour à la page d'accueil"
    },
    "social": {
      "share_on_facebook": "Partager",
      "share_on_twitter": "Tweeter",
      "share_on_pinterest": "Épingler",
      "alt_text": {
        "share_on_facebook": "Partager sur Facebook",
        "share_on_twitter": "Tweeter sur Twitter",
        "share_on_pinterest": "Épingler sur Pinterest"
      }
    },
    "newsletter_form": {
      "newsletter_email": "Inscrivez-vous à notre infolettre",
      "newsletter_confirmation": "Merci de vous être inscrit",
      "submit": "S'inscrire"
    },
    "search": {
      "view_more": "Voir plus",
      "collections": "Collections:",
      "pages": "Pages:",
      "articles": "Des articles:",
      "no_results_html": "Votre recherche pour \"{{ terms }}\" n'a pas généré de résultats.",
      "results_for_html": "Votre recherche pour \"{{ terms }}\" a généré les résultats suivants:",
      "title": "Rechercher",
      "placeholder": "Rechercher dans la boutique",
      "submit": "Recherche",
      "result_count": {
        "one": "{{ count }} résultat",
        "other": "{{ count }} résultats"
      }
    },
    "drawers": {
      "navigation": "Navigation",
      "close_menu": "Fermer le menu",
      "expand_submenu": "Ouvrir le menu",
      "collapse_submenu": "Replier le menu"
    },
    "currency": {
      "dropdown_label": "Devise"
    },
    "language": {
      "dropdown_label": "Langue"
    }
  },
  "sections": {
    "map": {
      "get_directions": "Obtenir des directions",
      "address_error": "Vous ne trouvez pas cette adresse",
      "address_no_results": "Aucun résultat pour cette adresse",
      "address_query_limit_html": "Vous avez dépassé la limite de Google utilisation de l'API. Envisager la mise à niveau à un <a href=\"https://developers.google.com/maps/premium/usage-limits\">régime spécial</a>.",
      "auth_error_html": "Il y avait un problème authentifier votre compte Google Maps . Créer et activer <a href=\"https://developers.google.com/maps/documentation/javascript/get-api-key\">l'API JavaScript</a> et <a href=\"https://developers.google.com/maps/documentation/geocoding/get-api-key\">l'API de géocodage</a> les autorisations de application."
    },
    "slideshow": {
      "play_slideshow": "Lire le diaporama",
      "pause_slideshow": "Diaporama Pause"
    }
  },
  "blogs": {
    "article": {
      "view_all": "Voir toutes",
      "tags": "Tags",
      "read_more": "Plus",
      "back_to_blog": "Retour à {{ title }}"
    },
    "comments": {
      "title": "Laissez un commentaire",
      "name": "Nom",
      "email": "Courriel",
      "message": "Message",
      "post": "Publier le commentaire",
      "moderated": "Veuillez noter que les commentaires doivent être approvés avant d'être affichés",
      "success_moderated": "Votre commentaire a été soumis avec succès. Nous le publierons sous peu, suite à notre processus de modération.",
      "success": "Votre commentaire a été publié avec succès!",
      "with_count": {
        "one": "{{ count }} commentaire",
        "other": "{{ count }} commentaires"
      }
    }
  },
  "cart": {
    "general": {
      "title": "Panier",
      "remove": "Retirer",
      "note": "Instructions spéciales pour la commande",
      "subtotal": "Sous-total",
      "discounts": "Réductions",
      "shipping_at_checkout": "Les codes promo, les frais d'envoi et les taxes seront ajoutés à la caisse.",
      "update": "Mettre à jour",
      "checkout": "Procéder au paiement",
      "empty": "Votre panier est vide.",
      "continue_browsing_html": "<a href='{{ url }}'>Retourner au magasinage</a>.",
      "close_cart": "Fermer le panier",
      "reduce_quantity": "Réduire la quantité de l'article de un",
      "increase_quantity": "Augmenter la quantité de l'article de un",
      "terms": "Je suis d'accord avec les termes et conditions",
      "terms_html": "Je suis d'accord avec les <a href='{{ url }}' target='_blank'>termes et conditions</a>",
      "terms_confirm": "Vous devez accepter les termes et conditions de vente pour vérifier"
    },
    "label": {
      "price": "Prix",
      "quantity": "Quantité",
      "total": "Total"
    }
  },
  "collections": {
    "general": {
      "catalog_title": "Catalogue",
      "all_of_collection": "Tout",
      "view_all_products_html": "Voir tous les<br>{{ count }} produits",
      "see_more": "Afficher plus",
      "see_less": "Voir moins",
      "no_matches": "Aucun produit ne correspond à votre recherche.",
      "items_with_count": {
        "one": "{{ count }} item",
        "other": "{{ count }} items"
      }
    },
    "sorting": {
      "title": "Appliquer"
    },
    "filters": {
      "title_tags": "Filtrer",
      "all_tags": "Tous les produits",
      "categories_title": "Catégories"
    }
  },
  "contact": {
    "form": {
      "name": "Nom",
      "email": "Courriel",
      "phone": "Téléphone",
      "message": "Message",
      "send": "Envoyer",
      "post_success": "Merci de nous avoir avoir contacté. Nous vous reviendrons le plus rapidement possible."
    }
  },
  "customer": {
    "account": {
      "title": "Mon compte",
      "details": "Détails du compte",
      "view_addresses": "Voir les adresses",
      "return": "Retour au détails du compte"
    },
    "activate_account": {
      "title": "Activer le compte",
      "subtext": "Créez votre mot de passe pour activer le compte.",
      "submit": "Activer le compte",
      "cancel": "Refuser l'invitation",
      "password": "Mot de passe",
      "password_confirm": "Confirmer le mot de passe"
    },
    "addresses": {
      "title": "Votre adresse",
      "default": "Par défaut",
      "add_new": "Ajouter une nouvelle adresse",
      "edit_address": "Éditer l'adresse",
      "first_name": "Prénom",
      "last_name": "Nom",
      "company": "Compagnie",
      "address1": "Adresse 1",
      "address2": "Adresse 2",
      "city": "Ville",
      "country": "Pays",
      "province": "Province",
      "zip": "Code postal",
      "phone": "Téléphone",
      "set_default": "Définir comme adresse par défaut",
      "add": "Ajouter l'adresse",
      "update": "Mettre à jour l'adresse",
      "cancel": "Annuler",
      "edit": "Éditer",
      "delete": "Supprimer",
      "delete_confirm": "Êtes-vous certain(e) de vouloir supprimer cette adresse?"
    },
    "login": {
      "title": "Connexion",
      "email": "Courriel",
      "password": "Mot de passe",
      "forgot_password": "Mot de passe oublié?",
      "sign_in": "Se connecter",
      "cancel": "Retourner à la boutique",
      "guest_title": "Continuer en tant qu'invité",
      "guest_continue": "Continuer"
    },
    "orders": {
      "title": "Historique des commandes",
      "order_number": "Commande",
      "date": "Date",
      "payment_status": "Statut du paiement",
      "fulfillment_status": "Statut du traitement de la commande",
      "total": "Total",
      "none": "Vous n'avez pas placé de commande à ce jour."
    },
    "order": {
      "title": "Commande {{ name }}",
      "date_html": "Placée le {{ date }}",
      "cancelled_html": "Commande annulée le {{ date }}",
      "cancelled_reason": "Motif: {{ reason }}",
      "billing_address": "Adresse de facturation",
      "payment_status": "Statut du paiement",
      "shipping_address": "Adresse de livraison",
      "fulfillment_status": "Statut du traitement de la commande",
      "discount": "Rabais appliqué",
      "shipping": "Livraison",
      "tax": "Taxes",
      "product": "Produit",
      "sku": "SKU",
      "price": "Prix",
      "quantity": "Quantité",
      "total": "Total",
      "fulfilled_at_html": "Traitée le {{ date }}",
      "subtotal": "Sous-total"
    },
    "recover_password": {
      "title": "Réinitialiser votre mot de passe",
      "email": "Courriel",
      "submit": "Soumettre",
      "cancel": "Annuler",
      "subtext": "Nous vous ferons parvenir un courriel pour réinitialiser votre mot de passe.",
      "success": "Nous vous avons fait parvenir un courriel pour réinitialiser votre mot de passe."
    },
    "reset_password": {
      "title": "Réinitialiser le mot de passe du compte",
      "subtext": "Entrez un nouveau mot de passe pour {{ email }}",
      "submit": "Réinitialiser le mot de passe",
      "password": "Mot de passe",
      "password_confirm": "Confirmer le mot de passe"
    },
    "register": {
      "title": "Créer un compte",
      "first_name": "Prénom",
      "last_name": "Nom",
      "email": "Courriel",
      "submit": "Créer",
      "cancel": "Retour à la boutique",
      "password": "Mot de passe"
    }
  },
  "home_page": {
    "onboarding": {
      "product_title": "Titre du produit",
      "product_description": "Cette partie est utilisée pour la fiche du produit. Parlez aux clients de l'allure, du ressenti et du style de votre produit. Ajoutez des détails sur la couleur, les matériaux utilisés, le dimensionnement, et où il a été fabriqué.",
      "collection_title": "Titre de la collection",
      "no_content": "Cette section ne contient actuellement aucun contenu. Ajoutez-en en utilisant la barre latérale."
    }
  },
  "layout": {
    "cart": {
      "title": "Panier"
    },
    "customer": {
      "account": "Compte",
      "log_out": "Se déconnecter",
      "log_in": "Se connecter",
      "create_account": "Créer un compte"
    },
    "footer": {
      "social_platform": "{{ name }} sur {{ platform }}"
    }
  },
  "products": {
    "general": {
      "color_swatch_trigger": "Couleur",
      "size_trigger": "Taille",
      "size_chart": "Tableau des tailles",
      "save_html": "Épargnez {{ saved_amount }}",
      "collection_return": "Retour à {{ collection }}",
      "next_product": "Suivant: {{ title }}",
      "sale": "Réduit",
      "sale_price": "Prix réduit",
      "regular_price": "Prix régulier",
      "from_text_html": "À partir de {{ price }}",
      "recent_products": "Vu récemment",
      "reviews": "Commentaires"
    },
    "product": {
      "description": "Description",
      "in_stock_label": "En stock",
      "stock_label": {
        "one": "Stock réduit - {{ count }} item over",
        "other": "Stock réduit - {{ counts }} articles sur"
      },
      "sold_out": "Épuisé",
      "unavailable": "Non disponible",
      "quantity": "Quantité",
      "add_to_cart": "Ajouter au panier",
      "preorder": "Pré-commander",
      "include_taxes": "Taxes incluses.",
      "shipping_policy_html": "<a href='{{ link }}'>Frais d'expédition</a> calculés lors du passage à la caisse.",
      "will_not_ship_until": "Sera expédié après {{ date }}",
      "will_be_in_stock_after": "Sera en stock à compter de {{ date }}",
      "waiting_for_stock": "Inventaire sur le chemin",
      "view_in_space": "Afficher dans votre espace",
      "view_in_space_label": "Affichage dans votre espace, charge l'article dans une fenêtre de réalité augmentée"
    }
  },
  "store_availability": {
    "general": {
      "view_store_info": "Afficher les informations de magasin",
      "check_other_stores": "Vérifier la disponibilité dans d'autres magasins",
      "pick_up_available": "Récupération disponible",
      "pick_up_currently_unavailable": "Récupération actuellement indisponible",
      "pick_up_available_at_html": "Récupération disponible à <strong> {{location_name}} </strong>",
      "pick_up_unavailable_at_html": "Récupération actuellement indisponible à <strong> {{location_name}} </strong>"
    }
  },
  "gift_cards": {
    "issued": {
      "title_html": "Votre carte-cadeau {{ shop }} d'une valeur de {{ value }}!",
      "subtext": "Voici votre carte-cadeau!",
      "disabled": "Désactivée",
      "expired": "Expirée le {{ expiry }}",
      "active": "Expire le {{ expiry }}",
      "redeem": "Entrez ce code lors du paiement pour utiliser votre carte-cadeau",
      "shop_link": "Boutique",
      "print": "Imprimer",
      "add_to_apple_wallet": "Ajouter à Apple Wallet"
    }
  },
  "date_formats": {
    "month_day_year": "%d %B, %Y"
  },
  "pagefly": {
    "products": {
      "product": {
        "regular_price": "Regular price",
        "sold_out": "Sold out",
        "unavailable": "Unavailable",
        "on_sale": "Sale",
        "quantity": "Quantity",
        "add_to_cart": "Add to cart",
        "back_to_collection": "Back to {{ title }}",
        "view_details": "View details"
      }
    },
    "article": {
      "tags": "Tags:",
      "all_topics": "All topics",
      "by_author": "by {{ author }}",
      "posted_in": "Posted in",
      "read_more": "Read more",
      "back_to_blog": "Back to {{ title }}"
    },
    "comments": {
      "title": "Leave a comment",
      "name": "Name",
      "email": "Email",
      "message": "Message",
      "post": "Post comment",
      "moderated": "Please note, comments must be approved before they are published",
      "success_moderated": "Your comment was posted successfully. We will publish it in a little while, as our blog is moderated.",
      "success": "Your comment was posted successfully! Thank you!",
      "comments_with_count": {
        "one": "{{ count }} comment",
        "other": "{{ count }} comments"
      }
    },
    "password_page": {
      "login_form_message": "Enter store using password:",
      "login_form_password_label": "Password",
      "login_form_password_placeholder": "Your password",
      "login_form_submit": "Enter",
      "signup_form_email_label": "Email",
      "signup_form_success": "We will send you an email right before we open!",
      "password_link": "Enter using password"
    }
  }
}
