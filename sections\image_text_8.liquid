
<section class="image_text_8"  style="background:{{section.settings.bg_color}};">
      <div class="wrapper">
 <div class="banner_image">
   <img src="{{ section.settings.bg_image |img_url :'master' }}">
 <img class="mobile_img" style="display:none;" src="{{ section.settings.mob_bg_image |img_url :'master' }}">
 </div>
  <div class="page_wrapper">
    <div class="content_wrapper">


        {% if section.settings.heading != blank %}
        <h2 class="heading">{{ section.settings.heading }}</h2>
        {% endif %}
        {% if section.settings.content != blank %}
        <div class="content">{{ section.settings.content }}</div>
        {% endif %}

   
    </div>
    </div>
  </div>
</section>

<style>


section.image_text_8 .wrapper .text_block {
    width: 100%;
    display: flex;
      gap: 40px;
    flex-direction: column;

}
section.image_text_8 .wrapper h2.heading {
    font-size: 74px;
    font-family: Helvetica-Bold;
    margin: 0;
  color:#ffffff;

      text-transform: none;
}
  section.image_text_8 .wrapper .text_block h2.heading p{
      margin: 0;
  }
section.image_text_8 .wrapper .content  {
        margin: 0;
    font-size: 25px;
    font-family: Helvetica-Bold;
    color: #ffffff;
    width: 100%;
    max-width: 470px;
  font-weight:700;
}
 section.image_text_8 {
    padding: 0px!important;
 }
   section.image_text_8 .wrapper{
   position: relative;
}
  .image_text_8 .banner_image {
    font-size: 0;
    line-height: 0;
    width: 100%;
}
  .image_text_8 .banner_image img{
    width: 100%;
}
  section.image_text_8 .page_wrapper {
    position: absolute;
    inset: 0;
    display: flex
;
    justify-content: center;
    align-items: center;
    padding: 100px 150px;
}
  section.image_text_8 .wrapper h2.heading p{
    margin:0;
  }
  section.image_text_8 .page_wrapper .content_wrapper {
    display: flex
;
    flex-direction: column;
    justify-content: start;
    align-items: start;
    height: 100%;
    width: 100%;
    gap: 50px;
}
  @media only screen and (min-width: 2600px) {
 section.image_text_8 .wrapper h2.heading {
    font-size: 135px;
 }
 section.image_text_8 .wrapper .content {
    font-size: 50px;
    max-width: 950px;
}
  }
   @media only screen and (max-width: 1600px) {
  section.image_text_8 {
    padding: 50px 0px;
}
     section.image_text_8 .page_wrapper {

    padding: 50px 60px;
}
   }
  @media only screen and (max-width: 1280px) {
section.image_text_8 .wrapper h2.heading {
    font-size: 48px;
}
     section.image_text_8 .wrapper .content {
    font-size: 20px;
     }
    section.image_text_8 .page_wrapper .content_wrapper {
    gap: 30px;
    }
  }
  @media only screen and (max-width: 1024px) {

  }
  @media only screen and (max-width: 840px) {
        section.image_text_8 {
        padding: 30px 0px;
    }
section.image_text_8 .page_wrapper {
    padding: 30px 20px;
}
    .image_text_8 .banner_image img {
          height: 710px;
        object-fit: cover;
        display: none;
        object-position: right;
}
.image_text_8 .banner_image img.mobile_img {
    display: flex !important
;
}
  }
  @media only screen and (max-width: 480px) {

  section.image_text_8 .wrapper h2.heading {
    font-size: 38px;
}
    section.image_text_8 .wrapper .content {
        font-size: 18px;
    }

  }


  
</style>

<script>
 $(document).ready(function() {
  // Hide all icon_items after the 14th one (index starts from 0)
  $('.image_text_8 .icon_item').slice(13).hide();

  // On click of .more_btn, show the hidden items and add a class
  $('.image_text_8 .more_btn').on('click', function() {
    $('.image_text_8 .icon_item').show(); // Show all items
    $(this).addClass('clicked').hide(); // Add class and hide "More" button
  });
});


 

  $(document).ready(function () {
    var mySwiper = new Swiper('.image_text_8 .timmer_swiper', {
      loop: true,
      slidesPerView: 1,
      spaceBetween: 0,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      effect: 'slide' // use 'fade' if you want fading
    });
  });



</script>


  

{% schema %}
{
  "name": "Image Text 8",
  "settings": [
    {
      "type": "color",
      "id": "bg_color",
      "default": "transparent",
      "label": "Backgroud Color"
    },
       {
      "type": "image_picker",
      "id": "bg_image",
      "label": "Background Image"
    },
       {
      "type": "image_picker",
      "id": "mob_bg_image",
      "label": "Background Mobile Image"
    },
    

      {
          "type": "richtext",
          "id": "heading",
          "label": "Heading"
        },
      {
          "type": "text",
          "id": "content",
          "label": "Content"
        },

      
  ],


  
  "presets": [
    {
      "name": "Image Text 8",
      "blocks": []
    }
  ]
}
{% endschema %}

