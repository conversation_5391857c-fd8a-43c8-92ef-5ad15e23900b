<section class="image_text_2"  style="background:{{section.settings.bg_color}}; {% if section.settings.bg_image != blank %}background-image: url({{ section.settings.bg_image | img_url: 'master' }}); background-size: cover; background-repeat: no-repeat; background-position: center;{% endif %};">
  <div class="page_wrapper">
    <div class="wrapper">
      <div class="text_block">
        {% if section.settings.heading != blank %}
        <h2 class="heading">{{ section.settings.heading }}</h2>
        {% endif %}
        {% if section.settings.content != blank %}
        <div class="content">{{ section.settings.content }}</div>
        {% endif %}
      </div>

<div class="icon_text_wrapper">
  {% for block in section.blocks %}
<div class="icon_text_item">
    {% if block.settings.icon !=blank %}
  <div class="icon_icon"><img src="{{ block.settings.icon |img_url :'master' }}"></div>
  {% endif %}
    <div class="icon_heading">
      {% if block.settings.heading_icon !=blank %}
      <img src="{{ block.settings.heading_icon |img_url :'master' }}">
      {% endif %}
{% if block.settings.heading != blank or block.settings.sub_heading != blank %}
    <p class="item_heading">{{ block.settings.heading }}<span>{{ block.settings.sub_heading }}</span></p>
      {% endif %}
       </div>
  {% if block.settings.content != blank %}
    <p class="item_content">{{ block.settings.content }}</p>
      {% endif %}
</div>
  {% endfor %}
  
</div>
      
      
    </div>
  </div>
    <div class="image_block">
        {% if section.settings.image != blank %}
        <img src="{{ section.settings.image |img_url :'master' }}">
        {% endif %}
      </div>
</section>

<style>


section.image_text_2 .wrapper .text_block {
    width: 100%;
    display: flex
;
    flex-direction: column;
    gap: 10px;
}
section.image_text_2 .wrapper .text_block h2.heading {
    font-size: 74px;
    font-family: Helvetica-Bold;
    margin: 0;
    color: #2a2b2b;
    width: 100%;
    max-width: 620px;
    margin: 0 auto;
      text-align: center;
      text-transform: none;
}
section.image_text_2 .wrapper .text_block .content p {
    margin: 0;
    font-size: 25px;
    font-family: Helvetica-Bold;
    color: #676666;
  font-weight: 700;
}

  section.image_text_2 .wrapper .text_block .content {
    width: 100%;
    max-width: 100%;
    text-align: center;
    padding-top:50px;
}
  .image_text_2 .icon_text_wrapper {
    display: grid
;
    grid-template-columns: repeat(7, auto);
    padding: 70px 0;
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
}
.image_text_2 .icon_text_wrapper .icon_text_item {
    width: 100%;
}
  .image_text_2 .icon_text_wrapper .icon_text_item .icon_icon {
    width: 100%;
    max-width: 50px;
    font-size: 0;
}
  .image_text_2 .icon_text_wrapper .icon_text_item .icon_icon img{
    width: 100%;
}
  .image_text_2 .icon_text_wrapper .icon_text_item .icon_heading {
margin: 0;
    display: flex
;
    align-items: center;
    justify-content: start;
    padding-top: 10px;
    color: #676666;
}
  .image_text_2 .icon_text_wrapper .icon_text_item .icon_heading p.item_heading {
    margin: 0;
    font-size: 18px;
    font-family: Helvetica-Bold;
    color: #676666;
      font-weight: 700;
}
  .image_text_2 .icon_text_wrapper .icon_text_item .icon_heading p.item_heading span {
    font-size: 11px;
}
  .image_text_2 .icon_text_wrapper .icon_text_item .icon_heading img {
    width: 30px;
    font-size: 0;
    line-height: 0;
    position: relative;
    top: 3px;
    margin-right: 2px;
}
  .image_text_2 .icon_text_wrapper .icon_text_item .item_content {
    margin: 0;
    font-size: 14px;
    font-family: Helvetica-Bold;
    color: #676666;
    font-weight: 700;
}
.image_text_2 .image_block {
    width: 100%;
    max-width: 100%;
    font-size: 0;
}
  .image_text_2 .image_block img{
    width: 100%;
box-shadow: 0 10px 10px -10px rgba(0, 0, 0, 0.3);
}
  section.image_text_2 {
    padding: 50px 0px;
}
  section.image_text_2 .page_wrapper{
    padding:0 150px;
  }
  @media only screen and (min-width: 2600px) {
    section.image_text_2 .wrapper .text_block h2.heading {
    font-size: 135px;
    max-width: 1130px;
    }
    .image_text_2 .icon_text_wrapper .icon_text_item .icon_icon {
    max-width: 100px;
    }
    .image_text_2 .icon_text_wrapper .icon_text_item .icon_heading img {
    width: 40px;
    }
  section.image_text_2 .wrapper .text_block .content p {
 
    font-size: 51px;
  }
    .image_text_2 .icon_text_wrapper .icon_text_item .icon_heading p.item_heading {
    margin: 0;
    font-size: 33px;
    }
    .image_text_2 .icon_text_wrapper .icon_text_item .item_content {
    margin: 0;
    font-size: 29px;
    }
    .image_text_2 .icon_text_wrapper .icon_text_item .icon_heading p.item_heading span {
    font-size: 21px;
}
    .image_text_2 .icon_text_wrapper {
    max-width: 2200px;
    }
  }
  
    @media only screen and (max-width: 1600px) {
  section.image_text_2 {
    padding: 25px 0px;
}
       section.image_text_2 .page_wrapper{
    padding:0 60px;
  }
      section.image_text_2 .wrapper .text_block .content {
    padding-top: 30px;
}
      
    }
    @media only screen and (max-width: 1280px) {
    section.image_text_2 .wrapper .text_block h2.heading {
    font-size: 48px;
    max-width: 400px;
    }
       section.image_text_2 .wrapper .text_block .content p {
  
    font-size: 20px;
       }
    }
   @media only screen and (max-width: 840px) {
        section.image_text_2 {
        padding: 30px 0px;
    }
      section.image_text_2 .page_wrapper{
    padding:0 20px;
  }
     .image_text_2 .icon_text_wrapper {
    display: grid
;
    grid-template-columns: repeat(4, auto);
         gap: 20px;
     }
   }
   @media only screen and (max-width: 580px) {
  .image_text_2 .icon_text_wrapper {
    display: grid
;
    grid-template-columns: repeat(2, auto);
    padding: 50px 0;
  }
   }
     @media only screen and (max-width: 480px) {
section.image_text_2 .wrapper .text_block h2.heading {
        font-size: 38px;
        max-width: 318px;
    }
           section.image_text_2 .wrapper .text_block .content {
        padding-top: 20px;
    }
       section.image_text_2 .wrapper .text_block .content p {

    font-size: 18px;
       }
       
.image_text_2 .icon_text_wrapper .icon_text_item .icon_icon {
    width: 100%;
    max-width: 40px;
}
       .image_text_2 .icon_text_wrapper .icon_text_item {

    display: flex
;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}
       .image_text_2 .icon_text_wrapper .icon_text_item .icon_heading img {
    width: 20px;
       }
       section.image_text_2 .wrapper .text_block .content p {
    margin: 0;
    font-size: 20px;
       }
     }

</style>




  

{% schema %}
{
  "name": "Image Text 1",
  "settings": [
    {
      "type": "color",
      "id": "bg_color",
      "default": "transparent",
      "label": "Backgroud Color"
    },
       {
      "type": "image_picker",
      "id": "bg_image",
      "label": "Background Image"
    },
      {
          "type": "text",
          "id": "heading",
          "label": "Heading"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content"
        },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    }
    
    
    
    
  ],
  "blocks": [
    {
      "type": "block",
      "name": "Block",
      "settings": [
        {
      "type": "image_picker",
      "id": "icon",
      "label": "Icon"
    },
        {
          "type": "image_picker",
          "id": "heading_icon",
          "label": "Heading Icon"
        },
         {
          "type": "text",
          "id": "heading",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "sub_heading",
          "label": "Sub Heading"
        },
        {
          "type": "text",
          "id": "content",
          "label": "Content"
        },
    
    
      
      ]
    }
  ],
  "presets": [
    {
      "name": "Image Text 2",
      "blocks": []
    }
  ]
}
{% endschema %}

