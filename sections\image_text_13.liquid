
<section class="image_text_13"  style="background:{{section.settings.bg_color}}; {% if section.settings.bg_image != blank %}background-image: url({{ section.settings.bg_image | img_url: 'master' }}); background-size: cover; background-repeat: no-repeat; background-position: center;{% endif %};">
  <div class="page_wrapper">
    <div class="wrapper">
      <div class="text_block">

        {% if section.settings.heading != blank %}
        <h2 class="heading">{{ section.settings.heading }}</h2>
        {% endif %}
        <div class="image_container"> 
        <div class="image_wrapper">
               
      {% for block in section.blocks %}
        <div classs="image_item">
      {% if block.settings.image !=blank %}
        <div class="image"><img src="{{ block.settings.image |img_url :'master' }}"></div>
      {% endif %}
      
        </div>
      {% endfor %}
        </div>
        </div>
 <div class="image_container text_container"> 
        <div class="image_wrapper">
               
      {% for block in section.blocks %}
        <div classs="image_item">
     
        {% if block.settings.text !=blank %}
        <p class="text">{{ block.settings.text }}</p>
          {% endif %}
        </div>
      {% endfor %}
        </div>
        </div>

        
        </div>
      
    </div>
  </div>
</section>

<style>


section.image_text_13 .wrapper .text_block {
    width: 100%;
    display: flex;
      gap: 30px;
    flex-direction: column;

}
section.image_text_13 .wrapper .text_block h2.heading {
    font-size: 74px;
    font-family: Helvetica-Bold;
    margin: 0;
  color:#2a2b2b;

      text-transform: none;
          margin: 0 auto;
        text-align: center;
      width: 100%;
    max-width: 1000px;
}
  section.image_text_13 .wrapper .text_block h2.heading p{
      margin: 0;
  }



    section.image_text_13 {
    padding: 50px 150px;
}
  .image_text_13 .image {
     width: 100%;
    font-size: 0;
}
.image_text_13 .image img {
    width: 100%;
 
}
  section.image_text_13 .wrapper .text_block .image_wrapper {
  grid-template-columns: 1fr 1fr 1fr;
    display: grid
;
    align-items: start;
    gap: 0;
    width: 100%;
    max-width: 90%;
    margin: 0 auto;
}
  section.image_text_13 .wrapper .text_block .image_container {
    background: #f7f7f7;
    padding: 40px 40px 0px 40px;
    width: 100%;
}
  section.image_text_13 .wrapper .text_block .image_container.text_container {
    padding-top: 0;
    background: transparent;
}
  section.image_text_13 .wrapper .text_block .image_container.text_container p.text {
    font-size: 37px;
    font-family: Helvetica-Bold;
    text-transform: none;
    text-align: center;
    width: 100%;
    max-width: 1000px;
    font-weight: 600;
    color: #86868b;
    margin: 0;
}
  @media only screen and (min-width: 2600px) {
section.image_text_13 .wrapper .text_block h2.heading {
        font-size: 135px;
        width: 100%;
        max-width: 1810px;
}
section.image_text_13 .wrapper .text_block .image_container.text_container p.text {
    font-size: 74px;
  
}

  }
   @media only screen and (max-width: 1600px) {
  section.image_text_13 {
    padding: 25px 60px;
}
   }
  @media only screen and (max-width: 1280px) {
section.image_text_13 .wrapper .text_block h2.heading {
    font-size: 48px;
 max-width: 730px;
}
    section.image_text_13 .wrapper .text_block .image_container.text_container p.text {
    font-size: 28px;
    }
    section.image_text_13 .wrapper .text_block {
    gap: 20px;
    }
  
  }
  @media only screen and (max-width: 1024px) {
 
  }
  @media only screen and (max-width: 840px) {
        section.image_text_13 {
        padding: 30px 20px;
    }
  section.image_text_13 .wrapper .text_block .image_wrapper {
     width: 100%;
    max-width: 100%;
  }
        section.image_text_13 .wrapper .text_block .image_container.text_container p.text {
        font-size: 20px;
    }
  }
  @media only screen and (max-width: 480px) {

  section.image_text_13 .wrapper .text_block h2.heading {
    font-size: 38px;
}
  section.image_text_13 .wrapper .text_block .image_container {

    padding: 40px 20px 0px 20px;
  }
        section.image_text_13 .wrapper .text_block .image_container.text_container p.text {
        font-size: 16px;
    }

  }


  
</style>


  

{% schema %}
{
  "name": "Image Text 13",
  "settings": [
    {
      "type": "color",
      "id": "bg_color",
      "default": "transparent",
      "label": "Backgroud Color"
    },
       {
      "type": "image_picker",
      "id": "bg_image",
      "label": "Background Image"
    },
    

      {
          "type": "richtext",
          "id": "heading",
          "label": "Heading"
        },
  
     
               
      
  ],

 "blocks": [
    {
      "type": "block",
      "name": "Block",
      "settings": [

        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
         {
          "type": "text",
          "id": "text",
          "label": "Text"
        },
               
    
    
      
      ]
    }
  ],
  
  "presets": [
    {
      "name": "Image Text 13",
      "blocks": []
    }
  ]
}
{% endschema %}

