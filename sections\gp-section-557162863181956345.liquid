

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-557162863181956345.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-557162863181956345.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-557162863181956345.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-557162863181956345.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-557162863181956345.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-557162863181956345.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-557162863181956345.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-557162863181956345.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-557162863181956345.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-557162863181956345.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-557162863181956345.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-557162863181956345.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-557162863181956345.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-557162863181956345.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-557162863181956345.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-557162863181956345.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-557162863181956345.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-557162863181956345.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-557162863181956345.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-557162863181956345.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-557162863181956345.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-557162863181956345.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-557162863181956345.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-557162863181956345.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-557162863181956345.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-557162863181956345.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-557162863181956345.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-557162863181956345.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-557162863181956345.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-557162863181956345.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-557162863181956345.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-557162863181956345.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-557162863181956345.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-557162863181956345.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-557162863181956345.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-557162863181956345.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-557162863181956345.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-557162863181956345.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-557162863181956345.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-557162863181956345.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-557162863181956345.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-557162863181956345.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-557162863181956345.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-557162863181956345.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-557162863181956345.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-557162863181956345.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-557162863181956345.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-557162863181956345.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-557162863181956345.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-557162863181956345.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-557162863181956345.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-557162863181956345.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-557162863181956345.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-557162863181956345.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-557162863181956345.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-557162863181956345.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-557162863181956345.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-557162863181956345.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-557162863181956345.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-557162863181956345.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-557162863181956345.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-557162863181956345.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-557162863181956345.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-557162863181956345.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-557162863181956345.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-557162863181956345.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-557162863181956345.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-557162863181956345.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-557162863181956345.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-557162863181956345.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-557162863181956345 .gp-relative{position:relative}.gps-557162863181956345 .gp-z-1{z-index:1}.gps-557162863181956345 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-557162863181956345 .gp-mb-0{margin-bottom:0}.gps-557162863181956345 .gp-flex{display:flex}.gps-557162863181956345 .gp-inline-flex{display:inline-flex}.gps-557162863181956345 .gp-grid{display:grid}.gps-557162863181956345 .\!gp-hidden{display:none!important}.gps-557162863181956345 .gp-hidden{display:none}.gps-557162863181956345 .gp-h-full{height:100%}.gps-557162863181956345 .gp-max-w-full{max-width:100%}.gps-557162863181956345 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-557162863181956345 .gp-flex-col{flex-direction:column}.gps-557162863181956345 .gp-items-center{align-items:center}.gps-557162863181956345 .gp-justify-center{justify-content:center}.gps-557162863181956345 .gp-gap-y-0{row-gap:0}.gps-557162863181956345 .gp-overflow-hidden{overflow:hidden}.gps-557162863181956345 .gp-break-words{overflow-wrap:break-word}.gps-557162863181956345 .gp-rounded-none{border-radius:0}.gps-557162863181956345 .gp-text-center{text-align:center}.gps-557162863181956345 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-557162863181956345 .gp-no-underline{text-decoration-line:none}.gps-557162863181956345 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-557162863181956345 .gp-duration-200{transition-duration:.2s}.gps-557162863181956345 .gp-duration-300{transition-duration:.3s}.gps-557162863181956345 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-557162863181956345 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-557162863181956345 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-557162863181956345 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-557162863181956345 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-557162863181956345 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-557162863181956345 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-557162863181956345 .tablet\:\!gp-hidden{display:none!important}.gps-557162863181956345 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-557162863181956345 .mobile\:\!gp-hidden{display:none!important}.gps-557162863181956345 .mobile\:gp-hidden{display:none}}.gps-557162863181956345 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-557162863181956345 .\[\&_p\]\:gp-inline p{display:inline}.gps-557162863181956345 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-557162863181956345 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-557162863181956345 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      id="gIIJMl7Whc" data-id="gIIJMl7Whc"
        style="--blockPadding:base;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mt:72px;--pt:var(--g-s-2xl);--pb:0px;--mt-mobile:12px;--pt-mobile:12px;--pl-mobile:24px;--pb-mobile:0px;--pr-mobile:24px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gIIJMl7Whc gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gDtn8fonJS gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      parentTag="Col" id="gqxGwNuSOo" data-id="gqxGwNuSOo"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-l);--cg:50px;--pc:start;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gqxGwNuSOo gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gkqws57YAy gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gAkxnqTXY4">
    <div
      parentTag="Col"
        class="gAkxnqTXY4 "
        style="--ta:center;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:15px;--mb-mobile:16px"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:50%;--w-tablet:50%;--w-mobile:100%;--ta:center;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:40px;--size-tablet:30px;--size-mobile:22px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggAkxnqTXY4_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gCVjQ_h6Ry">
    <div
      parentTag="Col"
        class="gCVjQ_h6Ry "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:31px;--mb-mobile:18px"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:60%;--w-tablet:60%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#424242;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:16px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggCVjQ_h6Ry_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:31px;--mb-mobile:0px;--ta:center"
    
  >
    <style>
    .gsNIkEMPV0.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      
    }

    .gsNIkEMPV0:hover::before {
      
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .gsNIkEMPV0:hover .gp-button-icon {
      color: undefined;
    }

     .gsNIkEMPV0 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gsNIkEMPV0:hover .gp-button-price {
      color: undefined;
    }

    .gsNIkEMPV0 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gsNIkEMPV0 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gsNIkEMPV0:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#g5UWRTEtPs" target="_self" data-id="gsNIkEMPV0" aria-label="<p>SHOP THE FEMALE COLLECTION</p>"
      
      data-state="idle"
      class="gsNIkEMPV0 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center"
      style="--hvr-bg:#1180FF;--bg:#096de3;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--w:Auto;--w-tablet:Auto;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:32px;--pr:32px;--pt:12px;--pb:12px;--pl-tablet:32px;--pr-tablet:32px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:32px;--pr-mobile:32px;--pt-mobile:12px;--pb-mobile:12px;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--fs:normal;--weight:600;--ls:1px;--lh-tablet:150%"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggsNIkEMPV0_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 8",
    "tag": "section",
    "class": "gps-557162863181956345 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555482666321838914&sectionId=557162863181956345)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggAkxnqTXY4_text","label":"ggAkxnqTXY4_text","default":"READY TO TRANSFORM YOUR EVERYDAY HEALTH?"},{"type":"html","id":"ggCVjQ_h6Ry_text","label":"ggCVjQ_h6Ry_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:18px;\">Experience the difference a GardPro smartwatch can make in your daily routine. Track your vitals, stay motivated, and enjoy the peace of mind that comes with advanced health insights—all from your wrist.</span></p>"},{"type":"html","id":"ggsNIkEMPV0_label","label":"ggsNIkEMPV0_label","default":"<p>SHOP THE FEMALE COLLECTION</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
