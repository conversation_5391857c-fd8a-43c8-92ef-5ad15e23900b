{%- paginate blog.articles by 21 -%}

<div data-section-id="{{ section.id }}" data-section-type="blog">
  <div class="page-width page-content">

    {%- render 'breadcrumbs' -%}

    <header class="section-header{% if section.settings.blog_show_tag_filter and blog.tags.size > 0 %}{% unless settings.type_headers_align_text %} section-header--with-link{% endunless %}{% endif %}">
      <h1 class="section-header__title">
        {{ blog.title }}

        {%- if section.settings.blog_show_rss -%}
          <a href="{{ shop.url }}{{ blog.url }}.atom" class="rss-link">
            <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-rss" viewBox="0 0 20 20"><path fill="#444" d="M5.903 16.586a2.489 2.489 0 1 1-4.977 0 2.489 2.489 0 0 1 4.977 0zM12.956 19.075H9.43c0-4.688-3.817-8.505-8.505-8.505V7.044c6.638 0 12.031 5.393 12.031 12.031z"/><path fill="#444" d="M15.445 19.075c0-8.028-6.493-14.52-14.52-14.52V.925c10.019 0 18.15 8.131 18.15 18.15h-3.63z"/></svg>
            <span class="icon__fallback-text">RSS</span>
          </a>
        {%- endif -%}
      </h1>

      {%- if section.settings.blog_show_tag_filter and blog.tags.size > 0 -%}
        <select id="BlogTagFilter">
          <option value="/blogs/{{ blog.handle }}">All</option>
          {%- for tag in blog.all_tags -%}
            {% if tag contains "_" %}{%- assign tag_starts_with = tag | slice: 0 -%}{% if tag_starts_with == "_" %}{% if tag_count %}{%- assign tag_count = tag_count | minus: 1 | at_least: 0 -%}{% endif %}{% continue %}{% endif %}{% endif %}
            <option value="/blogs/{{ blog.handle }}/tagged/{{ tag | handleize }}" {% if current_tags contains tag %}selected{% endif %}>{{ tag }}</option>
          {%- endfor -%}
        </select>
      {%- endif -%}
    </header>

    <div class="grid grid--uniform">
      {%- for article in blog.articles -%}
        {%- render 'article-grid-item', article: article, grid_item_width: 'medium-up--one-third', per_row: '3', image_size: section.settings.blog_image_size -%}
      {%- endfor -%}
    </div>

    {%- if paginate.pages > 1 -%}
      {%- render 'pagination', paginate: paginate -%}
    {%- endif -%}

  </div>
</div>

{%- endpaginate -%}

{% schema %}
{
  "name": "t:sections.blog-template.name",
  "settings": [
    {
      "type": "checkbox",
      "id": "blog_show_tag_filter",
      "label": "t:sections.blog-template.settings.blog_show_tag_filter.label"
    },
    {
      "type": "checkbox",
      "id": "blog_show_rss",
      "label": "t:sections.blog-template.settings.blog_show_rss.label"
    },
    {
      "type": "checkbox",
      "id": "blog_show_tags",
      "label": "t:sections.blog-template.settings.blog_show_tags.label"
    },
    {
      "type": "checkbox",
      "id": "blog_show_date",
      "label": "t:sections.blog-template.settings.blog_show_date.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "blog_show_comments",
      "label": "t:sections.blog-template.settings.blog_show_comments.label",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "blog_show_author",
      "label": "t:sections.blog-template.settings.blog_show_author.label"
    },
    {
      "type": "checkbox",
      "id": "blog_show_excerpt",
      "label": "t:sections.blog-template.settings.blog_show_excerpt.label"
    },
    {
      "type": "select",
      "id": "blog_image_size",
      "label": "t:sections.blog-template.settings.blog_image_size.label",
      "default": "wide",
      "options": [
        {
          "value": "natural",
          "label": "t:sections.blog-template.settings.blog_image_size.options.natural.label"
        },
        {
          "value": "square",
          "label": "t:sections.blog-template.settings.blog_image_size.options.square.label"
        },
        {
          "value": "landscape",
          "label": "t:sections.blog-template.settings.blog_image_size.options.landscape.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.blog-template.settings.blog_image_size.options.portrait.label"
        },
        {
          "value": "wide",
          "label": "t:sections.blog-template.settings.blog_image_size.options.wide.label"
        }
      ]
    }
  ]
}
{% endschema %}
